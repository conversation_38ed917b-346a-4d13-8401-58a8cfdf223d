import { Router } from "express";
import { z } from "zod";

import checkAuthMiddleware from "@/middlewares/checkAuth";
import { requestParamsValidator } from "@/middlewares/request.validators";
import { isProjectOwnerOrInvitedUserGuard } from "@/middlewares/project";
import * as sseController from "@/controllers/sse.controller";
import { projectIdParamsValidator } from "@/validators/project.validators";

export const sseRouter = Router();

// All SSE routes require authentication
sseRouter.use(checkAuthMiddleware);

// Project access guard - user must be project owner or invited member
const projectAccessGuard = isProjectOwnerOrInvitedUserGuard();

/**
 * @route GET /api/sse/projects/:projectId
 * @desc Subscribe to Server-Sent Events for a specific project
 * @access Private (Project member or owner)
 * @rateLimited Yes (5 connections per hour per user)
 * 
 * Flow:
 * 1. Authenticate user (checkAuthMiddleware)
 * 2. Validate projectId parameter
 * 3. Check project access permissions 
 * 4. Check rate limiting
 * 5. Establish SSE connection
 * 6. Store connection in database
 * 7. Setup heartbeat and cleanup handlers
 */
sseRouter.get(
  "/projects/:projectId",
  requestParamsValidator(projectIdParamsValidator),
  projectAccessGuard,
  sseController.subscribeToProject
);

export default sseRouter; 
