import express from "express";
import checkAuthMiddleware from "@/middlewares/checkAuth";

import { createJwtHandler } from "@/controllers/live-transcribe.controller";

const router = express.Router();

/**
 * @route POST /live-transcribe/jwt
 * @desc Get a JWT for a live transcription session
 * @access Private (requires authentication)
 */
router.post("/jwt", checkAuthMiddleware, createJwtHandler);

export { router as liveTranscribeRouter };
