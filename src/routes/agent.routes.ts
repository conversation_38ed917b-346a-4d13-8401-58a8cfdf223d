import { Router } from "express";

import {
  createConversation,
  createMessageStream,
  convertConversationToResource,
  getAvailableModels,
  validateModel,
  getSystemStatus,
} from "@/controllers/agent.controller";
import checkAuthMiddleware from "@/middlewares/checkAuth";

const router = Router();

// Route to create a new conversation
router.post("/conversations", checkAuthMiddleware, createConversation);

// Route to add a message with streaming response
router.post(
  "/conversations/:conversationId/message/stream",
  checkAuthMiddleware,
  createMessageStream
);

// Route to convert conversation to resource
router.post(
  "/conversations/:conversationId/convert-to-resource",
  checkAuthMiddleware,
  convertConversationToResource
);

// Route to get all available models
router.get("/models", checkAuthMiddleware, getAvailableModels);

// Route to validate a specific model
router.post("/models/validate", checkAuthMiddleware, validateModel);

// Route to get system status
router.get("/status", checkAuthMiddleware, getSystemStatus);

export default router;
