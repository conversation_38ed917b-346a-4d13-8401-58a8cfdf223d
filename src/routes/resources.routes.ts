import { express } from "@/handlers/expressErrorHandler";
import createResourceHandler from "@/handlers/resources/createResourceHandler";
import deleteResourceHandler from "@/handlers/resources/deleteResourceHandler";
import getResourceUploadUrlHandler from "@/handlers/resources/getResourceUploadUrlHandler";
import updateResourceHandler from "@/handlers/resources/updateResourceHandler";
import uploadResourceThumbnailHandler from "@/handlers/resources/uploadResourceThumbnailHandler";
import getSpriteSheetHandler from "@/handlers/resources/getSpritesheetHandler";
import checkAuthMiddleware from "@/middlewares/checkAuth";
import { getUserAccessibleProjectIdsContext, isProjectExists, isUserHaveAccessToProject, validateUserProjectRole } from "@/middlewares/project";
import { isResourceExists, isUserHaveAccessToResource } from "@/middlewares/resource";
import * as controller from "@/controllers/resource.controller";
import { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import extendResourceHandler from "@/handlers/resources/extendResourceHandler";
import { convertMarkdownResourceToBlobHandler } from "@/handlers/resources/convertMarkdownResource.handler";
import { validateBody } from "@/middlewares/validation";
import { convertMarkdownSchema } from "@/validators/conversion.validator";

// Resources
const router = express.Router()

const canUserEditResource = isUserHaveAccessToResource({ requiredEditAccess: true });
const canUserViewResource = isUserHaveAccessToResource({ requiredEditAccess: false });

router.post("/resource/upload-url",
    checkAuthMiddleware,
    getResourceUploadUrlHandler)

router.post("/resource/:id/extend-retention",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserEditResource,
    extendResourceHandler
)

router.post("/resource/:id/convert-download",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserViewResource,
    validateBody(convertMarkdownSchema),
    convertMarkdownResourceToBlobHandler
)

router.get("/resources",
    checkAuthMiddleware,
    getUserAccessibleProjectIdsContext,
    controller.getAllUserResources)

router.post("/resources/transcoding-status",
    checkAuthMiddleware,
    controller.getResourcesTranscodingStatus)

router.delete("/resource/:id",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserEditResource,
    deleteResourceHandler
)

router.get("/resource/:id",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserViewResource,
    controller.getResourceById)

router.put("/resource/:id",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserEditResource,
    updateResourceHandler
)
router.put("/resource/:id/upload-thumbnail",
    checkAuthMiddleware,
    isResourceExists('params.id'),
    canUserEditResource,
    uploadResourceThumbnailHandler
);

router.get("/resource/:id/spriteSheet",
    isResourceExists('params.id'),
    getSpriteSheetHandler
);

router.post("/resource",
    checkAuthMiddleware,
    isProjectExists('body.projectId', false),
    isUserHaveAccessToProject,
    validateUserProjectRole(ProjectMemberRole.EDITOR),
    getUserAccessibleProjectIdsContext,
    createResourceHandler
);


export const resourcesRouter = router;