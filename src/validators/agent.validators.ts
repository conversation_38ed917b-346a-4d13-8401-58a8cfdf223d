import { z } from "zod";
import { getAllModels } from "@/config/models";
import { AgentUtilsService } from "@/services/agent/utils/AgentUtilsService";

// Get all supported model IDs for validation
const ALL_MODEL_IDS = getAllModels().map((model) => model.id);

// Token usage statistics schema
const tokenUsageSchema = z
  .object({
    inputTokens: z.number().int().min(0).optional(),
    outputTokens: z.number().int().min(0).optional(),
    totalTokens: z.number().int().min(0).optional(),
    inputCharacters: z.number().int().min(0).optional(),
    outputCharacters: z.number().int().min(0).optional(),
    imageTokens: z.number().int().min(0).optional(), // Token cost for processing images
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "At least one usage field must be provided",
  });

// Chat message schema for history validation
const chatMessageSchema = z.object({
  role: z.enum(["user", "assistant"]),
  content: z.string().min(1),
  model: z.enum(ALL_MODEL_IDS as [string, ...string[]]).optional(),
  usage: tokenUsageSchema.optional(),
  cost: z.number().min(0).optional(),
  resources: z.array(z.string()).optional(),
});

// Schema for creating a new conversation
export const createConversationSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
});

// Schema for user preferences
const preferencesSchema = z.object({
  instructions: z.string().optional(),
  ragSearchEnabled: z.boolean().optional(),
});

// Image validation schema
const imageSchema = z
  .object({
    data: z.string().min(1, "Image data is required"),
    mimeType: z.enum(AgentUtilsService.imageConfig.supportedMimeTypes as any, {
      errorMap: () => ({
        message: `Unsupported image format. Supported formats: ${AgentUtilsService.imageConfig.supportedMimeTypes.join(
          ", "
        )}`,
      }),
    }),
    filename: z.string().min(1, "Filename is required").optional(),
    size: z
      .number()
      .int()
      .min(1)
      .max(AgentUtilsService.imageConfig.maxImageSize, {
        message: `Image size must be less than ${
          AgentUtilsService.imageConfig.maxImageSize / (1024 * 1024)
        }MB`,
      })
      .optional(),
  })
  .refine(
    (data) => {
      // Validate base64 data format
      if (data.data.startsWith("data:")) {
        const [header, base64Data] = data.data.split(",");
        if (!header.includes("base64") || !base64Data) {
          return false;
        }
        // Basic base64 validation
        try {
          Buffer.from(base64Data, "base64");
          return true;
        } catch {
          return false;
        }
      }
      // If not data URL, assume it's pure base64
      try {
        Buffer.from(data.data, "base64");
        return true;
      } catch {
        return false;
      }
    },
    {
      message: "Invalid image data format. Must be valid base64 or data URL",
    }
  );

// Schema for creating a new message within a conversation
export const createMessageSchema = z
  .object({
    model: z.enum(ALL_MODEL_IDS as [string, ...string[]]).optional(),
    message: z.string().min(1),
    history: z.array(chatMessageSchema).optional(),
    resources: z.array(z.string()).optional(), // Array of resource IDs
    images: z
      .array(imageSchema)
      .max(AgentUtilsService.imageConfig.maxImagesPerMessage, {
        message: `Maximum ${AgentUtilsService.imageConfig.maxImagesPerMessage} images allowed per message`,
      })
      .optional(), // Array of image data
    preferences: preferencesSchema.optional(),
  })
  .refine(
    (data) => {
      // Validate total payload size if images are provided
      if (data.images && data.images.length > 0) {
        let totalSize = 0;
        for (const image of data.images) {
          if (image.size) {
            totalSize += image.size;
          } else {
            // Estimate size from base64 data
            const base64Data = image.data.includes(",")
              ? image.data.split(",")[1]
              : image.data;
            totalSize += Math.floor((base64Data.length * 3) / 4); // Base64 to bytes conversion
          }
        }

        const maxTotalSize = 100 * 1024 * 1024; // 100MB total payload limit
        if (totalSize > maxTotalSize) {
          return false;
        }
      }
      return true;
    },
    {
      message: "Total image payload size exceeds 100MB limit",
    }
  );

// Export types inferred from schemas
export type TokenUsage = z.infer<typeof tokenUsageSchema>;
export type IChatMessage = z.infer<typeof chatMessageSchema>;
export type CreateMessageRequest = z.infer<typeof createMessageSchema>;
export type ImageData = z.infer<typeof imageSchema>;
