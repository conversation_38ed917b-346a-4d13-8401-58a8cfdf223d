import { z } from "zod";
import { ConversionFormat } from "@/services/conversion";

/**
 * Schema for validating markdown conversion requests
 * Ensures the format is one of the supported conversion formats
 */
export const convertMarkdownSchema = z.object({
  format: z.enum([ConversionFormat.DOCX, ConversionFormat.TXT])
    .describe("The target format for conversion (docx, pdf, txt)")
});

/**
 * Type definition for markdown conversion request
 */
export type ConvertMarkdownRequest = z.infer<typeof convertMarkdownSchema>; 
