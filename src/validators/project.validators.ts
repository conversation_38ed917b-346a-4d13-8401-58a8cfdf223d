// project invite user
import { InvitationStatus } from "@/schemas/project/ProjectInvitation.model";
import { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import { ProjectAccessRequestStatus } from "@/schemas/project/ProjectAccessRequest.model";
import { z } from "zod";
import {
  DEFAULT_PAGE_NUMBER,
  DEFAULT_PAGE_SIZE,
  MAX_PAGE_SIZE,
  DECIMAL_RADIX
} from "@/constants/pagination";

// Helper functions for common validation patterns
const createPageValidator = (defaultValue: string = DEFAULT_PAGE_NUMBER.toString()) => 
    z.string().optional().default(defaultValue).transform((val) => {
        const parsed = parseInt(val, DECIMAL_RADIX);
        if (isNaN(parsed) || parsed < DEFAULT_PAGE_NUMBER) {
            throw new Error('page must be a positive number');
        }
        return parsed;
    });

const createLimitValidator = (defaultValue: string = DEFAULT_PAGE_SIZE.toString(), maxLimit: number = MAX_PAGE_SIZE) => 
    z.string().optional().default(defaultValue).transform((val) => {
        const parsed = parseInt(val, DECIMAL_RADIX);
        if (isNaN(parsed) || parsed < DEFAULT_PAGE_NUMBER || parsed > maxLimit) {
            throw new Error(`limit must be between ${DEFAULT_PAGE_NUMBER} and ${maxLimit}`);
        }
        return parsed;
    });

const createOptionalIntValidator = () => 
    z.string().optional().transform((val) => val ? parseInt(val, DECIMAL_RADIX) : undefined);

const createOptionalBooleanValidator = () => 
    z.string().optional().transform((val) => val === 'true');

export const projectIdParamsValidator = z.object({
    projectId: z.string(),
});

/**
 * Project code params validator
 */
export const projectCodeParamsValidator = z.object({
    code: z.string(),
});

export const inviteUserToProjectBodyValidator = z.object({
    email: z.string().email(),
    role: z.nativeEnum(ProjectMemberRole),
});

export const inviteUserToProjectPayloadValidator = projectIdParamsValidator
    .merge(inviteUserToProjectBodyValidator);

export const acceptInvitationToProjectValidator = projectCodeParamsValidator

export const rejectInvitationToProjectValidator = projectCodeParamsValidator

export type ProjectIdParamsDTO = z.infer<typeof projectIdParamsValidator>;


export type InviteUserToProjectDTO = z.infer<typeof inviteUserToProjectBodyValidator>;
export type InviteUserToProjectPayloadDTO = z.infer<typeof inviteUserToProjectPayloadValidator>;
export type AcceptInvitationToProjectDTO = z.infer<typeof acceptInvitationToProjectValidator>;
export type RejectInvitationToProjectDTO = z.infer<typeof rejectInvitationToProjectValidator>;

/**
 * Create project shared link
 */
export const createProjectSharedLinkValidator = z.object({
    expriedInDays: z.number().optional(),
    maxAccessCount: z.number().optional(),
});

export const verifyProjectSharedLinkValidator = z.object({
    token: z.string(),
});

export type CreateProjectSharedLinkDTO = z.infer<typeof createProjectSharedLinkValidator> & ProjectIdParamsDTO;
export type VerifyProjectSharedLinkDTO = z.infer<typeof verifyProjectSharedLinkValidator>;

export const accessRequestIdParamsValidator = z.object({
    accessRequestId: z.string(),
}).merge(projectIdParamsValidator);

export const responseProjectAccessRequestValidator = z.object({
    isApproved: z.boolean(),
});

export type AccessRequestIdParamsDTO = z.infer<typeof accessRequestIdParamsValidator>;
export type ApproveProjectAccessRequestDTO = z.infer<typeof responseProjectAccessRequestValidator>;


/**
 * Change project member role
 */
export const changeProjectMemberRoleValidator = z.object({
    role: z.nativeEnum(ProjectMemberRole),
});

export const projectMemberIdParamsValidator = z.object({
    memberUserId: z.string(),
}).merge(projectIdParamsValidator);

export type UpdateProjectMemberParamsDTO = z.infer<typeof projectMemberIdParamsValidator>;
export type UpdateProjectMemberDTO = z.infer<typeof changeProjectMemberRoleValidator>;

/**
 * Project invitation status query validator
 */
export const projectInvitationStatusQueryValidator = z.object({
    status: z.nativeEnum(InvitationStatus).optional(),
});

export type ProjectInvitationStatusQueryDTO = z.infer<typeof projectInvitationStatusQueryValidator>;

/**
 * Project access request status query validator
 */
export const projectAccessRequestStatusQueryValidator = z.object({
    status: z.nativeEnum(ProjectAccessRequestStatus).optional(),
});

export const resendInvitationValidator = z.object({
    email: z.string().email()
});

/**
 * Cancel invitation params validator
 */
export const cancelInvitationParamsValidator = z.object({
    projectId: z.string(),
    inviteId: z.string(),
});

export type CancelInvitationParamsDTO = z.infer<typeof cancelInvitationParamsValidator>;

/**
 * Create project resource validator
 */
export const createProjectResourceValidator = z.object({
    uploadedFileName: z.string(),
    fileName: z.string(),
    fileSize: z.number(),
    fileLastModified: z.string(),
    transcript: z.array(z.object({
        sentence: z.string(),
        startTime: z.number(),
        endTime: z.number(),
        speakerId: z.string(),
        content: z.string(),
    })),
});

type LiveTranscript = {
    sentence: string;
    startTime: number;
    endTime: number;
    speakerId: string;
    content: string;
}

/**
 * Change invitation role validators
 */
export const changeInvitationRoleValidator = z.object({
    role: z.nativeEnum(ProjectMemberRole),
});

export const invitationIdParamsValidator = z.object({
    inviteId: z.string(),
}).merge(projectIdParamsValidator);

export type ChangeInvitationRoleDTO = z.infer<typeof changeInvitationRoleValidator>;
export type InvitationIdParamsDTO = z.infer<typeof invitationIdParamsValidator>;

/**
 * Project details query parameters validator
 */
export const projectDetailsQueryValidator = z.object({
    search: z.string().optional(),
    limit: createOptionalIntValidator(),
    page: createOptionalIntValidator(),
    sort: z.enum(['name', 'createdAt', 'fileSize', 'duration', 'fileLastModified']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
}).refine((data) => {
    // Validate that limit is a positive number if provided
    if (data.limit !== undefined && (isNaN(data.limit) || data.limit < DEFAULT_PAGE_NUMBER)) {
        return false;
    }
    // Validate that page is a positive number if provided
    if (data.page !== undefined && (isNaN(data.page) || data.page < DEFAULT_PAGE_NUMBER)) {
        return false;
    }
    return true;
}, {
    message: "limit and page must be positive numbers"
});

export type ProjectDetailsQueryDTO = z.infer<typeof projectDetailsQueryValidator>;

/**
 * Project resources query parameters validator
 */
export const projectResourcesQueryValidator = z.object({
    page: createPageValidator(),
    limit: createLimitValidator(),
    search: z.string().optional(),
    fileType: z.string().optional(), // comma-separated values like "video,audio"
    processingStatus: z.enum(['completed', 'processing', 'failed', 'pending']).optional(),
    sortBy: z.enum(['dateAdded', 'name', 'fileSize', 'lastModified', 'processingStatus']).optional().default('dateAdded'),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
}).refine((data) => {
    // Validate that page is a positive number
    if (data.page < DEFAULT_PAGE_NUMBER) {
        return false;
    }
    return true;
}, {
    message: "page must be positive"
});

export type ProjectResourcesQueryDTO = z.infer<typeof projectResourcesQueryValidator>;
