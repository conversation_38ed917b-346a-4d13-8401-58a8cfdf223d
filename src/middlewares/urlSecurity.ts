import { Request, Response, NextFunction } from "express";
import { log } from "@/services/logger";
import { urlShortenerService } from "@/services/urlShortener";


/**
 * Input validation middleware for short code resolution
 * Validates the short code format and HMAC signature to prevent malicious input
 */
export const validateShortCode = (req: Request, res: Response, next: NextFunction): void => {
  const { shortCode } = req.params;
  
  // Basic validation
  if (!shortCode) {
    res.status(400).json({ error: "Short code is required" });
    return;
  }
  
  // Validate length (should match the configured length in URL shortener service)
  if (shortCode.length < 5 || shortCode.length > 20) {
    res.status(400).json({ error: "Invalid short code format" });
    return;
  }
  
  // Validate characters (alphanumeric only)
  if (!/^[A-Za-z0-9]+$/.test(shortCode)) {
    res.status(400).json({ error: "Invalid short code characters" });
    return;
  }
  
  // Verify HMAC signature for security
  try {
    if (!urlShortenerService.verifyShortCode(shortCode)) {
      res.status(400).json({ error: "Invalid short code signature" });
      return;
    }
  } catch (error) {
    log.warn("Short code verification failed", { shortCode, error: error.message });
    res.status(400).json({ error: "Invalid short code format" });
    return;
  }
  
  next();
};
