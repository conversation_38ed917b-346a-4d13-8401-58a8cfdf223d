import express from "express";
import { log } from "@/services/logger";
import { aidaApiKey } from "@/config";

const checkApiKeyMiddleware = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const apiKey = req.headers["x-api-key"];

  if (!apiKey) {
    log.warn("API key missing from request");
     res.status(401).json({ error: "API key is required" });
  }

  if (apiKey !== aidaApiKey) {
    log.warn("Invalid API key provided");
     res.status(403).json({ error: "Invalid API key" });
  }

  next();
};

export default checkApiKeyMiddleware; 
