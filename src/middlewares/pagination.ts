import { Request, Response, NextFunction } from "express";
import {
  DEFAULT_PAGE_NUMBER,
  DEFAULT_PAGE_SIZE,
  MAX_PAGE_SIZE,
  DECIMAL_RADIX
} from "@/constants/pagination";

export interface PaginationQuery {
  page?: number;
  limit?: number;
  search?: string;
  sort?: string;
  sortOrder?: string;
}

export interface PaginationData {
  page: number;
  limit: number;
  offset: number;
  search?: string;
  sort?: string;
  sortOrder?: string;
}

/**
 * Middleware to parse and validate pagination parameters
 * Adds pagination data to res.locals.pagination
 */
export const paginationMiddleware = (defaultLimit: number = DEFAULT_PAGE_SIZE, maxLimit: number = MAX_PAGE_SIZE) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { page, limit, search, sort, sortOrder } = req.query as Record<string, string>;

    // Parse and validate page
    const parsedPage = page ? parseInt(page, DECIMAL_RADIX) : DEFAULT_PAGE_NUMBER;
    const validPage = isNaN(parsedPage) || parsedPage < DEFAULT_PAGE_NUMBER ? DEFAULT_PAGE_NUMBER : parsedPage;

    // Parse and validate limit
    const parsedLimit = limit ? parseInt(limit, DECIMAL_RADIX) : defaultLimit;
    const validLimit = isNaN(parsedLimit) || parsedLimit < DEFAULT_PAGE_NUMBER 
      ? defaultLimit 
      : Math.min(parsedLimit, maxLimit);

    // Calculate offset
    const offset = (validPage - DEFAULT_PAGE_NUMBER) * validLimit;

    // Validate sort field - allow only specific fields for security
    const allowedSortFields = ['name', 'createdAt', 'fileSize', 'duration', 'fileLastModified'];
    const validSort = sort && allowedSortFields.includes(sort) ? sort : undefined;

    // Validate sort order
    const validSortOrder = sortOrder && ['asc', 'desc'].includes(sortOrder.toLowerCase()) 
      ? sortOrder.toLowerCase() 
      : undefined;

    // Add pagination data to res.locals
    res.locals.pagination = {
      page: validPage,
      limit: validLimit,
      offset,
      search: search?.trim() || undefined,
      sort: validSort,
      sortOrder: validSortOrder,
    } as PaginationData;

    next();
  };
};

/**
 * Helper function to create pagination metadata for responses
 */
export const createPaginationMeta = (
  total: number,
  page: number,
  limit: number
) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNextPage: page < totalPages,
    hasPrevPage: page > DEFAULT_PAGE_NUMBER,
  };
}; 