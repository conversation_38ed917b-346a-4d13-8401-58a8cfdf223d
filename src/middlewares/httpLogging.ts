import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import { <PERSON><PERSON>og<PERSON> } from "@/services/logger/winston";
import { isHttpLoggingEnabled, LogLevel } from "@/services/logger/config";

// Sensitive headers to sanitize
const SENSITIVE_HEADERS = [
  "authorization",
  "cookie",
  "x-api-key",
  "x-auth-token",
  "x-access-token",
  "x-refresh-token",
];

// Sensitive body fields to sanitize
const SENSITIVE_BODY_FIELDS = [
  "password",
  "token",
  "secret",
  "key",
  "apiKey",
  "accessToken",
  "refreshToken",
  "authorization",
];

// API endpoints to exclude from logging (to reduce noise)
const EXCLUDED_ENDPOINTS = [
  "/api/sessions/ongoing",
  // Add more endpoints as needed
];

interface HttpLogData {
  requestId: string;
  method: string;
  url: string;
  path: string;
  query: any;
  requestHeaders: Record<string, string>;
  requestBody?: any;
  ip: string;
  userAgent: string;
  userId?: string;
  decodedToken?: any;
  feature: string;
  labels: {
    feature: string;
  };
}

class HttpLogger {
  private logger: <PERSON><PERSON>ogger;

  constructor() {
    this.logger = new WinstonLogger("HTTP-Request");
  }

  private sanitizeHeaders(
    headers: Record<string, any>
  ): Record<string, string> {
    const sanitized: Record<string, string> = {};

    Object.entries(headers).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      if (SENSITIVE_HEADERS.includes(lowerKey)) {
        sanitized[key] = "[REDACTED]";
      } else {
        sanitized[key] = Array.isArray(value)
          ? value.join(", ")
          : String(value);
      }
    });

    return sanitized;
  }

  private sanitizeData(data: any, sensitiveFields: string[]): any {
    if (!data || typeof data !== "object") {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.sanitizeData(item, sensitiveFields));
    }

    const sanitized: any = {};
    Object.entries(data).forEach(([key, value]) => {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.includes(lowerKey)) {
        sanitized[key] = "[REDACTED]";
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = this.sanitizeData(value, sensitiveFields);
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }

  private isExcludedEndpoint(url: string): boolean {
    return EXCLUDED_ENDPOINTS.some((excluded) => {
      // Check for exact match or if URL starts with the excluded endpoint
      return (
        url === excluded ||
        url.startsWith(excluded + "/") ||
        url.startsWith(excluded + "?")
      );
    });
  }

  private extractFeature(url: string): string {
    // Extract feature from URL path after /api
    // Handle both /api/v1/feature and /api/feature patterns
    const match = url.match(/\/api\/(?:v1\/)?([^\/?]+)/);
    if (match) {
      return match[1];
    }

    // Fallback: extract first path segment after /api
    const pathSegments = url.split("/").filter(Boolean);
    const apiIndex = pathSegments.findIndex((segment) => segment === "api");
    if (apiIndex !== -1 && apiIndex + 1 < pathSegments.length) {
      return pathSegments[apiIndex + 1];
    }

    return "unknown";
  }

  private getRequestData(req: Request): Partial<HttpLogData> {
    const feature = this.extractFeature(req.originalUrl);

    return {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      path: req.path,
      query: req.query,
      requestHeaders: this.sanitizeHeaders(req.headers),
      requestBody: req.body
        ? this.sanitizeData(req.body, SENSITIVE_BODY_FIELDS)
        : undefined,
      ip: req.ip || req.connection.remoteAddress || "unknown",
      userAgent: req.headers["user-agent"] || "unknown",
      userId: (req as any).locals?.user?.id,
      decodedToken: (req as any).locals?.user
        ? {
            id: (req as any).locals.user.id,
            email: (req as any).locals.user.email,
            name: (req as any).locals.user.name,
          }
        : undefined,
      feature,
      labels: {
        feature,
      },
    };
  }

  logRequest(req: Request): void {
    if (!isHttpLoggingEnabled()) return;

    // Skip logging for excluded endpoints
    if (this.isExcludedEndpoint(req.originalUrl)) return;

    const logData = this.getRequestData(req);

    this.logger.info("HTTP Request", {
      ...logData,
      logger: "HTTP-Request",
    });
  }
}

// Create singleton instance
const httpLogger = new HttpLogger();

// Middleware to log HTTP requests and responses
export const httpLoggingMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 1. FIRST: Generate request ID if not provided by client
  if (!req.requestId) {
    // Check if client provided X-Request-ID header
    let requestId = req.headers["x-request-id"] as string;

    // Validate request ID format - should be reasonable length and ASCII
    if (
      requestId &&
      (requestId.length > 64 || !/^[\x00-\x7F]*$/.test(requestId))
    ) {
      // Invalid format, generate new UUID instead
      requestId = uuidv4();
    } else if (!requestId) {
      // No request ID provided, generate new UUID
      requestId = uuidv4();
    }

    req.requestId = requestId;
  }

  // 2. Log request (uses existing req.requestId)
  httpLogger.logRequest(req);

  // 3. Set X-Request-ID header in response
  res.setHeader("X-Request-ID", req.requestId);

  next();
};

// Export the logger instance for direct use if needed
export { httpLogger };
