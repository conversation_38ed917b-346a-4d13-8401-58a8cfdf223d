import { Request, Response, NextFunction } from "express";
import { getProjectById } from "@/services/project/project.service";
import { getAllProjectMembersByUserId, getProjectMember } from "@/services/project/projectMember.service";
import { deepGetRequestPropsData } from "./utils";
import { isRoleGTE, ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import ProjectModel from "@/schemas/project/Project.model";
import { Op } from "sequelize";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { findOrCreateDefaultProject } from "@/utils";

/**
 * @Authenticated_Required
 * Guard to check if the user is the owner of the project
 * @param projectIdRequestPath - The path of the project id in the request:  params.projectId | query.projectId | body.projectId
 * @returns 
 */
export const projectOwnerGuard = (projectIdRequestPath: string = 'params.projectId') => {
    return async (req: Request, res: Response, next: NextFunction) => {
        const projectId = deepGetRequestPropsData(req, projectIdRequestPath);

        if (!projectId) {
            res.status(400).json({
                success: false,
                message: "Project id is required",
                statusCode: 400,
            });
            return
        }

        const project = await getProjectById(projectId);

        if (!project.success) {
            res.status(project.statusCode).json(project);
            return;
        }

        const checkIsOwner = isProjectOwner(project.data.createdById, res.locals.user.id);

        if (!checkIsOwner.isOwner) {
            res.status(403).json({
                success: false,
                message: checkIsOwner.message,
                statusCode: 403,
            });
            return;
        }

        next();
    }
}


/**
 * @Authenticated_Required
 * Guard to check if the user is the owner of the project or a member of the project
 * @param projectIdRequestPath - The path of the project id in the request:  params.projectId | query.projectId | body.projectId
 * @returns res.locals.isProjectOwner or res.locals.isProjectMember and res.locals.userProjectRole
 */
export const isProjectOwnerOrInvitedUserGuard = (projectIdRequestPath: string = 'params.projectId') => {
    return async (req: Request, res: Response, next: NextFunction) => {
        const projectId = deepGetRequestPropsData(req, projectIdRequestPath);
        const project = await getProjectById(projectId);

        if (!project.success) {
            res.status(project.statusCode).json(project);
            return;
        }

        const checkIsOwner = isProjectOwner(project.data.createdById, res.locals.user.id);

        if (checkIsOwner.isOwner) {
            res.locals.isProjectOwner = true;
            next();
            return;
        }

        const invitedUser = await getProjectMember(projectId, res.locals.user.id);

        if (invitedUser.success && invitedUser.data) {
            res.locals.isProjectMember = true
            res.locals.userProjectRole = invitedUser.data.role;
            next();
            return;
        }

        res.status(403).json({
            success: false,
            message: "You are not a member nor the owner of this project",
            statusCode: 403,
        });

        return;
    }
}


/**
 * @Authenticated_Required
 * Should not be call before checkAuthMiddleware
 * Get all projects that the user has access to
 * @param req - The request object
 * @param res - The response object
 * @param next - The next function
 * @returns res.locals.accessibleProjects
 */
export const getUserAccessibleProjectIdsContext = async (req: Request, res: Response, next: NextFunction) => {
    if (!res.locals.user) {
        res.status(401).json({
            success: false,
            message: "User not found",
            statusCode: 401,
        });
        return;
    }

    const getProjectsMembers = await getAllProjectMembersByUserId(res.locals.user.id);

    if (!getProjectsMembers.success) {
        res.status(getProjectsMembers.statusCode).json(getProjectsMembers);
        return;
    }

    const invitedProjectIds = getProjectsMembers.data.map((projectMember) => projectMember.projectId);

    const ownedProjects = await ProjectModel.xFind({
        createdById: res.locals.user.id,
    });

    const accessibleProjects = [...invitedProjectIds, ...ownedProjects.map((project) => project.id)];

    res.locals.accessibleProjects = accessibleProjects;
    next();
}

/**
 * @Authenticated_required
 * 
 * Check if the user has access to the project
 * 
 * Before this middleware, we need to have the projectId in the res.locals.projectId
 * 
 * @param req - The request object
 * @param res - The response object
 * @param next - The next function
 * @returns { isOwner: boolean, projectRole: ProjectMemberRole }
 */
export const isUserHaveAccessToProject = async (req: Request, res: Response, next: NextFunction) => {
    // Further we can check wether user in higher levels like workspace or organization
    // For now we will check only for project level
    if (res.locals.byPassProjectIdCheck) {
        next();
        return;
    }

    const projectId = res.locals.projectId;
    const userId = res.locals.user?.id;

    if (!projectId) {
        res.status(403).json({ message: "Project ID is required" });
        return;
    }

    if (!userId) {
        res.status(401).json({ message: "User Authentication is required" });
        return;
    }

    const [getProjectMemberResult, getProjectResult] = await Promise.all([
        getProjectMember(projectId, userId,),
        getProjectById(projectId)
    ]);

    const isOwner = getProjectResult.data?.createdById === userId;
    const projectRole = getProjectMemberResult.data?.role;

    if (!isOwner && !projectRole) {
        res.status(403).json({ message: "User does not have access to this project" });
        return;
    }

    res.locals.isOwner = isOwner;
    res.locals.projectRole = projectRole;

    next();

    return;
}


/**
 * @Authenticated_required
 * 
 * Check for specific project role. Should be called after isUserHaveAccessToProject middleware
 * 
 * Before this middleware, we need to have the projectId in the res.locals.projectId
 * 
 * @param req - The request object
 * @param res - The response object
 * @param next - The next function
 */
export const validateUserProjectRole = (role: ProjectMemberRole) => async (req: Request, res: Response, next: NextFunction) => {
    const isOwner = res.locals.isOwner;
    const projectRole = res.locals.projectRole;

    // Project owners always have permission
    // For members, check if their role level >= required role level
    if (!isRoleGTE(projectRole, role) && !isOwner) {
        res.status(403).json({ message: "You don't have enough permissions to perform this action", statusCode: 403 });
        return;
    }

    next();
}

export const isProjectExists = (projectIdRequestPath: string = 'params.projectId', required: boolean = false) => async (req: Request, res: Response, next: NextFunction) => {
    const projectId = deepGetRequestPropsData(req, projectIdRequestPath);

    if (!projectId && !required) {
        res.locals.byPassProjectIdCheck = true;
        next();
        return;
    }

    const project = await getProjectById(projectId);

    if (!project.success) {
        res.locals.project = null;
        res.locals.projectId = null;
        next();
        return;
    }
    res.locals.project = project.data;
    res.locals.projectId = projectId;
    next();
}

/**
 * Middleware to ensure all user resources have a project
 * This can be used on endpoints where you want to guarantee all resources are mapped to projects
 */
export const ensureResourcesHaveProject = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const currentUser = res.locals.user;
    
    // Check if user has a default project
    const defaultProjectResponse = await findOrCreateDefaultProject(currentUser.id);
    if (!defaultProjectResponse.success || !defaultProjectResponse.data) {
      log.error(`Failed to get or create default project for user ${currentUser.id}`, defaultProjectResponse);
      next();
      return;
    }
    const defaultProject = defaultProjectResponse.data;

    if (defaultProject) {
      // Check if there are any resources without a project
      const orphanedResourcesCount = await models.Resource.count({
        where: {
          createdById: currentUser.id,
          projectId: {
            [Op.or]: [{ [Op.is]: null }, ""],
          },
        },
      });

      if (orphanedResourcesCount > 0) {
        // Import the resource service function
        const { backfillUserResourcesWithDefaultProject } = await import("@/handlers/resources/resource.service");
        
        // Assign orphaned resources to default project
        await backfillUserResourcesWithDefaultProject(
          currentUser.id,
          defaultProject.id
        );
        
        log.info(`Assigned ${orphanedResourcesCount} orphaned resources to default project for user ${currentUser.id}`);
      }
    }

    next();
  } catch (error) {
    log.error(`Error in ensureResourcesHaveProject middleware: ${error.message}`);
    // Continue with the request even if this fails
    next();
  }
};


/**************************************************************************************
 *                              PRIVATE FUNCTIONS
 *  Should not be exported below functions cause they are not used outside of this file
 **************************************************************************************/

/**
 * Check if the user is the owner of the project
 * @param projectOwnerId - The id of the project owner
 * @param userId - The id of the user
 * @returns { isOwner: boolean, message: string }
 */
function isProjectOwner(projectOwnerId: string, userId: string) {
    const isOwner = projectOwnerId === userId;
    return {
        isOwner,
        message: isOwner ? "User is the owner of the project" : "User is not the owner of the project"
    }
}


