import { Request } from "express";

/**
* Get the project id from the request
* @param req - The request object
* @param projectIdRequestPath - The path of the project id in the request:  params.projectId | query.projectId | body.projectId
* @returns The project id
*/
export function deepGetRequestPropsData(req: Request, projectIdRequestPath: string = 'params.projectId') {
    const paths = projectIdRequestPath.split('.');
    return paths.reduce((acc, path) => {
        if (acc === undefined || acc === null) {
            return undefined;
        }
        return acc[path];
    }, req);
}
