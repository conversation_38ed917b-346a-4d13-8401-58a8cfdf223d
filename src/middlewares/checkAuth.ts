import express from "express";
import { log } from "@/services/logger";
import { getFirebaseAuth } from "@/services/firebase";
import { identifyUser } from "@/services/posthog";

// Set to track which users have been identified
const identifiedUsers = new Set<string>();

const checkAuthMiddleware = async (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {

  if (req.method === "OPTIONS") {
    next();
    return;
  }

  let token: string = "";

  // Check for token in Authorization header first
  if (req.headers && req.headers.authorization) {
    const parts = req.headers.authorization.split(" ");
    if (parts.length == 2) {
      const [scheme, credentials] = parts;
      if (/^Bearer$/i.test(scheme)) {
        token = credentials;
      }
    }
  }
  
  // For SSE endpoints, also check query parameters since EventSource can't send custom headers
  if (!token && req.query.token) {
    token = `${req.query.token}`;
  }

  if (!token) {
    res.status(401).send("Authorization header required.");
    return;
  }

  try {
    const auth = await getFirebaseAuth();
    const decodedToken = await auth.verifyIdToken(token);
    const user = {
      id: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name,
    };

    res.locals.user = user;

    // Only identify if this user hasn't been identified before
    if (!identifiedUsers.has(user.id)) {
      identifyUser(user.id, {
        email: user.email,
        name: user.name,
      });
      identifiedUsers.add(user.id);
    }

    next();
  } catch (error) {
    if (error.code === 'auth/id-token-expired') {
      log.info(`Token expired for request to ${req.path}`);
    } else {
      log.error(`Failed to verify token: ${error.message}`);
    }
    res.status(401).end("Unauthorized");
  }
};

export default checkAuthMiddleware;
