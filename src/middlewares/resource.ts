import { NextFunction, Request, Response } from "express";
import ResourceModel from "@/schemas/resource/Resource.model";
import { deepGetRequestPropsData } from "./utils";
import { createLogger } from "@/services/logger";
import { isUserAccessibleResource } from "@/handlers/resources/resource.service";
import { isRoleGTE, ProjectMemberRole } from "@/schemas/project/ProjectMember.model";

const logger = createLogger("middlewares_notes");

/**
 * @Authenticated_required
 * 
 * Check if the resource exists
 * 
 * @param projectIdRequestPath - The path of the project id in the request:  params.projectId | query.projectId | body.projectId
 * @returns { isOwner: boolean, message: string }
 */
export const isResourceExists = (projectIdRequestPath: string = 'body.resourceId') => async (req: Request, res: Response, next: NextFunction) => {
    const resourceId = deepGetRequestPropsData(req, projectIdRequestPath);

    if (!resourceId) {
        res.status(400).json({ message: "ResourceId id is required in " + projectIdRequestPath });
        return;
    }

    const resource = await ResourceModel.xFind1({
        id: resourceId,
    });

    if (!resource) {
        res.status(404).json({ message: "Resource not found" });
        return;
    }

    res.locals.resource = resource;
    res.locals.projectId = resource.projectId;

    next();
}


interface IsUserHaveAccessToResourceOptions {
    requiredEditAccess?: boolean;
}

export const isUserHaveAccessToResource =
    ({ requiredEditAccess = false }: IsUserHaveAccessToResourceOptions = {}) =>
        async (req: Request, res: Response, next: NextFunction) => {
            const resource = res.locals.resource;
            const currentUser = res.locals.user;

            const isUserHaveAccessToResource = await isUserAccessibleResource(currentUser.id, resource);

            if (!isUserHaveAccessToResource.success) {
                res.status(isUserHaveAccessToResource.statusCode).json({ message: isUserHaveAccessToResource.message });
                return;
            }

            if (requiredEditAccess && !isUserHaveAccessToResource.data.canEdit) {
                res.status(403).json({ message: "You are not allowed to edit this resource" });
                return;
            }

            res.locals.resourceUserPermissions = isUserHaveAccessToResource.data;
            next();
        }
