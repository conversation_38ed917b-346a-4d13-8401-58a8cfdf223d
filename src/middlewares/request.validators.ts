import { log } from "@/services/logger";
import { Request, Response, NextFunction } from "express";
import { ZodSchema } from "zod";

const handleValidationResult = (data: any, schema: ZodSchema, res: Response, next: NextFunction) => {
    const result = schema.safeParse(data);
    if (result.success) {
        next();
        return;
    }

    log.warn(result.error.message.replace(/\n|\s+/g, ""));
    res.status(400).json(result.error);
    return;
}

export const requestBodyValidator = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction) => {
        return handleValidationResult(req.body, schema, res, next);
    };
};

export const requestParamsValidator = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction) => {
        return handleValidationResult(req.params, schema, res, next);
    };
};

export const requestQueryValidator = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction) => {
        return handleValidationResult(req.query, schema, res, next);
    };
};

export const requestHeaderValidator = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction) => {
        return handleValidationResult(req.headers, schema, res, next);
    };
};
