import { Response } from 'express';
import { z } from 'zod';
import { getSSEService } from "@/services/sse/sse.service";
import { ServiceResponse } from "@/types";
import { IntentionalError } from "@/utils/errors";
import { AuthUser } from '@/types/user';

interface SubscribeToProjectParams {
  projectId: string;
}

interface SubscribeToProjectData {
  connectionId: string;
  channel: string;
  userId: string;
  projectId: string;
}

const subscribeToProjectSchema = z.object({
  projectId: z.string(),
});

export const subscribeToProjectHandler = async (
  params: SubscribeToProjectParams,
  user: AuthUser,
  res: Response,
  userAgent?: string,
  ipAddress?: string
): Promise<ServiceResponse<SubscribeToProjectData>> => {
  try {
    // Validate input parameters
    const { projectId } = subscribeToProjectSchema.parse(params);
    if (!projectId) {
      return {
        success: false,
        message: `Validation error: projectId is required`,
        statusCode: 400,
      };
    }
    const sseService = getSSEService();
    const result = await sseService.subscribeToProject(
      user.id,
      projectId,
      res,
      userAgent,
      ipAddress
    );
    
    if (!result.success) {
      return result;
    }
    return result;
  } catch (error) {
    
    if (error instanceof IntentionalError) {
      return {
        success: false,
        message: error.message,
        statusCode: 500,
      };
    }
    
    return {
      success: false,
      message: 'Internal server error',
      statusCode: 500,
    };
  }
}; 
