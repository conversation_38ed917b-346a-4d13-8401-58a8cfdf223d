import type { Request, Response } from "express";

import { agentService } from "@/services/agent/agent.service";
import type { CreateMessageRequest } from "@/validators/agent.validators";
import { createLogger } from "@/services/logger";
import type { ProgressReporter } from "@/types/agent";

const logger = createLogger("CreateMessageStreamHandler");

const FLUSH_INTERVAL = 100; // Flush every 100ms

export async function createMessageStreamHandler(
  req: Request<{ conversationId: string }, any, CreateMessageRequest>,
  res: Response,
  progressReporter?: ProgressReporter
): Promise<void> {
  const { conversationId } = req.params;
  const userId = res.locals.user.id;
  const body = req.body;
  const requestId = req.requestId;

  try {
    logger.info("Processing streaming message request", {
      conversationId,
      userId,
      messageLength: body.message.length,
      model: body.model,
      hasCustomInstructions: !!body.preferences?.instructions,
      customInstructions: body.preferences?.instructions ? body.preferences.instructions.substring(0, 100) + '...' : null,
      requestId: requestId || 'not-provided'
    });

    const result = await agentService.createMessageStream(
      conversationId,
      userId,
      body,
      requestId,
      progressReporter
    );

    if (!result.success || !result.data) {
      // Send error as SSE format instead of JSON
      const errorData = {
        type: "error",
        error: result.message,
        statusCode: result.statusCode,
      };
      res.write(`data: ${JSON.stringify(errorData)}\n\n`);
      res.write("data: [DONE]\n\n");
      res.end();
      return;
    }

    // Send start signal
    const startData = {
      type: "start",
      conversationId,
      timestamp: Date.now(),
    };
    res.write(`data: ${JSON.stringify(startData)}\n\n`);

    // Stream the response with improved error handling
    const stream = result.data;
    const reader = stream.getReader();
    let chunkCount = 0;
    let lastFlushTime = Date.now();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunkCount++;

        // Write the chunk to the response
        res.write(value);

        // Periodic flushing for better real-time streaming
        const now = Date.now();
        if (now - lastFlushTime > FLUSH_INTERVAL) {
          try {
            res.flush?.(); // Optional flush if available
          } catch (flushError) {
            // Ignore flush errors
          }
          lastFlushTime = now;
        }

        // Check if client disconnected
        if (res.destroyed || res.writableEnded) {
          logger.warn("Client disconnected during streaming", {
            conversationId,
            userId,
            chunkCount,
          });
          break;
        }
      }
    } catch (streamError) {
      logger.error("Error during stream processing", {
        error: streamError,
        conversationId,
        userId,
        chunkCount,
      });

      // Send error to client
      const errorData = {
        type: "error",
        error: "Stream processing error",
        timestamp: Date.now(),
      };

      try {
        res.write(`data: ${JSON.stringify(errorData)}\n\n`);
        res.write("data: [DONE]\n\n");
      } catch (writeError) {
        logger.error("Failed to write error to response", { writeError });
      }
    } finally {
      reader.releaseLock();

      // Ensure response is properly closed
      if (!res.destroyed && !res.writableEnded) {
        try {
          res.end();
        } catch (endError) {
          logger.error("Error ending response", { endError });
        }
      }
    }

    logger.info("Streaming message response completed", {
      conversationId,
      userId,
    });
  } catch (error) {
    logger.error("Failed to process streaming message request", {
      error,
      conversationId,
      userId,
      message: body.message,
    });

    res.status(500).json({
      success: false,
      message: "Failed to generate streaming response",
    });
  }
}
