import type { Request, Response } from "express";

import { agentService } from "@/services/agent/agent.service";
import { createErrorResponse } from "@/utils/response";
import { log } from "@/services/logger";

export async function convertConversationToResourceHandler(
  req: Request<
    { conversationId: string },
    any,
    { format?: "markdown" | "docx" | "txt" }
  >,
  res: Response
) {
  try {
    const userId = res.locals.user.id;
    const { conversationId } = req.params;
    const { format = "markdown" } = req.body; // Default to markdown if not specified

    log.info("Converting conversation to resource", {
      conversationId,
      userId,
      format,
    });

    const result = await agentService.convertConversationToResource(
      conversationId,
      userId,
      format
    );

    return res.status(201).json({
      success: true,
      message: "Conversation converted to resource successfully",
      data: result,
      statusCode: 201,
    });
  } catch (error) {
    log.error("Error converting conversation to resource:", error);
    return res
      .status(500)
      .json(createErrorResponse("Internal server error", 500));
  }
}
