import type { Request, Response } from "express";

import { agentService } from "@/services/agent/agent.service";
import { createErrorResponse } from "@/utils/response";
import { log } from "@/services/logger";

export async function createConversationHandler(req: Request, res: Response) {
  try {
    const userId = res.locals.user.id;
    const { projectId } = req.body;

    const conversation = await agentService.createConversation(
      userId,
      projectId
    );

    return res.status(201).json({
      success: true,
      message: "Conversation created successfully",
      data: conversation,
      statusCode: 201,
    });
  } catch (error) {
    log.error("Error creating conversation:", error);
    return res
      .status(500)
      .json(createErrorResponse("Internal server error", 500));
  }
}
