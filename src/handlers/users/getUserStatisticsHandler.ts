import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const getUserStatisticsHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const currentUser = res.locals.user;

    const userStatistics = await models.UserStatistics.xFind1By(
      "userId",
      currentUser.id,
    );

    res.json(userStatistics);
  } catch (error) {
    log.error(`Failed to get user statistics: ${error.message}`, error);
    throw new IntentionalError("Failed to get user statistics");
  }
};

export default getUserStatisticsHandler;
