import { models } from "@/schemas";
import { getFirebaseUserInfoById } from "@/services/firebase";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import express from "express";
import { Op } from "sequelize";

const getUserInitialContextHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const currentUser = res.locals.user;

    log.info(`Getting initial context for user: ${currentUser.id}`);

    const projects = await models.Project.findAll({
      where: {
        [Op.or]: [
          { createdById: currentUser.id },
          {
            id: res.locals.accessibleProjects,
          },
        ],
        deletedAt: null,
      },
      attributes: ["id", "name", "createdAt", "createdById", "isDefault"],
      include: [
        {
          model: models.ProjectFolder,
          as: "folders",
          attributes: ["id", "name", "createdAt"],
        },
      ],
    });

    log.info(`Found ${projects.length} projects for user: ${currentUser.id}`);

    const formattedProjects = await Promise.all(projects.map(async (project) => {
      const projectData = project.toJSON();
      const createdBy = await getFirebaseUserInfoById(projectData.createdById);
      return {
        ...projectData,
        createdBy,
        isOwner: projectData.createdById === currentUser.id,
      };
    }));

    res.json({
      projects: formattedProjects,
    });
  } catch (error) {
    log.error(`Failed to get user initial context: ${error.message}`, error);
    throw new IntentionalError("Failed to get user initial context");
  }
};

export default getUserInitialContextHandler;
