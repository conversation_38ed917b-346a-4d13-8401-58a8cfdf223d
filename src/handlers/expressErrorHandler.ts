import type { NextFunction, Request, Response } from "express";
import express from "express";
import { log } from "@/services/logger";
import { Error404, IntentionalError } from "@/utils/errors";

export { express };
export type { NextFunction, Request, Response } from "express";

export type ExpressErrorResponse = { error: string } & Pick<
  IntentionalError,
  "isIntentionalError" | "code" | "detail" | "extra"
>;

const expressErrorHandler = (
  err: IntentionalError,
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (!err) {
    err = new Error404("The requested url was not found");
  } else if (typeof err.json !== "function") {
    log.stack(err);
    err = new IntentionalError("An error occurred", err);
  }
  res.status(err.code).json(err.json());
};

export default expressErrorHandler;
