import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Op } from "sequelize";
import { SessionStatus, SessionWhere } from "@/schemas/session/Session.model";

const getOngoingSessionsHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const currentUser = res.locals.user;
    const { projectId } = req.query;

    const whereClause: SessionWhere = {
      createdById: currentUser.id,
      status: [
        SessionStatus.NotStarted,
        SessionStatus.InProgress,
        SessionStatus.Processing,
      ],
    };

    if (projectId) {
      whereClause.projectId = projectId as string;
    }

    const rawSessions = await models.Session.findAll({
      where: whereClause,
    });

    const sessions = rawSessions.map((s) => s.toJSON());

    const sortedSessions = sessions.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    res.json(sortedSessions);
  } catch (error) {
    log.error(`Failed to get sessions: ${error.message}`);
    throw new IntentionalError("Failed to get sessions");
  }
};

export default getOngoingSessionsHandler;
