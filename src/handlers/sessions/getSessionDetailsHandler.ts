import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const getSessionDetailsHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  const { id: sessionId } = req.params;
  const currentUser = res.locals.user;

  try {
    const session = await models.Session.xFind1({
      createdById: currentUser.id,
      id: sessionId,
    });

    if (!session) {
      res.status(404).json({ message: "Session not found" });
      return;
    }

    res.status(200).json(session);
  } catch (error) {
    log.error(`Failed to get session ${sessionId}`, error);
    throw new IntentionalError(`Failed to get session ${sessionId}`);
  }
};

export default getSessionDetailsHandler;
