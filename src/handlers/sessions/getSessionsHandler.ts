import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const getSessionsHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const currentUser = res.locals.user;

    const rawSessions = await models.Session.findAll({
      where: {
        createdById: currentUser.id,
      },
    });

    const sessions = rawSessions.map((s) => s.toJSON());

    const sortedSessions = sessions.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    res.json(sortedSessions);
  } catch (error) {
    log.error(`Failed to get sessions: ${error.message}`);
    throw new IntentionalError("Failed to get sessions");
  }
};

export default getSessionsHandler;
