import express, { response } from "express";
import { format } from "date-fns";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { createBot } from "@/services/recall/utils/createBot";
import { models } from "@/schemas";
import { SessionStatus } from "@/schemas/session/Session.model";
import { broadcastSessionStatusUpdate } from "@/utils/session-sse";
import { SSEEventType } from "@/services/sse/pubsub.service";

const recordMeetingHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { url: meetingUrl, projectId } = req.body;
    const currentUser = res.locals.user;

    if (!meetingUrl) {
      res.status(400).json({ message: "Missing meeting URL" });
      return;
    }

    log.info(`[recordMeetingHandler] Start recording meeting ${meetingUrl}`);

    // Add bot to meeting
    const bot = await createBot(
      meetingUrl,
      {},
      new Date().toISOString(),
      currentUser.name
    );

    const meetingTitle =
      bot.meeting_metadata?.title ||
      `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`;
    // Create session
    const session = await models.Session.xCreate({
      recallBotId: bot.id,
      title: meetingTitle,
      startTime: new Date(bot.join_at),
      meetingUrl,
      createdById: currentUser.id,
      status: SessionStatus.NotStarted,
      projectId,
    });

    log.info(`[recordMeetingHandler] Created session`, session);

    // Broadcast session creation event via SSE
    await broadcastSessionStatusUpdate(
      {
        sessionId: session.id,
        title: session.title,
        status: SessionStatus.NotStarted,
        projectId: projectId,
        userId: currentUser.id,
        userName: currentUser.name || currentUser.email,
        metadata: {
          recallBotStatus: session.recallBotStatus,
          meetingUrl: session.meetingUrl,
        },
      },
      SSEEventType.SESSION_CREATED
    );

    res.status(200).json({ session });
  } catch (error) {
    log.error(`[recordMeetingHandler] Failed to record meeting`, error);
    throw new IntentionalError("Failed to record meeting", error);
  }
};

export default recordMeetingHandler;
