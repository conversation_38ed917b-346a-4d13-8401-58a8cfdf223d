import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { SessionStatus } from "@/schemas/session/Session.model";
import { recallAiClient } from "@/services/recall";
import { sendMeetingDeletedEmail } from "@/services/email";
import { broadcastSessionDeletion } from "@/utils/session-sse";

const deleteSessionHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: sessionId } = req.params;
    const currentUser = res.locals.user;

    const session = await models.Session.xFind1({
      createdById: currentUser.id,
      id: sessionId,
    });

    if (!session) {
      res.status(404).json({ message: "Session not found" });
      return;
    }

    // If session is in progress, remove bot from call
    if (session.status === SessionStatus.InProgress && session.recallBotId) {
      try {
        await recallAiClient.removeBotFromCall(session.recallBotId);
      } catch (error) {
        //TODO: Handle error more gracefully
        // When we implement the Soft Delete we can update the session status to Done
        log.error(
          `Failed to remove bot from call for session ${sessionId}: ${error.message}`
        );
      }
    }

    if (currentUser.id === session.createdById) {
      // Remove existing summary
      const resource = await models.Resource.xFind1({
        sessionId: session.id,
      });
      if (resource) {
        const riie = await models.ResourceInInsightEngine.xFind1({
          resourceId: resource.id,
        });

        // Remove transcription if exists
        const transcript = await models.Transcription.xFind1({
          resourceInInsightEngineId: riie.id,
        });
        if (transcript) {
          await models.Transcription.xUpdateById(transcript.id, {
            isDeleted: true,
            deletedAt: new Date(),
          });
        }

        // Remove notes in insight engine
        const notes = await models.Note.xFind({
          resourceId: resource.id,
        });
        for (const note of notes) {
          await models.Note.xUpdateById(note.id, {
            isDeleted: true,
            deletedAt: new Date(),
          });
        }

        // Send email to
        await sendMeetingDeletedEmail({
          to: currentUser.email,
          substitutions: {
            meetingTitle: session.title,
            username: currentUser.name || "there",
          },
        });
      }
    }

    // Broadcast session deletion via SSE before deleting
    await broadcastSessionDeletion({
      sessionId: session.id,
      title: session.title,
      projectId: session.projectId,
      userId: currentUser.id,
      userName: currentUser.name || currentUser.email,
    });

    await models.Session.xDestroyById(sessionId);

    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete session: ${error.message}`);
    throw new IntentionalError("Failed to delete session");
  }
};

export default deleteSessionHandler;
