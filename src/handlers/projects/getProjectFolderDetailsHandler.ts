import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { ProjectFolder } from "@/schemas/project/ProjectFolder.model";
import { getResourceData } from "@/schemas/resource/utils";
import { ResourceData } from "@/types/resources";
import { Op } from "sequelize";

type ProjectFolderDetails = {
  folder: ProjectFolder;
  resources: ResourceData[];
};

const getProjectFolderDetailsHandler = async (
  req: express.Request,
  res: express.Response
) => {
  const { id: projectId, folderId } = req.params;
  const currentUser = res.locals.user;
  const accessibleProjectIds = res.locals.accessibleProjects || [];
  try {
    const project = await models.Project.xFind1({
      [Op.or]: [
        { createdById: currentUser.id },
        { id: { [Op.in]: accessibleProjectIds } },
      ],
    });

    if (!project) {
      res.status(404).json({ message: "Project not found" });
      return;
    }

    const folder = await models.ProjectFolder.findOne({
      where: {
        id: folderId,
        projectId,
      },
    });

    if (!folder) {
      res.status(404).json({ message: "Folder not found" });
      return;
    }

    const resources = await models.Resource.xFindBy("folderId", folderId);

    const formattedResources = await Promise.all(
      resources.map(async (resource) => {
        return getResourceData(resource);
      })
    );

    const output: ProjectFolderDetails = {
      folder: folder.toJSON(),
      resources: formattedResources,
    };

    res.status(200).json(output);
  } catch (error) {
    log.error(`Failed to get project folder ${folderId}`, error);
    throw new IntentionalError(`Failed to get project folder ${folderId}`);
  }
};

export default getProjectFolderDetailsHandler;
