import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Op } from "sequelize";

const createProjectFolderHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const currentUser = res.locals.user;
    const { id: projectId } = req.params;
    const { name } = req.body;
    const accessibleProjectIds = res.locals.accessibleProjects || [];
    const project = await models.Project.xFind1({
      [Op.or]: [
        { createdById: currentUser.id },
        { id: { [Op.in]: accessibleProjectIds } },
      ],
      id: projectId,
    });

    if (!project) {
      throw new IntentionalError("Project not found");
    }

    const folder = await models.ProjectFolder.xCreate({
      name,
      projectId: project.id,
    });

    log.info(`Project folder created: ${folder.id}`);

    res.json(folder);
  } catch (error) {
    log.error(`Failed to create project folder: ${error.message}`);
    throw new IntentionalError("Failed to create project folder");
  }
};

export default createProjectFolderHandler;
