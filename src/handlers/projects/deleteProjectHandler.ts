import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import * as projectService from "@/services/project/project.service";
import { DocumentProcessorService, DocumentProcessorValidationError } from "@/services/documents/documentProcessor.service";
import { isDocumentOrImage } from "@/utils/file";
import { DocumentUpdateType } from "@/types";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/types";

const deleteProjectHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: projectId } = req.params;
    const currentUser = res.locals.user;

    const defaultProject = await projectService.getProjectDefault(
      currentUser.id
    );

    const project = await models.Project.xFind1({
      id: projectId,

      createdById: currentUser.id,
    });

    if (!project) {
      res.status(404).json({ message: "Project not found" });
      return;
    }

    if (project.isDefault) {
      res.status(400).json({ message: "Default project cannot be deleted" });
      return;
    }

    // Get all resources in the project before deletion
    const projectResources = await models.Resource.findAll({
      where: { projectId },
    });

    // Publish document processing update messages for document/image files
    const documentProcessorPromises = projectResources
      .map(resource => resource.toJSON())
      .filter(resource => isDocumentOrImage(resource.name))
      .map(async (resource) => {
        try {
          // Ensure we have a valid default project ID
          const defaultProjectId = defaultProject.data?.id;
          if (!defaultProjectId) {
            log.error("Missing default project ID for document processor message", {
              resourceId: resource.id,
              fileName: resource.name,
              deletingProjectId: projectId,
              defaultProjectData: defaultProject.data,
            });
            throw new Error("Missing default project ID");
          }

          // Ensure we have a valid resource name
          if (!resource.name) {
            log.error("Missing resource name for document processor message", {
              resourceId: resource.id,
              deletingProjectId: projectId,
              defaultProjectId,
            });
            throw new Error("Missing resource name");
          }

          await DocumentProcessorService.publishUpdateMessage(
            resource.id, // file_id
            currentUser.id, // user_id
            defaultProjectId, // new project_id (default project)
            DocumentUpdateType.PROJECT_ID, // update_type
            {
              original_name: resource.name,
              old_project_id: projectId,
            }
          );
          
          log.info("Document processing update message published for project deletion", {
            resourceId: resource.id,
            fileName: resource.name,
            oldProjectId: projectId,
            newProjectId: defaultProject.data?.id,
            userId: currentUser.id,
          });
        } catch (error) {
          if (error instanceof DocumentProcessorValidationError) {
            log.error("Document processor validation failed for project deletion", {
              validationError: error.message,
              field: error.field,
              resourceId: resource.id,
              fileName: resource.name,
              oldProjectId: projectId,
              newProjectId: defaultProject.data?.id,
              userId: currentUser.id,
            });
          } else {
            log.error("Failed to publish document processing update message for project deletion", {
              error: error instanceof Error ? error.message : String(error),
              resourceId: resource.id,
              fileName: resource.name,
              oldProjectId: projectId,
              newProjectId: defaultProject.data?.id,
              userId: currentUser.id,
            });
          }
          // Don't fail project deletion if document processing message fails
        }
      });

    // Wait for all document processor messages to be published
    await Promise.all(documentProcessorPromises);

    const resourceIds = projectResources.map((resource) => resource.toJSON().id);

    await Promise.all([
      // Soft delete project (using both isDeleted and new deletedAt/deletedBy fields)
      models.Project.xDestroyById(projectId, currentUser.id),

      // Update resources to move them to default project
      models.Resource.xUpdateBy("projectId", projectId, {
        projectId: defaultProject.data?.id ?? null,
        folderId: null,
      }),
    ]);

    if (projectId) {
      try {
        const sseService = getSSEService();
        await sseService.broadcastToProject(projectId, {
          type: SSEEventType.PROJECT_DELETED,
          projectId,
          userId: currentUser.id,
          data: {
            projectId,
            projectDefaultId: defaultProject.data?.id ?? null,
            projectName: project.name,
            deletedBy: currentUser.id,
            deletedAt: new Date().toISOString(),
          },
        });
      } catch (error) {
        log.error('Failed to broadcast project.deleted event via SSE', {
          error,
          projectId: projectId,
          userId: currentUser.id,
        });
      }
    }

    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete project: ${error.message}`);
    throw new IntentionalError("Failed to delete project");
  }
};

export default deleteProjectHandler;
