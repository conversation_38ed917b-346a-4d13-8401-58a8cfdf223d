import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const createProjectHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const currentUser = res.locals.user;

    const { name = "Untitled Project", description = "" } = req.body;

    const project = await models.Project.xCreate({
      name,
      description,
      createdById: currentUser.id,
    });

    log.info(`Project created: ${project.id}`);

    res.json(project);
  } catch (error) {
    log.error(`Failed to create project: ${error.message}`);
    throw new IntentionalError("Failed to create project");
  }
};

export default createProjectHandler;
