import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Op } from "sequelize";

const deleteProjectFolderHandler = async (
  req: express.Request,
  res: express.Response
) => {
  const { id: projectId, folderId } = req.params;
  const currentUser = res.locals.user;
  const accessibleProjectIds = res.locals.accessibleProjects || [];

  try {
    const project = await models.Project.findOne({
      where: {
        [Op.or]: [
          { createdById: currentUser.id },
          { id: { [Op.in]: accessibleProjectIds } },
        ],
      },
    });

    if (!project) {
      throw new IntentionalError("Project not found");
    }

    const folder = await models.ProjectFolder.findOne({
      where: {
        id: folderId,
        projectId,
      },
    });

    if (!folder) {
      throw new IntentionalError("Folder not found");
    }

    await folder.destroy();

    res.json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete project folder: ${error.message}`);
    throw new IntentionalError("Failed to delete project folder");
  }
};

export default deleteProjectFolderHandler;
