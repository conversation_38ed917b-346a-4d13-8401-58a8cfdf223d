import { log } from "@/services/logger";
import { TranscodingSyncMessage, TranscoderJob, TranscoderJobState } from "./types";
import { VideoProcessingType } from "@/schemas/video/VideoProcessing.model";
import {
  getVideoProcessingJobById,
  completeSpriteSheetGeneration,
  handleSpriteSheetFailure,
} from "@/services/videoProcessing/videoProcessing.service";

/**
 * Handler for spriteSheet generation Pub/Sub messages
 * Processes transcoding job updates specifically for spriteSheet generation
 * @param message The Pub/Sub message containing job data
 * @param attributes Message attributes
 */
export const spriteSheetProcessingHandler = async (
  message: TranscodingSyncMessage,
  attributes: Record<string, string>
): Promise<void> => {
  const { job } = message;

  if (!job) {
    log.warn("No job data found in spriteSheet message");
    return;
  }

  try {
    const jobId = job.name.split('/').pop() || '';
    
    log.info(`Processing spriteSheet job update`, {
      jobId,
      state: job.state,
    });

    // Check if this is a spritesheet job based on our video processing record
    // We can't rely on job.labels as they're not included in the basic TranscoderJob type

    // Get the video processing job record
    const jobResult = await getVideoProcessingJobById(jobId);
    
    if (!jobResult.success || !jobResult.data) {
      log.warn(`Video processing job not found for transcoder job: ${jobId}`);
      return;
    }

    const processingJob = jobResult.data;
    
    // Verify this is a spritesheet job
    if (processingJob.processingType !== VideoProcessingType.SPRITESHEET) {
      log.warn(`Job ${jobId} is not a spriteSheet job, ignoring`, {
        actualType: processingJob.processingType,
      });
      return;
    }

    // Process based on job state
    if (job.state === TranscoderJobState.SUCCEEDED) {
      await handleSpriteSheetSuccess(jobId, job);
    } else if (job.state === TranscoderJobState.FAILED) {
      await handleSpriteSheetJobFailure(jobId, job);
    } else {
      log.info(`Received spriteSheet job update with state: ${job.state}. No action needed.`, {
        jobId,
        resourceId: processingJob.resourceId,
      });
    }
  } catch (error) {
    log.error("Error processing spritesheet job update", {
      error: error instanceof Error ? error.message : String(error),
      jobId: job.name?.split('/').pop(),
      jobState: job.state,
    });
  }
};

/**
 * Handle successful spriteSheet generation
 * @param jobId The ID of the completed job
 * @param jobData The job data from Pub/Sub notification
 */
const handleSpriteSheetSuccess = async (jobId: string, jobData: TranscoderJob): Promise<void> => {
  log.info(`Processing successful spriteSheet generation job: ${jobId}`);
  
  try {
    // Get the processing job record to find the expected output URI
    const jobResult = await getVideoProcessingJobById(jobId);
    const outputUri = jobResult.success && jobResult.data ? jobResult.data.outputUri : '';
    
    // Complete the spriteSheet generation process
    const result = await completeSpriteSheetGeneration(jobId, outputUri);
    
    if (result.success) {
      log.info(`Successfully completed spritesheet generation for job ${jobId}`, {
        resourceId: result.data?.resourceId,
        vttPath: result.data?.vttPath,
      });

    } else {
      log.error(`Failed to complete spritesheet generation for job ${jobId}`, {
        error: result.message,
      });
    }
  } catch (error) {
    log.error(`Error completing spritesheet generation for job ${jobId}`, {
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * Handle failed spriteSheet generation
 * @param jobId The ID of the failed job
 * @param jobData The job data from Pub/Sub notification
 */
const handleSpriteSheetJobFailure = async (jobId: string, jobData: TranscoderJob): Promise<void> => {
  log.info(`Processing failed spriteSheet generation job: ${jobId}`);
  
  try {
    const errorDetails = {
      message: jobData.error?.message || 'SpriteSheet generation failed',
      code: jobData.error?.code,
      details: jobData.error?.details,
      jobData: jobData,
    };

    // Handle the failure and determine if retry is needed
    const result = await handleSpriteSheetFailure(jobId, errorDetails);
    
    if (result.success) {
      if (result.data?.shouldRetry) {
        log.info(`SpriteSheet job ${jobId} marked for retry`, {
          retryCount: result.data.retryCount,
          maxRetries: result.data.maxRetries,
        });
        
        // TODO: Implement retry logic if needed
        // For now, manual retry or separate retry processor could handle this
        
      } else {
        log.error(`SpriteSheet generation permanently failed for job ${jobId}`, {
          retryCount: result.data?.retryCount,
          maxRetries: result.data?.maxRetries,
        });
      }
    } else {
      log.error(`Failed to handle spriteSheet failure for job ${jobId}`, {
        error: result.message,
      });
    }
  } catch (error) {
    log.error(`Error handling spriteSheet failure for job ${jobId}`, {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}; 
