import { log } from "@/services/logger";
import { TranscodingSyncMessage, TranscoderJob, TranscoderJobState } from "./types";
import { transcoderService } from "@/services/transcoder";
import { createTranscodingPayload } from "@/utils/transcoding";
import { getFileSize, formatFileSize } from "@/utils";
import { gcsBucket } from "@/config";
import { getTranscodingJobById, updateTranscodingJob } from "@/services/resourceTranscodedJob/resourceTranscodedJob.service";
import { models } from "@/schemas";
import { TranscodingMode } from "@/schemas/transcoded/ResourceTranscodedJob.model";
import { GCS_URI_PREFIX } from "@/constants/storage";
import { deleteFile } from "@/services/storage";
import { VideoProcessingType } from "@/schemas/video/VideoProcessing.model";
import { getVideoProcessingJobById } from "@/services/videoProcessing/videoProcessing.service";
import { spriteSheetProcessingHandler } from "./spritesheetProcessing.handler";

// Number of retry attempts for critical operations
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_MS = 1000;


/**
 * Interface for resource update data
 */
interface ResourceUpdateData {
  isTranscoding: boolean;
  transcodedFileSize?: number;
  url?: string;
}

/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param maxAttempts Maximum number of retry attempts
 * @param initialDelay Initial delay in milliseconds
 */
const retry = async <T>(
  fn: () => Promise<T>, 
  maxAttempts: number = MAX_RETRY_ATTEMPTS,
  initialDelay: number = RETRY_DELAY_MS
): Promise<T> => {
  let attempts = 0;
  let delay = initialDelay;
  
  while (attempts < maxAttempts) {
    try {
      return await fn();
    } catch (error) {
      attempts++;
      if (attempts >= maxAttempts) {
        throw error;
      }
      
      log.warn(`Retry attempt ${attempts}/${maxAttempts} failed. Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }
  
  throw new Error(`Failed after ${maxAttempts} attempts`);
};

/**
 * Extract file path from GCS URI
 * @param gcsUri GCS URI in format gs://bucket/path
 * @returns Relative path without bucket prefix
 */
const extractPathFromGcsUri = (gcsUri: string): string => {
  if (!gcsUri) return '';
  
  return gcsUri.startsWith(`${GCS_URI_PREFIX}${gcsBucket}/`) 
    ? gcsUri.replace(`${GCS_URI_PREFIX}${gcsBucket}/`, '')
    : gcsUri;
};

/**
 * Handle successful transcoding job
 * @param jobId The ID of the completed job
 * @param jobData The job data from Pub/Sub notification
 */
const handleTranscodingSuccess = async (jobId: string, jobData: TranscoderJob): Promise<void> => {
  log.info(`Processing successful transcoding job: ${jobId}`);
  
  // Get the transcoding job record
  const result = await getTranscodingJobById(jobId);
  
  if (!result.success) {
    log.warn(`Could not find transcoding record for job ID: ${jobId}`);
    return;
  }
  
  const transcodingJob = result.data;
  
  // Extract output information
  const outputFolderGcsUri = transcodingJob.get('output') as string;
  const outputFileName = transcodingJob.get('outputFileName') as string;
  const transcodingMode = transcodingJob.get('mode') as TranscodingMode || TranscodingMode.KEEP_BOTH;
  
  // Construct the relative path for finding the resource
  const outputRelativePath = `${extractPathFromGcsUri(outputFolderGcsUri)}${outputFileName}`;
  
  log.info(`Looking for resource with transcodedUrl: ${outputRelativePath}`);
  
  // Find and update the resource
  const resource = await models.Resource.findOne({
    where: { transcodedUrl: outputRelativePath }
  });
  
  if (resource) {
    log.info(`Updating resource ${resource.get('id')} - setting isTranscoding to false`);
    
    // Get the file size of the transcoded file
    const fileSize = await getFileSize(outputRelativePath);
    
    let updateData: ResourceUpdateData = {
      isTranscoding: false,
      transcodedFileSize: fileSize
    };

    // Handle delete-original mode
    if (transcodingMode === TranscodingMode.DELETE_ORIGINAL) {
      log.info(`Transcoding mode is DELETE_ORIGINAL - replacing original file with transcoded file`);
      
      try {
        // Get original file path
        const originalUrl = resource.get('url') as string;
        
        if (originalUrl) {
          // Extract the path from gs:// format
          const originalFilePath = extractPathFromGcsUri(originalUrl);

          // Delete the original file with retry
          await retry(() => deleteFile(originalFilePath));
          log.info(`Successfully deleted original file at: ${originalFilePath}`);
          
          // Update resource URL to point to transcoded file
          updateData.url = resource.get('transcodedUrl') as string;
          log.info(`Replacing resource.url with transcoded url: ${resource.get('transcodedUrl')}`);
        } else {
          log.warn(`Original URL not found for resource ${resource.get('id')}`);
        }
      } catch (error) {
        log.error(`Error deleting original file for resource ${resource.get('id')}:`, error);
      }
    }
    
    // Update the resource with retry
    await retry(() => resource.update(updateData));
    
    // Update the transcoding job record
    await updateTranscodingJob(jobId, { callback_data: jobData });
    
    if (fileSize) {
      log.info(`Successfully updated resource ${resource.get('id')} with transcoded file size: ${formatFileSize(fileSize)}`);
    } else {
      log.warn(`Could not get file size for transcoded file: ${outputRelativePath}`);
      log.info(`Successfully updated resource ${resource.get('id')} and marked transcoding as complete`);
    }
  } else {
    log.warn(`Could not find resource with transcodedUrl: ${outputRelativePath}`);
  }
};

/**
 * Handle failed transcoding job and retry without audio
 * @param jobId The ID of the failed job
 * @param jobData The job data from Pub/Sub notification
 */
const retryTranscodingWithoutAudio = async (jobId: string, jobData: TranscoderJob): Promise<void> => {
  log.info(`Retrying failed job without audio. Original job ID: ${jobId}`);
  
  // Get the transcoding job record
  const result = await getTranscodingJobById(jobId);
  
  if (!result.success) {
    log.warn(`Could not find transcoding record for job ID: ${jobId}`);
    return;
  }
  
  const transcodingJob = result.data;
  
  // Extract necessary information for retry
  const sourceGcsUri = transcodingJob.get('input') as string;
  const destinationFolderGcsUri = transcodingJob.get('output') as string;
  const outputFileName = transcodingJob.get('outputFileName') as string;
  
  // Create new config without audio
  const configWithoutAudio = createTranscodingPayload(
    {
      inputUri: sourceGcsUri,
      outputUri: destinationFolderGcsUri,
      fileName: outputFileName,
      hasAudio: false,
    }
  );
  
  // Create new job with retry
  const newJob = await retry(() => transcoderService.createJob(
    sourceGcsUri,
    destinationFolderGcsUri,
    configWithoutAudio
  ));
  
  const newJobId = newJob.name?.split('/').pop() || '';
  
  log.info(`Created new transcoding job without audio. New job ID: ${newJobId}`);
  
  // Update the transcoding job record with new job information
  await updateTranscodingJob(jobId, {
    jobId: newJobId,
    callback_data: newJob,
    retryCount: (transcodingJob.get('retryCount') as number || 0) + 1,
    retryReason: 'Missing audio track',
    hasAudio: false
  });
};

/**
 * Handler for the CloudlabTranscodingSync PubSub topic
 * Processes transcoding job updates from Google Cloud Transcoder
 * @param message The Pub/Sub message containing job data
 * @param attributes Message attributes
 */
export const syncTranscodingHandler = async (
  message: TranscodingSyncMessage,
  attributes: Record<string, string>
): Promise<void> => {
  const { job } = message;

  if (!job) {
    log.warn("No job data found in message");
    return;
  }

  try {
    const jobId = job.name.split('/').pop() || '';
    
    // Check if this is a spritesheet job by looking up the video processing record
    const videoProcessingResult = await getVideoProcessingJobById(jobId);

    if (videoProcessingResult.success && videoProcessingResult.data) {
      const processingJob = videoProcessingResult.data;
      
      if (processingJob.processingType === VideoProcessingType.SPRITESHEET) {
        log.info(`Routing spritesheet job to spritesheet handler: ${jobId}`);
        await spriteSheetProcessingHandler(message, attributes);
        return;
      } 
    } 
    
    // Process based on job state
    if (job.state === TranscoderJobState.SUCCEEDED) {
      await handleTranscodingSuccess(jobId, job);
    } else if (
      job.state === TranscoderJobState.FAILED &&
      job.error?.message?.includes('with an audio track')
    ) {
      await retryTranscodingWithoutAudio(jobId, job);
    } else {
      log.info(`Received job update with state: ${job.state}. No action needed.`);
    }
  } catch (error) {
    log.error("Error processing transcoding job update", error);
    // Note: We don't rethrow the error here to prevent the Pub/Sub message from being redelivered
    // This is intentional as we've already logged the error and don't want to retry
  }
};
