export interface CloudlabResourceSyncMessage {
    firebaseUID: string;
    /**
     * https://storage.googleapis.com/beam-storages/beam-dev-01JQ3KFFZWA9AEES5J9C3RF1R3.mp4
     */
    publicURL: string;
}

/**
 * Possible states for a transcoder job
 */
export enum TranscoderJobState {
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  PENDING = 'PENDING',
  RUNNING = 'RUNNING'
}

/**
 * Error details for a transcoder job
 */
export interface TranscoderJobErrorDetail {
    '@type': string;
    fieldViolations: {
      field: string;
      description: string;
    }[];
}
  
/**
 * Error information for a transcoder job
 */
export interface TranscoderJobError {
    code: number;
    message: string;
    details?: TranscoderJobErrorDetail[];
    timestamp?: string;
    severity?: 'ERROR' | 'WARNING' | 'INFO';
}
  
/**
 * Represents a Google Cloud Transcoder job
 */
export interface TranscoderJob {
    name: string;
    state: TranscoderJobState;
    error?: TranscoderJobError;
}
  
/**
 * Message structure for Pub/Sub notifications from Cloud Transcoder
 */
export interface TranscodingSyncMessage {
    job: TranscoderJob;
}

export interface IMessage<T> {
    payload: T;
    attributes: {
        type: string;
        timestamp: string;
    };
}