import { gcsResourceFolder } from "@/config";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { createNewResource } from "@/schemas/resource/utils";
import { gcsUpload } from "@/services/storage";
import downloadFileFromUrl from "@/utils/downloadFileFromUrl";
import { rm } from "fs/promises";
import { v4 as uuidv4 } from "uuid";
import { CloudlabResourceSyncMessage } from "./types";
import { MediaSyncPayload } from "../resources/mediaSyncJobHandler";

const syncCloudlabResource = async ({
  firebaseUID,
  publicURL: videoUrl,
  fileName,
}: MediaSyncPayload) => {
  const _fileName = fileName || videoUrl.split("/").pop() || `cloudlab-sync-${uuidv4()}.mp4`;
  const filePath = `${gcsResourceFolder}/${fileName}`;

  const downloadedFile = await downloadFileFromUrl(videoUrl, fileName);

  // Upload the file to GCS
  await gcsUpload(downloadedFile.path, filePath, downloadedFile.mimetype, true);

  // Remove the file after uploaded to GCS
  await rm(downloadedFile.path).catch(console.error);

  await createNewResource({
    gcsFilePath: filePath,
    fileName: _fileName,
    fileSize: downloadedFile.size,
    userId: firebaseUID,
    title: fileName,
    uploadAction: IEUploadAction.UPLOAD,
  });
};

export const syncCloudlabResourceHanler = async (
  message: CloudlabResourceSyncMessage,
  attributes: any
) => {
  console.log("[syncCloudlabResource] Received message:", {
    message,
    attributes,
  });
  syncCloudlabResource(message);
};
