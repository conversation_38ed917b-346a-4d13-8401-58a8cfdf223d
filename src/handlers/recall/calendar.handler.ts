import express from "express";
import {
  aidaEndpoint,
  defaultTimezone,
  recallSystemCalendarEmail,
} from "@/config";
import { models } from "@/schemas";
import { SessionStatus } from "@/schemas/session/Session.model";
import { UserAida } from "@/schemas/userAida/UserAida.model";
import {
  sendConfirmAidaFirstJoinEmail,
  sendConfirmedMeetingEmail,
  sendSessionDeletedEmail,
  sendSessionUpdatedEmail,
} from "@/services/email";
import { MeetingType } from "@/services/email/template/confirmedMeeting";
import { getFirebaseAuth } from "@/services/firebase";
import { log } from "@/services/logger";
import { recallAiClient, removeBotFromCall } from "@/services/recall";
import { scheduleBot } from "@/services/recall/utils/createBot";
import { AppCalendarEvent, RecallBody } from "@/types/recall";
import { format } from "date-fns";
import { UserRecord } from "firebase-admin/auth";
import { onRecurringEventChanged } from "./recurringEvent.handler";
import moment from "moment-timezone";
import { urlShortenerService, UrlType } from "@/services/urlShortener";
import { findOrCreateDefaultProject } from "@/utils";

type RecallSyncPayload = RecallBody<"calendar.sync_events">["data"];
type RecallUpdatePayload = RecallBody<"calendar.update">["data"];

const AIDA_BOT_EMAILS = recallSystemCalendarEmail;

const isAidaBotCalendar = async (calendarId) => {
  const calendar = await recallAiClient.v2_retrieveCalendarById(calendarId);
  return calendar && calendar.oauth_email === AIDA_BOT_EMAILS;
};

async function sessionHandler(
  fU: UserRecord,
  event: AppCalendarEvent,
  shouldSchedule = false
) {
  // check if session is existed
  let session = await models.Session.xFind1({
    createdById: fU.uid,
    meetingUrl: event.meeting_url,
  });
  // check if the session is existed and updated
  if (session) {
    // check if the event is deleted
    if (event.is_deleted) {
      // delete the session
      await models.Session.xDestroyById(session.id);
      // send email to the user
      await sendSessionDeletedEmail({
        to: fU.email,
        substitutions: {
          username: fU.displayName || "there",
          meetingTitle: event.raw.summary,
        },
      });
      return;
    }
    // session is existed and updated
    // check startTime or endTime is changed, we need to send email to the user
    const isStartTimeChanged =
      new Date(session.startTime).getTime() !==
      new Date(event.start_time).getTime();
    const isEndTimeChanged =
      new Date(session.endTime).getTime() !==
      new Date(event.end_time).getTime();
    if (isStartTimeChanged || isEndTimeChanged) {
      // reschedule the bot by removing the old bot and creating a new one and then update the session
      await removeBotFromCall(session.recallBotId);
      const bot = await scheduleBot(
        event.id,
        event.meeting_url,
        {},
        event.start_time,
        fU.email
      );
      await models.Session.xUpdateById(session.id, {
        startTime: new Date(event.start_time),
        endTime: new Date(event.end_time),
        recallBotId: bot.bot_id,
      });

      // send email to the user
      await sendSessionUpdatedEmail({
        to: fU.email,
        substitutions: {
          username: fU.displayName || "there",
          meetingTitle: event.raw.summary,
        },
      });
    }
    return;
  }
  if (shouldSchedule) {
    const bot = await scheduleBot(
      event.id,
      event.meeting_url,
      {},
      event.start_time,
      fU.email
    );
    if (!session) {
      const meetingTitle =
        event.raw.summary ||
        `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`;
      
      // Get or create default project for the user
      const defaultProjectResponse = await findOrCreateDefaultProject(fU.uid);
      if (!defaultProjectResponse.success || !defaultProjectResponse.data) {
        log.error(`Failed to get or create default project for user ${fU.uid}`, defaultProjectResponse);
        return;
      }
      const defaultProject = defaultProjectResponse.data;
      const defaultProjectId = defaultProject.id;
      
      session = await models.Session.xCreate({
        recallBotId: bot.bot_id,
        title: meetingTitle,
        startTime: new Date(event.start_time),
        endTime: new Date(event.end_time),
        meetingUrl: event.meeting_url,
        createdById: fU.uid,
        status: SessionStatus.NotStarted,
        shouldSendSummaryToEmail: true,
        eventMeetingId: event.id,
        projectId: defaultProjectId,
      });
    }

    // send announcement email to organizer
    await sendAnnouncementEmail({
      fU, event
    });
    return;
  }

  // send confirmation email to user with magic link fuid/eventid
  await sendConfirmationEmail(fU, event.id);
}

async function attendeeWithEventHandler(
  attendeeEmail: string,
  event: AppCalendarEvent
) {
  // process attendee with if attendee is existed in the database => auto schedule bot
  // if not => add to the database then send email to verify
  const auth = await getFirebaseAuth();
  let fUser: UserRecord = await auth
    .getUserByEmail(attendeeEmail)
    .catch(() => null);
  let userAida: UserAida;
  if (fUser) {
    userAida = await models.UserAida.xFind1({
      userId: fUser.uid,
    });
    if ((userAida && userAida.isConfirmed) || fUser.emailVerified) {
      await sessionHandler(fUser, event, true);
      return;
    } else {
      await sessionHandler(fUser, event, false);
    }
  } else {
    fUser = await auth.createUser({
      email: attendeeEmail,
      emailVerified: false,
    });
    // create user aida
    userAida = await models.UserAida.xCreate({
      userId: fUser.uid,
      isConfirmed: false,
    });

    await sessionHandler(fUser, event, false);
  }
}

// handler event changed
async function onEventChanged(event: AppCalendarEvent) {
  // if it is deleted, we should remove it from the database
  // check attendees not include aida bot
  const organizer =
    event.raw.attendees.find(
      ({ email, organizer }) => email !== AIDA_BOT_EMAILS && organizer
    ) || event.raw.organizer;
  await attendeeWithEventHandler(organizer.email, event);
}

/**
 * This function is used to sync the calendar events from the calendar to the database
 * @param payload
 * @returns
 * note: this function is called when the calendar is updated as well as the event is created/updated/deleted
 */

export async function recallCalendarSyncHandler(payload: RecallSyncPayload) {
  const isAida = await isAidaBotCalendar(payload.calendar_id);
  if (!isAida) {
    // ignore other bot/user calendar
    // TODO: handle other bot/user calendar later
    log.info(`Ignore other bot/user calendar ${payload.calendar_id}`);
    return;
  }

  const { calendar_id, last_updated_ts } = payload;

  const events = (
    await recallAiClient.v2_retrieveCalendarEvents(calendar_id, last_updated_ts)
  ).results;

  if (events.length === 0) {
    // could not find any events
    log.info(`No events found for calendar ${calendar_id}`);
    return;
  }

  const baseEvents = events.filter((event) => !event.raw.recurringEventId);
  for (const event of baseEvents) {
    // should be move to pubsub
    await onEventChanged(event);
  }

  const recurringEvents = events.filter((event) => event.raw.recurringEventId);
  // group recurring events by recurringEventId or seriesMasterId
  const recurringEventIds: string[] = [];
  const recurringEventsMap = new Map<string, AppCalendarEvent[]>();
  for (const event of recurringEvents) {
    if (event.raw.recurringEventId) {
      const recurringId = event.raw.recurringEventId;
      if (!recurringEventsMap.has(recurringId)) {
        recurringEventsMap.set(recurringId, []);
        recurringEventIds.push(recurringId);
      }
      recurringEventsMap.get(recurringId).push(event);
    }
  }
  // handle recurring events
  for (const [recurringEventId, events] of recurringEventsMap.entries()) {
    // should be move to pubsub
    await onRecurringEventChanged(recurringEventId, events);
  }
}

export async function recallCalendarUpdateHandler(
  payload: RecallUpdatePayload
) {
  // Calendar Update
  // This webhook is sent whenever the calendar's data changes (for instance, when status becomes disconnected).
}

export const sendConfirmationEmail = async (
  fUid: UserRecord,
  eventId: string
) => {
  try {
    const originalMagicLink = `${aidaEndpoint}/confirm-aida?uid=${fUid.uid}&eventId=${eventId}`;
    const magicLink = await urlShortenerService.shortenUrl(originalMagicLink, {
      urlType: UrlType.CONFIRMATION,
      entityId: eventId,
      userId: fUid.uid,
    });

    await sendConfirmAidaFirstJoinEmail({
      to: fUid.email,
      substitutions: {
        username: fUid.displayName ?? "there",
        magicLink: magicLink,
      },
      attachments: [],
    });
  } catch (error) {
    // Fallback to original link if shortening fails
    console.warn("Magic link shortening failed, using original link:", error);
    const magicLink = `${aidaEndpoint}/confirm-aida?uid=${fUid.uid}&eventId=${eventId}`;
    await sendConfirmAidaFirstJoinEmail({
      to: fUid.email,
      substitutions: {
        username: fUid.displayName ?? "there",
        magicLink: magicLink,
      },
      attachments: [],
    });
  }
};

type sendAnnouncementEmail = {
  fU: UserRecord;
  event: AppCalendarEvent;
  type?: MeetingType;
  dateString?: string;
};

export const sendAnnouncementEmail = async ({
  fU,
  event,
  type = MeetingType.Existed,
  dateString = "",
}: sendAnnouncementEmail) => {
  let dateTime = dateString;
  if(dateString) {
    const date = new Date(event.start_time);
  const dateFormat = format(date, "EEEE, MMMM dd, yyyy");
  const firstStartDateMoment = moment(event.start_time).tz(
    event.raw.start.timeZone || ""
  );
  const firstEndDateMoment = moment(event.end_time).tz(
    event.raw.end.timeZone || ""
  );

  const firstStartTime = `${firstStartDateMoment.format("h:mm A")}`;
  const timezoneText = `${firstStartDateMoment.format("[(GMT] Z[)]")}`;

  const firstEndTime = `${firstEndDateMoment.format("h:mm A")}`;
   dateTime = `on ${dateFormat} from ${firstStartTime} to ${firstEndTime} ${timezoneText}`;
  }

  await sendConfirmedMeetingEmail(
    {
      to: fU.email,
      substitutions: {
        username: fU.displayName ?? "there",
        meetingTitle: event.raw.summary,
        dateTime,
      },
      attachments: [],
    },
    type
  );
};
