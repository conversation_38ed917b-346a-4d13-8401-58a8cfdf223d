import express from "express";
import { models } from "@/schemas";
import { RagSyncStatus } from "@/schemas/resource/Resource.model";
import { DocumentProcessorCallbackPayload } from "@/types/documentProcessor";
import { WinstonLogger } from "@/services/logger/winston";
import { deleteFile } from "@/services/storage";
import { gcsTempFolderForDocumentProcessor } from "@/config";
import path from "path";

const logger = new WinstonLogger("CreateResourceHandler");
const documentProcessorHandler = async (
  req: express.Request,
  res: express.Response
): Promise<void> => {
  try {
    const payload = req.body as DocumentProcessorCallbackPayload;
    logger.info("Received document processor callback", {
      resourceId: payload.resourceId,
      status: payload.status,
      projectId: payload.projectId,
    });

    // Validate required fields
    if (!payload.resourceId || !payload.status || !payload.projectId) {
      logger.error("Invalid callback payload", { payload });
      res.status(400).json({ 
        success: false, 
        message: "Missing required fields: resourceId, status, user_id, projectId" 
      });
      return;
    }

    // Validate status is a valid RagSyncStatus
    if (!Object.values(RagSyncStatus).includes(payload.status)) {
      logger.error("Invalid status value", { 
        status: payload.status, 
        valid_statuses: Object.values(RagSyncStatus) 
      });
      res.status(400).json({ 
        success: false, 
        message: `Invalid status. Must be one of: ${Object.values(RagSyncStatus).join(', ')}` 
      });
      return;
    }

    // Find the resource
    const resource = await models.Resource.xFind1ById(payload.resourceId);
    if (!resource) {
      logger.warn("Resource not found for callback", { file_id: payload.resourceId });
      res.status(404).json({ 
        success: false, 
        message: "Resource not found" 
      });
      return;
    }

   
    if (resource.projectId !== payload.projectId) {
      logger.warn("Unauthorized access attempt - project mismatch", { 
        file_id: payload.resourceId, 
        callback_project_id: payload.projectId,
        resource_project_id: resource.projectId
      });
      res.status(403).json({ 
        success: false, 
        message: "Unauthorized access" 
      });
      return;
    }

    // TODO: Need to uncomment this once we remove the Pinecone Assistant
    /*
    // If the resource is already synced, skip the update
    if (resource.ragSyncStatus === RagSyncStatus.SYNCED) {
      logger.info("Resource already synced, skipping update", {
        file_id: payload.resourceId,
        current_status: resource.ragSyncStatus,
      });
      res.status(200).json({
        success: true,
        message: "Resource is already synced. No update performed.",
        data: {
          file_id: payload.resourceId,
          status: resource.ragSyncStatus
        }
      });
      return;
    } */

    // Update the resource status
    await models.Resource.xUpdateById(payload.resourceId, {
      ragSyncStatus: payload.status,
    });

    // If the resource is synced, delete the transcript file
    if (payload.status === RagSyncStatus.SYNCED) {
      const { name } = path.parse(resource.name);
      const uniqueTranscriptFilename = `${name}_${resource.id}_transcript.txt`;
      await deleteFile(`${gcsTempFolderForDocumentProcessor}/${uniqueTranscriptFilename}`);
    }

    logger.info("Resource status updated successfully", {
      file_id: payload.resourceId,
      project_id: payload.projectId,
      new_status: payload.status,
    });

    res.status(200).json({ 
      success: true, 
      message: "Resource status updated successfully",
      data: {
        file_id: payload.resourceId,
        status: payload.status,
        updated_at: new Date().toISOString(),
      }
    });

  } catch (error) {
    logger.error("Document processor callback processing failed", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      payload: req.body,
    });
    
    res.status(500).json({ 
      success: false, 
      message: "Internal server error" 
    });
  }
};

export default documentProcessorHandler;
