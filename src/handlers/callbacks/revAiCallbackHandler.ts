import type { JobStatus, Rev<PERSON><PERSON><PERSON><PERSON><PERSON>ob } from "revai-node-sdk";
import { JobType } from "revai-node-sdk";

import express from "express";
import { log } from "@/services/logger";
import { NPromise } from "@/types";
import { handleTranscriptSuccess } from "@/services/transcription/handleTranscriptSuccess";
import { handleTranscriptFailed } from "@/services/transcription/handleTranscriptFailed";
import { handleTopicExtractionSuccess } from "@/services/transcription/handleTopicExtractionSuccess";
import { handleTopicExtractionFailed } from "@/services/transcription/handleTopicExtractionFailed";

type WebhookHandler = (data: RevAiApiJob) => NPromise;

const revAiCallbackHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  const { job } = req.body;
  try {
    let handler: WebhookHandler;
    switch (job.type) {
      case JobType.Async:
        handler = asyncHandler[job.status as JobStatus];
        break;
      case JobType.TopicExtraction:
        handler = topicExtractionHandler[job.status as JobStatus];
        break;
      default:
        break;
    }

    if (handler) {
      await handler(job);
    } else {
      log.warn(`No Revai webhook handler for status ${job.type}`, job);
    }
  } catch (e) {
    log.error(`Rev ai handle job error:${e}`);
  }
  res.end("OK");
};

const asyncHandler: { [k in JobStatus]: WebhookHandler } = {
  transcribed: (d) => handleTranscriptSuccess(d),
  completed: (d) => handleTranscriptSuccess(d),
  failed: (d) => handleTranscriptFailed(d),
  in_progress: (d) => void d,
};

const topicExtractionHandler: {
  [k in JobStatus]: WebhookHandler;
} = {
  transcribed: (d) => handleTopicExtractionSuccess(d),
  completed: (d) => handleTopicExtractionSuccess(d),
  failed: (d) => handleTopicExtractionFailed(d),
  in_progress: (d) => void d,
};

export default revAiCallbackHandler;
