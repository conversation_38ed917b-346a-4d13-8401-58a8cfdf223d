import { SessionStatus } from "@/schemas/session/Session.model";
import { updateSessionByBotId, isBotNotJoiningCallTimeout } from "@/schemas/session/utilts";
import { log } from "@/services/logger";
import handleBotDone from "@/services/recall/utils/handleBotDone";
import { NPromise } from "@/types";
import { BotChangeData, BotChangeStatusCode, BotChangeSubCodeTimeout, RecallBody } from "@/types/recall";
import express from "express";
import { recallCalendarSyncHandler, recallCalendarUpdateHandler } from "../recall/calendar.handler";

type Body =
  | RecallBody<"bot.status_change">
  | RecallBody<"calendar.sync_events">
  | RecallBody<"calendar.update">;

/**
 * Recall AI webhook handler
 * This is already in order of the status codes changed
 * Reference: https://docs.recall.ai/docs/bot-status-change-events#bot-status-transition-diagram
 */
const asyncHandler: {
  [k in BotChangeStatusCode]: (data: BotChangeData) => NPromise;
} = {
  joining_call: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.JoiningCall,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  in_waiting_room: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.InWaitingRoom,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  in_call_not_recording: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.InCallNotRecording,
      status: SessionStatus.InProgress,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  recording_permission_allowed: (d) => {
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  recording_permission_denied: (d) => {
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  in_call_recording: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.InCallRecording,
      status: SessionStatus.InProgress,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  call_ended: async (d) => {
    const timeoutSubCodes = Object.values(BotChangeSubCodeTimeout);
    if (timeoutSubCodes.includes(d.status.sub_code as BotChangeSubCodeTimeout)) {
      await updateSessionByBotId(d.bot_id, {
        recallBotStatus: BotChangeStatusCode.TimeoutExceededWaitingRoom,
      });
    } else {
      await updateSessionByBotId(d.bot_id, {
        recallBotStatus: BotChangeStatusCode.CallEnded,
        actualEndTime: new Date(),
      });
    }
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  done: async (d) => {
    const isBotNotJoining = await isBotNotJoiningCallTimeout(d.bot_id);
    if (!isBotNotJoining) {
      await updateSessionByBotId(d.bot_id, {
        recallBotStatus: BotChangeStatusCode.Done,
      });
      await handleBotDone(d.bot_id);
    }
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  fatal: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.Fatal,
      status: SessionStatus.Failed,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  analysis_done: (d) => {
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  analysis_failed: (d) => {
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  media_expired: (d) => {
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
  timeout_exceeded_waiting_room: async (d) => {
    await updateSessionByBotId(d.bot_id, {
      recallBotStatus: BotChangeStatusCode.TimeoutExceededWaitingRoom,
    });
    log.info(`Recall Ai handle status => ${d.status.code}`);
    return;
  },
};

enum CalendarAsyncEvents {
  CALENDAR_UPDATE = "calendar.update",
  CALENDAR_SYNC_EVENTS = "calendar.sync_events",
}


const recallCallbackHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  const { data, event } = req.body as Body;

  try {
    switch (event) {
      case "bot.status_change":
        const h = asyncHandler[data.status.code as BotChangeStatusCode];
        if (h) {
          await h(data);
        } else {
          log.warn(`No Recall webhook handler for status ${data.status.code}`);
        }
        break;

      case CalendarAsyncEvents.CALENDAR_SYNC_EVENTS:
        log.info(`Recall handle calendar sync events`)
        await recallCalendarSyncHandler(data);
        break;
      case CalendarAsyncEvents.CALENDAR_UPDATE:
        log.info(`Recall handle calendar update`)
        await recallCalendarUpdateHandler(data);
        break;
      default:
        log.info(`Unhandled the event type ${event}`);
        break;
    }
  } catch (e) {
    log.error(`Recall handle job error:${e}`);
  }

  res.end("200");
};

export default recallCallbackHandler;
