import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { UserFeedback } from "@/schemas/UserFeedback/UserFeedback.model";

const getUserFeedbackHandler = async (
    req: express.Request,
    res: express.Response
) => {
    const { entityId } = req.params;
    const currentUser = res.locals.user;

    try {
        if (!entityId) {
            res.status(400).json({ message: "Missing required parameters: entityId" });
            return;
        }

        const userFeedback = await models.UserFeedback.xFind1({
            entityId,
            userId: currentUser.id
        });

        if (!userFeedback) {
            res.status(404).json({ message: "Feedback not found" });
            return;
        }

        const userFeedbackDetails = await models.UserFeedback.findOne({
            where: {
                entityId,
                userId: currentUser.id
            }
        });

        const formattedFeedback = userFeedbackDetails.toJSON<UserFeedback>();

        res.status(200).json(formattedFeedback);
    } catch (error) {
        log.error(`Failed to get feedback for resource ${entityId} and user ${currentUser.id}`, error);
        throw new IntentionalError(`Failed to get feedback for resource ${entityId} and user ${currentUser.id}`, error);
    }
};

export default getUserFeedbackHandler;