import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import posthog, { EVENTS } from "@/services/posthog";
import { SUMMARY_PROMPT } from "@/services/transcription/generateSummary";

const createUserEmailFeedbackHandler = async (
    req: express.Request,
    res: express.Response
) => {
    try {
        const { entityId, rating, userId, prompt_used, entityType } = req.body;

        if (!entityId) {
            res.status(400).json({ message: "Missing resource id" });
            return;
        }
        if (!userId) {
            res.status(400).json({ message: "Missing user id" });
            return;
        }
        let userFeedback
        const userFeedbackExists = await models.UserFeedback.xFind1({
            entityId,
            userId,
            type: entityType
        });
        if (userFeedbackExists) {
             await models.UserFeedback.xUpdateById(userFeedbackExists.id, {
                feedback: '',
                reason: '',
                rating,
            });
            userFeedback = {
                ...userFeedbackExists,
                feedback: '',
                reason: '',
                rating,
            }
        } else {
            userFeedback = await models.UserFeedback.xCreate({
                entityId,
                userId,
                feedback: '',
                reason: '',
                type: entityType,
                rating,
            });
        }

        // Track feature flag check
        posthog.capture({
            distinctId: userId,
            event: EVENTS.SUMMARY_FEEDBACK_GIVEN,
            properties: {
                entityId,
                rating,
                prompt_used: prompt_used || SUMMARY_PROMPT,
                platform: "Aida",
            },
        });

        res.status(201).json(userFeedback);
    } catch (error) {
        log.error(`[createUserEmailFeedbackHandler] Failed to create user email feedback`, error);
        throw new IntentionalError("Failed to create user email feedback", error);
    }
};

export default createUserEmailFeedbackHandler;