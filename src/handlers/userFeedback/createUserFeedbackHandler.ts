import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import posthog, { EVENTS } from "@/services/posthog";

const createUserFeedbackHandler = async (
    req: express.Request,
    res: express.Response
) => {
    try {
        const { entityId, comment, reason, entityType, rating, userId } = req.body;

        if (!entityId) {
            res.status(400).json({ message: "Missing resource id" });
            return;
        }

        let userFeedback
        const userFeedbackExists = await models.UserFeedback.xFind1({
            entityId,
            userId,
            type: entityType
        });
        if (userFeedbackExists) {
            await models.UserFeedback.xUpdateById(userFeedbackExists.id, {
                feedback: comment,
                reason,
                rating,
            });
            userFeedback = {
                ...userFeedbackExists,
                feedback: comment,
                reason,
                rating,
            }
        } else {
            userFeedback = await models.UserFeedback.xCreate({
                entityId,
                userId: userId,
                feedback: comment,
                reason,
                type: entityType,
                rating,
            });
        }


        // Track feature flag check
        posthog.capture({
            distinctId: userId,
            event: EVENTS.SUMMARY_FEEDBACK_DETAILED,
            properties: {
                feedback: comment,
                entityId,
                reason,
                type: entityType,
                rating,
            },
        });

        res.status(201).json(userFeedback);
    } catch (error) {
        log.error(`[createUserFeedback] Failed to create user feedback`, error);
        throw new IntentionalError("Failed to create user feedback", error);
    }
};

export default createUserFeedbackHandler;