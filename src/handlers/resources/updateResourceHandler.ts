import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { getInsightEngineByResourceId } from "@/schemas/insightEngine/utils";
import { RagSyncStatus, Resource } from "@/schemas/resource/Resource.model";
import { getResourceData } from "@/schemas/resource/utils";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/services/sse/pubsub.service";

import { isUserAccessibleResource } from "./resource.service";
import { DocumentProcessorService, DocumentProcessorValidationError } from "@/services/documents/documentProcessor.service";
import { DocumentUpdateType } from "@/types";
import { FEATURE_FLAGS, isFeatureEnabled } from "@/services/featureFlags";

const updateResourceHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;
    const payload = req.body as Partial<Resource>;

    const resource = await models.Resource.xFind1({
      id: resourceId,
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }

    const isEditable = await isUserAccessibleResource(currentUser.id, resource);

    if (!isEditable.success || !isEditable.data.canEdit) {
      res.status(403).json({ message: isEditable.message });
      return;
    }

    const { insightEngine } = await getInsightEngineByResourceId(resourceId);
    const { name, ...rest } = payload;

    if (name) {
      await models.InsightEngine.xUpdateById(insightEngine.id, {
        name,
      });
    }

    await models.Resource.xUpdateById(resourceId, rest);

    const updatedResource = await models.Resource.xFind1({
      id: resourceId,
    });

    const updatedResourceData = await getResourceData(updatedResource);
    const enableBeingsMCP = await isFeatureEnabled(
      currentUser.id,
      FEATURE_FLAGS.BEINGS_RAG_ENGINE
    );
    // Publish document-processor message if name or projectId was changed for document/image files
    const shouldPublishUpdate = (name || rest.projectId) && (rest.projectId || resource.projectId);
    if (shouldPublishUpdate && resource.ragSyncStatus === RagSyncStatus.SYNCED && enableBeingsMCP) {
      let updateType: typeof DocumentUpdateType[keyof typeof DocumentUpdateType] | undefined;
      try {
        // Determine update type
        if (name) {
          updateType = DocumentUpdateType.NAME;
        } else if (rest.projectId) {
          updateType = DocumentUpdateType.PROJECT_ID;
        }

        // Ensure we have a valid updateType before proceeding
        if (!updateType) {
          log.error("Unable to determine update type for document processor message", {
            resourceId,
            hasName: !!name,
            hasProjectIdChange: !!rest.projectId,
            currentProjectId: resource.projectId,
            newProjectId: rest.projectId,
          });
          throw new Error("Unable to determine update type");
        }

        // Ensure we have a valid insightEngine with name
        if (!insightEngine || !insightEngine.name) {
          log.error("Missing insightEngine or insightEngine.name for document processor message", {
            resourceId,
            hasInsightEngine: !!insightEngine,
            insightEngineName: insightEngine?.name,
          });
          throw new Error("Missing insightEngine or insightEngine.name");
        }

        // Ensure we have valid project IDs
        const targetProjectId = updatedResource.projectId || resource.projectId;
        if (!targetProjectId) {
          log.error("Missing target project ID for document processor message", {
            resourceId,
            updatedResourceProjectId: updatedResource.projectId,
            originalResourceProjectId: resource.projectId,
          });
          throw new Error("Missing target project ID");
        }

        await DocumentProcessorService.publishUpdateMessage(
          resourceId,
          currentUser.id,
          targetProjectId,
          updateType,
          {
            original_name: insightEngine.name,
            new_name: updateType === DocumentUpdateType.NAME ? (name || '') : '',
            old_project_id: resource.projectId || ''
          }
        );
        log.info("Document processor update message published", {
          resourceId,
          userId: currentUser.id,
          projectId: rest.projectId || resource.projectId,
          changedFields: {
            name: !!name,
            projectId: !!rest.projectId,
          },
          updateType,
        });

      } catch (error) {
        if (error instanceof DocumentProcessorValidationError) {
          log.error("Document processor validation failed for update", {
            validationError: error.message,
            field: error.field,
            resourceId,
            userId: currentUser.id,
            updateType,
          });
        } else {
          log.error("Failed to publish document processor update message", {
            error: error instanceof Error ? error.message : String(error),
            resourceId,
            userId: currentUser.id,
          });
        }
        // Don't fail the update operation if publishing fails
      }
    }

    const isResourceMoved = updatedResource.projectId !== resource.projectId;

    if (updatedResource.projectId) {
      try {
        const sseService = getSSEService();

        const projectId = resource.projectId;
        const type = isResourceMoved ? SSEEventType.RESOURCE_MOVED_OUT_OF_PROJECT : SSEEventType.RESOURCE_UPDATED;

        const data = isResourceMoved ? {
          id: updatedResourceData.id,
          name: updatedResourceData.name,
          movedById: currentUser.id,
          movedBy: currentUser.email,
          movedAt: new Date().toISOString(),
          movedFrom: resource.projectId,
          movedTo: updatedResource.projectId,
        } : {
          id: updatedResourceData.id,
          name: updatedResourceData.name,
          updatedBy: currentUser.id,
          updatedAt: new Date().toISOString(),
        };

        await sseService.broadcastToProject(projectId, {
          type,
          projectId,
          userId: currentUser.id,
          data,
        });

      } catch (error) {
        log.error('Failed to broadcast resource.updated event via SSE', {
          error,
          resourceId: updatedResource.id,
          projectId: updatedResource.projectId,
          userId: currentUser.id,
        });
      }
    }

    // Broadcast resource.moved-to-project event via SSE if resource is moved to a project
    if (isResourceMoved) {
      try {
        const sseService = getSSEService();
        await sseService.broadcastToProject(updatedResource.projectId, {
          type: SSEEventType.RESOURCE_MOVED_TO_PROJECT,
          projectId: updatedResource.projectId,
          userId: currentUser.id,
          data: {
            id: updatedResourceData.id,
            name: updatedResourceData.name,
            movedById: currentUser.id,
            movedBy: currentUser.email,
            movedAt: new Date().toISOString(),
            movedFrom: resource.projectId,
            movedTo: updatedResource.projectId,
          },
        });
      } catch (error) {
        log.error('Failed to broadcast resource.moved-to-project event via SSE', {
          error,
          resourceId: updatedResource.id,
          projectId: updatedResource.projectId,
          userId: currentUser.id,
        });
      }
    }

    res.status(200).json(updatedResourceData);
  } catch (error) {
    log.error(`Failed to update resource: ${error.message}`);
    throw new IntentionalError("Failed to update resource");
  }
};

export default updateResourceHandler;
