import { models } from "@/schemas";
import { ConversionService } from "@/services/conversion/conversionService";
import { log } from "@/services/logger";
import { createSuccessResponse, createErrorResponse } from "@/utils/response";
import { Request, Response } from "express";
import { isUserAccessibleResource } from "./resource.service";

/**
 * <PERSON><PERSON> for converting markdown resources to blob for direct download
 */
export const convertMarkdownResourceToBlobHandler = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id: resourceId } = req.params;
    const { format } = req.body;
    const currentUser = res.locals.user;

    // Validate format parameter
    if (!format) {
      const errorResponse = createErrorResponse(
        "Missing 'format' parameter in request body",
        400
      );
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    // Validate format is supported
    const supportedFormats = ConversionService.getSupportedFormats();
    if (!supportedFormats.includes(format)) {
      const errorResponse = createErrorResponse(
        `Invalid format '${format}'. Supported formats: ${supportedFormats.join(', ')}`,
        400
      );
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    // Get the resource to check permissions
    const resource = await models.Resource.xFind1({ id: resourceId });
    if (!resource) {
      const errorResponse = createErrorResponse("Resource not found", 404);
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    // Check if user has access to this resource
    const accessResult = await isUserAccessibleResource(currentUser.id, resource);
    if (!accessResult.success || !accessResult.data.canView) {
      const errorResponse = createErrorResponse(
        "You don't have permission to access this resource",
        403
      );
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    // Perform the conversion and get blob
    const conversionResult = await ConversionService.convertMarkdownResourceToBlob(
      resourceId,
      format,
    );

    if (!conversionResult.success || !conversionResult.data) {
      const errorResponse = createErrorResponse(
        conversionResult.message,
        conversionResult.statusCode
      );
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    // Set headers for file download
    res.set({
      'Content-Type': conversionResult.data.contentType,
      'Content-Disposition': `attachment; filename="${conversionResult.data.fileName}"`,
      'Content-Length': conversionResult.data.fileBuffer.length.toString(),
    });

    // Send the file buffer
    res.send(conversionResult.data.fileBuffer);

  } catch (error) {
    log.error(`Failed to convert markdown resource to blob: ${error.message}`);
    const errorResponse = createErrorResponse("Failed to convert markdown resource", 500);
    res.status(errorResponse.statusCode).json(errorResponse);
  }
};

