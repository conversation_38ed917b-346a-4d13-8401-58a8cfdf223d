import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { deleteFile } from "@/services/storage";
import { getInsightEngineByResourceId } from "@/schemas/insightEngine/utils";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/services/sse/pubsub.service";
import { isUserAccessibleResource } from "./resource.service";
import { DocumentProcessorService, DocumentProcessorValidationError } from "@/services/documents/documentProcessor.service";
import { isDocumentOrImage } from "@/utils/file";
import { FEATURE_FLAGS, isFeatureEnabled } from "@/services/featureFlags";

const deleteResourceHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;

    // Use withDeleted scope to find resource even if already soft-deleted
    const resource = await models.Resource.findOne({
      where: { id: resourceId },
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }

    const resourceJson = resource.toJSON();

    // Check if resource is already soft-deleted
    if (resourceJson.deletedAt) {
      res.status(409).json({ message: "Resource is already deleted" });
      return;
    }

    const { data: { canEdit } } = await isUserAccessibleResource(currentUser.id, resourceJson);

    if (!canEdit) {
      res.status(403).json({ message: "You are not allowed to delete this resource" });
      return;
    }

    // Store projectId before deletion for SSE broadcast
    const projectId = resourceJson.projectId;

    // Perform soft delete instead of hard delete
    await models.Resource.xDestroyById(resourceId, currentUser.id);

    // Soft delete related data instead of hard delete
    const { insightEngine, resourceInInsightEngine } =
      await getInsightEngineByResourceId(resourceId);

    const softDeletePromises = [];

    // TODO: Uncomment this when we have a way to delete files from storage
    // await Promise.all([
    //   deleteFile(resourceJson.thumbnailUrl),
    //   deleteFile(resourceJson.url),
    // ]);

    const enableBeingsMCP = await isFeatureEnabled(
      currentUser.id,
      FEATURE_FLAGS.BEINGS_RAG_ENGINE
    );

    // Soft delete VideoProcessing records
    softDeletePromises.push(
      models.VideoProcessing.xDestroyById(resourceId, currentUser.id)
    );

    // Soft delete ResourceInInsightEngine if exists
    if (resourceInInsightEngine) {
      softDeletePromises.push(
        models.ResourceInInsightEngine.xDestroyById(resourceInInsightEngine.id, currentUser.id)
      );
    }

    // Soft delete InsightEngine if exists
    if (insightEngine) {
      softDeletePromises.push(
        models.InsightEngine.xDestroyById(insightEngine.id, currentUser.id)
      );
    }

    // Soft delete Transcriptions if resourceInInsightEngine exists
    if (resourceInInsightEngine) {
      softDeletePromises.push(
        models.Transcription.xDestroyBy(
          "resourceInInsightEngineId",
          resourceInInsightEngine.id,
          currentUser.id
        )
      );
    }

    await Promise.all(softDeletePromises);
    
    // Publish document processing message for document/image files
    if (isDocumentOrImage(resourceJson.name) && resourceJson.projectId && enableBeingsMCP) {
      try {
        // Ensure we have valid required fields
        if (!resourceJson.id) {
          log.error("Missing resource ID for document processor delete message", {
            fileName: resourceJson.name,
            projectId: resourceJson.projectId,
          });
          throw new Error("Missing resource ID");
        }

        if (!resourceJson.projectId) {
          log.error("Missing project ID for document processor delete message", {
            resourceId: resourceJson.id,
            fileName: resourceJson.name,
          });
          throw new Error("Missing project ID");
        }

        await DocumentProcessorService.publishDeleteMessage(
          resourceJson.id, // file_id
          currentUser.id, // user_id
          resourceJson.projectId // project_id
        );

        log.info("Document processing message published for resource deletion", {
          resourceId: resourceJson.id,
          fileName: resourceJson.name,
          projectId: resourceJson.projectId,
        });
      } catch (error) {
        if (error instanceof DocumentProcessorValidationError) {
          log.error("Document processor validation failed for deletion", {
            validationError: error.message,
            field: error.field,
            resourceId: resourceJson.id,
            fileName: resourceJson.name,
            projectId: resourceJson.projectId,
          });
        } else {
          log.error("Failed to publish document processing message for deletion", {
            error: error instanceof Error ? error.message : String(error),
            resourceId: resourceJson.id,
            fileName: resourceJson.name,
            projectId: resourceJson.projectId,
          });
        }
        // Don't fail resource deletion if document processing message fails
      }
    }

    // Broadcast resource.deleted event via SSE if projectId exists
    if (projectId) {
      try {
        const sseService = getSSEService();
        await sseService.broadcastToProject(projectId, {
          type: SSEEventType.RESOURCE_DELETED,
          projectId,
          userId: currentUser.id,
          data: {
            id: resourceJson.id,
            deletedBy: currentUser.id,
            deletedAt: new Date().toISOString(),
          },
        });

      } catch (error) {
        log.error('Failed to broadcast resource.deleted event via SSE', {
          error,
          resourceId: resourceJson.id,
          projectId,
          userId: currentUser.id,
        });
      }
    }

    res.status(200).json({ message: "Success", projectId: resourceJson.projectId });
  } catch (error) {
    log.error(`Failed to delete resource: ${error.message}`);
    throw new IntentionalError("Failed to delete resource");
  }
};

export default deleteResourceHandler;
