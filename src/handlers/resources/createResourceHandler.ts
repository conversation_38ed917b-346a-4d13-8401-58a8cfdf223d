import express from "express";
import { IntentionalError } from "@/utils/errors";
import { gcsResourceFolder } from "@/config";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { createNewResource } from "@/schemas/resource/utils";
import { createTranscodingJob } from "@/services/resourceTranscodedJob/resourceTranscodedJob.service";
import { FILE_EXTENSIONS, TRANSCODED_FILE_PREFIX } from "@/constants/storage";
import { TranscodingMode } from "@/schemas/transcoded/ResourceTranscodedJob.model";
import {
  isDocumentOrImage,
  isVideoOrAudio,
  isSpritesheetEligible,
} from "@/utils/file";
import {
  uploadResourceToRAG,
  shouldUploadToRAG,
} from "@/services/rag/ragService";
import { getFileStream } from "@/services/storage";
import { RagSyncStatus } from "@/schemas/resource/Resource.model";
import { models } from "@/schemas";
import { Winston<PERSON>ogger } from "@/services/logger/winston";
import { createSpritesheetJob } from "@/services/videoProcessing/videoProcessing.service";
import { FEATURE_FLAGS, isFeatureEnabled } from "@/services/featureFlags";

// Create dedicated logger for RAG service
const logger = new WinstonLogger("CreateResourceHandler");

/**
 * Interface for resource creation data
 */
interface ResourceData {
  gcsFilePath: string;
  fileLastModified?: Date;
  fileName: string;
  fileSize?: number;
  userId: string;
  title: string;
  uploadAction: IEUploadAction;
  projectId?: string;
  folderId?: string;
  transcodedUrl?: string;
  isTranscoding?: boolean;
}

/**
 * Interface for RAG upload parameters
 */
interface RagUploadParams {
  resourceId: string;
  fileName: string;
  filePath: string;
  fileSize?: number;
  userId: string;
  projectId?: string;
}

/**
 * Handles RAG upload for a resource in the background
 * This function manages the entire RAG sync lifecycle including status updates
 */
export const handleRagUpload = async (
  params: RagUploadParams
): Promise<void> => {
  const { resourceId, fileName, filePath, fileSize, userId, projectId } =
    params;

  try {
    // Mark as syncing
    await models.Resource.xUpdateById(resourceId, {
      ragSyncStatus: RagSyncStatus.SYNCING,
    });

    // Get file stream from GCS
    const fileStream = await getFileStream(filePath);

    if (!fileStream) {
      await models.Resource.xUpdateById(resourceId, {
        ragSyncStatus: RagSyncStatus.FAILED,
      });
      logger.error("Failed to get file stream from GCS", {
        resource_id: resourceId,
        file_path: filePath,
      });
      return;
    }

    // Upload to RAG system
    const result = await uploadResourceToRAG(
      {
        resourceId,
        fileName,
        filePath,
        fileSize,
        userId,
        projectId,
      },
      fileStream
    );
    // Update status based on result
    const newStatus = result.success
      ? RagSyncStatus.SYNCED
      : RagSyncStatus.FAILED;
    await models.Resource.xUpdateById(resourceId, {
      ragSyncStatus: newStatus,
    });

    if (result.success) {
      logger.info("RAG upload completed successfully", {
        resource_id: resourceId,
        user_id: userId,
        project_id: projectId,
      });
    } else {
      logger.error("RAG upload failed", {
        resource_id: resourceId,
        user_id: userId,
        project_id: projectId,
        error: result.error,
      });
    }
  } catch (error) {
    // Log error and mark as failed
    logger.error("RAG upload failed with exception", {
      resource_id: resourceId,
      user_id: userId,
      project_id: projectId,
      error: error instanceof Error ? error.message : String(error),
    });

    // Mark as failed
    await models.Resource.xUpdateById(resourceId, {
      ragSyncStatus: RagSyncStatus.FAILED,
    });
  }
};

/**
 * Initiates RAG sync process for a resource
 * Handles both eligible and non-eligible files appropriately
 */
export const initiateRagSync = async (
  resource: { id: string },
  fileName: string,
  filePath: string,
  fileSize: number | undefined,
  userId: string,
  projectId?: string
): Promise<void> => {
  //TODO: Once we stop using pinecone assistant, we can remove this check, and just upload to RAG system based on the file types supported by the RAG system
  if (shouldUploadToRAG(fileName, projectId)) {
    // Run RAG upload in background - don't await to avoid blocking resource creation
    handleRagUpload({
      resourceId: resource.id,
      fileName,
      filePath,
      fileSize,
      userId,
      projectId,
    }).catch((error) => {
      // Final safety net - ensure any unhandled errors are logged
      logger.error("Unhandled error in RAG upload process", {
        resource_id: resource.id,
        error: error instanceof Error ? error.message : String(error),
      });
    });
  } else {
    // If the file is not applicable for RAG, mark it as skipped.
    // Audio/video files are handled separately after transcription, so they are not skipped here.
    const isAudioVideo = isVideoOrAudio(fileName);
    if (!isAudioVideo) {
      await models.Resource.xUpdateById(resource.id, {
        ragSyncStatus: RagSyncStatus.SKIPPED,
      });
    }
  }
};

/**
 * Handler for creating a new resource, with optional transcoding
 */
const createResourceHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const {
      fileName,
      fileSize,
      fileLastModified,
      uploadedFileName,
      projectId,
      folderId,
      transcoded,
      mode,
    } = req.body;
    const currentUser = res.locals.user;

    if (!fileName) {
      res.status(400).json({ message: "Missing file name" });
      return;
    }

    if (!uploadedFileName) {
      res.status(400).json({ message: "Missing uploaded file name" });
      return;
    }

    const filePath = `${gcsResourceFolder}/${uploadedFileName}`;
    let resourceData: ResourceData = {
      gcsFilePath: filePath,
      fileLastModified,
      fileName,
      fileSize,
      userId: currentUser.id,
      title: fileName,
      uploadAction: IEUploadAction.UPLOAD,
      projectId,
      folderId,
    };

    // Handle transcoding only for video/audio files
    // Skip transcoding for documents and images
    let transcodedFileName = "";
    const isDocOrImage = isDocumentOrImage(fileName);
    const isVideo = isVideoOrAudio(fileName);

    if (transcoded && isVideo && !isDocOrImage) {
      // Replace file extension with .mp4 - supporting multiple formats
      const baseFileName = uploadedFileName.replace(
        FILE_EXTENSIONS.MKV,
        FILE_EXTENSIONS.MP4
      );

      transcodedFileName = `${TRANSCODED_FILE_PREFIX}${baseFileName}`;

      // Add transcoding info to resource
      resourceData.transcodedUrl = `${gcsResourceFolder}/${transcodedFileName}`;
      resourceData.isTranscoding = true;
    }

    const { resource } = await createNewResource(resourceData);

    // Initiate RAG sync process (async, non-blocking)
    await initiateRagSync(
      resource,
      fileName,
      filePath,
      fileSize,
      currentUser.id,
      projectId
    );

    // Covert MKV to MP4
    // Only attempt transcoding for video/audio files that aren't documents or images
    if (transcoded && isVideo && !isDocOrImage) {
      try {
        // Validate that the job mode is valid
        const transcodingMode = mode as TranscodingMode;

        // Use the new service to create the transcoding job
        const result = await createTranscodingJob(
          filePath,
          transcodedFileName,
          resource.id,
          transcodingMode
        );

        if (!result.success) {
          logger.error(`Failed to create transcoding job: ${result.message}`);
        } else {
          logger.info(
            `Created transcoding job ${result.jobId || ""} for resource ${
              resource.id
            }`
          );
        }
      } catch (error) {
        logger.error(`Failed to create transcoding job`, error);
        // Continue without failing the resource creation
      }
    } else if (isDocOrImage) {
      logger.info(`Skipping transcoding for document/image file: ${fileName}`);
    }

    // Generate spritesheet
    const enableSpriteSheet = await isFeatureEnabled(
      currentUser.id,
      FEATURE_FLAGS.BEINGS_SPRITESHEET
    );

    if (enableSpriteSheet && isSpritesheetEligible(fileName)) {
      try {
        logger.info(
          `Attempting spritesheet generation for resource ${resource.id}`
        );

        const spritesheetResult = await createSpritesheetJob(
          resource.id,
          fileName,
          filePath
        );

        if (spritesheetResult.success) {
          logger.info(
            `Created spritesheet job ${
              spritesheetResult.jobId || ""
            } for resource ${resource.id}`
          );
        } else {
          logger.warn(
            `Failed to create spritesheet job for resource ${resource.id}: ${spritesheetResult.message}`
          );
        }
      } catch (error) {
        logger.error(
          `Failed to create spritesheet job for resource ${resource.id}`,
          error
        );
      }
    } else if (enableSpriteSheet) {
      logger.info(
        `Skipping spritesheet generation for non-eligible file: ${fileName}`
      );
    }
    res.status(200).json(resource);
  } catch (error) {
    logger.error(`Failed to create resource`, error);
    throw new IntentionalError("Failed to create resource");
  }
};

export default createResourceHandler;
