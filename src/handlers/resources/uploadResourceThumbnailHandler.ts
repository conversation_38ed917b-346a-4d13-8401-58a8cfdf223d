import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import fs from "fs-extra";
import path from "path";
import { apiDevUploadOutput, gcsResourceFolder } from "@/config";
import { gcsUpload, generateSignedUrlForRead } from "@/services/storage";

const uploadResourceThumbnailHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: resourceId } = req.params;
    const payload = req.body as { imageUrl: string };
    const currentUser = res.locals.user;

    const resource = await models.Resource.xFind1({
      createdById: currentUser.id,
      id: resourceId,
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }
    const { name: fileName } = path.parse(resource.name);
    const base64Data = payload.imageUrl.replace(/^data:image\/png;base64,/, "");
    const name = `${fileName}-${resourceId}-thumbnail.png`;


    const filePath = path.join(apiDevUploadOutput, name);

    // Write the base64 data to a file
    fs.writeFileSync(filePath, base64Data, "base64");

    // Upload the thumbnail to GCS
    const thumbnailUrl = await gcsUpload(
      filePath,
      `${gcsResourceFolder}/${name}`,
      "image/x-png",
      true
    );

    // Remove the file after uploaded to GCS
    fs.rm(filePath).catch(log.stack);

    // Update the resource with the new thumbnail URL
    await models.Resource.xUpdateById(resourceId, {
      thumbnailUrl,
    });

    // Generate a signed URL for the uploaded thumbnail
    const uploadedUrl = await generateSignedUrlForRead(thumbnailUrl);

    res.status(200).json({ url: uploadedUrl });
  } catch (error) {
    log.error(`Failed to upload resource thumbnail: ${error.message}`);
    throw new IntentionalError("Failed to upload resource thumbnail");
  }
};

export default uploadResourceThumbnailHandler;
