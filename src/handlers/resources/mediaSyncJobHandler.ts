import express from "express";
import { log } from "@/services/logger";
import { pubsubService } from "@/services/pubsub";

export interface MediaSyncPayload {
  firebaseUID: string;
  publicURL: string;
  fileName?: string;
}

const mediaSyncJobHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const payload = req.body as MediaSyncPayload;

    if (!payload.firebaseUID || !payload.publicURL) {
      res.status(400).json({ 
        message: "Missing required fields: firebaseUID and publicURL" 
      });
      return;
    }

    // Publish message to PubSub
    await pubsubService.publishMessage({
      data: JSON.stringify(payload),
      attributes: {
        type: "media-sync",
        timestamp: new Date().toISOString()
      }
    });

    log.info("Media sync job published successfully", { payload });

    res.status(200).json({ 
      message: "Media sync job queued successfully" 
    });
  } catch (error) {
    log.error("Error publishing media sync job:", error);
    res.status(500).json({ 
      message: "Failed to queue media sync job" 
    });
  }
};

export default mediaSyncJobHandler;
