import { DATA_RETENTION_DAYS } from "@/config";
import { models } from "@/schemas";
import { Resource } from "@/schemas/resource/Resource.model";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import express from "express";
import { isUserAccessibleResource } from "./resource.service";
import { addDays } from "date-fns";

const extendResourceHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;

    const resource = await models.Resource.xFind1({
      id: resourceId,
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }

    const retentionSetting = await models.RetentionSetting.xFind1By('resourceId', resourceId);
    if (!retentionSetting) {
      res.status(404).json({ message: "Retention setting not found" });
      return;
    }
    
    
    const isEditable = await isUserAccessibleResource(currentUser.id, resource);

    if (!isEditable.success || !isEditable.data.canEdit) {
      res.status(403).json({ message: isEditable.message });
      return;
    }

    const updatedRetentionSetting = await models.RetentionSetting.xUpdateById(retentionSetting.id, {
      extensionCount: retentionSetting.extensionCount + 1,
      lastExtensionDate: new Date(),
      expiryDate: addDays(retentionSetting.expiryDate, DATA_RETENTION_DAYS),
      extensionHistory: [
        ...(retentionSetting.extensionHistory as any[]),
        {
          extensionDate: new Date(),
          extensionDays: DATA_RETENTION_DAYS,
          previousExpiryDate: retentionSetting.expiryDate,
        },
      ],
    });

    res.status(200).json(updatedRetentionSetting);
  } catch (error) {
    log.error(`Failed to extend resource: ${error.message}`);
    throw new IntentionalError("Failed to extend resource");
  }
};

export default extendResourceHandler;
