import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { generateSignedUrlForRead } from "@/services/storage";
import { createErrorResponse } from "@/utils/response";
import { generateSpriteSheetVTTWithSignedUrls } from "@/utils/transcoding";

/**
 * <PERSON><PERSON> for getting spriteSheet VTT content with signed URLs for a video resource
 * GET /resources/:id/spriteSheet
 * Returns VTT content directly with signed sprite image URLs
 */
const getSpriteSheetHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;

    if (!resourceId) {
      res.status(400).json(createErrorResponse("Resource ID is required", 400));
      return;
    }

    // Find the resource and verify access
    const resource = await models.Resource.xFind1({
      id: resourceId,
      // createdById: currentUser.id,
    });

    if (!resource) {
      res.status(404).json(createErrorResponse("Resource not found", 404));
      return;
    }

    // Check if spriteSheet is available
    if (!resource.isSpriteSheets || !resource.vttSrc) {
      res.status(404).json(createErrorResponse(
        "SpriteSheet not available for this resource. The resource may not be a video file or spriteSheet generation may not be complete.",
        404
      ));
      return;
    }

    const imageURL = await generateSignedUrlForRead(`videos/${resourceId}/sprite0000000000.jpeg`);

    const vttContent = await generateSpriteSheetVTTWithSignedUrls(
      resource.vttSrc,
      imageURL
    );

    res.setHeader('Content-Type', 'text/vtt; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="sprite_${resourceId}.vtt"`);
    res.setHeader('Cache-Control', 'public, max-age=3600');

    res.status(200).send(vttContent);
  } catch (error) {
    log.error(`Failed to get spriteSheet VTT: ${error.message}`, {
      resourceId: req.params.id,
      userId: res.locals.user?.id,
      error: error instanceof Error ? error.message : String(error),
    });
    throw new IntentionalError("Failed to get spriteSheet VTT");
  }
};

export default getSpriteSheetHandler; 
