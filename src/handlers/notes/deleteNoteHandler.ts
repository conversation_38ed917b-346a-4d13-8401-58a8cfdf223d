import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Note } from "@/schemas/note/Note.model";
import { Project } from "@/schemas/project/Project.model";
import {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/types";

const deleteNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: noteId } = req.params;
    const currentUser = res.locals.user;
    const accessibleProjectIds = res.locals.accessibleProjects || [];

    const noteQuery = await models.Note.findOne({
      where: {
        id: noteId,
      },
      include: [
        {
          model: models.Project,
          as: "project",
          required: false,
        },
      ],
    });

    const note = noteQuery?.toJSON() as Note & { project: Project };
    if (!note) {
      res.status(404).json({
        message:
          "Note not found or you don't have permission to delete this note",
      });
      return;
    }

    if (note.project.id) {
      /**
       * If note belong to a project => To delete the note, the user must be a member of the project and has editor role or project owner
       */
      const projectQuery = await models.Project.findOne({
        where: {
          id: note.project.id,
        },
        include: [
          {
            model: models.ProjectMember,
            where: {
              userId: currentUser.id,
            },
            as: "members",
            required: false,
          },
        ],
      });

      const project = projectQuery?.toJSON() as Project & {
        members: ProjectMember[];
      };

      if (!project) {
        res.status(404).json({
          message:
            "Project not found or you don't have permission to access it",
        });
        return;
      }

      const projectMember = project.members?.[0];

      if (!projectMember && project.createdById !== currentUser.id) {
        res.status(403).json({
          message: "You are not in this project member list nor project owner",
        });
        return;
      }

      // Check role-based permissions
      if (projectMember && projectMember?.role !== ProjectMemberRole.EDITOR && projectMember?.role !== ProjectMemberRole.COMMENTER) {
        res
          .status(403)
          .json({ message: "You don't have permission to delete this note" });
        return;
      }

      // If user is COMMENTER, they can only delete notes they created
      if (projectMember && projectMember.role === ProjectMemberRole.COMMENTER && note.createdById !== currentUser.id) {
        res
          .status(403)
          .json({ message: "Commenters can only delete notes they created" });
        return;
      }
    }

    await models.Note.xDestroyById(noteId);

    // Broadcast note.deleted event via SSE if projectId exists
    if (note.project?.id) {
      try {
        const sseService = getSSEService();
        await sseService.broadcastToProject(note.project.id, {
          type: SSEEventType.NOTE_DELETED,
          projectId: note.project.id,
          userId: currentUser.id,
          data: {
            id: note.id,
            deletedById: currentUser.id,
            deletedBy: currentUser.name,
            deletedAt: new Date().toISOString(),
          },
        });

      } catch (error) {
        log.error('Failed to broadcast note.deleted event via SSE', {
          error,
          noteId,
          projectId: note.project.id,
          userId: currentUser.id,
        });
      }
    }
    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`[deleteNoteHandler] Failed to delete note`, error);
    throw new IntentionalError("Failed to delete note");
  }
};

export default deleteNoteHandler;
