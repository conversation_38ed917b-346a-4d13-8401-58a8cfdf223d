import express from 'express';
import { urlShortenerService, UrlType } from '@/services/urlShortener';
import { log } from '@/services/logger';

interface CreateShortLinkRequest {
  originalUrl: string;
  urlType: UrlType;
  entityId?: string;
  expiresInDays?: number;
}

export const createShortLinkHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const { originalUrl, urlType, entityId, expiresInDays }: CreateShortLinkRequest = req.body;
    const currentUser = res.locals.user;

    // Validate required fields
    if (!originalUrl) {
      res.status(400).json({ 
        error: 'originalUrl is required',
        details: 'Please provide the original URL to shorten'
      });
      return;
    }

    if (!urlType) {
      res.status(400).json({ 
        error: 'urlType is required',
        details: 'Please specify the type of URL',
        allowedTypes: Object.values(UrlType)
      });
      return;
    }

    // Validate URL type
    if (!Object.values(UrlType).includes(urlType)) {
      res.status(400).json({ 
        error: 'Invalid urlType',
        details: 'Please use one of the allowed URL types',
        allowedTypes: Object.values(UrlType)
      });
      return;
    }

    // Validate URL format
    try {
      new URL(originalUrl);
    } catch (urlError) {
      res.status(400).json({ 
        error: 'Invalid URL format',
        details: 'Please provide a valid URL with protocol (http:// or https://)'
      });
      return;
    }

    // Validate expiration days if provided
    if (expiresInDays !== undefined) {
      if (!Number.isInteger(expiresInDays) || expiresInDays < 1 || expiresInDays > 365) {
        res.status(400).json({ 
          error: 'Invalid expiration days',
          details: 'expiresInDays must be an integer between 1 and 365'
        });
        return;
      }
    }

    // Check authentication for user-specific URL types
    const userSpecificTypes = [UrlType.FEEDBACK, UrlType.CONFIRMATION, UrlType.RETENTION_EXTEND];
    if (userSpecificTypes.includes(urlType) && !currentUser) {
      res.status(401).json({ 
        error: 'Authentication required',
        details: `URL type '${urlType}' requires user authentication`
      });
      return;
    }

    // Create shortened URL
    const shortUrl = await urlShortenerService.shortenUrl(originalUrl, {
      urlType,
      entityId,
      userId: currentUser?.id,
      expiresInDays,
    });

    // Extract short code from the full URL
    const shortCode = shortUrl.split('/').pop();

    log.info('Created short link', { 
      userId: currentUser?.id,
      urlType,
      entityId,
      shortCode,
      originalUrl: originalUrl.substring(0, 100) + (originalUrl.length > 100 ? '...' : '') // Log truncated URL for privacy
    });

    res.status(201).json({
      success: true,
      shortUrl,
      originalUrl,
      message: 'Short link created successfully'
    });

  } catch (error: any) {
    log.error('Error creating short link:', error);
    
    if (error.message?.includes('Invalid domain')) {
      res.status(400).json({ 
        error: 'Invalid domain',
        details: 'URL must be from an allowed domain'
      });
      return;
    }

    if (error.message?.includes('requires a userId')) {
      res.status(400).json({ 
        error: 'User authentication required',
        details: error.message
      });
      return;
    }

    res.status(500).json({ 
      error: 'Internal server error',
      details: 'Failed to create short link'
    });
  }
};
