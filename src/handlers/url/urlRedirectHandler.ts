import express from 'express';
import { urlShortenerService } from '@/services/urlShortener';

export const resolveShortenUrl = async (
  req: express.Request,
  res: express.Response,
) => {
   try {
     const { shortCode } = req.params;
      const currentUser = res.locals.user;
 
     if (!shortCode || shortCode.length < 5) {
       res.status(400).json({ error: 'Invalid short code' });
       return;
     }
 
     const result = await urlShortenerService.resolveUrl(shortCode, currentUser?.id);
     
     if (!result.isValid) {
       res.status(404).json({ 
         error: result.errorMessage || 'URL not found',
         requiresAuth: result.errorMessage?.includes('user-specific') || result.errorMessage?.includes('Access denied')
       });
       return;
     }
 
     res.status(200).json({
       success: true,
       originalUrl: result.originalUrl,
       shortCode
     });
     
   } catch (error) {
     console.error('Error resolving short URL for frontend:', error);
     res.status(500).json({ error: 'Internal server error' });
   }
}