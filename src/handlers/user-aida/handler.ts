import { models } from "@/schemas";
import { getFirebaseAuth } from "@/services/firebase";
import { recallAiClient } from "@/services/recall";
import { scheduleBot } from "@/services/recall/utils/createBot";
import express from "express";
import { format } from "date-fns";
import { UserRecord } from "firebase-admin/lib/auth/user-record";
import { sendConfirmedMeetingEmail } from "@/services/email";
import { MeetingType } from "@/services/email/template/confirmedMeeting";
import { sendAnnouncementEmail } from "../recall/calendar.handler";
import { findOrCreateDefaultProject } from "@/utils";

type UserAidaRequest = {
  // userAidaId
  fUid: string;
  // event id of recall calendar
  eventId: string;
};

export async function userAidaHandler(
  req: express.Request,
  res: express.Response
) {
  try {
    const { fUid, eventId } = req.query as UserAidaRequest;

    await models.UserAida.xUpdate(
      {
        id: fUid,
      },
      {
        isConfirmed: true,
      }
    );

    // schedule bot to the event and session(shouldSendSummaryToEmail: true,) then send email to user
    // fetch event from recall calendar
    const firebaseAuth = await getFirebaseAuth();
    const fUser = await firebaseAuth.getUser(fUid).catch(() => null);
    if (!fUser) {
      res.status(404).send("User not found");
      return;
    }
    const event = await recallAiClient.v2_retrieveCalendarEventById(eventId);
    if(!event) {
      res.status(404).send("Event not found");
      return;
    }
    if(event?.bots.length) {
      res.status(400).send("Bot already scheduled for this event");
      return;
    }
    const bot = await scheduleBot(
      event.id,
      event.meeting_url,
      {},
      event.start_time,
      fUser.email
    );
    if (!bot) {
      res.status(404).send("Unable to schedule bot");
      return;
    }
    const meetingTitle = event.raw.summary || `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`;
    
    // Get or create default project for the user
    const defaultProjectResponse = await findOrCreateDefaultProject(fUser.uid);
    if (!defaultProjectResponse.success || !defaultProjectResponse.data) {
      res.status(defaultProjectResponse.statusCode).json(defaultProjectResponse);
      return;
    }
    const defaultProject = defaultProjectResponse.data;
    const defaultProjectId = defaultProject.id;
    
    await models.Session.xCreate({
      recallBotId: bot.bot_id,
      title: meetingTitle,
      startTime: new Date(event.start_time),
      meetingUrl: event.meeting_url,
      createdById: fUser.uid,
      shouldSendSummaryToEmail: true,
      projectId: defaultProjectId, // ← Assign to default project
    });

    await sendAnnouncementEmail({
      fU: fUser, event, type: MeetingType.Confirmed
    });

    res.status(201).send("Aida confirmed for your meeting");
  } catch (error) {
    console.error("Error in userAidaHandler:", error);
    res.status(500).send("Internal server error");
  }
}