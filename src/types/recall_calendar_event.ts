export interface RecallCalendarEvent {
  id: string;
  start_time: string;
  end_time: string;
  calendar_id: string;
  raw: Raw;
  platform: string;
  platform_id: string;
  ical_uid: string;
  meeting_platform: string;
  meeting_url: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  bots: Bot[];
}

export interface Bot {
  bot_id: string;
  start_time: string;
  deduplication_key: string;
  end_time: string;
}

export interface Raw {
  id: string;
  end: End;
  etag: string;
  kind: string;
  start: End;
  status: string;
  created: string;
  creator: Creator;
  iCalUID: string;
  summary: string;
  updated: string;
  htmlLink: string;
  sequence: number;
  attendees: Attendee[];
  eventType: string;
  organizer: Creator;
  reminders: Reminders;
  hangoutLink: string;
  conferenceData: ConferenceData;
}

export interface ConferenceData {
  entryPoints: EntryPoint[];
  conferenceId: string;
  conferenceSolution: ConferenceSolution;
}

export interface ConferenceSolution {
  key: Key;
  name: string;
  iconUri: string;
}

export interface Key {
  type: string;
}

export interface EntryPoint {
  uri: string;
  label: string;
  entryPointType: string;
}

export interface Reminders {
  useDefault: boolean;
}

export interface Attendee {
  self?: boolean;
  email: string;
  responseStatus: string;
  organizer?: boolean;
}

export interface Creator {
  email: string;
}

export interface End {
  dateTime: string;
  timeZone: string;
}