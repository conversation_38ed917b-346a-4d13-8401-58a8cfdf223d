import { Project } from "@/schemas/project/Project.model";
import { ResourceData } from "./resources";
import { ProjectFolder } from "@/schemas/project/ProjectFolder.model";

export interface ProjectData extends Project {
  totalFilesSize: number;
  totalFilesCount: number;
  resources: ResourceData[];
  folders: ProjectFolder[];
  userPermissions?: {
    canView: boolean;
    canEdit: boolean;
  };
}
