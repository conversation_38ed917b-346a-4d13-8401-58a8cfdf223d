
import { AudioVideoMetadata, FileCategory } from "./vectorDb";

import { RagSyncStatus } from "@/schemas/resource/Resource.model";



/**
 * Document processing action types
 */
export const DocumentProcessingType = {
  UPLOAD: "upload",
  DELETE: "delete",
  UPDATE: "update",
} as const;

/**
 * Update types for document processing
 */
export const DocumentUpdateType = {
  NAME: "name",
  PROJECT_ID: "project_id"
} as const;

/**
 * Message structure for document processing
 */
export interface DocumentProcessorMessage {
  file_id: string;
  user_id: string;
  presigned_url: string;
  project_id: string;
  metadata?: {
    // ========== FILE CONTEXT (Essential for citations) ==========
    /** Original filename as uploaded - appears in citations */
    filename: string;
    /** File category for processing pipeline routing */
    category?: FileCategory;

    // ========== AUDIO/VIDEO CONTEXT (Only for transcripts) ==========
    /** Audio/video metadata when applicable */
    audio_video?: AudioVideoMetadata;

    // ========== OPTIONAL ENHANCEMENTS ==========
    /** Search/filtering tags */
    tags?: string[];
    /** Additional custom metadata */
    custom?: Record<string, any>;
  }
  type: typeof DocumentProcessingType[keyof typeof DocumentProcessingType];
}

/**
 * Callback payload structure for document processor webhook
 */
export interface DocumentProcessorCallbackPayload {
  resourceId: string;
  status: RagSyncStatus;
  projectId: string;
} 