import { AuthUser } from "@/types/auth";
import { PaginationData } from "@/middlewares/pagination";
import { Request } from "express";

declare module "express-serve-static-core" {
  interface Locals {
    user: AuthUser;
    pagination?: PaginationData;
    accessibleProjects?: string[];
  }

  interface Request {
    /**
     * Unique request identifier generated by HTTP middleware
     * Used for request tracing and correlation across services
     */
    requestId?: string;
    /**
     * Request start timestamp for response time calculation
     */
    startTime?: number;
  }
}

/**
 * Utility function to extract request ID from Express request
 * Returns the request ID if available, otherwise returns undefined
 */
export function getRequestId(req: Request): string | undefined {
  return req.requestId;
}
