// Unified Vector Database Types for AIDA RAG System
// Simplified for both Pinecone Assistant (current) and Unstract + Pinecone (future)

import { audioExts, videoExts, textExts, imageExts } from "@/utils/file";
import { ServiceResponse } from "@/types";

// ============================================================================
// CORE TYPES
// ============================================================================

/**
 * File category classification based on existing file utilities
 */
export type FileCategory = "audio" | "video" | "text" | "image";

/**
 * Supported file extensions (derived from existing file utilities)
 */
export type SupportedFileExtension =
  | keyof typeof audioExts
  | keyof typeof videoExts
  | keyof typeof textExts
  | keyof typeof imageExts;

/**
 * Document processing status
 */
export type ProcessingStatus =
  | "pending"
  | "processing"
  | "completed"
  | "failed"
  | "retry";

/**
 * Audio/video specific metadata for transcripts
 */
export interface AudioVideoMetadata {
  speakers?: string[];
  audio_duration?: number;
  language?: string;
}

/**
 * Unified document metadata for both Pinecone Assistant and Unstract
 * Simplified to only include what's actually needed by both platforms
 */
export interface UnifiedDocumentMetadata {
  // ========== CORE IDENTIFICATION (Required for both platforms) ==========
  /** Firebase user ID for multi-tenant security */
  user_id: string;
  /** Project UUID for project-level isolation */
  project_id: string;
  /** Unique file identifier */
  file_id: string;

  // ========== FILE CONTEXT (Essential for citations) ==========
  /** Original filename as uploaded - appears in citations */
  filename: string;
  /** File category for processing pipeline routing */
  category?: FileCategory;

  // ========== AUDIO/VIDEO CONTEXT (Only for transcripts) ==========
  /** Audio/video metadata when applicable */
  audio_video?: AudioVideoMetadata;

  // ========== OPTIONAL ENHANCEMENTS ==========
  /** Search/filtering tags */
  tags?: string[];
  /** Additional custom metadata */
  custom?: Record<string, any>;
}

/**
 * Complete unified document payload for vector database operations
 */
export interface UnifiedDocumentPayload {
  /** File content or file stream */
  content?: string;
  /** File stream for direct uploads */
  file_stream?: ReadableStream | Blob;
  /** Complete unified metadata */
  metadata: UnifiedDocumentMetadata;
}

// ============================================================================
// PLATFORM-SPECIFIC RESULTS
// ============================================================================

/**
 * Single platform upload result
 */
export interface PlatformUploadResult {
  success: boolean;
  document_id?: string;
  error?: string;
  processing_time_ms?: number;
}

/**
 * Dual platform upload result
 */
export interface DualPlatformUploadResult {
  pinecone_assistant: PlatformUploadResult;
  unstract: PlatformUploadResult;
  processing_time_ms: number;
}

// ============================================================================
// QUERY INTERFACES (Simplified)
// ============================================================================

/**
 * Unified query payload for vector database queries
 */
export interface UnifiedQueryPayload {
  /** Natural language query */
  query: string;
  /** User ID for security filtering */
  user_id: string;
  /** Project ID for security filtering */
  project_id: string;
  /** Maximum number of results */
  max_results?: number;
  /** Minimum confidence threshold */
  min_confidence?: number;
  /** Include citations in response */
  include_citations?: boolean;
}

/**
 * Citation information in responses
 */
export interface CitationInfo {
  /** Source document ID */
  source_id: string;
  /** Original filename */
  source_filename: string;
  /** Exact quote from source */
  quote: string;
  /** Page number for document sources */
  page_number?: number;
  /** Timestamp for audio/video sources */
  timestamp?: string;
  /** Speaker for audio sources */
  speaker?: string;
  /** Confidence score for citation */
  confidence: number;
}

/**
 * Unified query response data structure
 */
export interface UnifiedQueryData {
  /** Generated answer */
  answer: string;
  /** Source citations */
  citations: CitationInfo[];
}

/**
 * Unified response format - uses ServiceResponse from main types
 */
export type UnifiedQueryResponse = ServiceResponse<UnifiedQueryData>;
