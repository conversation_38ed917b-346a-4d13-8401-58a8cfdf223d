import { ChatUserRole } from "@/schemas/agent/ChatMessage.model";
import type { Part, Tool, FunctionCallingConfigMode } from "@google/genai";
import { SupportedModel } from "./universal-ai";

// Re-export Tool for other modules
export type { Tool };

// ============================================================================
// CORE TYPES
// ============================================================================

// Token usage statistics from AI model
export interface TokenUsage {
  inputTokens?: number;
  outputTokens?: number;
  totalTokens?: number;
  inputCharacters?: number;
  outputCharacters?: number;
  imageTokens?: number; // Token cost for processing images
}

// Defines the shape of a single message in the conversation history
export interface IChatMessage {
  role: ChatUserRole | "user" | "assistant"; // Support both enum and string for flexibility
  content: string;
  model?: SupportedModel;
  usage?: TokenUsage;
  cost?: number;
  resources?: string[]; // Array of resource IDs been used as context
  hasImages?: boolean; // Quick lookup flag to check if this message contains images
  imageCount?: number; // Number of images attached to this message
}

// ============================================================================
// REQUEST/RESPONSE INTERFACES
// ============================================================================

// Defines the request body for creating a new conversation message
export interface CreateMessageRequest {
  model: SupportedModel;
  message: string;
  // While the full history is loaded from the DB,
  // the client might send its current view of the history
  // for context or validation.
  history?: IChatMessage[];
  resources?: string[]; // Array of resource IDs to use as context
}

// Alternative interface for service-internal usage (more flexible)
export interface ICreateMessageRequest {
  message: string;
  model?: SupportedModel;
  resources?: string[];
}

// Response when creating a new conversation
export interface ConversationResponse {
  id: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Conversation data interface for service usage
export interface IConversationData {
  id: string;
  createdById: string;
  projectId?: string;
  createdAt: string | Date;
  updatedAt?: string | Date;
}

// Response when creating a new message
export interface MessageResponse {
  id: string;
  conversationId: string;
  role: ChatUserRole;
  content: string;
  model?: SupportedModel;
  usage?: TokenUsage;
  cost?: number;
  resources?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// STREAMING INTERFACES
// ============================================================================

// Stream context for tracking streaming state
export interface IStreamContext {
  readonly conversationId: string;
  readonly userId: string;
  readonly model: SupportedModel;
  readonly streamStartTime: number;

  // Content state
  accumulatedText: string;
  finishReason: string;

  // Chunk tracking
  chunkCount: number;
  textChunkCount: number;
  consecutiveEmptyChunks: number;
  consecutiveWhitespaceChunks: number;

  // Timeout management
  streamTimeout: NodeJS.Timeout | null;

  // RAG tracking
  ragToolCalls: number;
  ragProcessingTime: number;
  ragResultsCount: number;
  ragSuccess: boolean;
  ragUsed: boolean;
}

// Stream processing result
export interface IStreamProcessingResult {
  usage: TokenUsage;
  cost: number | null;
  success: boolean;
}

// Stream chunk data
export interface IStreamChunkData {
  type: "content" | "completion" | "error";
  text?: string;
  timestamp: number;
  finishReason?: string;
  totalChunks?: number;
  textChunks?: number;
  responseLength?: number;
  duration?: number;
  success?: boolean;
}

// ============================================================================
// RESOURCE PROCESSING INTERFACES
// ============================================================================

// Resource processing result
export interface IResourceProcessingResult {
  resourceContext: string;
  availableTools: Tool[];
  inlineDataParts: Part[];
}

// Function call result
export interface IFunctionCallResult {
  success: boolean;
  data?: any;
  error?: string;
  snippetCount?: number;
  metadata?: {
    snippetCount?: number;
    processingTime?: number;
    source?: string;
  };
}

// ============================================================================
// RAG INTERFACES
// ============================================================================

// RAG metrics for tracking
export interface IRagMetrics {
  enabled: boolean;
  success: boolean;
  responseTimeMs?: number;
  estimatedTokenSavings?: number;
  toolsUsed?: number;
  qualityScore?: number;
}

// RAG performance metrics
export interface IRagPerformanceMetrics {
  processingTimeMs?: number;
  resultsCount?: number;
  improvedResponse?: boolean;
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

// Generation configuration
export interface IGenerationConfig {
  temperature: number;
  maxOutputTokens: number;
  systemInstruction: {
    parts: Part[];
  };
  tools?: Tool[];
  toolConfig?: {
    functionCallingConfig: {
      mode: FunctionCallingConfigMode;
    };
  };
}

// Stream configuration
export interface IStreamConfig {
  readonly timeout: number;
  readonly maxChunks: number;
  readonly maxConsecutiveWhitespace: number;
  readonly maxConsecutiveEmpty: number;
  readonly heartbeatInterval: number;
}

// ============================================================================
// UTILITY INTERFACES
// ============================================================================

// Strict API response type
export interface IApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  statusCode: number;
  error?: string;
}

// Session metadata
export interface ISessionMetadata {
  sessionDate: string;
  sessionHour: number;
  timestamp: string;
}

// Cost breakdown
export interface ICostBreakdown {
  inputCost: number;
  outputCost: number;
  totalCost: number;
  costPerToken: number;
  costTier: "high" | "medium" | "low" | "minimal";
}

// ============================================================================
// ERROR TYPES
// ============================================================================

// Agent service error types
export type AgentErrorType =
  | "validation"
  | "api"
  | "streaming"
  | "model"
  | "general"
  | "rag"
  | "timeout"
  | "quota"
  | "auth"
  | "safety"
  | "not_found";

// RAG error types
export type RagErrorType =
  | "retrieval_failed"
  | "timeout"
  | "no_results"
  | "embedding_failed"
  | "quota_exceeded"
  | "auth_failed";

// Stream finish reasons
export type StreamFinishReason =
  | "STOP"
  | "MAX_TOKENS"
  | "SAFETY"
  | "CIRCUIT_BREAKER_WHITESPACE"
  | "CIRCUIT_BREAKER_EMPTY"
  | "CIRCUIT_BREAKER_TOTAL_CHUNKS"
  | "TIMEOUT"
  | "ERROR";

// ============================================================================
// REQUEST PREFERENCES & CONTEXT
// ============================================================================

export interface UserPreferences {
  instructions?: string;
  ragSearchEnabled?: boolean;
}

export interface RequestContext {
  userId: string;
  conversationId: string;
  projectId?: string;
  requestId?: string;
  resourceIds: string[];
  preferences?: UserPreferences;
  requestedResources: string[];
  timestamp: number;
}

// ============================================================================
// FILE PROCESSING VALIDATION
// ============================================================================

export interface ValidationResult {
  valid: boolean;
  processedCount?: number;
  searchableCount?: number;
  totalRequested?: number;
  missingFiles?: string[];
  message?: string;
}

export interface FileProcessingReport {
  totalRequested: number;
  processed: number;
  searchable: number;
  errors: string[];
}

// ============================================================================
// CONVERSATION STATE MANAGEMENT
// ============================================================================

export interface ToolCallRecord {
  toolName: string;
  args: Record<string, any>;
  result: any;
  timestamp: number;
  processingTime: number;
}

// ============================================================================
// PROGRESS STREAMING TYPES
// ============================================================================

export interface ProgressStep {
  type: "progress";
  step: ProgressStepType;
  message: string;
  timestamp: number;
  metadata?: {
    fileCount?: number;
    searchResults?: number;
    complexity?: "simple" | "complex";
    estimatedTime?: number;
    details?: string;
  };
}

export type ProgressStepType =
  | "analyzing" // Analyzing user input and documents
  | "searching" // Searching through knowledge base
  | "reasoning" // AI thinking/reasoning process
  | "generating" // Generating the response
  | "synthesizing"; // Combining information into final answer

export interface ProgressReporter {
  analyzing(fileCount: number, complexity?: "simple" | "complex"): void;
  searching(fileCount: number, searchQuery?: string): void;
  reasoning(complexity?: "simple" | "complex", details?: string): void;
  generating(): void;
  synthesizing(searchResults: number): void;
}
