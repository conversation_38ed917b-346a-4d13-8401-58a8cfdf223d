// Simple types for billing (replacing removed ai-providers.ts)
import { ProviderType, ModelTier, FinishReason } from "./universal-ai";

export interface DetailedUsageRecord {
  id: string;
  messageId: string;
  conversationId: string;
  userId: string;
  projectId?: string;

  // Provider attribution
  provider: ProviderType;
  modelId: string;
  modelTier: ModelTier;

  // Token tracking
  inputTokens: number;
  outputTokens: number;
  imageTokens?: number;
  totalTokens: number;

  // Cost tracking (in USD)
  inputCost: number;
  outputCost: number;
  totalCost: number;
  costPerToken: number;

  // Context metadata
  ragUsed: boolean;
  toolCalls: number;
  messageLength: number;
  processingTimeMs: number;

  // Quality metrics
  finishReason: FinishReason;
  errorCount: number;

  timestamp: Date;
}

export interface CostBreakdown {
  inputCost: number;
  outputCost: number;
  imageCost?: number;
  totalCost: number;
}

export interface UsageContext {
  messageId: string;
  projectId?: string;
  ragUsed?: boolean;
  toolCalls?: number;
  messageLength?: number;
  processingTimeMs?: number;
  finishReason?: FinishReason;
  errorCount?: number;
}

export interface BillingInsight {
  id: string;
  userId: string;
  projectId?: string;

  insightType: "cost_optimization" | "usage_anomaly" | "model_recommendation";
  insightCategory: "billing" | "performance" | "efficiency";
  priority: "low" | "medium" | "high" | "critical";

  title: string;
  description: string;
  recommendation?: string;
  potentialSavingsUsd?: number;

  supportingData?: Record<string, any>;
  generatedAt: Date;

  status: "active" | "acknowledged" | "applied" | "dismissed";
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
}

export interface UsageQuota {
  id: string;
  userId?: string;
  projectId?: string;
  organizationId?: string;

  quotaType: "tokens_per_month" | "cost_per_month" | "requests_per_hour";
  quotaLimit: number;
  quotaPeriod: "hourly" | "daily" | "monthly";

  provider?: ProviderType;
  modelTier?: ModelTier;

  currentUsage: number;
  resetAt: Date;

  isActive: boolean;
  warningThreshold: number; // 0.8 = 80%
}

export interface UsageAggregate {
  id: string;
  userId: string;
  projectId?: string;
  organizationId?: string;
  provider: ProviderType;
  modelTier: ModelTier;

  periodType: "hourly" | "daily" | "monthly";
  periodStart: Date;
  periodEnd: Date;
  periodKey: string; // '2024-12-01', '2024-12', etc.

  totalRequests: number;
  totalTokens: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalCostUsd: number;

  avgResponseTimeMs: number;
  successRate: number; // 0.95 = 95%
  errorCount: number;
  fallbackCount: number;

  avgCostPerToken: number;
  avgTokensPerRequest: number;
}
