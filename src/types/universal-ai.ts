// Universal AI Provider Types - Foundation for True Multi-Provider Architecture
// This file is the single source of truth for multi-provider types

export type ProviderType = "anthropic" | "google";

// Model tier classification for billing and routing
export type ModelTier = "economy" | "balanced" | "premium";

// Define all supported models as union type for type safety
export type SupportedModel =
  // Anthropic Models (Latest 4)
  | "claude-3-7-sonnet-********"
  | "claude-sonnet-4-********"
  | "claude-opus-4-********"
  | "claude-opus-4-1-********"
  // Google Models (Latest)
  | "gemini-2.5-flash"
  | "gemini-2.5-pro";

export interface UniversalRequest {
  model: SupportedModel;
  messages: UniversalMessage[];
  systemInstructions?: string[];
  tools?: UniversalTool[];
  maxTokens?: number;
  temperature?: number;
  stream: true;
}

export interface UniversalMessage {
  role: "user" | "assistant" | "system";
  content: string | UniversalMessageContent[];
  metadata?: MessageMetadata;
}

export interface UniversalMessageContent {
  type: "text" | "image";
  text?: string;
  image?: UniversalImageContent;
}

export interface UniversalImageContent {
  mimeType: string;
  data: string; // base64 encoded
}

export interface UniversalTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

export interface UniversalToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  status: "building" | "complete" | "error";
}

export interface UniversalResponse {
  provider: ProviderType;
  model: SupportedModel;
  content: string;
  usage: TokenUsage;
  finishReason: FinishReason;
  metadata: ResponseMetadata;
  toolCalls?: UniversalToolCall[];
}

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  imageTokens?: number;
  totalTokens: number;
}

export interface ModelCapabilities {
  maxContextLength: number;
  supportsTools: boolean;
  supportsImages: boolean;
  supportsStreaming: boolean;
}

export interface MessageMetadata {
  timestamp?: string;
  requestId?: string;
  conversationId?: string;
}

export interface ResponseMetadata {
  timestamp: string;
  requestId?: string;
  latency?: number;
  cached?: boolean;
}

export type FinishReason =
  | "stop"
  | "length"
  | "tool_calls"
  | "content_filter"
  | "error"
  | "continue"; // For chunks that are still streaming

// Provider-specific request/response types for internal use
export interface OpenAIRequest {
  messages: Array<{ role: string; content: string }>;
  tools?: Array<{
    type: "function";
    function: {
      name: string;
      description: string;
      parameters: Record<string, any>;
    };
  }>;
}

export interface AnthropicRequest {
  messages: Array<{ role: "user" | "assistant"; content: string }>;
  tools?: Array<{
    name: string;
    description: string;
    input_schema: {
      type: "object";
      properties: Record<string, any>;
      required: string[];
    };
  }>;
}

export interface GoogleRequest {
  contents: Array<{
    role: "user" | "model";
    parts: Array<{ text: string }>;
  }>;
  tools?: Array<{
    functionDeclarations: Array<{
      name: string;
      description: string;
      parameters: Record<string, any>;
    }>;
  }>;
}
