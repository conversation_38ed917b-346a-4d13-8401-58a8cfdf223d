import type {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CalendarEventAddBot,
  PaginatedCalendarEventList,
} from "recall-ai";

export enum BotEventEnum {
  StatusChange = "bot.status_change",
  CalendarSyncEvent = "calendar.sync_events",
  CalendarUpdate = "calendar.update",
}

export type RecallEventType =
  | "bot.status_change"
  | "calendar.sync_events"
  | "calendar.update";

export type RecallEventData = {
  "bot.status_change": BotChangeData;
  "calendar.sync_events": CalendarEventChangeData;
  "calendar.update": CalendarChangeData;
};

export type RecallBody<T extends RecallEventType> = {
  event: T;
  data: RecallEventData[T];
};

export enum BotChangeStatusCode {
  JoiningCall = "joining_call",
  InWaitingRoom = "in_waiting_room",
  InCallNotRecording = "in_call_not_recording",
  RecordingPermissionAllowed = "recording_permission_allowed",
  RecordingPermissionDenied = "recording_permission_denied",
  InCallRecording = "in_call_recording",
  CallEnded = "call_ended",
  Done = "done",
  Fatal = "fatal",
  AnalysisDone = "analysis_done",
  AnalysisFailed = "analysis_failed",
  MediaExpired = "media_expired",
  TimeoutExceededWaitingRoom = "timeout_exceeded_waiting_room",
}

export enum BotChangeSubCodeTimeout {
  TimeoutExceededWaitingRoom = "timeout_exceeded_waiting_room",
  TimeoutExceededNoOneJoined = "timeout_exceeded_noone_joined",
  CallEndedByPlatformWaitingRoomTimeout = "call_ended_by_platform_waiting_room_timeout",
}

export type BotChangeResponse = {
  event: BotEventEnum;
  data: BotChangeData;
};
export type BotChangeData = {
  bot_id: string;
  status: BotEvent;
};

export type CalendarEventChangeData = {
  calendar_id: string;
  last_updated_ts: string;
};

export type CalendarChangeData = {
  calendar_id: string;
};

export type CalendarEventList = Omit<PaginatedCalendarEventList, "results"> & {
  results?: Array<AppCalendarEvent>;
};

export type AppCalendarEvent = CalendarEvent & {
  fomattedRaw: EventRawData;
  meeting_url: string | null;
};

export type EventRawData = {
  title: string;
  organizerEmail: string;
  attendees: { email: string }[];
  startTime: {
    dateTime: string;
    timeZone: string;
  };
  endTime: {
    dateTime: string;
    timeZone: string;
  };
  raw: Record<string, any>;
};

export type ScheduleBotData = Omit<CalendarEventAddBot, "bot_config"> & {
  bot_config: Partial<CreateBotBody>;
};

export type CreateBotBody = Pick<
  Bot,
  | "bot_name"
  | "automatic_audio_output"
  | "automatic_video_output"
  | "chat"
  | "join_at"
  | "real_time_transcription"
  | "real_time_media"
  | "transcription_options"
  | "zoom"
  | "google_meet"
  | "slack_huddle_observer"
  | "automatic_leave"
  | "recording_mode"
  | "recording_mode_options"
  | "meeting_url"
> & {
  metadata: Record<string, any>;
  output_media: object;
};

export type RBot = Omit<Bot, "meeting_url"> & {
  meeting_url: Record<string, any>;
  metadata: Record<string, any>;
};

export enum MeetingPlatformUrl {
  GOOGLE_MEET = "https://meet.google.com",
  MICROSOFT_TEAMS = "https://teams.live.com",
}

export interface RecallCalendarDetail {
  id: string;
  oauth_client_id: string;
  oauth_client_secret: string;
  oauth_refresh_token: string;
  platform: string;
  webhook_url?: any;
  oauth_email: string;
  platform_email: string;
  status: string;
  status_changes: RecallCalendarStatus[];
  created_at: string;
  updated_at: string;
}

export interface RecallCalendarStatus {
  status: string;
  created_at: string;
}

export interface RecallError extends Error {
  response?: {
    data: {
      code: string;
      message: string;
      errors: Record<string, string[]>;
    };
  };
}