import { db } from "../db";
import ProjectModel from "./Project.model";
import { ProjectSharedLinkModel } from "./ProjectSharedLink.model";

export enum ProjectAccessRequestStatus {
    PENDING = "PENDING",
    APPROVED = "APPROVED",
    REJECTED = "REJECTED",
}

export const ProjectAccessRequestModel = db.xDefine("ProjectAccessRequest", {
    projectId: {
        type: "STRING",
        allowNull: false,
    },
    sharedLinkId: {
        type: "STRING",
        allowNull: false,
    },
    requestedById: {
        type: "STRING",
        allowNull: false,
    },
    status: {
        type: "STRING",
        allowNull: false,
        defaultValue: ProjectAccessRequestStatus.PENDING,
        values: Object.values(ProjectAccessRequestStatus),
    },
    approvedAt: {
        type: "DATE",
        allowNull: true,
    },
    rejectedAt: {
        type: "DATE",
        allowNull: true,
    },
    rejectedReason: {
        type: "STRING",
        allowNull: true,
    },
    expiredAt: {
        type: "DATE",
        allowNull: false,
    },
});

export type ProjectAccessRequestModelType = typeof ProjectAccessRequestModel;
export type ProjectAccessRequest = ProjectAccessRequestModelType["$M"];
export type ProjectAccessRequestCol = ProjectAccessRequestModelType["$K"];
export type ProjectAccessRequestCreate = ProjectAccessRequestModelType["$C"];
export type ProjectAccessRequestUpdate = ProjectAccessRequestModelType["$U"];
export type ProjectAccessRequestWhere = ProjectAccessRequestModelType["$W"];

// Associations
ProjectAccessRequestModel.belongsTo(ProjectModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "project",
});

ProjectAccessRequestModel.belongsTo(ProjectSharedLinkModel, {
    foreignKey: "sharedLinkId",
    constraints: false,
    as: "sharedLink",
}); 