import { db } from "../db";
import ProjectModel from "./Project.model";

export const ProjectSharedLinkModel = db.xDefine("ProjectSharedLink", {
    projectId: {
        type: "STRING",
        allowNull: false,
    },
    code: {
        type: "STRING",
        allowNull: false,
    },
    expiredAt: {
        type: "DATE",
        allowNull: false,
    },
    maxAccessCount: {
        type: "INTEGER",
        allowNull: false,
    },
    currentAccessCount: {
        type: "INTEGER",
        allowNull: false,
        defaultValue: 0,
    },
});

export type ProjectSharedLinkModelType = typeof ProjectSharedLinkModel;
export type ProjectSharedLink = ProjectSharedLinkModelType["$M"];
export type ProjectSharedLinkCol = ProjectSharedLinkModelType["$K"];
export type ProjectSharedLinkCreate = ProjectSharedLinkModelType["$C"];
export type ProjectSharedLinkUpdate = ProjectSharedLinkModelType["$U"];
export type ProjectSharedLinkWhere = ProjectSharedLinkModelType["$W"];

ProjectSharedLinkModel.belongsTo(ProjectModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "project",
});