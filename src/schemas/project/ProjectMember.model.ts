import { db } from "../db";
import ProjectModel from "./Project.model";

export enum ProjectMemberRole {
    VIEWER = "VIEWER",
    COMMENTER = "COMMENTER",
    EDITOR = "EDITOR"
}

export const ProjectMemberRoleLevel = {
    [ProjectMemberRole.EDITOR]: 1,
    [ProjectMemberRole.COMMENTER]: 2,
    [ProjectMemberRole.VIEWER]: 3,
}

export const getProjectMemberRoleLevel = (role: ProjectMemberRole) => {
    return ProjectMemberRoleLevel[role] || 10_000; // Default level is 10_000 is under the lowest role
}

export const isRoleGTE = (role: ProjectMemberRole, targetRole: ProjectMemberRole) => {
    return getProjectMemberRoleLevel(role) <= getProjectMemberRoleLevel(targetRole);
}



export type ProjectMemberModel = typeof ProjectMemberModel;
export type ProjectMember = ProjectMemberModel["$M"];
export type ProjectMemberCol = ProjectMemberModel["$K"];
export type ProjectMemberCreate = ProjectMemberModel["$C"];
export type ProjectMemberUpdate = ProjectMemberModel["$U"];
export type ProjectMemberWhere = ProjectMemberModel["$W"];

const ProjectMemberModel = db.xDefine("ProjectMember", {
    id: {
        type: "STRING",
        primaryKey: true,
        autoIncrement: true,
    },
    projectId: {
        type: "STRING",
        allowNull: false,
    },
    userId: {
        type: "STRING",
        allowNull: false,
    },
    role: {
        type: "STRING",
        values: Object.values(ProjectMemberRole),
        defaultValue: ProjectMemberRole.VIEWER,
        allowNull: false,
    },
    createdAt: {
        type: "DATE",
        allowNull: false,
        isCreatedAt: true,
        defaultValue: new Date(),
    },
});

// Association with Project
ProjectMemberModel.belongsTo(ProjectModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "project",
});

// Add association to Project model
ProjectModel.hasMany(ProjectMemberModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "members",
});

export default ProjectMemberModel; 