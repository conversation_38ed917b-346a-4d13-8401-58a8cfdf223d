import { db } from "../db";
import ResourceModel from "../resource/Resource.model";

export type ProjectFolderModel = typeof ProjectFolderModel;
export type ProjectFolder = ProjectFolderModel["$M"];
export type ProjectFolderCol = ProjectFolderModel["$K"];
export type ProjectFolderCreate = ProjectFolderModel["$C"];
export type ProjectFolderUpdate = ProjectFolderModel["$U"];
export type ProjectFolderWhere = ProjectFolderModel["$W"];

const ProjectFolderModel = db.xDefine("ProjectFolder", {
  name: {
    type: "STRING",
  },
  description: {
    type: "TEXT",
    allowNull: true,
  },
  projectId: {
    type: "STRING",
  },
  parentId: {
    type: "STRING",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
    allowNull: true,
  },
  // For soft delete
  isDeleted: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  // Additional soft delete fields (keeping existing isDeleted for backward compatibility)
  deletedAt: {
    type: "DATE",
    allowNull: true,
  },
  deletedBy: {
    type: "STRING",
    allowNull: true,
  },
}, {
  indexes: [
    {
      // Index for soft delete queries
      fields: ["deletedAt"],
      name: "idx_project_folder_deleted_at",
    },
    {
      // Composite index for project-based queries excluding deleted ones
      fields: ["projectId", "deletedAt"],
      name: "idx_project_folder_project_not_deleted",
    },
    {
      // Combined index for both soft delete approaches
      fields: ["isDeleted", "deletedAt"],
      name: "idx_project_folder_soft_delete_combined",
    },
  ],
});

ResourceModel.belongsTo(ProjectFolderModel, {
  foreignKey: "folderId",
  constraints: false,
  as: "folder",
});

export default ProjectFolderModel;
