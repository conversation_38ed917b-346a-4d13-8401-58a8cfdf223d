import { ts } from "@/types";
import { db } from "../db";
import { getAllModels } from "@/config/models";

export enum ChatUserRole {
  USER = "user",
  ASSISTANT = "assistant",
}

const ChatMessageModel = db.xDefine(
  "ChatMessage",
  {
    conversationId: {
      type: "STRING", // Corresponds to the ULID from Conversation
      allowNull: false,
    },
    role: {
      type: "STRING",
      allowNull: false,
      tsType: ts<ChatUserRole>(),
    },
    content: {
      type: "TEXT",
      allowNull: false,
    },
    model: {
      type: "STRING",
      allowNull: true,
      comment:
        "AI model used to generate this message (e.g., gemini-2.5-flash)",
      validate: {
        isValidModel(value) {
          if (value === null || value === undefined) return;
          const allModelIds = getAllModels().map((m) => m.id);
          if (!allModelIds.includes(value)) {
            throw new Error(`Model must be one of: ${allModelIds.join(", ")}`);
          }
        },
      },
    },
    usage: {
      type: "JSON",
      allowNull: true,
      comment:
        "Token usage statistics: { inputTokens: number, outputTokens: number, totalTokens: number }",
      validate: {
        isValidUsage(value) {
          if (value === null || value === undefined) return;
          if (typeof value !== "object") {
            throw new Error("Usage must be an object");
          }
          const validKeys = [
            "inputTokens",
            "outputTokens",
            "totalTokens",
            "inputCharacters",
            "outputCharacters",
          ];
          const hasValidKeys = Object.keys(value).some((key) =>
            validKeys.includes(key)
          );
          if (!hasValidKeys) {
            throw new Error(
              "Usage must contain at least one of: inputTokens, outputTokens, totalTokens, inputCharacters, outputCharacters"
            );
          }
        },
      },
    },
    cost: {
      type: "FLOAT", // Cost in USD with floating point precision
      allowNull: true,
      comment: "Cost in USD calculated from token usage and model pricing",
      validate: {
        min: 0,
      },
    },
    resources: {
      type: "JSON",
      allowNull: true,
      comment:
        "Array of resource IDs that were used as context for this message",
      validate: {
        isValidResourceIds(value) {
          if (value === null || value === undefined) return;
          if (!Array.isArray(value)) {
            throw new Error("Resources must be an array");
          }
          if (
            value.length > 0 &&
            !value.every((id) => typeof id === "string")
          ) {
            throw new Error("All resource IDs must be strings");
          }
        },
      },
    },
    hasImages: {
      type: "BOOLEAN",
      allowNull: false,
      defaultValue: false,
      comment: "Quick lookup flag to check if this message contains images",
    },
    imageCount: {
      type: "INTEGER",
      allowNull: false,
      defaultValue: 0,
      comment: "Number of images attached to this message",
      validate: {
        min: 0,
        max: 16, // Gemini API limit
      },
    },
  },
  {
    paranoid: true,
  }
);

export type ChatMessage = typeof ChatMessageModel.$M;
export default ChatMessageModel;
