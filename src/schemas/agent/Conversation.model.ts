import { db } from "../db";
import ProjectModel from "../project/Project.model";
import ChatMessageModel from "./ChatMessage.model";
import ConversationImageModel from "./ConversationImage.model";

const ConversationModel = db.xDefine(
  "Conversation",
  {
    projectId: {
      type: "STRING",
      allowNull: true,
    },
    createdById: {
      type: "STRING",
      allowNull: true,
    },
  },
  {
    paranoid: true, // Enable soft deletes
  }
);

// Associations
ConversationModel.hasMany(ChatMessageModel, {
  foreignKey: "conversationId",
  constraints: false,
  as: "messages",
});

ChatMessageModel.belongsTo(ConversationModel, {
  foreignKey: "conversationId",
  constraints: false,
  as: "conversation",
});

// ConversationImage associations
ConversationModel.hasMany(ConversationImageModel, {
  foreignKey: "conversationId",
  constraints: false,
  as: "images",
});

ConversationImageModel.belongsTo(ConversationModel, {
  foreignKey: "conversationId",
  constraints: false,
  as: "conversation",
});

ChatMessageModel.hasMany(ConversationImageModel, {
  foreignKey: "messageId",
  constraints: false,
  as: "images",
});

ConversationImageModel.belongsTo(ChatMessageModel, {
  foreignKey: "messageId",
  constraints: false,
  as: "message",
});

ConversationModel.belongsTo(ProjectModel, {
  foreignKey: "projectId",
  constraints: false,
  as: "project",
});

export type Conversation = typeof ConversationModel.$M;
export default ConversationModel;
