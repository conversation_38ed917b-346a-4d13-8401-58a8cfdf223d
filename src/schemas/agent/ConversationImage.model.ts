import { ts } from "@/types";
import { db } from "../db";

const ConversationImageModel = db.xDefine(
  "ConversationImage",
  {
    conversationId: {
      type: "STRING",
      allowNull: false,
      comment: "ID of the conversation this image belongs to",
    },
    messageId: {
      type: "STRING",
      allowNull: false,
      comment: "ID of the specific message this image was sent with",
    },
    createdById: {
      type: "STRING",
      allowNull: true, // Can be null if created by system or anonymous
      comment: "ID of the user who uploaded this image",
    },
    originalFileName: {
      type: "STRING",
      allowNull: false,
      comment: "Original filename of the uploaded image",
    },
    mimeType: {
      type: "STRING",
      allowNull: false,
      comment: "MIME type of the image (e.g., image/jpeg, image/png)",
      validate: {
        isValidMimeType(value: string) {
          const supportedTypes = [
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/webp",
            "image/bmp",
            "image/tiff",
          ];
          if (!supportedTypes.includes(value)) {
            throw new Error(`Unsupported MIME type: ${value}`);
          }
        },
      },
    },
    fileSize: {
      type: "INTEGER",
      allowNull: false,
      comment: "File size in bytes",
      validate: {
        min: 1,
        max: 20 * 1024 * 1024, // 20MB limit
      },
    },
    gcsPath: {
      type: "STRING",
      allowNull: false,
      comment: "Full GCS path to the stored image",
    },
    thumbnailPath: {
      type: "STRING",
      allowNull: true,
      comment: "GCS path to the thumbnail image (WebP format)",
    },
    width: {
      type: "INTEGER",
      allowNull: true,
      comment: "Image width in pixels",
      validate: {
        min: 1,
      },
    },
    height: {
      type: "INTEGER",
      allowNull: true,
      comment: "Image height in pixels",
      validate: {
        min: 1,
      },
    },
    inlineDataHash: {
      type: "STRING",
      allowNull: true,
      comment: "Hash of the inline data for deduplication",
    },
  },
  {
    paranoid: true, // Enable soft deletes
    indexes: [
      {
        fields: ["conversationId"],
        name: "conversation_images_conversation_id_idx",
      },
      {
        fields: ["messageId"],
        name: "conversation_images_message_id_idx",
      },
      {
        fields: ["createdById"],
        name: "conversation_images_created_by_idx",
      },
      {
        fields: ["conversationId", "messageId"],
        name: "conversation_images_conversation_message_idx",
      },
    ],
  }
);

export type ConversationImage = typeof ConversationImageModel.$M;
export default ConversationImageModel;
