import type { CalendarPlatformEnum, MeetingPlatformEnum } from "recall-ai";
import { db } from "../db";
import { ts } from "@/types";
import { Models } from "@/services/db/Model";
import { BotChangeStatusCode } from "@/types/recall";

export type SessionModel = typeof SessionModel;
export type Session = SessionModel["$M"];
export type SessionCol = SessionModel["$K"];
export type SessionCreate = SessionModel["$C"];
export type SessionUpdate = SessionModel["$U"];
export type SessionWhere = SessionModel["$W"];

export type SessionPlatForm = "Default" | CalendarPlatformEnum;
export type SessionMeetingPlatForm = "Default" | MeetingPlatformEnum;

export enum SessionStatus {
  NotStarted = "NotStarted",
  InProgress = "InProgress",
  Processing = "Processing",
  Failed = "Failed",
  Completed = "Completed",
}

const SessionModel = db.xDefine("Session", {
  orgId: {
    type: "STRING",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
  },
  meetingUrl: {
    type: "STRING",
  },
  videoUrl: {
    type: "TEXT",
    allowNull: true,
  },
  title: {
    type: "STRING",
  },
  description: {
    type: "STRING",
    defaultValue: "",
  },
  settings: {
    type: "JSON",
    allowNull: true,
  },
  layout: {
    type: "JSON",
    allowNull: true,
  },
  startTime: {
    type: "DATE",
  },
  endTime: {
    type: "DATE",
    allowNull: true,
  },
  // this is the actual end time of the session
  // it could be different from the endTime if the session is extended
  actualEndTime: {
    type: "DATE",
    allowNull: true,
  },
  insightEngineId: {
    type: "STRING",
    defaultValue: "",
  },
  recallBotId: {
    type: "STRING",
    allowNull: true,
  },
  platForm: {
    type: "STRING",
    tsType: ts<SessionPlatForm>(),
    defaultValue: <SessionPlatForm>"Default",
  },
  eventMeetingId: {
    type: "STRING",
    allowNull: true,
  },
  eventMeetingPlatform: {
    type: "STRING",
    tsType: ts<SessionMeetingPlatForm>(),
    defaultValue: <SessionMeetingPlatForm>"Default",
  },
  isBlocked: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  status: {
    type: "STRING",
    tsType: ts<SessionStatus>(),
    defaultValue: SessionStatus.NotStarted,
  },
  recallBotStatus: {
    type: "STRING",
    tsType: ts<BotChangeStatusCode>(),
    allowNull: true,
  },
  shouldSendSummaryToEmail: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  projectId: {
    type: "STRING",
    allowNull: true,
  },
});

export default SessionModel;
