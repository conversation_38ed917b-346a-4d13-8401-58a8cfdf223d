import { BotChangeStatusCode } from "@/types/recall";
import models from "../models";
import { Session } from "./Session.model";
import { broadcastSessionStatusUpdate } from "@/utils/session-sse";
import { log } from "@/services/logger";

export const updateSessionByBotId = async (
  botId: string,
  data: Partial<Session>
) => {
  try {
    // First, get the current session to have all data for broadcasting
    const currentSession = await models.Session.xFind1By("recallBotId", botId);
    if (!currentSession) {
      log.warn(`Session not found for botId: ${botId}`);
      return models.Session.xUpdateBy("recallBotId", botId, data);
    }

    // Update the session
    const result = await models.Session.xUpdateBy("recallBotId", botId, data);

    // Broadcast SSE event if session has projectId and status/recallBotStatus changed
    if (currentSession.projectId && (data.status || data.recallBotStatus)) {
      const updatedSession = { ...currentSession, ...data };

      await broadcastSessionStatusUpdate({
        sessionId: currentSession.id,
        title: currentSession.title,
        status: updatedSession.status,
        projectId: currentSession.projectId,
        userId: currentSession.createdById,
        metadata: {
          recallBotStatus: updatedSession.recallBotStatus,
          botId: botId,
          updateSource: "recall_callback",
          ...(data.actualEndTime && {
            actualEndTime: data.actualEndTime.toISOString(),
          }),
          ...(data.videoUrl && { videoUrl: data.videoUrl }),
        },
      });

      log.info(
        "SSE broadcast sent for session update via updateSessionByBotId",
        {
          sessionId: currentSession.id,
          botId,
          status: updatedSession.status,
          recallBotStatus: updatedSession.recallBotStatus,
          projectId: currentSession.projectId,
        }
      );
    }

    return result;
  } catch (error) {
    log.error("Failed to update session and broadcast SSE", {
      error,
      botId,
      updateData: data,
    });
    // Still try to update the session even if SSE broadcast fails
    return models.Session.xUpdateBy("recallBotId", botId, data);
  }
};

export const isBotNotJoiningCallTimeout = async (botId: string) => {
  const session = await models.Session.xFind1({
    recallBotId: botId,
  });
  return (
    session?.recallBotStatus === BotChangeStatusCode.TimeoutExceededWaitingRoom
  );
};
