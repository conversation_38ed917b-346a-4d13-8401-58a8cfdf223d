import { ts } from "@/types";
import { db } from "../db";
import SessionModel from "../session/Session.model";
import { Op } from "sequelize";

export type ResourceModel = typeof ResourceModel;
export type Resource = ResourceModel["$M"];
export type ResourceCol = ResourceModel["$K"];
export type ResourceCreate = ResourceModel["$C"];
export type ResourceUpdate = ResourceModel["$U"];
export type ResourceWhere = ResourceModel["$W"];

export enum ResourceType {
  // user
  avatar = "avatar",
  avatarDeleted = "avatarDeleted",
  // org
  systemLogoPending = "systemLogoPending",
  originalResource = "originalResource",
  originalResourcePending = "originalResourcePending",
  systemLogo = "systemLogo",
  systemLogoDeleted = "systemLogoDeleted",
  coverPhoto = "coverPhoto",
  coverPhotoDeleted = "coverPhotoDeleted",
  // ams
  ams = "ams",
  amsDeleted = "amsDeleted",
  // session
  chat = "chat",
  image = "image",
  logo = "logo",
  overlay = "overlay",
  video = "video",
  audio = "audio",
  record = "record",
  defaultImage = "default-image",
  defaultLogo = "default-logo",
  defaultVideo = "default-video",
  defaultAudio = "default-audio",
  defaultRecord = "default-record",
  thumbnail = "thumbnail",
  note = "note",
  // insight engine
  insightEngine = "insightEngine",
  destinationThumbnail = "destination-thumbnail",
  resource = "resource",
}

export enum RagSyncStatus {
  NOT_SYNCED = "not_synced",
  SYNCING = "syncing",
  SYNCED = "synced",
  FAILED = "failed",
  SKIPPED = "skipped",
}

const ResourceModel = db.xDefine(
  "Resource",
  {
    orgId: {
      type: "STRING",
      // can be null in seeding
      // or to lazy clean up after association deleted
      allowNull: true,
    },
    name: {
      type: "STRING",
    },
    url: {
      type: "TEXT",
    },
    transcodedUrl: {
      type: "TEXT",
      allowNull: true,
    },
    transcodedFileSize: {
      type: "FLOAT",
      allowNull: true,
    },
    isTranscoding: {
      type: "BOOLEAN",
      defaultValue: false,
    },
    fileSize: {
      type: "FLOAT",
    },
    fileLastModified: {
      type: "DATE",
      allowNull: true,
    },
    /**
     * > in case of recording
     * the resource will be used only in that session
     * we supply the sessionId directly here so it will be easier
     * to compute the storage usage on each event of recording progress
     * at the end of recording a relation row will be added into the junction
     * table ResourceInSession so it could be displayed on UI
     * > in case of uploading
     * this field will be left empty
     * the relation will be added immediately into the junction table
     */
    sessionId: {
      type: "STRING",
      allowNull: true,
    },
    // ffprobe output for video files
    duration: {
      type: "FLOAT",
      defaultValue: 0,
    },
    ffprobe: {
      type: "JSON",
      allowNull: true,
    },
    thumbnailUrl: {
      type: "STRING",
      allowNull: true,
    },
    // topics extracted from the resource by RevAI
    topics: {
      type: "JSON",
      allowNull: true,
    },
    createdById: {
      type: "STRING",
      // can be null in seeding
      // or to lazy clean up after association deleted
      allowNull: true,
    },
    // Indicate the project that the resource belongs to
    projectId: {
      type: "STRING",
      allowNull: true,
    },
    // Indicate the folder that the resource belongs to within a project
    folderId: {
      type: "STRING",
      allowNull: true,
    },
    /**
     * FIXME: This field just to support backward compatibility with Beam DB;
     * Will optimize the schema in the future
     */
    originalResourceState: {
      type: "JSON",
      allowNull: true,
    },
    thumbnailResourceId: {
      type: "STRING",
      allowNull: true,
    },
    originalResourceId: {
      type: "STRING",
      allowNull: true,
    },
    type: {
      type: "STRING",
      tsType: ts<ResourceType>(),
      defaultValue: ResourceType.resource,
    },
    ragSyncStatus: {
      type: "STRING",
      tsType: ts<RagSyncStatus>(),
      defaultValue: RagSyncStatus.NOT_SYNCED,
      allowNull: false,
    },
    isSpriteSheets: {
      type: "BOOLEAN",
      defaultValue: false,
      allowNull: false,
    },
    vttSrc: {
      type: "TEXT",
      allowNull: true,
    },
    transcriptionSrc: {
      type: "TEXT",
      allowNull: true,
    },
    // Soft delete fields
    deletedAt: {
      type: "DATE",
      allowNull: true,
    },
    deletedBy: {
      type: "STRING",
      allowNull: true,
    },
  },
  {
    // Database indexes for optimized querying
    indexes: [
      {
        // Composite index for project resources listing (most common query)
        fields: ["projectId", "folderId", "createdAt"],
        name: "idx_resource_project_folder_created",
      },
      {
        // Index for project-based search queries
        fields: ["projectId", "name"],
        name: "idx_resource_project_name",
      },
      {
        // Index for sorting by file size
        fields: ["projectId", "fileSize"],
        name: "idx_resource_project_filesize",
      },
      {
        // Index for sorting by last modified
        fields: ["projectId", "fileLastModified"],
        name: "idx_resource_project_lastmod",
      },
      {
        // Index for user's resources
        fields: ["createdById"],
        name: "idx_resource_creator",
      },
      {
        // Index for spritesheet filtering
        fields: ["isSpriteSheets"],
        name: "idx_resource_spriteSheets",
      },
      {
        // Index for soft delete queries
        fields: ["deletedAt"],
        name: "idx_resource_deleted_at",
      },
      {
        // Composite index for project resources excluding deleted ones
        fields: ["projectId", "deletedAt"],
        name: "idx_resource_project_not_deleted",
      },
    ],
  }
);

// Associations
ResourceModel.hasOne(SessionModel, {
  sourceKey: "sessionId",
  foreignKey: "id",
  constraints: false,
  as: "session",
});

export default ResourceModel;
