import { generateSignedUrlForRead } from "@/services/storage";
import { ffprobe } from "@/services/upload/ffprobe";
import { isVideoOrAudio, isDocumentOrImage, isVideo } from "@/utils";
import { submitJob as submitTranscriptionJob } from "@/services/transcription";
import models from "../models";
import { upsertUserStatistics } from "../statistics/utils";
import { IEUploadAction } from "../insightEngine/InsightEngine.model";
import { TranscriptStatus } from "./ResourceInInsightEngine.model";
import { log } from "@/services/logger";
import {
  Resource,
  ResourceCreate,
  ResourceModel,
  ResourceType,
} from "./Resource.model";
import { getInsightEngineByResourceId } from "../insightEngine/utils";
import { ResourceData } from "@/types/resources";
import posthog, { EVENTS } from "@/services/posthog";
import { getType } from "mime";
import path from "path";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/services/sse/pubsub.service";
import { findOrCreateDefaultProject } from "@/utils";

interface FFProbeResult {
  duration: number;
  thumbnailUrl: string | null;
  [key: string]: any;
}

export const createNewResource = async ({
  gcsFilePath,
  fileName,
  fileSize = 0,
  fileLastModified = new Date(),
  userId,
  title,
  uploadAction,
  sessionId,
  projectId,
  folderId,
  transcodedUrl,
  isTranscoding,
  transcribe = true,
}: {
  gcsFilePath: string;
  fileName: string;
  fileSize?: number;
  fileLastModified?: Date;
  userId: string;
  title: string;
  uploadAction: IEUploadAction;
  sessionId?: string;
  projectId?: string;
  folderId?: string;
  transcodedUrl?: string;
  isTranscoding?: boolean;
  transcribe?: boolean;
}) => {
  // Ensure all resources have a project by assigning to default project if none specified
  let finalProjectId = projectId;
  if (!finalProjectId) {
    try {
      const defaultProjectResponse = await findOrCreateDefaultProject(userId);
      if (defaultProjectResponse.success && defaultProjectResponse.data) {
        finalProjectId = defaultProjectResponse.data.id;
        log.debug("[createNewResource] Assigned resource to default project", {
          userId,
          defaultProjectId: finalProjectId,
          fileName,
        });
      } else {
        log.error("[createNewResource] Failed to get or create default project", {
          userId,
          fileName,
          response: defaultProjectResponse,
        });
      }
    } catch (error) {
      log.error("[createNewResource] Error finding or creating default project", {
        error,
        userId,
        fileName,
      });
    }
  }

  const resourceSignedUrl = await generateSignedUrlForRead(gcsFilePath);
  const isDocOrImage = isDocumentOrImage(fileName);
  const isVideoAudio = isVideoOrAudio(fileName);

  // Skip transcription for documents and images
  const shouldTranscribe = transcribe && !isDocOrImage;

  log.debug("[createNewResource] Start processing resource", {
    resourceSignedUrl,
    fileName,
    isDocOrImage,
    isVideoAudio,
    shouldTranscribe,
    projectId: finalProjectId,
  });

  const resourcePayload: ResourceCreate = {
    fileSize,
    name: fileName,
    createdById: userId,
    fileLastModified,
    url: gcsFilePath,
    type: ResourceType.resource,
    sessionId,
    projectId: finalProjectId,
    folderId,
    transcodedUrl,
    isTranscoding,
  };

  if (isVideoAudio) {
    log.debug("[createNewResource] Processing video or audio file:", fileName);
    try {
      const ffprobeResult = (await Promise.race([
        ffprobe(
          resourceSignedUrl,
          fileName,
          isVideoAudio
        ) as Promise<FFProbeResult>,
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("ffprobe timeout")), 30000)
        ),
      ])) as FFProbeResult;

      resourcePayload.duration = ffprobeResult.duration;
      resourcePayload.ffprobe = ffprobeResult;
      resourcePayload.thumbnailUrl = ffprobeResult.thumbnailUrl;
    } catch (error) {
      log.error("[createNewResource] Error processing media file:", error);
      // Continue with default values if ffprobe fails
      resourcePayload.duration = 0;
      resourcePayload.ffprobe = {};
      resourcePayload.thumbnailUrl = null;
    }
  } else if (isDocOrImage) {
    log.debug(
      "[createNewResource] Processing document or image file:",
      fileName
    );
    // Set default values for non-media files
    resourcePayload.duration = 0;
    resourcePayload.ffprobe = {};
    resourcePayload.thumbnailUrl = null;
  }

  const resource = await models.Resource.xCreate(resourcePayload);
  log.debug("[createNewResource] Created resource", resource);
  if (isVideoAudio) {
    posthog.capture({
      distinctId: userId,
      event: EVENTS.AUDIO_VIDEO_MINS,
      properties: {
        fileName,
        fileSize,
        duration: resourcePayload.duration,
        mimeType: getType(path.extname(fileName)) || "application/octet-stream",
        resourceId: resource.id,
        projectId,
        folderId,
      },
    });
  }

  try {
    await upsertUserStatistics(userId, {
      totalResourcesDuration: resourcePayload.duration
        ? Number(resourcePayload.duration)
        : 0,
      totalResourcesCount: 1,
      totalFilesSize: fileSize,
    });
    log.debug("[createNewResource] Upserted user statistics");
  } catch (error) {
    log.error("[createNewResource] Error updating user statistics:", error);
    // Continue even if statistics update fails
  }

  const insightEngine = await models.InsightEngine.xCreate({
    name: title,
    createdById: userId,
    uploadAction,
  });
  log.debug("[createNewResource] Created insight engine", insightEngine);

  const resourceInIE = await models.ResourceInInsightEngine.xCreate({
    insightEngineId: insightEngine.id,
    resourceId: resource.id,
  });
  log.debug(
    "[createNewResource] Created resource in insight engine",
    resourceInIE
  );

  if (isVideoAudio && shouldTranscribe) {
    const job = await submitTranscriptionJob(resourceSignedUrl);
    log.debug("[createNewResource] Submitted transcription job", job);

    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      revAiJobId: job.id,
      status: TranscriptStatus.InProgress,
    });
  } else {
    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      status: TranscriptStatus.Completed,
    });

    if (isDocOrImage) {
      log.debug(
        "[createNewResource] Skipping transcription for document/image file:",
        fileName
      );
    }
  }
  log.debug(
    "[createNewResource] Updated resource in insight engine",
    resourceInIE
  );

  const formattedResource = await getResourceData(resource);

  // Broadcast resource.created event via SSE if projectId exists
  if (projectId) {
    try {
      const sseService = getSSEService();
      await sseService.broadcastToProject(projectId, {
        type: SSEEventType.RESOURCE_CREATED,
        projectId,
        userId,
        data: {
          id: resource.id,
          title: resource.name,
          name: resource.name,
          type: 'resource',
          fileName: resource.name,
          fileSize: resource.fileSize,
          createdBy: userId,
          createdAt: resource.createdAt,
          folderId: resource.folderId,
          sessionId: resource.sessionId,
          duration: resource.duration,
          url: resource.url,
          thumbnailUrl: resource.thumbnailUrl,
        },
      });
      
    } catch (error) {
      log.error('Failed to broadcast resource.created event via SSE', {
        error,
        resourceId: resource.id,
        projectId,
        userId,
      });
    }
  }

  return { resource: formattedResource, insightEngine, resourceInIE };
};

export const getResourceData = async (
  resource: Resource
): Promise<ResourceData> => {
  const { insightEngine, resourceInInsightEngine: riie } =
    await getInsightEngineByResourceId(resource.id);

  const transcription = riie?.id 
    ? await models.Transcription.xFindBy("resourceInInsightEngineId", riie.id)
    : null;

  // Prepare all signed URL generation promises for parallel execution
  const signedUrlPromises = [
    generateSignedUrlForRead(resource.url),
    generateSignedUrlForRead(resource.thumbnailUrl),
  ];

  // Add conditional signed URL generations
  if (resource.vttSrc) {
    signedUrlPromises.push(
      generateSignedUrlForRead(resource.vttSrc).catch(error => {
        console.warn(`Failed to generate signed URL for VTT: ${resource.vttSrc}`, error);
        return null;
      })
    );
  } else {
    signedUrlPromises.push(Promise.resolve(null));
  }

  if (resource.transcriptionSrc) {
    signedUrlPromises.push(
      generateSignedUrlForRead(resource.transcriptionSrc).catch(error => {
        console.warn(`Failed to generate signed URL for transcription: ${resource.transcriptionSrc}`, error);
        return null;
      })
    );
  } else {
    signedUrlPromises.push(Promise.resolve(null));
  }

  if (resource.isSpriteSheets && resource.transcodedUrl) {
    signedUrlPromises.push(generateSignedUrlForRead(resource.transcodedUrl));
  } else {
    signedUrlPromises.push(Promise.resolve(resource.transcodedUrl));
  }

  // Execute all signed URL generations in parallel
  const [publicUrl, thumbnailPublicUrl, vttPublicUrl, transcriptionSrc, transcodedUrl] = 
    await Promise.all(signedUrlPromises);

  return {
    ...resource,
    transcodedUrl: transcodedUrl,
    fileName: resource.name,
    name: insightEngine?.name ?? resource.name,
    url: publicUrl,
    thumbnailUrl: thumbnailPublicUrl,
    fileLastModified: resource.fileLastModified ?? resource.createdAt,
    transcription,
    transcriptionJobStatus: riie?.status,
    // Include spritesheet information
    isSpriteSheets: resource.isSpriteSheets || false,
    vttSrc: vttPublicUrl, // Use signed URL instead of raw path
    transcriptionSrc: transcriptionSrc,
  };
};
