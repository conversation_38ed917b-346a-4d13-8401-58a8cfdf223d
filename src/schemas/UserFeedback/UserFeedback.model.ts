import { db } from "../db";
import ResourceModel from "@/schemas/resource/Resource.model";

export type UserFeedbackModel = typeof UserFeedbackModel;
export type UserFeedback = UserFeedbackModel["$M"];
export type UserFeedbackCol = UserFeedbackModel["$K"];
export type UserFeedbackCreate = UserFeedbackModel["$C"];
export type UserFeedbackUpdate = UserFeedbackModel["$U"];
export type UserFeedbackWhere = UserFeedbackModel["$W"];

const UserFeedbackModel = db.xDefine("UserFeedback", {
    entityId: {
        type: "STRING",
        allowNull: false,
        primaryKey: true,
    },
    userId: {
        type: "STRING",
        allowNull: false,
        primaryKey: true,
    },
    feedback: {
        type: "TEXT",
        allowNull: true,
        defaultValue: null,
    },
    reason: {
        type: "TEXT",
        allowNull: true,
        defaultValue: null,
    },
    type: {
        type: "STRING",
        defaultValue: null,
    },
    rating: {
        type: "STRING",
        allowNull: true,
        defaultValue: null,
    },
    createdAt: {
        type: "DATE",
        defaultValue: db.fn("NOW"),
    },
    updatedAt: {
        type: "DATE",
        defaultValue: db.fn("NOW"),
    },
    metadata: {
        type: "JSON",
        allowNull: true,
        defaultValue: null,
    }
}, {
    // The options argument with indexes definition
    indexes: [
        {
            unique: true,
            fields: ['entityId', 'userId', 'type'],
            name: 'unique_resource_user_type_feedback'
        }
    ]
});




export default UserFeedbackModel;