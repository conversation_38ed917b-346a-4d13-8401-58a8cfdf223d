import { db } from "../db";
import ResourceModel from "../resource/Resource.model";

export type RetentionSettingModel = typeof RetentionSettingModel;
export type RetentionSetting = RetentionSettingModel["$M"];
export type RetentionSettingCreate = RetentionSettingModel["$C"];
export type RetentionSettingUpdate = RetentionSettingModel["$U"];
export type RetentionSettingWhere = RetentionSettingModel["$W"];

const RetentionSettingModel = db.xDefine("RetentionSetting", {
  resourceId: {
    type: "STRING",
    allowNull: false,
    unique: true,
  },
  expiryDate: {
    type: "DATE",
    allowNull: true,
  },
  extensionCount: {
    type: "INTEGER",
    defaultValue: 0,
  },
  lastExtensionDate: {
    type: "DATE",
    allowNull: true,
  },
  extensionHistory: {
    type: "JSON",
    defaultValue: [],
  },
  // Optional: Add more fields for future flexibility
  policyType: {
    type: "STRING",
    defaultValue: "default",
  },
  notes: {
    type: "TEXT",
    allowNull: true,
  }
});

// Associations
RetentionSettingModel.belongsTo(ResourceModel, {
  foreignKey: "resourceId",
  constraints: false,
  as: "resource",
});

ResourceModel.hasOne(RetentionSettingModel, {
  foreignKey: "resourceId",
  constraints: false,
  as: "retentionSetting",
});

export default RetentionSettingModel; 