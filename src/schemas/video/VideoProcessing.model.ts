import { ts } from "@/types";
import { db } from "../db";
import { Op } from "sequelize";

export type VideoProcessingModel = typeof VideoProcessingModel;
export type VideoProcessing = VideoProcessingModel["$M"];
export type VideoProcessingCreate = VideoProcessingModel["$C"];
export type VideoProcessingUpdate = VideoProcessingModel["$U"];
export type VideoProcessingWhere = VideoProcessingModel["$W"];

/**
 * Processing status for video jobs
 */
export enum VideoProcessingStatus {
  PENDING = "pending",
  PROCESSING = "processing", 
  COMPLETED = "completed",
  FAILED = "failed",
  RETRY = "retry",
}

/**
 * Processing types supported
 */
export enum VideoProcessingType {
  SPRITESHEET = "spritesheet",
  TRANSCODING = "transcoding",
  THUMBNAIL = "thumbnail",
}

/**
 * Video processing job tracking model for spritesheet generation and other video processing tasks
 */
const VideoProcessingModel = db.xDefine(
  "VideoProcessing",
  {
    id: {
      type: "STRING",
      defaultValue: "UUIDV4",
      primaryKey: true,
    },
    // Related resource
    resourceId: {
      type: "STRING",
      allowNull: false,
      references: {
        model: "Resource",
        key: "id",
      },
    },
    // Video processing details
    processingType: {
      type: "STRING",
      tsType: ts<VideoProcessingType>(),
      allowNull: false,
    },
    status: {
      type: "STRING", 
      tsType: ts<VideoProcessingStatus>(),
      defaultValue: VideoProcessingStatus.PENDING,
      allowNull: false,
    },
    // Job tracking
    jobId: {
      type: "STRING",
      allowNull: false,
    },
    inputUri: {
      type: "TEXT",
      allowNull: false,
    },
    outputUri: {
      type: "TEXT",
      allowNull: true,
    },
    // Retry management
    retryCount: {
      type: "INTEGER",
      defaultValue: 0,
      allowNull: false,
    },
    maxRetries: {
      type: "INTEGER",
      defaultValue: 3,
      allowNull: false,
    },
    retryReason: {
      type: "TEXT",
      allowNull: true,
    },
    // Job metadata and error details
    jobData: {
      type: "JSON",
      allowNull: true,
    },
    errorDetails: {
      type: "JSON",
      allowNull: true,
    },
    // Timestamps
    startedAt: {
      type: "DATE",
      allowNull: true,
    },
    completedAt: {
      type: "DATE", 
      allowNull: true,
    },
    // Soft delete fields
    deletedAt: {
      type: "DATE",
      allowNull: true,
    },
    deletedBy: {
      type: "STRING",
      allowNull: true,
    },
  },
  {
    tableName: "VideoProcessing",
    timestamps: true,
    indexes: [
      {
        // Index for finding jobs by resource
        fields: ["resourceId"],
        name: "idx_video_processing_resource",
      },
      {
        // Index for finding jobs by status and type
        fields: ["status", "processingType"],
        name: "idx_video_processing_status_type",
      },
      {
        // Index for finding jobs by external job ID
        fields: ["jobId"],
        name: "idx_video_processing_job_id",
        unique: true,
      },
      {
        // Index for retry management
        fields: ["status", "retryCount", "createdAt"],
        name: "idx_video_processing_retry",
      },
      {
        // Index for soft delete queries
        fields: ["deletedAt"],
        name: "idx_video_processing_deleted_at",
      },
      {
        // Composite index for resource-based queries excluding deleted ones
        fields: ["resourceId", "deletedAt"],
        name: "idx_video_processing_resource_not_deleted",
      },
    ],
  }
);

export default VideoProcessingModel;
