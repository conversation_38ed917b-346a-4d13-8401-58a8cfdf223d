import { ts } from "@/types";
import { db } from "../db";

export type ShortenedUrlModel = typeof ShortenedUrlModel;
export type ShortenedUrl = ShortenedUrlModel["$M"];
export type ShortenedUrlCol = ShortenedUrlModel["$K"];
export type ShortenedUrlCreate = ShortenedUrlModel["$C"];
export type ShortenedUrlUpdate = ShortenedUrlModel["$U"];
export type ShortenedUrlWhere = ShortenedUrlModel["$W"];

export enum UrlType {
  FEEDBACK = 'feedback',
  TRANSCRIPT = 'transcript',
  CONFIRMATION = 'confirmation',
  PROJECT_INVITATION = 'project_invitation',
  RETENTION_EXTEND = 'retention_extend',
  PROJECT_ACCESS_REQUEST = 'project_access_request',
}

const ShortenedUrlModel = db.xDefine(
  "ShortenedUrl",
  {
    shortCode: {
      type: "STRING",
      allowNull: false,
      unique: true,
    },
    originalUrl: {
      type: "TEXT",
      allowNull: false,
    },
    urlType: {
      type: "STRING",
      tsType: ts<UrlType>(),
      allowNull: false,
    },
    entityId: {
      type: "STRING",
      allowNull: true,
    },
    userId: {
      type: "STRING",
      allowNull: true,
    },
    expiresAt: {
      type: "DATE",
      allowNull: false,
    },
    clickCount: {
      type: "INTEGER",
      defaultValue: 0,
    },
    isActive: {
      type: "BOOLEAN",
      defaultValue: true,
    },
  },
  {
    indexes: [
      {
        unique: true,
        fields: ["shortCode"],
        name: "idx_shortened_url_code",
      },
      {
        fields: ["expiresAt", "isActive"],
        name: "idx_shortened_url_expiry",
      },
      {
        fields: ["userId", "urlType"],
        name: "idx_shortened_url_user_type",
      },
      {
        fields: ["entityId", "urlType"],
        name: "idx_shortened_url_entity_type",
      },
    ],
  }
);

export default ShortenedUrlModel;
