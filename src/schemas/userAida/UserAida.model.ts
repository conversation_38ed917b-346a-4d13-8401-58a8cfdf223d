import { db } from "../db";

export type UserAidaModel = typeof UserAidaModel;
export type UserAida = UserAidaModel["$M"];
export type UserAidaModelCol = UserAidaModel["$K"];
export type UserAidaModelCreate = UserAidaModel["$C"];
export type UserAidaModelUpdate = UserAidaModel["$U"];
export type UserAidaModelWhere = UserAidaModel["$W"];

const UserAidaModel = db.xDefine("UserAida", {
  userId: {
    type: "STRING",
  },
  isConfirmed: {
    type: "BOOLEAN",
    defaultValue: false,
  }
});

export default UserAidaModel;
