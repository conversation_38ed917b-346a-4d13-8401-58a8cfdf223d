import { ts } from "@/types";
import { db } from "@/schemas/db";

export type ResourceTranscodedJobModel = typeof ResourceTranscodedJobModel;
export type ResourceTranscodedJob = ResourceTranscodedJobModel["$M"];
export type ResourceTranscodedJobCol = ResourceTranscodedJobModel["$K"];
export type ResourceTranscodedJobCreate = ResourceTranscodedJobModel["$C"];
export type ResourceTranscodedJobUpdate = ResourceTranscodedJobModel["$U"];
export type ResourceTranscodedJobWhere = ResourceTranscodedJobModel["$W"];

export enum TranscodingMode {
  KEEP_BOTH = "keep-both",
  DELETE_ORIGINAL = "delete-original"
}

const ResourceTranscodedJobModel = db.xDefine("ResourceTranscodedJob", {
  jobId: {
    type: "STRING",
    allowNull: false,
  },
  outputFileName: {
    type: "STRING",
    allowNull: true,
  },
  input: {
    type: "TEXT",
    allowNull: false,
  },
  output: {
    type: "TEXT",
    allowNull: false,
  },
  callback_data: {
    type: "JSON",
    allowNull: true,
  },
  retryCount: {
    type: "INTEGER",
    allowNull: true,
    defaultValue: 0
  },
  retryReason: {
    type: "STRING",
    allowNull: true
  },
  hasAudio: {
    type: "BOOLEAN",
    allowNull: true,
    defaultValue: true
  },
  mode: {
    type: "STRING",
    allowNull: true,
    defaultValue: TranscodingMode.KEEP_BOTH,
    tsType: ts<TranscodingMode>()
  },
  resourceId: {
    type: "STRING",
    allowNull: true,
  }
});

export default ResourceTranscodedJobModel; 
