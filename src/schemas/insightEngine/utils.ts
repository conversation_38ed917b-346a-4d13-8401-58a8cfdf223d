import models from "../models";
import { ResourceInInsightEngine } from "../resource/ResourceInInsightEngine.model";
import { InsightEngine } from "./InsightEngine.model";

export const getInsightEngineByResourceId = async (
  resourceId: string,
): Promise<{
  resourceInInsightEngine: ResourceInInsightEngine | undefined;
  insightEngine: InsightEngine | undefined;
}> => {
  const riie = await models.ResourceInInsightEngine.xFind1By(
    "resourceId",
    resourceId,
  );

  if (!riie) {
    return {
      resourceInInsightEngine: undefined,
      insightEngine: undefined,
    };
  }

  const insightEngine = await models.InsightEngine.xFind1ById(
    riie.insightEngineId,
  );

  return {
    resourceInInsightEngine: riie,
    insightEngine,
  };
};
