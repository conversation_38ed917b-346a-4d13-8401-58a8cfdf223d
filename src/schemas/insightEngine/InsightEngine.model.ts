import { ts } from "@/types";
import { db } from "../db";
import { Models } from "@/services/db/Model";
import { Op } from "sequelize";

export type InsightEngineModel = typeof InsightEngineModel;
export type InsightEngine = InsightEngineModel["$M"];
export type InsightEngineCol = InsightEngineModel["$K"];
export type InsightEngineCreate = InsightEngineModel["$C"];
export type InsightEngineUpdate = InsightEngineModel["$U"];
export type InsightEngineWhere = InsightEngineModel["$W"];

export enum IEPlatForm {
  MOBILE = "mobile",
  WEB = "web",
}

export enum IEUploadAction {
  UPLOAD = "upload",
  RECORD = "record",
}

const InsightEngineModel = db.xDefine("InsightEngine", {
  orgId: {
    type: "STRING",
    allowNull: true,
  },
  name: {
    type: "STRING",
    defaultValue: "Unknown name",
  },
  createdById: {
    type: "STRING",
    // can be null in seeding
    // or to lazy clean up after association deleted
    allowNull: true,
  },
  // optional
  deletedAt: {
    type: "DATE",
    allowNull: true,
  },
  deletedBy: {
    type: "STRING",
    allowNull: true,
  },
  platform: {
    type: "STRING",
    tsType: ts<IEPlatForm>(),
    defaultValue: IEPlatForm.WEB,
  },
  uploadAction: {
    type: "STRING",
    tsType: ts<IEUploadAction>(),
    defaultValue: IEUploadAction.UPLOAD,
  },
}, {
  indexes: [
    {
      // Index for soft delete queries
      fields: ["deletedAt"],
      name: "idx_insight_engine_deleted_at",
    },
  ],
});

export default InsightEngineModel;
