import { db } from "../db";

export type UserStatisticsModel = typeof UserStatisticsModel;
export type UserStatistics = UserStatisticsModel["$M"];
export type UserStatisticsCol = UserStatisticsModel["$K"];
export type UserStatisticsCreate = UserStatisticsModel["$C"];
export type UserStatisticsUpdate = UserStatisticsModel["$U"];
export type UserStatisticsWhere = UserStatisticsModel["$W"];

const UserStatisticsModel = db.xDefine("UserStatistics", {
  orgId: {
    type: "STRING",
    allowNull: true,
  },
  userId: {
    type: "STRING",
  },
  totalResourcesDuration: {
    type: "FLOAT",
    defaultValue: 0,
  },
  totalResourcesCount: {
    type: "INTEGER",
    defaultValue: 0,
  },
  totalFilesSize: {
    type: "FLOAT",
    defaultValue: 0,
  },
  totalTranscriptionsWordCount: {
    type: "INTEGER",
    defaultValue: 0,
  },
});

export default UserStatisticsModel;
