import models from "../models";
import { UserStatistics } from "./UserStatistics.model";

export const upsertUserStatistics = async (
  userId: string,
  payload: Partial<UserStatistics>
) => {
  const userStatistics = await models.UserStatistics.xFind1By("userId", userId);

  if (userStatistics) {
    const newPayload: UserStatistics = {
      ...userStatistics,
      totalResourcesDuration:
        userStatistics.totalResourcesDuration +
        (payload.totalResourcesDuration ?? 0),
      totalResourcesCount:
        userStatistics.totalResourcesCount + (payload.totalResourcesCount ?? 0),
      totalTranscriptionsWordCount:
        userStatistics.totalTranscriptionsWordCount +
        (payload.totalTranscriptionsWordCount ?? 0),
      totalFilesSize:
        userStatistics.totalFilesSize + (payload.totalFilesSize ?? 0),
    };
    await models.UserStatistics.xUpdateById(userStatistics.id, newPayload);
  } else {
    await models.UserStatistics.xCreate({ userId, ...payload });
  }
};
