import { DataTypes } from "sequelize";
import { db } from "../db";
import { v4 as uuidv4 } from "uuid";

export type SSEConnectionModelType = typeof SSEConnectionModel;
export type SSEConnection = SSEConnectionModelType["$M"];
export type SSEConnectionCol = SSEConnectionModelType["$K"];
export type SSEConnectionCreate = SSEConnectionModelType["$C"];
export type SSEConnectionUpdate = SSEConnectionModelType["$U"];
export type SSEConnectionWhere = SSEConnectionModelType["$W"];

export const SSEConnectionModel = db.xDefine("SSEConnection", {
  id: {
    type: "STRING",
    defaultValue: uuidv4(),
    primaryKey: true,
  },
  userId: {
    type: "STRING",
    allowNull: false,
    field: 'user_id',
    validate: {
      notEmpty: true,
    },
  },
  projectId: {
    type: "STRING",
    allowNull: false,
    field: 'project_id',
    validate: {
      notEmpty: true,
    },
  },
  channel: {
    type: "STRING",
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255],
    },
  },
  connectionId: {
    type: "STRING",
    allowNull: false,
    unique: true,
    field: 'connection_id',
    validate: {
      notEmpty: true,
    },
  },
  instanceId: {
    type: "STRING",
    allowNull: false,
    field: 'instance_id',
    validate: {
      notEmpty: true,
    },
  },
  userAgent: {
    type: "TEXT",
    allowNull: true,
    field: 'user_agent',
  },
  ipAddress: {
    type: "STRING",
    allowNull: true,
    field: 'ip_address',
  },
  connectedAt: {
    type: "DATE",
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'connected_at',
  },
  lastHeartbeat: {
    type: "DATE",
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'last_heartbeat',
  },
  isActive: {
    type: "BOOLEAN",
    allowNull: false,
    defaultValue: true,
    field: 'is_active',
  },
}, {
  tableName: 'SSEConnections',
  timestamps: true,
  paranoid: false,
  indexes: [
    {
      fields: ['project_id'],
      name: 'idx_sse_connections_project_id',
    },
    {
      fields: ['user_id'],
      name: 'idx_sse_connections_user_id',
    },
    {
      fields: ['channel'],
      name: 'idx_sse_connections_channel',
    },
    {
      fields: ['is_active'],
      name: 'idx_sse_connections_active',
    },
    {
      fields: ['last_heartbeat'],
      name: 'idx_sse_connections_heartbeat',
    },
    {
      fields: ['instance_id'],
      name: 'idx_sse_connections_instance',
    },
  ],
});

export default SSEConnectionModel; 
