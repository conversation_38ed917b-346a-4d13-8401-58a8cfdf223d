import { db } from "../db";
import ResourceModel from "../resource/Resource.model";

export type SummaryModel = typeof SummaryModel;
export type Summary = SummaryModel["$M"];
export type SummaryCreate = SummaryModel["$C"];

const SummaryModel = db.xDefine("Summary", {
  resourceId: {
    type: "STRING",
  },
  title: {
    type: "STRING",
  },
  purpose: {
    type: "TEXT",
    allowNull: true,
  },
  keyTopics: {
    type: "JSON",
    allowNull: true,
  },
  percentageTalktime: {
    type: "JSON",
    allowNull: true,
  },
  keyThemes: {
    type: "JSON",
    allowNull: true,
  },
  keyActions: {
    type: "TEXT",
    allowNull: true,
  },
  keySentiments: {
    type: "JSON",
    allowNull: true,
  },
  quantitativeInsights: {
    type: "JSON",
    allowNull: true,
  },
  qualitativeInsights: {
    type: "JSON",
    allowNull: true,
  },
  questionsWithAnswers: {
    type: "JSON",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
    allowNull: true,
  },
  updatedById: {
    type: "STRING",
    allowNull: true,
  },
});

// Associations
SummaryModel.belongsTo(ResourceModel, {
  foreignKey: "resourceId",
  constraints: false,
  as: "resource",
});

export default SummaryModel; 