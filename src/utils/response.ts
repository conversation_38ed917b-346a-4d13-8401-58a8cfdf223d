/**
 * Utility functions for standardized API responses
 */

/**
 * Standard response format for API endpoints
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  statusCode: number;
  data?: T;
  error?: string | Record<string, any>;
  pagination?: {
    totalItems?: number;
    pageSize?: number;
    nextPageToken?: string;
  };
}

/**
 * Create a success response object
 */
export const createSuccessResponse = <T = any>(
  data: T,
  message = 'Operation successful',
  statusCode = 200,
  pagination?: ApiResponse['pagination']
): ApiResponse<T> => ({
  success: true,
  message,
  statusCode,
  data,
  pagination
});

/**
 * Create an error response object
 */
export const createErrorResponse = (
  message = 'Operation failed',
  statusCode = 500,
  error?: string | Record<string, any>
): ApiResponse => ({
  success: false,
  message,
  statusCode,
  error
});

/**
 * Common error messages
 */
export const ERROR_MESSAGES = {
  INVALID_INPUT: 'Invalid input data',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Forbidden access',
  SERVER_ERROR: 'Internal server error',
  VALIDATION_ERROR: 'Validation error'
};
