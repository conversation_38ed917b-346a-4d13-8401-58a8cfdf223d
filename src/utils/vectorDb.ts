import { UnifiedDocumentMetadata } from "@/types/vectorDb";
import { getFileCategory, FileCategory } from "@/utils/file";

/**
 * Build unified metadata from resource data
 */
export function buildUnifiedMetadata(resourceData: {
  resourceId: string;
  fileName: string;
  userId: string;
  projectId?: string;
  speakers?: string[];
  audio_duration_seconds?: number;
  language_detected?: string;
}): UnifiedDocumentMetadata {
  const category = getFileCategory(resourceData.fileName);

  return {
    // Core identification
    user_id: resourceData.userId,
    project_id: resourceData.projectId || "",
    file_id: resourceData.resourceId,

    // File context
    filename: resourceData.fileName,
    category: category || undefined,

    // Audio/video metadata (only if applicable)
    ...(category === "audio" || category === "video"
      ? {
          audio_video: {
            speakers: resourceData.speakers,
            audio_duration: resourceData.audio_duration_seconds,
            language: resourceData.language_detected,
          },
        }
      : {}),
  };
}

/**
 * Determine appropriate Unstract workflow based on file category
 */
export function getUnstractWorkflow(category: FileCategory | null): string {
  switch (category) {
    case "text":
      return "pdf-extraction";
    case "image":
      return "image-ocr";
    case "audio":
    case "video":
      return "transcript-processing";
    default:
      return "general-processing";
  }
}
