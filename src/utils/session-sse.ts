import { log } from "@/services/logger";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/services/sse/pubsub.service";
import { SessionStatus } from "@/schemas/session/Session.model";

interface SessionBroadcastData {
  sessionId: string;
  title: string;
  status: SessionStatus;
  projectId?: string;
  userId: string;
  userName?: string;
  metadata?: {
    recallBotStatus?: string | number;
    [key: string]: any; // Additional session-specific data
  };
}

/**
 * Broadcast session status updates via SSE to project members
 */
export const broadcastSessionStatusUpdate = async (
  data: SessionBroadcastData,
  eventType: SSEEventType = SSEEventType.SESSION_STATUS_UPDATED
): Promise<void> => {
  try {
    const sseService = getSSEService();
    const timestamp = new Date().toISOString();

    // Only broadcast to project if session has a project
    if (data.projectId) {
      await sseService.broadcastToProject(data.projectId, {
        type: eventType,
        projectId: data.projectId,
        userId: data.userId,
        data: {
          sessionId: data.sessionId,
          title: data.title,
          status: data.status,
          createdBy: data.userName || "Unknown",
          updatedBy: data.userName || "Unknown",
          updatedAt: timestamp,
          ...data.metadata,
        },
      });

      log.info("SSE broadcast sent for session event", {
        eventType,
        sessionId: data.sessionId,
        status: data.status,
        userId: data.userId,
        projectId: data.projectId,
      });
    } else {
      log.debug("Session has no project, skipping SSE broadcast", {
        sessionId: data.sessionId,
        userId: data.userId,
      });
    }
  } catch (error) {
    log.error("Failed to broadcast session event via SSE", {
      error,
      eventType,
      sessionId: data.sessionId,
      userId: data.userId,
      projectId: data.projectId,
    });
    // Don't throw - SSE broadcasting should not break the main flow
  }
};

/**
 * Broadcast session deletion event
 */
export const broadcastSessionDeletion = async (data: {
  sessionId: string;
  title: string;
  projectId?: string;
  userId: string;
  userName?: string;
}): Promise<void> => {
  await broadcastSessionStatusUpdate(
    { ...data, status: SessionStatus.Failed }, // Use Failed as placeholder for deleted
    SSEEventType.SESSION_DELETED
  );
};
