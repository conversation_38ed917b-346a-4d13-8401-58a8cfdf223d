import { getFileSize as getGCSFileSize } from "@/services/storage";
import { log } from "@/services/logger";

/**
 * Get the size of a file from Google Cloud Storage
 * @param filePath - The path of the file in GCS
 * @returns The size of the file in bytes, or undefined if the file doesn't exist or there's an error
 */
export const getFileSize = async (filePath: string): Promise<number | undefined> => {
  try {
    return await getGCSFileSize(filePath);
  } catch (error) {
    log.error(`Failed to get file size for: ${filePath}`, error);
    return undefined;
  }
};

/**
 * Format file size to human-readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size (e.g., "1.5 MB", "800 KB")
 */
export const formatFileSize = (bytes?: number): string => {
  if (bytes === undefined || bytes === null) return "Unknown size";
  
  if (bytes === 0) return "0 Bytes";
  
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${parseFloat((bytes / Math.pow(1024, i)).toFixed(2))} ${sizes[i]}`;
}; 
