import axios from "axios";
import { createWriteStream, statSync } from "fs-extra";
import { getType } from "mime";
import path from "path";
import type { Stream } from "stream";
import { finished } from "stream/promises";
import { ulid } from "ulidx";

import { apiDevUploadOutput } from "../config";

const downloadFileFromUrl = async (url: string, fileName?: string) => {
  const name = fileName || `${ulid()}.mp4`;

  const filePath = path.join(apiDevUploadOutput, name);
  const writer = createWriteStream(filePath);
  const res = await axios.get<Stream>(url, {
    responseType: "stream",
  });
  res.data.pipe(writer);
  await finished(writer);
  const mimetype = getType(filePath) as string;
  const fileStat = statSync(filePath);

  return {
    path: filePath,
    name,
    mimetype,
    size: fileStat.size,
  };
};

export default downloadFileFromUrl;
