import { isArray } from "lodash";
import { extname } from "path";
import { getType } from "mime";
import { arrToMap } from "./arrays";

/**
 * File category classification based on existing file utilities
 */
export type FileCategory = "audio" | "video" | "text" | "image";

export const imageExts = arrToMap([
  ".png",
  ".jpg",
  ".jpeg",
  ".gif",
  ".svg",
  ".webp",
  ".bmp",
  ".tiff",
  ".tif",
]);
export const videoExts = arrToMap([
  ".mp4",
  ".mov",
  ".avi",
  ".flv",
  ".wmv",
  ".m4v",
  ".mpeg",
  ".mpg",
  ".webm",
  ".mkv",
  ".vob",
  ".ogg",
]);
export const audioExts = arrToMap([
  ".wav",
  ".aif",
  ".mp3",
  ".aac",
  ".m4a",
  ".ogg",
  ".flac",
  ".wma",
  ".m4r",
  ".amr",
]);

export const textExts = arrToMap([
  ".txt",
  ".md",
  ".pdf",
  ".docx",
  ".pptx",
  ".doc",
  ".ppt",
]);

export const uploadExts = {
  ...imageExts,
  ...videoExts,
  ...audioExts,
  ...textExts,
};

export const isVideoOrAudio = (pathOrUrl: string) =>
  isFileType(pathOrUrl, ["video", "audio"]);

export const isTextFile = (pathOrUrl: string) =>
  isFileType(pathOrUrl, ["text"]);

export const isImage = (pathOrUrl: string) => isFileType(pathOrUrl, ["image"]);

export const isDocumentOrImage = (pathOrUrl: string) =>
  isFileType(pathOrUrl, ["text", "image"]);

export const isFileType = (
  pathOrUrl: string,
  type: FileCategory | FileCategory[]
) => {
  const ext = extname(pathOrUrl).toLowerCase();
  const h = {
    video: videoExts,
    audio: audioExts,
    image: imageExts,
    text: textExts,
  } as { [k: string]: { [k2: string]: boolean } };
  return (
    (!isArray(type) ? h[type]?.[ext] : type.some((t) => h[t]?.[ext])) ?? false
  );
};

export const isVideo = (pathOrUrl: string) => isFileType(pathOrUrl, "video");

/**
 * Get file category from file path or filename using existing file utilities
 */
export function getFileCategory(pathOrFilename: string): FileCategory | null {
  if (isFileType(pathOrFilename, "audio")) return "audio";
  if (isFileType(pathOrFilename, "video")) return "video";
  if (isFileType(pathOrFilename, "text")) return "text";
  if (isFileType(pathOrFilename, "image")) return "image";
  return null;
}

/**
 * Enhanced file type classification for specific viewing modes
 */
export type EnhancedFileType =
  | "image" // Full-screen viewing with zoom capabilities
  | "pdf" // Multi-page rendering with navigation
  | "document" // Download interface with file type detection
  | "text" // Markdown-rendered content with scrolling
  | "markdown" // Markdown-rendered content with scrolling
  | "audio" // Built-in audio controls
  | "video" // Enhanced player with thumbnail capture
  | "unknown";

// Define specific extension maps for enhanced classification
export const pdfExts = arrToMap([".pdf"]);
export const documentExts = arrToMap([".docx", ".pptx", ".doc", ".ppt"]);
export const textFileExts = arrToMap([".txt"]);

export const markdownExts = arrToMap([
  ".md",
  ".markdown",
  ".mdown",
  ".mkd",
  ".mkdn",
]);

/**
 * Get enhanced file type with specific viewing/interaction modes
 */
export function getEnhancedFileType(pathOrFilename: string): EnhancedFileType {
  const ext = extname(pathOrFilename).toLowerCase();

  // Images → Full-screen viewing with zoom capabilities
  if (imageExts[ext]) {
    return "image";
  }

  // PDFs → Multi-page rendering with navigation
  if (pdfExts[ext]) {
    return "pdf";
  }

  // Documents → Download interface with file type detection
  if (documentExts[ext]) {
    return "document";
  }

  // Text/Markdown files → Markdown-rendered content with scrolling
  // Supports: .txt, .md, .markdown, .mdown, .mkd, .mkdn, .mdwn, .mdtxt, .mdtext, .text, .rmd
  if (textFileExts[ext]) {
    return "text";
  }

  if (markdownExts[ext]) {
    return "markdown";
  }

  // Audio files → Built-in audio controls
  if (audioExts[ext]) {
    return "audio";
  }

  // Videos → Enhanced player with thumbnail capture
  if (videoExts[ext]) {
    return "video";
  }

  return "unknown";
}

/**
 * Read chunks from a ReadableStream
 */
async function readStreamChunks(stream: ReadableStream): Promise<Uint8Array[]> {
  const chunks: Uint8Array[] = [];
  const reader = stream.getReader();

  try {
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      chunks.push(value);
    }
  } finally {
    reader.releaseLock();
  }

  return chunks;
}

/**
 * Convert file stream to Blob using existing utilities
 */
export async function convertToBlob(
  fileStream: ReadableStream | Blob,
  filename: string
): Promise<Blob> {
  if (fileStream instanceof Blob) {
    return fileStream;
  }

  if (fileStream instanceof ReadableStream) {
    const chunks = await readStreamChunks(fileStream);
    const contentType = getType(filename) || "application/octet-stream";
    return new Blob(chunks, { type: contentType });
  }

  // Fallback for other stream types
  return new Blob([fileStream as any], {
    type: getType(filename) || "application/octet-stream",
  });
}

/**
 * Check if file is a previewable video format that supports spritesheet generation
 * Currently supports .mp4 and .mov files only
 */
export function isPreviewableVideo(pathOrFilename: string): boolean {
  const ext = extname(pathOrFilename).toLowerCase();
  const previewableVideoExts = [".mp4", ".mov"];
  return previewableVideoExts.includes(ext);
}

/**
 * Validate MIME type for spritesheet-eligible videos
 */
export function isValidSpritesheetMimeType(mimeType: string): boolean {
  const validMimeTypes = ["video/mp4", "video/quicktime"];
  return validMimeTypes.includes(mimeType);
}

/**
 * Combined validation for spritesheet generation eligibility
 */
export function isSpritesheetEligible(
  filename: string,
  mimeType?: string
): boolean {
  const hasValidExtension = isPreviewableVideo(filename);
  const hasValidMimeType = mimeType
    ? isValidSpritesheetMimeType(mimeType)
    : true;
  return hasValidExtension && hasValidMimeType;
}
