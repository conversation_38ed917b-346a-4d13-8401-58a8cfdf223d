import { models } from "@/schemas";
import { Project } from "@/schemas/project/Project.model";
import { createLogger } from "@/services/logger";
import { ServiceResponse } from "@/types";
import { createSuccessResponse, createErrorResponse } from "@/utils/response";

const logger = createLogger("ProjectUtils");

// Cache for default projects to avoid repeated database calls
const defaultProjectCache = new Map<string, { project: Project; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Validates user ID input
 * @param userId - The user ID to validate
 * @throws Error if userId is invalid
 */
const validateUserId = (userId: string): void => {
  if (!userId || typeof userId !== 'string') {
    throw new Error('User ID must be a non-empty string');
  }
  
  if (userId.trim().length === 0) {
    throw new Error('User ID cannot be empty or whitespace');
  }
  
  // Firebase UID format validation (28 characters, alphanumeric)
  if (!/^[a-zA-Z0-9]{28}$/.test(userId)) {
    logger.warn(`User ID format may be invalid: ${userId}`, { userId });
  }
};

/**
 * Gets cached default project if available and not expired
 * @param userId - The user ID to get cached project for
 * @returns Cached project or null if not found/expired
 */
const getCachedDefaultProject = (userId: string): Project | null => {
  const cached = defaultProjectCache.get(userId);
  if (!cached) return null;
  
  const now = Date.now();
  if (now - cached.timestamp > CACHE_TTL) {
    defaultProjectCache.delete(userId);
    return null;
  }
  
  return cached.project;
};

/**
 * Caches a default project for a user
 * @param userId - The user ID to cache for
 * @param project - The project to cache
 */
const cacheDefaultProject = (userId: string, project: Project): void => {
  defaultProjectCache.set(userId, { project, timestamp: Date.now() });
};

/**
 * Clears cached default project for a user
 * @param userId - The user ID to clear cache for
 */
export const clearDefaultProjectCache = (userId: string): void => {
  defaultProjectCache.delete(userId);
  logger.debug(`Cleared default project cache for user ${userId}`);
};

/**
 * Finds or creates a default project for a user
 * @param userId - The user ID to find/create the default project for
 * @param projectName - Optional custom name for the default project (defaults to "Default")
 * @param projectDescription - Optional custom description (defaults to "Default project for user resources")
 * @returns Promise<ServiceResponse<Project>> - Standardized response with the found or created default project
 */
export const findOrCreateDefaultProject = async (
  userId: string,
  projectName: string = "Default",
  projectDescription: string = "Default project for user resources"
): Promise<ServiceResponse<Project>> => {
  const startTime = Date.now();
  
  try {
    // Validate input
    validateUserId(userId);
    
    // Check cache first
    const cachedProject = getCachedDefaultProject(userId);
    if (cachedProject) {
      logger.debug(`Found cached default project for user ${userId}`, {
        projectId: cachedProject.id,
        projectName: cachedProject.name,
        cacheHit: true,
      });
      
      return createSuccessResponse(
        cachedProject,
        "Default project found in cache",
        200
      );
    }

    // First try to find an existing default project
    const defaultProjectResponse = await getDefaultProject(userId);

    if (defaultProjectResponse.success && defaultProjectResponse.data) {
      const defaultProject = defaultProjectResponse.data;
      
      // Cache the found project
      cacheDefaultProject(userId, defaultProject);
      
      logger.debug(`Found existing default project for user ${userId}`, {
        projectId: defaultProject.id,
        projectName: defaultProject.name,
        cacheHit: false,
      });
      
      return createSuccessResponse(
        defaultProject,
        "Default project found",
        200
      );
    }

    // Create default project if it doesn't exist
    logger.info(`Creating default project for user ${userId}`, {
      projectName,
      projectDescription,
    });

    const newDefaultProject = await models.Project.xCreate({
      name: projectName,
      description: projectDescription,
      createdById: userId,
      isDefault: true,
    });

    // Cache the newly created project
    cacheDefaultProject(userId, newDefaultProject);

    logger.info(`Successfully created default project for user ${userId}`, {
      projectId: newDefaultProject.id,
      projectName: newDefaultProject.name,
      cacheHit: false,
    });

    const duration = Date.now() - startTime;
    logger.debug(`findOrCreateDefaultProject completed in ${duration}ms`, {
      userId,
      projectId: newDefaultProject.id,
      duration,
    });

    return createSuccessResponse(
      newDefaultProject,
      "Default project created successfully",
      201
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    logger.error(`Failed to find or create default project for user ${userId}`, {
      error: errorMessage,
      userId,
      duration,
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Return standardized error response
    if (error instanceof Error && error.message.includes('User ID must be')) {
      return createErrorResponse(
        errorMessage,
        400,
        { userId, field: 'userId' }
      );
    }

    return createErrorResponse(
      "Failed to find or create default project",
      500,
      { userId, originalError: errorMessage }
    );
  }
};

/**
 * Gets the default project for a user without creating one
 * @param userId - The user ID to find the default project for
 * @returns Promise<ServiceResponse<Project | null>> - Standardized response with the default project or null
 */
export const getDefaultProject = async (userId: string): Promise<ServiceResponse<Project | null>> => {
  const startTime = Date.now();
  
  try {
    // Validate input
    validateUserId(userId);
    
    // Check cache first
    const cachedProject = getCachedDefaultProject(userId);
    if (cachedProject) {
      logger.debug(`Found cached default project for user ${userId}`, {
        projectId: cachedProject.id,
        cacheHit: true,
      });
      
      return createSuccessResponse(
        cachedProject,
        "Default project found in cache",
        200
      );
    }

    const defaultProject = await models.Project.xMustFind1({
      isDefault: true,
      createdById: userId,
    });

    // Cache the found project
    cacheDefaultProject(userId, defaultProject);

    const duration = Date.now() - startTime;
    logger.debug(`getDefaultProject completed in ${duration}ms`, {
      userId,
      projectId: defaultProject.id,
      duration,
    });

    return createSuccessResponse(
      defaultProject,
      "Default project found",
      200
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // xMustFind1 throws if not found, so return null
    if (error instanceof Error && error.message.includes('not found')) {
      logger.debug(`No default project found for user ${userId}`, {
        userId,
        duration,
      });
      
      return createSuccessResponse(
        null,
        "No default project found",
        404
      );
    }

    // Handle other errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Error getting default project for user ${userId}`, {
      error: errorMessage,
      userId,
      duration,
      stack: error instanceof Error ? error.stack : undefined,
    });

    if (error instanceof Error && error.message.includes('User ID must be')) {
      return createErrorResponse(
        errorMessage,
        400,
        { userId, field: 'userId' }
      );
    }

    return createErrorResponse(
      "Failed to get default project",
      500,
      { userId, originalError: errorMessage }
    );
  }
};

/**
 * Gets the default project for a user without creating one (legacy function for backward compatibility)
 * @param userId - The user ID to find the default project for
 * @returns Promise<Project | null> - The default project or null if not found
 * @deprecated Use getDefaultProject() instead for standardized responses
 */
export const getDefaultProjectLegacy = async (userId: string): Promise<Project | null> => {
  try {
    const response = await getDefaultProject(userId);
    return response.success ? response.data : null;
  } catch (error) {
    logger.error(`Error in getDefaultProjectLegacy for user ${userId}`, { error });
    return null;
  }
};
