/**
 * Utility functions for video transcoding
 */

import { getTextFileFromStorage } from "@/services/storage";

interface CalcParamsToGoogleCloudTranscoderGenPreview {
  durationInSeconds: number;
  intervalSeconds: number;
  maxSprites: number;
}

export const calcParamsToGoogleCloudTranscoderGenPreview = (
  {
    durationInSeconds,
    intervalSeconds,
    maxSprites,
  }: CalcParamsToGoogleCloudTranscoderGenPreview
) => {
  const expectedTotalItems = Math.round(durationInSeconds / intervalSeconds);
  const shouldReduceSprites = expectedTotalItems > maxSprites;

  let totalSprites = shouldReduceSprites ? maxSprites : expectedTotalItems;
  const totalColumns = 10;
  let totalRows = 1;

  /**
   * 100 sprites |=> duration (1000s) / 100 sprites = 10s => intervalSeconds
   * => Interval need to recaculate when sprites exceed the maxSprites
   */
  intervalSeconds = Math.max(intervalSeconds, durationInSeconds / totalSprites);

  totalRows = totalSprites / totalColumns;

  if (Math.floor(totalRows) < totalRows) {
    totalRows = Math.floor(totalRows) + 1;
  }

  return {
    durationInSeconds,
    intervalSeconds,
    totalSprites,
    totalColumns,
    totalRows,
  };
};


interface CreateTranscodingPayload {
  inputUri: string;
  outputUri: string;
  fileName: string;
  hasAudio: boolean;
}

// Function to create transcoding job payload
export const createTranscodingPayload = (
  {
    inputUri,
    outputUri,
    fileName,
    hasAudio = true,
  }: CreateTranscodingPayload
) => {
  type VideoStream = {
    key: string;
    videoStream: {
      h264: {
        heightPixels: number;
        widthPixels: number;
        bitrateBps: number;
        frameRate: number;
      };
    };
  };

  type AudioStream = {
    key: string;
    audioStream: {
      codec: string;
      bitrateBps: number;
    };
  };

  type ElementaryStream = VideoStream | AudioStream;

  const elementaryStreams: ElementaryStream[] = [
    {
      key: "video-stream0",
      videoStream: {
        h264: {
          heightPixels: 720,
          widthPixels: 1280,
          bitrateBps: 2000000,
          frameRate: 30,
        },
      },
    },
  ];

  if (hasAudio) {
    elementaryStreams.push({
      key: "audio-stream0",
      audioStream: {
        codec: "aac",
        bitrateBps: 128000,
      },
    });
  }

  const streamKeys = hasAudio
    ? ["video-stream0", "audio-stream0"]
    : ["video-stream0"];

  return {
    inputs: [
      {
        key: "input0",
        uri: inputUri,
      },
    ],
    output: {
      uri: outputUri,
    },
    elementaryStreams,
    muxStreams: [
      {
        key: "sd",
        container: "mp4",
        elementaryStreams: streamKeys,
        fileName: fileName,
      },
    ],
  };
};

/**
 * Create spritesheet transcoding job payload
 * Note: Google Cloud Video Transcoder API does not support automatic VTT generation for spritesheets
 * VTT file for timeline navigation must be generated separately in our code
 */
interface CreateSpriteSheetPayload {
  inputUri: string;
  outputUri: string;
  videoId: string;
  durationInSeconds: number;
  hasAudio?: boolean;
}

export const createSpriteSheetPayload = (
  {
    inputUri,
    outputUri,
    videoId,
    durationInSeconds,
    hasAudio = true,
  }: CreateSpriteSheetPayload
) => {
  const config = calcParamsToGoogleCloudTranscoderGenPreview(
    {
      durationInSeconds,
      maxSprites: 200,
      intervalSeconds: 5,
    }
  )

  type VideoStream = {
    key: string;
    videoStream: {
      h264: {
        heightPixels: number;
        widthPixels: number;
        bitrateBps: number;
        frameRate: number;
      };
    };
  };

  type AudioStream = {
    key: string;
    audioStream: {
      codec: string;
      channelCount: number;
      bitrateBps: number;
      sampleRateHertz: number;
    };
  };

  type ElementaryStream = VideoStream | AudioStream;

  const elementaryStreams: ElementaryStream[] = [
    {
      key: "video-stream0",
      videoStream: {
        h264: {
          heightPixels: 720,
          widthPixels: 1280,
          bitrateBps: 2000000,
          frameRate: 30,
        },
      },
    },
  ];

  if (hasAudio) {
    elementaryStreams.push({
      key: "audio-stream0",
      audioStream: {
        codec: "aac",
        channelCount: 2,
        bitrateBps: 128000,
        sampleRateHertz: 48000
      }
    });
  }

  const streamKeys = hasAudio
    ? ["video-stream0", "audio-stream0"]
    : ["video-stream0"];
  
  return {
    inputs: [
      {
        key: "input0",
        uri: inputUri,
      },
    ],
    output: {
      uri: outputUri,
    },
    // Define elementary streams - required by Google Cloud Video Transcoder API
    elementaryStreams,
    // Define mux streams - required by Google Cloud Video Transcoder API
    muxStreams: [
      {
        key: "sd",
        container: "mp4",
        elementaryStreams: streamKeys,
        fileName: `encoded.mp4`, // Temporary output file for processing
      },
    ],
    // Configure sprite sheet generation with dynamic dimensions
    spriteSheets: [
      {
        filePrefix: "sprite",
        spriteWidthPixels: 128,
        spriteHeightPixels: 72,
        columnCount: config.totalColumns,
        rowCount: config.totalRows,
        totalCount: config.totalSprites,
        interval: {
          seconds: config.intervalSeconds,
        },
        quality: 75,
      },
    ],
    // Add metadata for callback correlation
    labels: {
      videoId,
      processingType: "spritesheet",
      duration: durationInSeconds.toString(),
      calculatedSprites: config.totalSprites.toString(),
    },
  };
};

export interface VTTTemplateConfig {
  videoDurationSeconds: number;
  intervalSeconds: number;
  spriteSheetWidth: number;
  itemWidth: number;
  itemHeight: number;
  maxSprites: number;
  videoId: string;
}

export const generateVTTTemplateByConfig = ({
  videoDurationSeconds,
  intervalSeconds,
  spriteSheetWidth: spriteWidth,
  itemWidth,
  itemHeight,
  maxSprites,
  videoId,
}: VTTTemplateConfig) => {

  if (videoDurationSeconds < intervalSeconds) {
    console.error("Video duration is less than interval seconds");
    return "WEBVTT\n\n";
  }

  let expectedTotalItems = Math.round(videoDurationSeconds / intervalSeconds);

  if (Math.floor(expectedTotalItems) < expectedTotalItems) {
    expectedTotalItems = Math.floor(expectedTotalItems) + 1;
  }

  const totalItems = Math.min(expectedTotalItems, maxSprites);

  let needToProcessRemainingItems = totalItems;
  const maxColumnCount = Math.ceil(spriteWidth / itemWidth);
  let columnCount = 1;
  let rowCount = 1;
  let i = 0;

  let vttContent = "WEBVTT\n\n";

  while (needToProcessRemainingItems >= 0) {
    const startTime = i * intervalSeconds;
    let endTime = Math.min(i * intervalSeconds + intervalSeconds, videoDurationSeconds)

    if (videoDurationSeconds - endTime <= intervalSeconds) {
      endTime = videoDurationSeconds;
    }

    needToProcessRemainingItems = totalItems - rowCount * columnCount;
    vttContent += `${formatVTTTime(startTime)} --> ${formatVTTTime(endTime)}\n`;

    /**
     * E.g: Row 1, Column 1, w = 128, h = 72, spriteSheetWidth = 1280, spriteHeight = 720
     * x = 0
     * y = 0
     * width = 128
     * height = 72
     * 
     * 
     * E.g: Row 2, Column 2, w = 128, h = 72, spriteSheetWidth = 1280, spriteHeight = 720
     * x = 128
     * y = 72
     * width = 128
     * height = 72
     * 
     * 
     */
    const x = itemWidth * (columnCount - 1); // top left corner of the sprite
    const y = itemHeight * (rowCount - 1); // bottom left corner of the sprite

    vttContent += `{sprite_url}#xywh=${x},${y},${itemWidth},${itemHeight}\n\n`;

    i++; // time gap indexes
    needToProcessRemainingItems--; // remaining items to process - 1
    columnCount++; // move to next column

    // if column count is greater than max column count, reset column count and increment row count
    if (columnCount > maxColumnCount) {
      columnCount = 0;
      rowCount++;
    }
  }

  return vttContent + "\n";
};

/**
 * Generate VTT content for spritesheet timeline navigation with signed URLs
 * This function generates VTT content where each sprite image URL is signed for direct access
 */
export const generateSpriteSheetVTTWithSignedUrls = async (
  vttSrc: string,
  imageURL: string
): Promise<string> => {
  const vttContent = await getTextFileFromStorage(vttSrc);
  const spriteUrlPattern = /\{sprite_url\}/g;
  return vttContent.replace(spriteUrlPattern, imageURL);
};

/**
 * Format seconds to VTT time format (HH:MM:SS.mmm)
 */
const formatVTTTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.toFixed(3).padStart(6, '0')}`;
};

/**
 * Get VTT source path pattern for a video resource
 */
export const getVttSrcPath = (videoId: string): string => {
  return `videos/${videoId}/sprite.vtt`;
};

export const getEncodedVideoPath = (videoId: string): string => {
  return `videos/${videoId}/encoded.mp4`;
};
