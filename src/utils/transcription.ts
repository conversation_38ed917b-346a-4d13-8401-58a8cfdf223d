import { Transcription } from "@/schemas/transcription/Transcription.model";
import { gcsUpload } from "@/services/storage";

/**
 * Format transcript content with timestamps and speakers for better citations
 */
export function formatTranscriptForCitations(
  transcriptions: Array<{
    content: string;
    nameFromRevAi: string;
    startTime: number;
    endTime: number;
  }>
): string {
  return transcriptions
    .map((t) => {
      const timestamp = formatTimestamp(t.startTime);
      return `[${timestamp}] ${t.nameFromRevAi}: ${t.content}`;
    })
    .join("\n");
}

/**
 * Format timestamp from milliseconds to HH:MM:SS
 */
export function formatTimestamp(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
}

/**
 * Converts transcription JSON data to WebVTT format
 * @param transcriptionData - Array of transcription segments
 * @param options - Configuration options for VTT generation
 * @returns WebVTT formatted string
 */
export function convertTranscriptionToVTT(
  transcriptionData: Transcription[] | string,
  options: {
    maxLineLength?: number;
    addSpeakerLabels?: boolean;
    maxGapSeconds?: number;
    maxSegmentDuration?: number;
    maxWordsPerSegment?: number;
  } = {}
): string {
  const {
    maxLineLength = 80,
    addSpeakerLabels = true,
    maxGapSeconds = 0.5,
    maxSegmentDuration = 3.0,
    maxWordsPerSegment = 15,
  } = options;

  // Handle case where transcriptionData might be a stringified JSON
  let parsedData: Transcription[];

  try {
    if (typeof transcriptionData === "string") {
      parsedData = JSON.parse(transcriptionData);
    } else if (Array.isArray(transcriptionData)) {
      parsedData = transcriptionData;
    } else {
      // Return empty VTT file for invalid data
      return "WEBVTT\n\n";
    }
  } catch (error) {
    // Return empty VTT file for parsing errors
    return "WEBVTT\n\n";
  }

  // Return empty VTT file if no valid data
  if (!Array.isArray(parsedData) || parsedData.length === 0) {
    return "WEBVTT\n\n";
  }

  // Validate that each item has the required properties
  const requiredProps = ["content", "startTime", "endTime"];
  const firstItem = parsedData[0];

  for (const prop of requiredProps) {
    if (!(prop in firstItem)) {
      // Return empty VTT file for invalid structure
      return "WEBVTT\n\n";
    }
  }

  // Filter out deleted segments and sort by start time
  const validSegments = parsedData
    .filter((segment) => !("isDeleted" in segment) || !segment.isDeleted) // Check if isDeleted exists and is not true
    .sort((a, b) => a.startTime - b.startTime);

  // Return empty VTT file if no valid segments after filtering
  if (validSegments.length === 0) {
    return "WEBVTT\n\n";
  }

  // Split long segments into smaller chunks
  const chunkedSegments = validSegments.flatMap((segment) => {
    const duration = segment.endTime - segment.startTime;
    const wordCount = segment.content.trim().split(/\s+/).length;

    // Check if segment needs to be split
    if (duration <= maxSegmentDuration && wordCount <= maxWordsPerSegment) {
      return [segment];
    }

    // Split the segment into smaller chunks
    return splitSegmentIntoChunks(
      segment,
      maxSegmentDuration,
      maxWordsPerSegment
    );
  });

  // Adjust segments to prevent gaps longer than maxGapSeconds
  const adjustedSegments = chunkedSegments.map((segment, index) => {
    const nextSegment = chunkedSegments[index + 1];
    let adjustedEndTime = segment.endTime;

    if (nextSegment) {
      const gap = nextSegment.startTime - segment.endTime;

      // If gap is longer than maxGapSeconds, extend current segment
      if (gap > maxGapSeconds) {
        // Extend end time but don't overlap with next segment
        adjustedEndTime = nextSegment.startTime - 0.1; // Leave 0.1s buffer
      }
    }

    return {
      ...segment,
      endTime: adjustedEndTime,
    };
  });

  // Start with WebVTT header
  let vttContent = "WEBVTT\n\n";

  adjustedSegments.forEach((segment, index) => {
    // Convert timestamps to VTT format (HH:MM:SS.mmm)
    const startTime = formatTimestamp(segment.startTime);
    const endTime = formatTimestamp(segment.endTime);

    // Add speaker label if enabled and speaker info exists
    let content = segment.content.trim();

    if (addSpeakerLabels && segment.nameFromRevAi) {
      const speakerLabel = segment.nameFromRevAi.replace(
        "Speaker ",
        "Speaker "
      );
      content = `${speakerLabel}: ${content}`;
    }

    // Split long content into multiple lines if needed
    const lines = splitContentIntoLines(content, maxLineLength);

    // Add cue number (optional but helpful for debugging)
    vttContent += `${index + 1}\n`;
    vttContent += `${startTime} --> ${endTime}\n`;

    // Add content lines
    lines.forEach((line) => {
      vttContent += `${line}\n`;
    });

    vttContent += "\n";
  });

  return vttContent.trim();
}

/**
 * Create and upload transcription VTT file to GCS
 * @param transcriptions - Array of transcriptions
 * @param resourceId - Resource ID
 * @returns VTT file URL (file key)
 */
export async function createAndUploadTranscriptionVTT(
  transcriptions: Transcription[],
  resourceId: string
) {
  const vttContent = convertTranscriptionToVTT(transcriptions);
  const vttFileName = `${resourceId}-transcripts.vtt`;

  const vttFile = await gcsUpload(vttContent, vttFileName, "text/vtt");

  return vttFile;
}

//--------------------------------
//      PRIVATE FUNCTIONS
//--------------------------------

/**
 * Splits content into lines that don't exceed the maximum length
 * @param content - Text content to split
 * @param maxLength - Maximum characters per line
 * @returns Array of lines
 */
function splitContentIntoLines(content: string, maxLength: number): string[] {
  if (content.length <= maxLength) {
    return [content];
  }

  const words = content.split(" ");
  const lines: string[] = [];
  let currentLine = "";

  words.forEach((word) => {
    if ((currentLine + " " + word).length <= maxLength) {
      currentLine += (currentLine ? " " : "") + word;
    } else {
      if (currentLine) {
        lines.push(currentLine);
      }
      currentLine = word;
    }
  });

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
}

/**
 * Splits a long segment into smaller chunks based on duration and word count
 * @param segment - The segment to split
 * @param maxDuration - Maximum duration per chunk in seconds
 * @param maxWords - Maximum words per chunk
 * @returns Array of smaller segments
 */
function splitSegmentIntoChunks(
  segment: Transcription,
  maxDuration: number,
  maxWords: number
): Transcription[] {
  const words = segment.content.trim().split(/\s+/);
  const totalDuration = segment.endTime - segment.startTime;
  const totalWords = words.length;

  // Calculate how many chunks we need
  const chunksByDuration = Math.ceil(totalDuration / maxDuration);
  const chunksByWords = Math.ceil(totalWords / maxWords);
  const numChunks = Math.max(chunksByDuration, chunksByWords);

  if (numChunks <= 1) {
    return [segment];
  }

  const chunks: Transcription[] = [];
  const wordsPerChunk = Math.ceil(totalWords / numChunks);
  const durationPerChunk = totalDuration / numChunks;

  for (let i = 0; i < numChunks; i++) {
    const startWordIndex = i * wordsPerChunk;
    const endWordIndex = Math.min((i + 1) * wordsPerChunk, totalWords);
    const chunkWords = words.slice(startWordIndex, endWordIndex);

    const chunkStartTime = segment.startTime + i * durationPerChunk;
    const chunkEndTime =
      i === numChunks - 1
        ? segment.endTime
        : segment.startTime + (i + 1) * durationPerChunk;

    chunks.push({
      ...segment,
      content: chunkWords.join(" "),
      startTime: chunkStartTime,
      endTime: chunkEndTime,
      id: `${segment.id}_chunk_${i + 1}`,
    });
  }

  return chunks;
}
