import {
  DetailedUsageRecord,
  CostBreakdown,
  UsageContext,
} from "@/types/billing";
import { ProviderType, TokenUsage } from "@/types/universal-ai";
import { getModel } from "@/config/models";
import { createLogger } from "@/services/logger";
import AgentUsageModel from "@/schemas/billing/AgentUsage.model";
import { v4 as uuidv4 } from "uuid";

const logger = createLogger("UsageTracker");

export interface BillingContext {
  messageId: string;
  conversationId: string;
  userId: string;
  projectId?: string;
  organizationId?: string;
  ragUsed?: boolean;
  toolCalls?: number;
  messageLength?: number;
  contextLength?: number;
  processingTimeMs?: number;
  finishReason?: string;
  errorCount?: number;
  retryCount?: number;
  fallbackUsed?: boolean;
  originalModelId?: string;
  requestId?: string;
  sessionId?: string;
}

export class UsageTracker {
  async trackUsage(
    provider: ProviderType,
    modelId: string,
    usage: TokenUsage,
    context: BillingContext
  ): Promise<DetailedUsageRecord> {
    const startTime = Date.now();

    try {
      logger.debug("Tracking AI model usage", {
        provider,
        modelId,
        usage,
        userId: context.userId,
        messageId: context.messageId,
      });

      // Get model information from simple config
      const model = getModel(modelId);

      // Calculate costs with model-specific pricing
      const costBreakdown = this.calculateCostBreakdown(usage, model);

      // Calculate billing periods
      const billingPeriods = this.calculateBillingPeriods();

      // Create detailed usage record
      const record: DetailedUsageRecord = {
        id: uuidv4(),
        messageId: context.messageId,
        conversationId: context.conversationId,
        userId: context.userId,
        projectId: context.projectId,

        provider,
        modelId,
        modelTier: "balanced", // Fallback since we don't have tiers in ModelConfig

        inputTokens: usage.inputTokens || 0,
        outputTokens: usage.outputTokens || 0,
        imageTokens: usage.imageTokens || 0,
        totalTokens:
          usage.totalTokens ||
          (usage.inputTokens || 0) + (usage.outputTokens || 0),

        inputCost: costBreakdown.inputCost,
        outputCost: costBreakdown.outputCost,
        totalCost: costBreakdown.totalCost,
        costPerToken:
          costBreakdown.totalCost / Math.max(usage.totalTokens || 1, 1),

        ragUsed: context.ragUsed || false,
        toolCalls: context.toolCalls || 0,
        messageLength: context.messageLength || 0,
        processingTimeMs: context.processingTimeMs || 0,

        finishReason: (context.finishReason || "stop") as any,
        errorCount: context.errorCount || 0,

        timestamp: new Date(),
      };

      // Save to database
      await this.saveUsageRecord(record, model, billingPeriods, context);

      // Track performance
      const trackingTime = Date.now() - startTime;
      logger.info("AI usage tracked successfully", {
        provider,
        modelId,
        totalTokens: record.totalTokens,
        totalCost: record.totalCost,
        trackingTimeMs: trackingTime,
        userId: context.userId,
      });

      return record;
    } catch (error) {
      logger.error("Failed to track AI usage", {
        provider,
        modelId,
        error: error.message,
        userId: context.userId,
        messageId: context.messageId,
      });

      // Don't throw - billing tracking shouldn't break the main flow
      // Return a minimal record for consistency
      return {
        id: uuidv4(),
        messageId: context.messageId,
        conversationId: context.conversationId,
        userId: context.userId,
        projectId: context.projectId,
        provider,
        modelId,
        modelTier: "balanced", // Fallback since we don't have tiers in ModelConfig

        inputTokens: usage.inputTokens || 0,
        outputTokens: usage.outputTokens || 0,
        imageTokens: usage.imageTokens || 0,
        totalTokens: usage.totalTokens || 0,
        inputCost: 0,
        outputCost: 0,
        totalCost: 0,
        costPerToken: 0,
        ragUsed: false,
        toolCalls: 0,
        messageLength: 0,
        processingTimeMs: 0,
        finishReason: "error",
        errorCount: 1,
        timestamp: new Date(),
      };
    }
  }

  private calculateCostBreakdown(usage: TokenUsage, model: any): CostBreakdown {
    const inputCost =
      (usage.inputTokens || 0) * (model.pricing.input / 1_000_000);
    const outputCost =
      (usage.outputTokens || 0) * (model.pricing.output / 1_000_000);
    // Simple model config doesn't include image pricing yet - set to 0
    const imageCost = 0;

    return {
      inputCost,
      outputCost,
      imageCost,
      totalCost: inputCost + outputCost + imageCost,
    };
  }

  private calculateBillingPeriods() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const quarter = Math.ceil(month / 3);

    return {
      month: parseInt(`${year}${month.toString().padStart(2, "0")}`), // 202412
      quarter: parseInt(`${year}${quarter}`), // 20244
      year,
    };
  }

  private async saveUsageRecord(
    record: DetailedUsageRecord,
    model: any,
    billingPeriods: any,
    context: BillingContext
  ): Promise<void> {
    await AgentUsageModel.create({
      id: record.id,
      messageId: record.messageId,
      conversationId: record.conversationId,
      userId: record.userId,
      projectId: record.projectId,
      organizationId: context.organizationId,

      provider: record.provider,
      modelId: record.modelId,
      modelTier: record.modelTier,

      inputTokens: record.inputTokens,
      outputTokens: record.outputTokens,
      imageTokens: record.imageTokens,
      totalTokens: record.totalTokens,

      inputCostUsd: record.inputCost,
      outputCostUsd: record.outputCost,
      imageCostUsd: record.totalCost - record.inputCost - record.outputCost,
      totalCostUsd: record.totalCost,
      costPerToken: record.costPerToken,

      pricingInputPer1m: model.pricing.input,
      pricingOutputPer1m: model.pricing.output,
      pricingCurrency: "USD",

      ragUsed: record.ragUsed,
      toolCalls: record.toolCalls,
      messageLength: record.messageLength,
      contextLength: context.contextLength || 0,
      processingTimeMs: record.processingTimeMs,

      finishReason: record.finishReason,
      errorCount: record.errorCount,
      retryCount: context.retryCount || 0,
      fallbackUsed: context.fallbackUsed || false,
      originalModelId: context.originalModelId,

      requestId: context.requestId,
      sessionId: context.sessionId,

      billingMonth: billingPeriods.month,
      billingQuarter: billingPeriods.quarter,
      billingYear: billingPeriods.year,

      billingStatus: "pending",
    } as any);
  }

  // Utility methods for billing queries
  async getUserMonthlyUsage(
    userId: string,
    billingMonth: number
  ): Promise<any[]> {
    try {
      return await AgentUsageModel.findAll({
        where: { userId, billingMonth } as any,
        attributes: [
          "provider",
          "modelTier",
          [AgentUsageModel.sequelize.fn("COUNT", "*"), "messageCount"],
          [
            AgentUsageModel.sequelize.fn(
              "SUM",
              AgentUsageModel.sequelize.col("totalTokens")
            ),
            "totalTokens",
          ],
          [
            AgentUsageModel.sequelize.fn(
              "SUM",
              AgentUsageModel.sequelize.col("totalCostUsd")
            ),
            "totalCost",
          ],
          [
            AgentUsageModel.sequelize.fn(
              "AVG",
              AgentUsageModel.sequelize.col("costPerToken")
            ),
            "avgCostPerToken",
          ],
        ],
        group: ["provider", "modelTier"],
        order: [[AgentUsageModel.sequelize.literal("totalCost"), "DESC"]],
      });
    } catch (error) {
      logger.error("Failed to get user monthly usage", {
        userId,
        billingMonth,
        error,
      });
      return [];
    }
  }

  async getProjectCosts(
    projectId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      return await AgentUsageModel.findAll({
        where: {
          projectId,
          createdAt: {
            $between: [startDate, endDate],
          },
        } as any,
        attributes: [
          "provider",
          "modelId",
          [AgentUsageModel.sequelize.fn("COUNT", "*"), "requestCount"],
          [
            AgentUsageModel.sequelize.fn(
              "SUM",
              AgentUsageModel.sequelize.col("totalTokens")
            ),
            "totalTokens",
          ],
          [
            AgentUsageModel.sequelize.fn(
              "SUM",
              AgentUsageModel.sequelize.col("totalCostUsd")
            ),
            "totalCost",
          ],
        ],
        group: ["provider", "modelId"],
        order: [[AgentUsageModel.sequelize.literal("totalCost"), "DESC"]],
      });
    } catch (error) {
      logger.error("Failed to get project costs", { projectId, error });
      return [];
    }
  }

  async getProviderPerformance(days: number = 30): Promise<any[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      return await AgentUsageModel.findAll({
        where: {
          createdAt: { $gte: cutoffDate },
        } as any,
        attributes: [
          "provider",
          [AgentUsageModel.sequelize.fn("COUNT", "*"), "requests"],
          [
            AgentUsageModel.sequelize.fn(
              "AVG",
              AgentUsageModel.sequelize.col("processingTimeMs")
            ),
            "avgResponseTime",
          ],
          [
            AgentUsageModel.sequelize.fn(
              "SUM",
              AgentUsageModel.sequelize.col("errorCount")
            ),
            "totalErrors",
          ],
          [
            AgentUsageModel.sequelize.fn(
              "AVG",
              AgentUsageModel.sequelize.col("totalCostUsd")
            ),
            "avgCostPerRequest",
          ],
        ],
        group: ["provider"],
        order: [
          [AgentUsageModel.sequelize.literal("avgCostPerRequest"), "ASC"],
        ],
      });
    } catch (error) {
      logger.error("Failed to get provider performance", { error });
      return [];
    }
  }
}

// Export singleton instance
export const usageTracker = new UsageTracker();
