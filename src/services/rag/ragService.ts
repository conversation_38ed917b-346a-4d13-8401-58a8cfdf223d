import { uploadDocumentToPinecone } from "@/services/pinecone";
import { UnifiedDocumentPayload, PlatformUploadResult } from "@/types/vectorDb";
import { getFileCategory } from "@/utils/file";
import { buildUnifiedMetadata } from "@/utils/vectorDb";
import { log } from "@/services/logger";
import { gcsUploadContent, generateSignedUrlForRead } from "@/services/storage";
import path from "path";
import { DocumentProcessorService, DocumentProcessorValidationError } from "../documents/documentProcessor.service";
import { gcsTempFolderForDocumentProcessor } from "@/config";
import { FEATURE_FLAGS, isFeatureEnabled } from "../featureFlags";

// Pinecone Assistant supported file extensions
const PINECONE_SUPPORTED_EXTENSIONS = new Set([
  ".docx",
  ".json",
  ".md",
  ".pdf",
  ".txt",
]);

// ============================================================================
// RAG SERVICE FOR RESOURCE INTEGRATION
// ============================================================================

interface ResourceRAGData {
  resourceId: string;
  fileName: string;
  filePath: string;
  fileSize?: number;
  userId: string;
  projectId?: string;
  // Audio/video specific metadata (optional)
  speakers?: string[];
  audio_duration_seconds?: number;
  language_detected?: string;
  speaker_count?: number;
  transcript_confidence?: number;
}

/**
 * Check if a file type should be uploaded to RAG system during resource creation
 * Audio/video files will be uploaded later after transcription is completed
 * This now checks against a specific list of Pinecone-supported extensions.
 */
export function shouldUploadToRAG(
  fileName: string,
  projectId?: string
): boolean {
  // 1. Only upload files that belong to a project
  if (!projectId) {
    return false;
  }

  // 2. Check if the file extension is supported by Pinecone Assistant
  const fileExtension = path.extname(fileName).toLowerCase();
  if (!PINECONE_SUPPORTED_EXTENSIONS.has(fileExtension)) {
    log.info(
      "[shouldUploadToRAG] Skipping RAG upload for unsupported file type",
      {
        filename: fileName,
        extension: fileExtension,
        project_id: projectId,
      }
    );
    return false;
  }

  // 3. Ensure it's not an audio/video file (handled separately after transcription)
  const fileCategory = getFileCategory(fileName);
  if (fileCategory === "audio" || fileCategory === "video") {
    return false;
  }

  return true;
}

/**
 * Upload resource to RAG system (currently Pinecone Assistant only)
 * Future: Will upload to both Pinecone Assistant and official RAG system
 */
export const uploadResourceToRAG = async (
  resourceData: ResourceRAGData,
  fileStream?: ReadableStream | Blob,
  content?: string
): Promise<PlatformUploadResult> => {
  const startTime = Date.now();

  const enableBeingsMCP = await isFeatureEnabled(
    resourceData.userId,
    FEATURE_FLAGS.BEINGS_RAG_ENGINE
  );

  try {
    // Skip RAG upload if no project ID (personal files)
    if (!resourceData.projectId) {
      log.info("Skipping RAG upload for resource without project", {
        resource_id: resourceData.resourceId,
        user_id: resourceData.userId,
        filename: resourceData.fileName,
      });

      return {
        success: true,
        processing_time_ms: Date.now() - startTime,
      };
    }

    const fileCategory = getFileCategory(resourceData.fileName);

    log.info("Starting RAG upload for resource", {
      resource_id: resourceData.resourceId,
      user_id: resourceData.userId,
      project_id: resourceData.projectId,
      filename: resourceData.fileName,
      file_category: fileCategory,
      has_file_stream: !!fileStream,
      has_content: !!content,
    });

    // Build unified metadata
    const metadata = buildUnifiedMetadata(resourceData);

    // Create unique filename to avoid conflicts and preserve extension
    // Format: {baseName}_{fileId}.{extension}
    const { name, ext } = path.parse(resourceData.fileName);
    const uniqueFilename = `${name}_${resourceData.resourceId}${ext}`;
    metadata.filename = uniqueFilename;

    // Add resource-specific metadata using the custom property
    metadata.custom = {
      ...metadata.custom,
      original_filename: resourceData.fileName,
    };

    // Create unified document payload
    const documentPayload: UnifiedDocumentPayload = {
      content,
      file_stream: fileStream,
      metadata,
    };
    const pineconeUploadResult = await uploadDocumentToPinecone(documentPayload);
    if (pineconeUploadResult.success) {
      log.info("Resource successfully uploaded to RAG system", {
        resource_id: resourceData.resourceId,
        document_id: pineconeUploadResult.document_id,
        user_id: resourceData.userId,
        project_id: resourceData.projectId,
        processing_time_ms: pineconeUploadResult.processing_time_ms,
      });
    } else {
      log.error("Failed to upload resource to RAG system", {
        resource_id: resourceData.resourceId,
        user_id: resourceData.userId,
        project_id: resourceData.projectId,
        error: pineconeUploadResult.error,
        processing_time_ms: pineconeUploadResult.processing_time_ms,
      });
    }

    // Only call document processor service when enableBeingsMCP flag is true
    if (enableBeingsMCP) {
      // Upload content to GCS temp bucket as text file
      if (resourceData.fileName) {
        try {
          const presignedUrl = await generateSignedUrlForRead(
            resourceData.filePath,
            false
          );
          await DocumentProcessorService.publishUploadMessage({
            file_id: resourceData.resourceId,
            user_id: resourceData.userId,
            presigned_url: presignedUrl,
            project_id: resourceData.projectId,
            metadata: {
              filename: uniqueFilename,
              category: metadata.category,
              audio_video: null,
              tags: metadata.tags || [],
              custom: {
                is_transcript: false,
                original_filename: resourceData.fileName,
                transcript_source: metadata.custom?.transcript_source,
              },
            },
          });

          log.info(
            "Document processing message published for resource creation",
            {
              resourceId: resourceData.resourceId,
              fileName: resourceData.fileName,
              projectId: resourceData.projectId,
            }
          );
        } catch (error) {
          if (error instanceof DocumentProcessorValidationError) {
            log.error("Document processor validation failed for resource creation", {
              validationError: error.message,
              field: error.field,
              resourceId: resourceData.resourceId,
              fileName: resourceData.fileName,
              projectId: resourceData.projectId,
            });
          } else {
            log.error("Failed to publish document processing message", {
              error: error instanceof Error ? error.message : String(error),
              resourceId: resourceData.resourceId,
              fileName: resourceData.fileName,
              projectId: resourceData.projectId,
            });
          }
          // Don't fail resource creation if document processing message fails
        }
      }
    }

    // Always upload to Pinecone regardless of the flag

    return pineconeUploadResult;

  } catch (error) {
    const processingTime = Date.now() - startTime;

    log.error("RAG upload service error", {
      error: error instanceof Error ? error.message : String(error),
      resource_id: resourceData.resourceId,
      user_id: resourceData.userId,
      project_id: resourceData.projectId,
      processing_time_ms: processingTime,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      processing_time_ms: processingTime,
    };
  }
};

/**
 * Upload transcript content to RAG system (for audio/video files)
 * This is called after transcription is completed in handleTranscriptSuccess
 */
export const uploadTranscriptToRAG = async (
  resourceData: {
    resourceId: string;
    fileName: string;
    userId: string;
    projectId?: string;
    fileSize?: number;
  },
  transcriptContent: string,
  audioMetadata?: {
    speakers?: string[];
    timestamps?: string[];
    audio_duration_seconds?: number;
    language_detected?: string;
  }
): Promise<PlatformUploadResult> => {
  const startTime = Date.now();

  const enableBeingsMCP = await isFeatureEnabled(
    resourceData.userId,
    FEATURE_FLAGS.BEINGS_RAG_ENGINE
  );

  try {
    // Skip RAG upload if no project ID (personal files)
    if (!resourceData.projectId) {
      log.info("Skipping RAG transcript upload for resource without project", {
        resource_id: resourceData.resourceId,
        user_id: resourceData.userId,
        filename: resourceData.fileName,
      });

      return {
        success: true,
        processing_time_ms: Date.now() - startTime,
      };
    }

    log.info("Uploading transcript to RAG system", {
      resource_id: resourceData.resourceId,
      user_id: resourceData.userId,
      project_id: resourceData.projectId,
      filename: resourceData.fileName,
      transcript_length: transcriptContent.length,
      has_audio_metadata: !!audioMetadata,
    });

    // Skip if no transcript content
    if (!transcriptContent || transcriptContent.trim().length === 0) {
      log.warn("No transcript content available for RAG upload", {
        resource_id: resourceData.resourceId,
        filename: resourceData.fileName,
      });

      return {
        success: true, // Not an error, just no content to upload
        processing_time_ms: Date.now() - startTime,
      };
    }

    // Build unified metadata with audio/video context
    const enhancedResourceData = {
      ...resourceData,
      speakers: audioMetadata?.speakers,
      audio_duration_seconds: audioMetadata?.audio_duration_seconds,
      language_detected: audioMetadata?.language_detected,
    };

    const metadata = buildUnifiedMetadata(enhancedResourceData);

    // Create unique transcript filename to avoid conflicts and preserve extension
    // Format: {baseName}_{fileId}_transcript.txt
    const { name } = path.parse(resourceData.fileName);
    const uniqueTranscriptFilename = `${name}_${resourceData.resourceId}_transcript.txt`;

    // Update metadata with the unique filename (file_id is already set by buildUnifiedMetadata)
    metadata.filename = uniqueTranscriptFilename;

    // Add transcript-specific metadata using the custom property
    metadata.custom = {
      ...metadata.custom,
      is_transcript: true,
      original_filename: resourceData.fileName,
      transcript_source: "revai",
    };

    // Create unified document payload with transcript content
    const documentPayload: UnifiedDocumentPayload = {
      content: transcriptContent,
      metadata,
    };
    // Always upload to Pinecone regardless of the flag
    const pineconeUploadResult = await uploadDocumentToPinecone(documentPayload);

    if (pineconeUploadResult.success) {
      log.info("Transcript successfully uploaded to RAG system", {
        resource_id: resourceData.resourceId,
        document_id: pineconeUploadResult.document_id,
        user_id: resourceData.userId,
        project_id: resourceData.projectId,
        transcript_filename: uniqueTranscriptFilename,
        processing_time_ms: pineconeUploadResult.processing_time_ms,
        transcript_length: transcriptContent.length,
      });
    } else {
      log.error("Failed to upload transcript to RAG system", {
        resource_id: resourceData.resourceId,
        user_id: resourceData.userId,
        project_id: resourceData.projectId,
        transcript_filename: uniqueTranscriptFilename,
        error: pineconeUploadResult.error,
        processing_time_ms: pineconeUploadResult.processing_time_ms,
      });
    }
    // Only call document processor service when enableBeingsMCP flag is true
    if (enableBeingsMCP) {
      // Upload content to GCS temp bucket as text file
      if (documentPayload.content) {
        gcsUploadContent(
          documentPayload.content,
          `${gcsTempFolderForDocumentProcessor}/${uniqueTranscriptFilename}`,
          {
            contentType: "text/plain; charset=utf-8",
            metadata: {
              original_filename: resourceData.fileName,
              resource_id: resourceData.resourceId,
              user_id: resourceData.userId,
              project_id: resourceData.projectId,
              uploaded_at: new Date().toISOString(),
            },
          }
        )
          .then(async () => {
            try {
              const presignedUrl = await generateSignedUrlForRead(
                `${gcsTempFolderForDocumentProcessor}/${uniqueTranscriptFilename}`,
                false
              );
              await DocumentProcessorService.publishUploadMessage({
                file_id: resourceData.resourceId,
                user_id: resourceData.userId,
                presigned_url: presignedUrl,
                project_id: resourceData.projectId,
                metadata: {
                  filename: uniqueTranscriptFilename,
                  category: metadata.category,
                  audio_video: {
                    speakers: audioMetadata?.speakers,
                    audio_duration: audioMetadata?.audio_duration_seconds,
                    language: audioMetadata?.language_detected || null,
                  },
                  tags: metadata.tags || [],
                  custom: {
                    is_transcript: true,
                    original_filename: resourceData.fileName,
                    transcript_source: metadata.custom?.transcript_source,
                  },
                },
              });

              log.info(
                "Document processing message published for transcript upload",
                {
                  resourceId: resourceData.resourceId,
                  fileName: resourceData.fileName,
                  projectId: resourceData.projectId,
                }
              );
            } catch (error) {
              if (error instanceof DocumentProcessorValidationError) {
                log.error("Document processor validation failed for transcript upload", {
                  validationError: error.message,
                  field: error.field,
                  resourceId: resourceData.resourceId,
                  fileName: resourceData.fileName,
                  projectId: resourceData.projectId,
                });
              } else {
                log.error("Failed to publish document processing message for transcript", {
                  error: error instanceof Error ? error.message : String(error),
                  resourceId: resourceData.resourceId,
                  fileName: resourceData.fileName,
                  projectId: resourceData.projectId,
                });
              }
              // Don't fail resource creation if document processing message fails
            }
            log.info("Content successfully uploaded to GCS temp bucket", {
              resource_id: resourceData.resourceId,
              temp_file_name: uniqueTranscriptFilename,
              content_length: documentPayload.content.length,
            });
          })
          .catch((gcsError) => {
            log.error("Failed to upload content to GCS temp bucket", {
              resource_id: resourceData.resourceId,
              error:
                gcsError instanceof Error ? gcsError.message : String(gcsError),
            });
          });
      }
    }



    return pineconeUploadResult;

  } catch (error) {
    const processingTime = Date.now() - startTime;

    log.error("RAG transcript upload service error", {
      error: error instanceof Error ? error.message : String(error),
      resource_id: resourceData.resourceId,
      user_id: resourceData.userId,
      project_id: resourceData.projectId,
      processing_time_ms: processingTime,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      processing_time_ms: processingTime,
    };
  }
};
