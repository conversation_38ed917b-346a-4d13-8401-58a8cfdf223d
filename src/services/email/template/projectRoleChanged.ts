import { webAppUrl } from '@/config'
import { simpleFooter } from "./simpleFooter";

interface ProjectRoleChangedTemplateParams {
    projectName: string
    userName?: string
    newRole: string
}

export const projectRoleChangedTemplate = ({ projectName, userName, newRole, }: ProjectRoleChangedTemplateParams): string => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1">
    </head>
    <body>
    <div>
        Hi ${userName || 'there'},<br /><br />

        You've been changed to ${newRole} for: ${projectName}<br /><br />

        ${simpleFooter}
    </div>
    </body>
    </html>
` 
