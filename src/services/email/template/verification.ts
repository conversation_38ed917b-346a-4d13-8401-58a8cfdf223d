// substitution: {{username}}, {{confirmLink}}

import { aidaFooter } from "./aidaFooter"

export const verificationTemplate = (): string => {
    return `<!DOCTYPE html>
  <html lang="en">
  
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
  </head>
  
  <body style="background-color:#f2f3fc">
      <div
          style="max-width:650px;margin:auto;font-family:'Open Sans','Helvetica Neue',Helvetica,Arial,sans-serif;font-size:14px;line-height:20px;color:#181c22;font-weight:400;background-color:#fff;box-shadow:0 1px 3px 1px rgba(0,0,0,.15);box-shadow:0 1px 2px 0 rgba(0,0,0,.3)">
          <link rel="preconnect" href="https://fonts.googleapis.com">
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          <link
              href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
              rel="stylesheet">
          <table style="width:100%;max-width:530px;margin:0 auto;border-spacing:0 24px;border-collapse:separate">
              <tr>
                  <td style="font-size:14px;font-weight:400;">
                      <p style="margin:0;padding-top:8px;text-align:left; color: #006dcc; font-weight:600;">Hi {{username}}, </p>
                      <p style="margin:0;padding-top:20px;text-align:left">Please verify your email address. </p>
                      <p>Please click the link below to confirm your email address and complete setup:</p>
                     <p>
                       <a href="{{confirmLink}}" style="text-decoration:none; font-size:14px;color: #006dcc;">Magic link</a>
                    </p>
                     <p>Once confirmed, Aida will be scheduled to join and take notes.</p>
                     <p style="margin:0;text-align:left">— Aida</p>
                  </td>
            </tr>  
            ${aidaFooter}
          </table>
          <div style="display:block;height:1px;border:0;border-top:1px solid #c1c6d4"></div>
          <table style="width:100%;max-width:530px;margin:0 auto;margin-top:40px">
              <tr>
                  <td align="center">
                  </td>
              </tr>
              <tr>
                  <td>
                      <p style="color:#717784;font-size:12px;font-weight:400;text-align:center;font-style:italic;">Use is subject to the <a href="https://beings.com/privacy/" style="color:#006dcc;">Privacy Policy</a> and <a href="https://beings.com/terms-and-conditions/"  style="color:#006dcc;">Terms of Service</a>.</p>
                  </td>
              </tr>
          </table>
      </div>
  </body>
  
  </html>`
  }