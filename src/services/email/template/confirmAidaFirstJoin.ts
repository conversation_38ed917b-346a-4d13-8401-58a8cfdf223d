import { aidaFooter } from "./aidaFooter"

export const confirmAidaFirstJoinTemplate = (
    username: string,
    magicLink: string,
): string => {
    return `
    <table>
        <tr>
            <td>
                Hi ${username},
            </td>
        </tr>
        <tr>
            <td>
                You’ve invited <PERSON><PERSON> to join a meeting.
            </td>
        </tr>
        <tr>
            <td>
                Please click the link below to confirm your email address and complete setup:
            </td>
        </tr>
        <tr>
            <td>
                <a href="${magicLink}">${magicLink}</a>
            </td>
        </tr>
        <tr>
            <td>
                Once confirmed, <PERSON><PERSON> will be scheduled to join and take notes.
            </td>
        </tr>
        ${aidaFooter}
    </table>
    `
}