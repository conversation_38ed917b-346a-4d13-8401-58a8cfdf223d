import { simpleFooter } from "./simpleFooter";

interface ProjectAccessRequestRejectedTemplateParams {
    requesterEmail: string
    projectName: string
    userName?: string
}

export const projectAccessRequestRejectedTemplate = ({ requesterEmail, projectName, userName }: ProjectAccessRequestRejectedTemplateParams): string => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1">
    </head>
    <body>
    <div>
        Hi ${userName || requesterEmail},<br /><br />

        Your request to join ${projectName} was declined.<br /><br />

        Contact the owner if you think this was a mistake.<br /><br />

        ${simpleFooter}
    </div>
    </body>
    </html>
` 
