export const generateRetentionReminderTemplate = (params: {
  resourceName: string;
  deletionDate: string;
  daysRemaining: string;
}): string => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="background-color: #fff;">
    <div style="max-width: 650px; margin: auto; font-family: 'Open Sans','Helvetica Neue',Helvetica,Arial,sans-serif; font-size: 14px; line-height: 20px; color: #181C22; font-weight: 400; background-color: #fff;">
        <table style="width: 100%; margin: 0 auto; border-spacing: 0 24px; border-collapse: separate;">
            <tr>
                <td>
                    <p style="font-size: 14px; font-weight: 400;">
                        Your recording "${params.resourceName}" will be automatically deleted on ${params.deletionDate} (in ${params.daysRemaining} days).
                    </p>
                    <p style="font-size: 14px; font-weight: 400;">
                        This is part of our data retention policy to ensure we only store your data for a limited time.
                    </p>
                    <p style="font-size: 14px; font-weight: 400;">
                        If you need to keep this recording longer, please download it before the deletion date.
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        — Aida @ Beings
                    </p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`; 