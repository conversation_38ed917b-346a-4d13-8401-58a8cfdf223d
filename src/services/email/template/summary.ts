import { aidaFooter } from "./aidaFooter"

export type SummarySection = {
    type: 'text' | 'list'
    title: string
    content: string[]
    contentStyle?: string
    wrapperStyle?: string
}

export const sectionGenerator = (section: SummarySection): string => {
    const wrapperStyle = `font-weight: 400; margin: 0; padding-top: 8px;${section.wrapperStyle || ''}`
    const contentStyle = section.contentStyle || ''

    const content = section.type === 'text'
        ? `<p style="${wrapperStyle}">${section.content.join('')}</p>`
        : `<ul style="${wrapperStyle}">${section.content.map(item => `<li style="${contentStyle}">${item}</li>`).join('')}</ul>`

    return `
        <tr>
            <td>
                <label>${section.title}</label>
                ${content}
            </td>
        </tr>`
}

export const generateSummaryTemplate = (sections: SummarySection[]): string =>
    `
        <table style="width: 100%;margin: 0 auto; border-spacing: 0 24px; border-collapse: separate;">
            <tr style="font-size: 14px;font-weight: 400;">
                <td>
                    Hi {{username}},
                </td>
            </tr>
            <tr>
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">Here are your Transcript from the meeting:</p>
            </tr>
            <tr>
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        📄 Full transcript: <a href="{{transcriptLink}}">{{transcriptLink}}</a>
                    </p>
                </td>
            </tr>
            ${aidaFooter}
            <tr>
                <td>
                  <p style="font-weight: 400; margin: 0; padding-top: 8px;">Was this summary helpful?</>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        👍 Yes : <a href="{{thumbupLink}}">{{thumbupLink}}</a>
                    </p>
                     <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        👎 No: <a href="{{thumbdownLink}}">{{thumbdownLink}}</a>
                    </p>
                    <p>(Your feedback helps Aida get smarter)</>
                </td>
            </tr>
        </table>`