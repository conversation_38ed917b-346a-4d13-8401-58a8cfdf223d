import { webAppUrl } from '@/config'
import { simpleFooter } from "./simpleFooter";

interface ProjectAccessRequestTemplateParams {
    requesterEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const projectAccessRequestTemplate = ({ requesterEmail, projectName, projectId, requesterName, approverName }: ProjectAccessRequestTemplateParams): string => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1">
    </head>
    <body>
    <div>
        Hi ${approverName},<br /><br />
        ${requesterName || requesterEmail} wants to join ${projectName}. <br /><br />
        👉 <a href="${webAppUrl}/project/${projectId}#sharing">Review request</a><br /><br />
        ${simpleFooter}
    </div>
    </body>
    </html>
` 
