
import { simpleFooter } from "./simpleFooter";

interface ProjectInvitationTemplateParams {
    projectName: string
    invitationLink: string
    message?: string
    projectOwnerName?: string
    recipientName?: string
    projectOwnerEmail?: string
}

export const projectInvitationTemplate = ({ 
    projectName, 
    invitationLink, 
    projectOwnerName = "Project owner",
    recipientName = "there",
    projectOwnerEmail
}: ProjectInvitationTemplateParams): string => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1">
    </head>
    <body>
    <div>
        Hi ${recipientName}<br /><br />
        ${projectOwnerName} invited you to collaborate on ${projectName}.<br /><br />
        👉: <a href="${invitationLink}">[Open project]</a><br /><br />
        ${simpleFooter}
    </div>
    </body>
    </html>
` 
