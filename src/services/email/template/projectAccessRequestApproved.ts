import { webAppUrl } from '@/config'
import { simpleFooter } from "./simpleFooter";

interface ProjectAccessRequestApprovedTemplateParams {
    projectName: string
    projectId: string
    userName?: string
}

export const projectAccessRequestApprovedTemplate = ({ projectName, projectId, userName }: ProjectAccessRequestApprovedTemplateParams): string => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width,initial-scale=1">
    </head>
    <body>
    <div>
        Hi ${userName},<br /><br />

        Your request to join ${projectName} has been approved.<br /><br />

        ${simpleFooter}
    </div>
    </body>
    </html>
` 
