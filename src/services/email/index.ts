import sendgrid from '@sendgrid/mail'
import { sendgridApiKey, sendgridFromName, sendgridFromEmail, webAppUrl } from '@/config'
import {  MeetingType } from './template/confirmedMeeting'
import { generateSummaryTemplate, SummarySection } from './template/summary'
import { createLogger } from '@/services/logger'
import { SendgridTemplates } from './template'
import { getFirebaseUserByEmail } from '../firebase'
import { urlShortenerService } from '@/services/urlShortener'
import { UrlType } from '@/schemas/url/ShortenedUrl.model'

if (sendgridApiKey) { sendgrid.setApiKey(sendgridApiKey) }

const logger = createLogger('EmailService')

export interface SendMailParams {
    to: string | string[]
    cc?: string | string[]
    // subject?: string
    text?: string
    html?: string
    attachments?: Array<Partial<{
        content: string
        filename: string
        type: string
        disposition: 'attachment' | 'inline'
    }>>
    substitutions?: Record<string, string>
    dynamicTemplateData?: Record<string, any>
}

export type SendSendMailParams = {
    html?: string
    templateId?: string
}

const sendEmail = async (options: SendMailParams, sendOptions: SendSendMailParams): Promise<boolean> => {
    try {
        logger.info(`Sending email with options: ${JSON.stringify({
            to: options.to,
            cc: options.cc,
            // subject: options.subject,
            text: options.text,
            hasHTML: options.html !== undefined,
            hasAttachments: options.attachments !== undefined,
        })}`)

        const msg = {
            personalizations: [{
                to: Array.isArray(options.to) ? options.to.map(email => ({ email })) : [{ email: options.to }],
                cc: Array.isArray(options.cc) ? options.cc.map(email => ({ email })) : options.cc ? [{ email: options.cc }] : undefined,
            }],
            from: {
                name: sendgridFromName,
                email: sendgridFromEmail,
            },
            // subject: options.subject,
            text: options.text,
            ...sendOptions,
            ...(sendOptions.templateId ? { dynamicTemplateData: options.dynamicTemplateData } : {substitutions: options.substitutions, substitutionWrappers: ['{{', '}}']}),
        }

        await sendgrid.send(msg)
        logger.info('Email sent successfully', { to: options.to, msg })
        return true
    } catch (error) {
        logger.error('Error sending email:', error)
        return false
    }
}

export const sendConfirmAidaFirstJoinEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_GUEST_VERIFICATION,
    })
}

export const sendVerificationEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_GUEST_VERIFICATION,
    })
}

export const sendConfirmedMeetingEmail = async (params: SendMailParams, type: MeetingType): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: type === MeetingType.Confirmed ? SendgridTemplates.AIDA_GUEST_JOINING_CONFIRMATION : SendgridTemplates.AIDA_CONFIRMED_MEETING_USER,
    })
}

export const sendSessionDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_DELETED,
    })
}

export const sendSessionUpdatedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_UPDATED,
    })
}


export const sendSummary = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SUMMARY,
    })
}



interface SendInvitationEmailPayload {
    to: string
    projectName: string
    code: string
    projectId: string
    message?: string
    invitedByUserEmail?: string
    projectOwnerName?: string
    recipientName?: string
}

export const sendProjectInvitationEmail = async (payload: SendInvitationEmailPayload): Promise<boolean> => {
    const { to, projectName, code, projectId, message, invitedByUserEmail } = payload

    const originalInvitationLink = `${webAppUrl}/accept-project-invitation?code=${code}&projectId=${projectId}`

    // Try to shorten the invitation URL
    let invitationLink = originalInvitationLink
    try {
        // Get recipient user to determine userId for access control
        const recipientUser = await getFirebaseUserByEmail(to)
        const recipientUserId = recipientUser?.uid

        if (recipientUserId) {
            // Create a user-specific shortened URL for security
            invitationLink = await urlShortenerService.shortenUrl(originalInvitationLink, {
                urlType: UrlType.PROJECT_INVITATION,
                entityId: `${projectId}-${code}`, // Unique identifier for this invitation
                userId: recipientUserId, // User-specific for security
            })
            
            logger.info('Created shortened invitation URL', { 
                originalUrl: originalInvitationLink, 
                shortUrl: invitationLink,
                projectId,
                recipientUserId
            })
        } else {
            logger.warn('Could not get recipient user ID, using original URL', { to, projectId })
        }
    } catch (error: any) {
        logger.error('Failed to shorten invitation URL, using original', { 
            error: error.message, 
            originalUrl: originalInvitationLink,
            projectId 
        })
        // Fallback to original URL if shortening fails
        invitationLink = originalInvitationLink
    }

    // const [getSenderUserResult, getRecipientUserResult] = await Promise.all([
    //     getFirebaseUserByEmail(invitedByUserEmail),
    //     getFirebaseUserByEmail(to)
    // ])

    const projectOwnerName = invitedByUserEmail
    const recipientName = to

    logger.info('Sending invitation email', { to, projectName, code, message, invitationLink })

    return sendEmail({
        to,
        dynamicTemplateData: {
            projectName,
            invitationLink,
            projectOwnerName,
            recipientName,
            projectOwnerEmail: invitedByUserEmail,
        },
        // cc: invitedByUserEmail ? [invitedByUserEmail] : undefined, // TODO: Uncomment this when we have a way to send cc emails
    }, {
        templateId: SendgridTemplates.PROJECT_INVITATION,
    })
}


interface SendProjectAccessRequestEmailPayload {
    requesterEmail: string
    approverEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const sendProjectAccessRequestEmail = async (payload: SendProjectAccessRequestEmailPayload): Promise<boolean> => {
    const { approverEmail, projectName, projectId, requestId, requesterEmail } = payload
    logger.info(`Sending project access request email to ${approverEmail} for project ${projectName}`)

    const [getRequesterUserResult, getApproverUserResult] = await Promise.all([
        getFirebaseUserByEmail(requesterEmail),
        getFirebaseUserByEmail(approverEmail)
    ])

    const requesterName = getRequesterUserResult?.displayName
    const approverName = getApproverUserResult?.displayName

    const reviewLink = `${webAppUrl}/project/${projectId}#sharing`
    // Use the urlShortenerService to shorten the review link before sending the email
    let shortReviewLink: string | undefined;
    try {
        shortReviewLink = await urlShortenerService.shortenUrl(reviewLink, {
            urlType: UrlType.PROJECT_ACCESS_REQUEST,
            entityId: projectId,
            userId: getApproverUserResult?.uid,
        })
    } catch (err) {
        logger.warn('Failed to shorten review link, using original', { reviewLink, error: err });
        shortReviewLink = reviewLink;
    }
    return sendEmail({
        to: approverEmail,
        dynamicTemplateData: {
            projectName,
            requesterName: requesterName || requesterEmail,
            approverName,
            projectId,
            requestId,
            shortReviewLink,
        },
    }, {
        templateId: SendgridTemplates.PROJECT_ACCESS_REQUEST,
    })
}

interface SendProjectAccessRequestRejectedEmailPayload {
    projectName: string
    requesterEmail: string
    userName?: string
}

export const sendProjectAccessRequestRejectedEmail = async (payload: SendProjectAccessRequestRejectedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, userName } = payload
    return sendEmail({
        to: requesterEmail,
        dynamicTemplateData: {
            projectName,
            userName: userName || requesterEmail,
        },
    }, {
        templateId: SendgridTemplates.PROJECT_ACCESS_REQUEST_REJECTED,
    })
}

interface SendProjectAccessRequestApprovedEmailPayload {
    projectName: string
    requesterEmail: string
    projectId: string
    userName?: string
}

export const sendProjectAccessRequestApprovedEmail = async (payload: SendProjectAccessRequestApprovedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, projectId, userName } = payload
    return sendEmail({
        to: requesterEmail,
        dynamicTemplateData: {
            projectName,
            userName,
            projectId,
        },
    }, {
        templateId: SendgridTemplates.PROJECT_ACCESS_REQUEST_APPROVED,
    })
}


export const sendMeetingDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        dynamicTemplateData: {
            ...params.substitutions,
        },
    }, {
        templateId: SendgridTemplates.MEETING_DELETED,
    })
}

export const sendRetentionReminderEmail = async (params: SendMailParams & {
  substitutions: {
    meetingTitle: string;
    deletionDate: string;
    daysRemaining: string;
    extendUrl: string;
  }
}) => {
  return sendEmail({
    to: params.to,
    cc: params.cc,
    attachments: params.attachments,
    dynamicTemplateData: {
        ...params.substitutions,
    },
  }, {
    templateId: SendgridTemplates.RETENTION_REMINDER,
  })
}

interface SendProjectRoleChangedEmailPayload {
    to: string;
    projectName: string;
    newRole: string;
    userName?: string;
}

export const sendProjectRoleChangedEmail = async (payload: SendProjectRoleChangedEmailPayload): Promise<boolean> => {
    const { to, projectName, newRole, userName } = payload;

    return sendEmail({
        to,
        dynamicTemplateData: {
            projectName,
            userName,
            newRole,
        },
    }, {
        templateId: SendgridTemplates.PROJECT_ROLE_CHANGED,
    });
};

interface SendInvitationCanceledEmailPayload {
    to: string;
    projectName: string;
    projectId: string;
    canceledByUserEmail?: string;
    canceledByUserName?: string;
}

export const sendProjectInvitationCanceledEmail = async (payload: SendInvitationCanceledEmailPayload): Promise<boolean> => {
    const { to, projectName, canceledByUserEmail, canceledByUserName } = payload;
    logger.info('Sending invitation canceled email', { to, projectName });

    const canceledByName = canceledByUserName || 'Project owner';

    return sendEmail({
        to,
        dynamicTemplateData: {
            projectName,
            canceledByUserEmail,
            canceledByName,
        },
        // cc: canceledByUserEmail ? [canceledByUserEmail] : undefined, // TODO: Uncomment this when we have a way to send cc emails
    }, {
        templateId: SendgridTemplates.PROJECT_INVITATION_CANCELLED,
    });
};
