import axios from 'axios'

import { googleGenerativeAIApiKey, geminiModel } from '@/config'
import { GEMINI_API } from './constants'
import { SummaryMeetingGemini } from '@/types/geminiAi'

export const generateContent = async (generationConfig: GeminiGenerationConfig, contents: GeminiGenerateContent[]) => {
    const response = await axios.post(`${GEMINI_API}/models/${geminiModel}:generateContent`, {
    generationConfig,
    contents
  }, {
    params: {
      key: googleGenerativeAIApiKey
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })

  const data = response?.data?.candidates?.[0]?.content?.parts?.[0]?.text

  try {
    return JSON.parse(data) as SummaryMeetingGemini
  } catch (err: any) {
    console.log('Error: ', err)
  }

  return {} as SummaryMeetingGemini
}

export type GeminiGenerationConfig = {
  responseMimeType: string
  responseSchema: any
}

export type GeminiGenerateContent = {
  role: string
  parts: GeminiGenerateContentPart[]
}

export type GeminiGenerateContentPart = {
  text: string
}

export const formatGeminiChatContent = (conversations: {
  speakerName: string
  content: string
}[], prompt: string): GeminiGenerateContent[] => {
  if (!conversations || conversations.length === 0) {
    return []
  }

  return conversations.map(conversation => ({
    role: 'user',
    parts: [
      {
        text: `${conversation.speakerName
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .replace(/ /g, '-')}: ${conversation.content}`
      }
    ]
  })).concat({
    role: 'model',
    parts: [{ text: prompt }]
  })
}

export const summaryGenerationConfig = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: 'OBJECT',
    properties: {
      // title
      title: {
        type: 'STRING',
      },

      // purpose
      purpose: {
        type: 'STRING',
      },

      // key topics
      keyTopics: {
        type: 'ARRAY',
        items: {
          type: 'STRING',
        },
      },

      // percentage talktime
      percentageTalktime: {
        type: 'ARRAY',
        // description: 'Format: ["{{speakerName}}: {{percentage}}%", ...]',
        items: {
          type: 'STRING',
          // description: 'Format: {{speakerName}}: {{percentage}}%',
        },
      },

      // key themes
      keyThemes: {
        type: 'ARRAY',
        items: {
          type: 'STRING',
        },
      },

      // key actions
      keyActions: {
        type: 'STRING',
      },

      // key Sentiments
      keySentiments: {
        type: 'ARRAY',
        items: {
          type: 'STRING',
        },
      },

      // Quantitative Insights
      quantitativeInsights: {
        type: 'ARRAY',
        items: {
          type: 'STRING',
        },
      },

      // Qualitative Insights
      qualitativeInsights: {
        type: 'ARRAY',
        items: {
          type: 'STRING',
        },
      },

      // Questions
      questionsWithAnswers: {
        type: 'ARRAY',
        // description: 'Format: ["Q: {{question}} - A: {{answer}}", ...]',
        items: {
          type: 'STRING',
          // description: 'Format: Q: {{question}} - A: {{answer}}',
        },
      },
    },
  },
}

export const summaryConservationWithPrompt = (conversations: Parameters<typeof formatGeminiChatContent>[0], prompt: string) => {
  const content = formatGeminiChatContent(conversations, prompt)
  return generateContent(summaryGenerationConfig, content) as SummaryMeetingGemini
}