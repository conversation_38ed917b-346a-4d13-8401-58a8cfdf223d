import { Document, Packer, Paragraph, TextRun } from "docx";

import { models } from "@/schemas";
import { sendSummary } from "../email";
import { SummarySection } from "../email/template/summary";
import { summaryConservationWithPrompt } from "../geminiAi";
import { aidaEndpoint } from "@/config";
import { getFirebaseAuth } from "../firebase";
import { urlShortenerService, UrlType } from "../urlShortener";

export type UserType = {
  email: string;
  name: string;
};

export const SUMMARY_PROMPT = `Write a summary, in UK english, of the transcript from the meeting including:
1. Percentage talk time in the format: Person name: %
2. Meeting Purpose
3. Key Themes
4. Key Topics
5. Key Actions
6. Key Sentiment
7. Quantitative insights
8. Qualitative Insights
9. Questions with responses in the format: Q: Question - A: Answer`;

type SectionKey =
  | keyof ReturnType<typeof summaryConservationWithPrompt>
  | "prompt";

const SUMMARY_SECTIONS: Array<
  Omit<SummarySection, "content"> & { key: SectionKey }
> = [
  { type: "text", title: "Purpose", key: "purpose" },
  {
    type: "text",
    title: "Prompt Used for Smart Notes Generation",
    key: "prompt",
  },
  { type: "list", title: "Key Topics", key: "keyTopics" },
  { type: "list", title: "Percentage Talktime", key: "percentageTalktime" },
  { type: "list", title: "Key Themes", key: "keyThemes" },
  { type: "text", title: "Key Actions", key: "keyActions" },
  { type: "list", title: "Key Sentiments", key: "keySentiments" },
  { type: "list", title: "Quantitative Insights", key: "quantitativeInsights" },
  { type: "list", title: "Qualitative Insights", key: "qualitativeInsights" },
  {
    type: "list",
    title: "Questions with Answers",
    key: "questionsWithAnswers",
  },
];

const invalidContent = ["null", "undefined"];

const capitalize = (s: string) =>
  String(s[0]).toUpperCase() + String(s).slice(1);

const defaultFormater = (content: string | string[]): string[] => {
  if (!Array.isArray(content)) {
    if (!content || invalidContent.includes(content)) return ["No content"];
    return [capitalize(content)];
  }
  return content.length
    ? invalidContent.includes(content[0])
      ? ["No content"]
      : content.map(capitalize)
    : ["No content"];
};
const contentFormater: {
  [key in SectionKey]: (content: string | string[]) => string[];
} = {
  prompt: (content: string | string[]) => {
    return content ? [`<i>"${content}"</i>`] : ["No content"];
  },
  questionsWithAnswers: (content: string | string[]) => {
    const formatedContent = defaultFormater(content);
    if (["No content", "null", "undefined"].includes(formatedContent[0]))
      return ["No question was asked"];
    return formatedContent.map((qa) => {
      const [question, answer] = qa.split(" - A: ");
      return [question.replace("Q: ", ""), `<ul><li>${answer}</li></ul>`].join(
        "<br>"
      );
    });
  },
  title: defaultFormater,
  purpose: defaultFormater,
  keyTopics: defaultFormater,
  percentageTalktime: defaultFormater,
  keyThemes: defaultFormater,
  keyActions: defaultFormater,
  keySentiments: defaultFormater,
  quantitativeInsights: defaultFormater,
  qualitativeInsights: defaultFormater,
};

const conservationsToDocx = (
  conservations: Parameters<typeof summaryConservationWithPrompt>[0]
) =>
  new Document({
    sections: [
      {
        children: conservations.map(({ speakerName, content }) => {
          const parentRow = new Paragraph({});
          const n = new TextRun({ text: `${speakerName} `, bold: true });
          const c = new TextRun({ text: ` ${content}` });
          const spacing = new TextRun({ text: "", break: 1 });
          parentRow.addChildElement(n);
          parentRow.addChildElement(c);
          parentRow.addChildElement(spacing);
          return parentRow;
        }),
      },
    ],
  });

export type GenerateSummaryArgs = {
  resourceInInsightEngineId: string;
  prompt?: string;
  sessionTitle?: string;
  sessionDate?: string;
  timezone?: string;
};

const extractFileNameWithoutExtension = (fileName?: string) => {
  if (!fileName) return "Unknown name";
  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1) {
    return fileName;
  }
  return fileName.substring(0, lastDotIndex);
};

export type GenerateSummaryResult = {
  summary: ReturnType<typeof summaryConservationWithPrompt>;
  base64Docx: string;
  summaryTitle: string;
  subjectDate: string;
};

const fetchTranscripts = async (resourceInInsightEngineId: string) => {
  const transcripts = await models.Transcription.xFind({
    resourceInInsightEngineId,
  });

  if (!transcripts.length) {
    throw new Error("No transcripts found");
  }

  return transcripts;
};

const prepareConversations = (
  transcripts: Awaited<ReturnType<typeof fetchTranscripts>>
): Parameters<typeof summaryConservationWithPrompt>[0] => {
  return transcripts.map(({ nameFromRevAi, content }) => ({
    speakerName: nameFromRevAi,
    content,
  }));
};

const generateDocxDocument = async (
  conversations: Parameters<typeof summaryConservationWithPrompt>[0]
): Promise<string> => {
  const doc = conservationsToDocx(conversations);
  return await Packer.toBase64String(doc);
};

const extractSummaryMetadata = async (
  resourceInInsightEngineId: string,
  sessionTitle?: string,
  sessionDate?: string,
  timezone?: string
) => {
  let subjectDate = (
    sessionDate ? new Date(sessionDate) : new Date()
  ).toLocaleString("en-GB", {
    day: "numeric",
    month: "short",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
    timeZone: timezone,
  });

  let summaryTitle = sessionTitle || "Title Unrecognizable";

  const riie = await models.ResourceInInsightEngine.xFind1ById(
    resourceInInsightEngineId
  );

  if (riie && !sessionTitle && !sessionDate) {
    const resource = await models.Resource.xFind1ById(riie?.resourceId);
    const extractedName = extractFileNameWithoutExtension(resource?.name);
    if (extractedName) summaryTitle = extractedName;
    if (resource) {
      subjectDate = new Date(resource.createdAt).toLocaleString("en-GB", {
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });
    }
  }

  return { summaryTitle, subjectDate, riie };
};

const storeSummaryInDatabase = async (
  resourceId: string | undefined,
  summaryTitle: string,
  summary: ReturnType<typeof summaryConservationWithPrompt>
) => {
  await models.Summary.xCreate({
    resourceId,
    title: summaryTitle,
    purpose: summary.purpose,
    keyTopics: summary.keyTopics,
    percentageTalktime: summary.percentageTalktime,
    keyThemes: summary.keyThemes,
    keyActions: summary.keyActions,
    keySentiments: summary.keySentiments,
    quantitativeInsights: summary.quantitativeInsights,
    qualitativeInsights: summary.qualitativeInsights,
    questionsWithAnswers: summary.questionsWithAnswers,
  });
};

export const generateSummary = async ({
  resourceInInsightEngineId,
  prompt,
}: {
  resourceInInsightEngineId: string;
  prompt?: string;
}) => {
  try {
    const transcripts = await fetchTranscripts(resourceInInsightEngineId);
    const conversations = prepareConversations(transcripts);
    const base64Docx = await generateDocxDocument(conversations);

    const targetPrompt = !prompt ? SUMMARY_PROMPT : prompt;
    const summary = await summaryConservationWithPrompt(
      conversations,
      targetPrompt
    );

    return {
      summary,
      base64Docx,
    };
  } catch (error) {
    throw error;
  }
};

// Helper function to create shortened URLs for email links
const createShortenedUrls = async (
  resourceId: string,
  userId: string
) => {
  try {
    const [transcriptLink, thumbUpLink, thumbDownLink] = await Promise.all([
      // Transcript link - will automatically become public due to URL type
      urlShortenerService.shortenUrl(
        `${aidaEndpoint}/download-caption/${resourceId}`,
        {
          urlType: UrlType.TRANSCRIPT,
          entityId: resourceId,
          userId, // Service will automatically remove this for public URLs
        }
      ),
      // Feedback links - will automatically remain user-specific
      urlShortenerService.shortenUrl(
        `${aidaEndpoint}/feedback?entityId=${resourceId}&entityType=resource&rating=up&userId=${userId}`,
        {
          urlType: UrlType.FEEDBACK,
          entityId: resourceId,
          userId, // Required for feedback URLs
        }
      ),
      urlShortenerService.shortenUrl(
        `${aidaEndpoint}/feedback?entityId=${resourceId}&entityType=resource&rating=down&userId=${userId}`,
        {
          urlType: UrlType.FEEDBACK,
          entityId: resourceId,
          userId, // Required for feedback URLs
        }
      )
    ]);

    return { transcriptLink, thumbUpLink, thumbDownLink };
  } catch (error) {
    // Fallback to original URLs if URL shortening fails
    console.warn('URL shortening failed, using original URLs:', error);
    return {
      transcriptLink: `${aidaEndpoint}/download-caption/${resourceId}`,
      thumbupLink: `${aidaEndpoint}/feedback?entityId=${resourceId}&entityType=resource&rating=up&userId=${userId}`,
      thumbdownLink: `${aidaEndpoint}/feedback?entityId=${resourceId}&entityType=resource&rating=down&userId=${userId}`
    };
  }
};

export const sendSummaryEmail = async ({
  summary,
  summaryTitle,
  subjectDate,
  receivers,
  subject,
  resourceInInsightEngineId,
}: GenerateSummaryResult & {
  receivers: UserType[];
  subject?: string;
  resourceInInsightEngineId: string;
}) => {
  const summarySections: SummarySection[] = SUMMARY_SECTIONS.map(
    ({ type, title, key }) => {
      const content = contentFormater[key](
        key === "prompt" ? SUMMARY_PROMPT : summary[key]?.toString() || ""
      );
      return {
        type: [
          "No content",
          "No question was asked",
          "null",
          "undefined",
        ].includes(content[0])
          ? "text"
          : type,
        title,
        content,
      };
    }
  );
  const riie = await models.ResourceInInsightEngine.xFind1ById(
    resourceInInsightEngineId
  );

  // thumbup/thumbdown link
  const firebaseAuth = await getFirebaseAuth();

  // Filter out receivers that don't have an email or for whom we can't get a Firebase user
  const validReceivers = await Promise.all(
    receivers.map(async (receiver) => {
      if (!receiver.email) {
        return null;
      }
      const fUser = await firebaseAuth
        .getUserByEmail(receiver.email)
        .catch(() => null);
      return fUser ? { receiver, fUser } : null;
    })
  ).then((results) => results.filter(Boolean));

  // Process valid receivers in parallel
  await Promise.all(
    validReceivers.map(async ({ receiver, fUser }) => {
      const { transcriptLink, thumbUpLink, thumbDownLink } = await createShortenedUrls(riie?.resourceId, fUser.uid);
      return sendSummary(
        {
          to: receiver.email,
          substitutions: {
            username: receiver.name || 'there',
            transcriptLink,
            thumbUpLink,
            thumbDownLink,
            summaryTitle,
            subjectDate,
          },
          attachments: [],
        },
      );
    })
  );
};

export const generateSummaryAndStore = async (
  args: GenerateSummaryArgs
): Promise<GenerateSummaryResult> => {
  const {
    resourceInInsightEngineId,
    prompt,
    sessionTitle,
    sessionDate,
    timezone
  } = args;
  const { summaryTitle, subjectDate, riie } = await extractSummaryMetadata(
    resourceInInsightEngineId,
    sessionTitle,
    sessionDate,
    timezone
  );
  console.log("Extracted", summaryTitle, subjectDate, riie);
  const targetPrompt = !prompt ? SUMMARY_PROMPT : prompt;
  const { summary, base64Docx } = await generateSummary({
    resourceInInsightEngineId,
    prompt: targetPrompt,
  });
  console.log("Generated summary:", summary);
  await storeSummaryInDatabase(riie?.resourceId, summaryTitle, summary);
  return {
    summary,
    base64Docx,
    summaryTitle,
    subjectDate,
  };
};

