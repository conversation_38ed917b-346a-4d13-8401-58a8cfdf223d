import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "revai-node-sdk";
import { revAiTopicExtractionClient } from "../revAi";
import { models } from "@/schemas";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";

export const handleTopicExtractionSuccess = async (job: RevAiApiJob) => {
  try {
    const resourceInIE = await models.ResourceInInsightEngine.xFind1By(
      "revAiExtractionJobId",
      job.id,
    );
    if (!resourceInIE) {
      return;
    }
    const resource = await models.Resource.xFind1By(
      "id",
      resourceInIE.resourceId,
    );
    const data = await revAiTopicExtractionClient.getResult(job.id);

    if (!data) {
      return;
    }

    const topics = data.topics ?? [];

    // Update topics
    await models.Resource.xUpdateById(resource.id, {
      topics,
    });

    // Update transcription job status
    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      status: TranscriptStatus.Completed,
    });
  } catch (error) {
    console.log("Error in handleTopicExtractionSuccess:", error);
  }
};
