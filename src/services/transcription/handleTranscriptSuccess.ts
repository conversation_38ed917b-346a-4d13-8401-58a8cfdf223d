import { models } from "@/schemas";
import type { Monologue, RevAiApiJob } from "revai-node-sdk";
import { revAiClient } from "../revAi";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import { upsertUserStatistics } from "@/schemas/statistics/utils";
import { SessionStatus } from "@/schemas/session/Session.model";
import { generateSummaryAndStore, sendSummaryEmail } from "./generateSummary";
import { getFirebaseAuth, getFirebaseUserDetails } from "../firebase";
import { addDays } from "date-fns";
import { DATA_RETENTION_DAYS, defaultTimezone } from "@/config";
import { ResourceLiveTranscript } from "@/types/resources";
import { uploadTranscriptToRAG } from "@/services/rag/ragService";
import { getFileCategory } from "@/utils/file";
import {
  createAndUploadTranscriptionVTT,
  formatTranscriptForCitations,
} from "@/utils/transcription";
import { RagSyncStatus } from "@/schemas/resource/Resource.model";
import { WinstonLogger } from "../logger/winston";
import { FEATURE_FLAGS, isFeatureEnabled } from "../featureFlags";
import { broadcastSessionStatusUpdate } from "@/utils/session-sse";

const logger = new WinstonLogger("HandleTranscriptSuccess");

export const handleTranscriptSuccess = async (job: RevAiApiJob) => {
  try {
    const resourceInIE = await models.ResourceInInsightEngine.xFind1By(
      "revAiJobId",
      job.id
    );
    if (!resourceInIE) {
      return;
    }
    const resource = await models.Resource.xFind1By(
      "id",
      resourceInIE.resourceId
    );
    const data = await revAiClient.getTranscriptObject(job.id);

    if (!data) {
      return;
    }

    // Create transcriptions
    await createTranscriptionsAndUpdateStatus(
      data.monologues,
      resourceInIE,
      resource
    );
  } catch (error) {
    logger.error("Error in handleTranscriptSuccess:", error);
  }
};

const createTranscript = async (
  m: Monologue,
  resourceInInsightEngineId: string
) => {
  // RevAI provides timestamps in seconds, convert to milliseconds for consistency
  const startTimeSeconds = Math.min(
    ...m.elements.filter((i) => i.ts).map((e) => e.ts as number)
  );
  const endTimeSeconds = Math.max(...m.elements.map((e) => e.end_ts ?? 0));

  // Convert from seconds to milliseconds
  const startTime = startTimeSeconds * 1000;
  const endTime = endTimeSeconds * 1000;

  const name = `Speaker ${m.speaker}`;
  let content = "";
  m.elements.forEach((e) => (content += e.value));

  const transcription = await models.Transcription.xCreate({
    resourceInInsightEngineId,
    content,
    startTime,
    nameFromRevAi: name,
    endTime,
  });
  return transcription;
};

const createLiveTranscript = async (
  data: ResourceLiveTranscript,
  riieId: string
) => {
  return models.Transcription.xCreate({
    resourceInInsightEngineId: riieId,
    startTime: data.startTime,
    endTime: data.endTime,
    content: data.content,
    nameFromRevAi: data.speakerId,
  });
};

export const getReceivers = async (
  meetingUrl: string
): Promise<{ email: string; name: string }[]> => {
  const sessions = await models.Session.xFindBy("meetingUrl", meetingUrl);
  if (sessions.length === 0) {
    return [];
  }
  const receiverUids = sessions
    .filter((s) => s.shouldSendSummaryToEmail)
    .map((s) => ({ uid: s.createdById }));
  const firebaseAuth = await getFirebaseAuth();
  const fUsers = (await firebaseAuth.getUsers(receiverUids)).users;
  if (fUsers.length === 0) {
    return [];
  }
  const receivers = fUsers.map((fUser) => ({
    email: fUser.email,
    name: fUser.displayName,
  }));
  return receivers;
};

export async function createTranscriptionsAndUpdateStatus(
  monologues: Monologue[] | ResourceLiveTranscript[],
  resourceInIE: { id: string },
  resource: {
    id: string;
    createdById: string;
    sessionId?: string;
    name: string;
    createdAt: Date;
  }
) {
  logger.info("Creating transcriptions");
  const transcriptions = await Promise.all(
    monologues.map((i) =>
      "elements" in i
        ? createTranscript(i, resourceInIE.id)
        : createLiveTranscript(i, resourceInIE.id)
    )
  );
  logger.info("Created transcriptions");

  const wordsCount = transcriptions.reduce((acc, curr) => {
    const text = (curr?.content ?? "").trim();
    acc += text.split(" ").length;
    return acc;
  }, 0);

  // Update user statistics
  logger.info("Updating user statistics");
  await upsertUserStatistics(resource.createdById, {
    totalTranscriptionsWordCount: wordsCount,
  });

  // Update transcription job status
  logger.info("Updating transcription job status");

  // Create transcription VTT file and update resource with the file key
  // TODO: Uncomment this when issue with VTT fixed
  // const createTranscriptionVTT = createAndUploadTranscriptionVTT(
  //   transcriptions,
  //   resource.id
  // ).then((vttFile) => {
  //   if (resource.id && vttFile) {
  //     models.Resource.xUpdateById(resource.id, {
  //       transcriptionSrc: vttFile,
  //     });
  //   } else {
  //     logger.error(
  //       "[createTranscriptionVTT] Resource ID not found in createTranscriptionsAndUpdateStatus"
  //     );
  //   }
  // });

  await Promise.all([
    models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      status: TranscriptStatus.Completed,
    }),
    // createTranscriptionVTT,
  ]);

  // Upload transcript to RAG system if applicable (async, don't block other operations)
  uploadTranscriptToRAGSystem(resource, transcriptions, monologues).catch(
    (error) => {
      logger.error("RAG transcript upload failed", {
        resource_id: resource.id,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  );

  const userDetails = await getFirebaseUserDetails(resource.createdById);
  let timeZone: string = defaultTimezone;
  if (userDetails?.preferences) {
    timeZone = userDetails.preferences.timezone;
  }

  // Update session status if it exists
  if (resource.sessionId) {
    logger.debug("Update session status to completed");
    const sessionId = await models.Session.xUpdateById(resource.sessionId, {
      status: SessionStatus.Completed,
    });

    const session = await models.Session.xFind1ById(resource.sessionId);

    // Broadcast session completion via SSE
    if (session) {
      await broadcastSessionStatusUpdate({
        sessionId: session.id,
        title: session.title,
        status: SessionStatus.Completed,
        projectId: session.projectId,
        userId: session.createdById,
        metadata: {
          recallBotStatus: session.recallBotStatus,
          resourceId: resource.id,
          resourceName: resource.name,
        },
      });
    }

    // generate Summary and meta data
    try {
      const { summary, base64Docx, summaryTitle, subjectDate } =
        await generateSummaryAndStore({
          resourceInInsightEngineId: resourceInIE.id,
          sessionTitle: session.title,
          sessionDate: session.startTime.toISOString(),
          timezone: timeZone,
        });

      if (session.shouldSendSummaryToEmail) {
        // create retention setting for the resource
        await models.RetentionSetting.xCreate({
          resourceId: resource.id,
          expiryDate: addDays(new Date(), DATA_RETENTION_DAYS),
        });
        const receivers = await getReceivers(session.meetingUrl);
        await sendSummaryEmail({
          summary,
          base64Docx,
          summaryTitle,
          subjectDate,
          receivers,
          resourceInInsightEngineId: resourceInIE.id,
        });
      }
    } catch (error) {
      logger.error("Error generating summary and sending email:", error);
    }
  } else if (transcriptions.length > 0) {
    try {
      await generateSummaryAndStore({
        resourceInInsightEngineId: resourceInIE.id,
        sessionTitle: resource.name,
        sessionDate: resource.createdAt.toISOString(),
      });
      logger.info("No session found, summary generated for resource");
    } catch (error) {
      logger.error("Error generating summary for resource:", error);
    }
  }
}

/**
 * Helper function to upload transcript to RAG system
 * Formats the data properly for the RAG service
 */
async function uploadTranscriptToRAGSystem(
  resource: {
    id: string;
    createdById: string;
    sessionId?: string;
    name: string;
    createdAt: Date;
  },
  transcriptions: Array<{
    content: string;
    nameFromRevAi: string;
    startTime: number;
    endTime: number;
  }>,
  monologues: Monologue[] | ResourceLiveTranscript[]
) {
  const enableBeingsMCP = await isFeatureEnabled(
    resource.createdById,
    FEATURE_FLAGS.BEINGS_RAG_ENGINE
  );
  try {
    // Get the full resource details to check if it belongs to a project
    const fullResource = await models.Resource.xFind1ById(resource.id);
    if (!fullResource) {
      logger.warn("Resource not found", {
        resource_id: resource.id,
      });
      return;
    }

    // Skip if resource doesn't belong to a project
    if (!fullResource.projectId) {
      logger.info("Skipping RAG upload for resource without project", {
        resource_id: resource.id,
        user_id: resource.createdById,
        filename: resource.name,
      });
      return;
    }

    // Mark the resource as syncing before starting the upload
    await models.Resource.xUpdateById(fullResource.id, {
      ragSyncStatus: RagSyncStatus.SYNCING,
    });

    // Check if this is an audio/video file
    const fileCategory = getFileCategory(fullResource.name);
    if (fileCategory !== "audio" && fileCategory !== "video") {
      logger.info("Skipping RAG upload for non-audio/video file", {
        resource_id: resource.id,
        filename: fullResource.name,
        file_category: fileCategory,
      });
      return;
    }

    // Format transcript content with timestamps for better citations
    const transcriptContent = formatTranscriptForCitations(transcriptions);

    // Extract speaker information from monologues
    const speakers = Array.from(
      new Set(
        monologues
          .filter((m): m is Monologue => "speaker" in m)
          .map((m) => `Speaker ${m.speaker}`)
      )
    );

    // Calculate audio duration from transcriptions
    const audioDuration =
      transcriptions.length > 0
        ? Math.max(...transcriptions.map((t) => t.endTime)) -
          Math.min(...transcriptions.map((t) => t.startTime))
        : undefined;

    // Build audio metadata
    const audioMetadata = {
      speakers,
      timestamps: transcriptions.map((t) => `${t.startTime}-${t.endTime}`),
      audio_duration_seconds: audioDuration
        ? Math.round(audioDuration / 1000)
        : undefined,
    };

    // Upload to RAG system
    await uploadTranscriptToRAG(
      {
        resourceId: fullResource.id,
        fileName: fullResource.name,
        userId: fullResource.createdById,
        projectId: fullResource.projectId,
        fileSize: fullResource.fileSize,
      },
      transcriptContent,
      audioMetadata
    );
    // For beings mcp, we don't need to mark as synced as there is a callback to update the status
    if (!enableBeingsMCP) {
      // Mark as synced on success
      await models.Resource.xUpdateById(fullResource.id, {
        ragSyncStatus: RagSyncStatus.SYNCED,
      });

      logger.info("Successfully uploaded transcript to RAG", {
        resource_id: fullResource.id,
        project_id: fullResource.projectId,
        transcript_length: transcriptContent.length,
        speakers_detected: speakers.length,
      });
    }
  } catch (error) {
    // Mark as failed if any error occurs
    if (resource && resource.id) {
      await models.Resource.xUpdateById(resource.id, {
        ragSyncStatus: RagSyncStatus.FAILED,
      });
    }
    logger.error("Failed to upload transcript to RAG", {
      resource_id: resource.id,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}
