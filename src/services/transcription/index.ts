import type { RevAiApiTranscript, RevAiJobOptions } from "revai-node-sdk";
import { revAiClient, revAiTopicExtractionClient } from "../revAi";
import { apiServerOrigin } from "@/config";
import { CustomerUrlData } from "revai-node-sdk/src/models/CustomerUrlData";
import { log } from "../logger";

export const submitJob = async (fileUrl: string) => {
  const sourceConfig: CustomerUrlData = { url: fileUrl };
  const notificationConfig: CustomerUrlData = {
    url: `${apiServerOrigin}/api/callback/revAi`,
  };
  const jobOptions: RevAiJobOptions = {
    source_config: sourceConfig,
    notification_config: notificationConfig,
  };
  const job = await revAiClient.submitJob(jobOptions);

  return job;
};

export const submitExtractionJob = async (jsonData: RevAiApiTranscript) => {
  if (!jsonData) {
    log.info(`[submitExtractionJob] Empty data for topic extraction`);
    return;
  }
  const notificationConfig: CustomerUrlData = {
    url: `${apiServerOrigin}/api/callback/revAi`,
  };

  const job = await revAiTopicExtractionClient.submitJobFromJson(jsonData, {
    notification_config: notificationConfig,
  });

  return job;
};
