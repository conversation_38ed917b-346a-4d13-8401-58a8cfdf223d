import { models } from "@/schemas";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import type { RevAiApiJob } from "revai-node-sdk";

export const handleTopicExtractionFailed = async (job: RevAiApiJob) => {
  const riie = await models.ResourceInInsightEngine.xFind1By(
    "revAiExtractionJobId",
    job.id,
  );
  if (!riie) {
    return;
  }
  await models.ResourceInInsightEngine.xUpdateById(riie.id, {
    status: TranscriptStatus.Failed,
  });
};
