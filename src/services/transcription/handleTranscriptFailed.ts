import { models } from "@/schemas";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import type { RevAiApiJob } from "revai-node-sdk";

export const handleTranscriptFailed = async (job: RevAiApiJob) => {
  const riie = await models.ResourceInInsightEngine.xFind1By(
    "revAiJobId",
    job.id,
  );
  if (!riie) {
    return;
  }
  await models.ResourceInInsightEngine.xUpdateById(riie.id, {
    status: TranscriptStatus.Failed,
  });
};
