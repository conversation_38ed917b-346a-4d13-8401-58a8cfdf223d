import crypto from 'crypto';
import { models } from '@/schemas';
import { UrlType } from '@/schemas/url/ShortenedUrl.model';
import { aidaEndpoint, urlShortenerConfig, webAppUrl } from '@/config';
import { createLogger } from '@/services/logger';

const logger = createLogger('UrlShortenerService');

export interface ShortenUrlOptions {
  urlType: UrlType;
  entityId?: string;
  userId?: string;
  expiresInDays?: number;
}

export interface UrlValidationResult {
  isValid: boolean;
  originalUrl?: string;
  errorMessage?: string;
}

class UrlShortenerService {
  private readonly DEFAULT_EXPIRY_DAYS = urlShortenerConfig.defaultExpirationDays;
  private readonly SHORT_CODE_LENGTH = urlShortenerConfig.shortCodeLength;
  private readonly SECRET_KEY = urlShortenerConfig.secretKey;
  private readonly MAX_RETRIES = 5;

  /**
   * Check if URL shortening is enabled
   */
  private isEnabled(): boolean {
    return urlShortenerConfig.enableShortening;
  }

  /**
   * Generate a cryptographically secure short code with HMAC verification
   */
  private generateShortCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let baseCode = '';
    const bytes = crypto.randomBytes(this.SHORT_CODE_LENGTH - 2); // Reserve 2 chars for HMAC
    
    // Generate base random code
    for (let i = 0; i < this.SHORT_CODE_LENGTH - 2; i++) {
      baseCode += chars[bytes[i] % chars.length];
    }
    
    // Generate HMAC signature (first 2 characters)
    const hmac = crypto.createHmac('sha256', this.SECRET_KEY);
    hmac.update(baseCode);
    const signature = hmac.digest('hex');
    
    // Take first 2 characters of HMAC as verification suffix
    const verificationSuffix = signature.slice(0, 2);
    
    return baseCode + verificationSuffix;
  }

  /**
   * Validate that the URL is from our domain for security
   */
  private isValidDomain(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // include localhost for development convenience
      if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1') {
        return true;
      }
      const allowedDomains = [
        new URL(aidaEndpoint).hostname,
        new URL(webAppUrl).hostname,
      ];
      
      return allowedDomains.includes(urlObj.hostname);
    } catch {
      return false;
    }
  }

  /**
   * Calculate expiration date based on URL type
   */
  private getExpirationDate(urlType: UrlType, customDays?: number): Date {
    const days = customDays || this.getDefaultExpiryDays(urlType);
    return new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }

  /**
   * Get default expiry days based on URL type
   */
  private getDefaultExpiryDays(urlType: UrlType): number {
    switch (urlType) {
      case UrlType.FEEDBACK:
        return 90;
      case UrlType.TRANSCRIPT:
        return 180;
      case UrlType.CONFIRMATION:
        return 7;
      case UrlType.PROJECT_INVITATION:
        return 30;
      case UrlType.RETENTION_EXTEND:
        return 14;
      default:
        return this.DEFAULT_EXPIRY_DAYS;
    }
  }

  /**
   * Create a shortened URL with automatic access control based on URL type
   */
  public async shortenUrl(originalUrl: string, options: ShortenUrlOptions): Promise<string> {
    // If URL shortening is disabled, return original URL
    if (!this.isEnabled()) {
      logger.info('URL shortening disabled, returning original URL');
      return originalUrl;
    }

    // Validate URL domain
    if (!this.isValidDomain(originalUrl)) {
      throw new Error('Invalid domain: URL must be from allowed domains');
    }

    // Auto-adjust userId based on URL type for security
    const finalOptions = { ...options };
    if (!this.shouldBeUserSpecific(options.urlType)) {
      // Remove userId for public URLs
      delete finalOptions.userId;
      logger.info('Creating public shortened URL', { 
        urlType: options.urlType, 
        entityId: options.entityId 
      });
    } else if (options.urlType === UrlType.FEEDBACK || options.urlType === UrlType.CONFIRMATION) {
      // Ensure user-specific URLs have a userId
      if (!finalOptions.userId) {
        throw new Error(`URL type ${options.urlType} requires a userId for security`);
      }
      logger.info('Creating user-specific shortened URL', { 
        urlType: options.urlType, 
        entityId: options.entityId,
        userId: finalOptions.userId 
      });
    }

    const expiresAt = this.getExpirationDate(finalOptions.urlType, finalOptions.expiresInDays);
    
    // Try to generate a unique short code
    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      const shortCode = this.generateShortCode();
      
      try {
        await models.ShortenedUrl.xCreate({
          shortCode,
          originalUrl,
          urlType: finalOptions.urlType,
          entityId: finalOptions.entityId,
          userId: finalOptions.userId,
          expiresAt,
          clickCount: 0,
          isActive: true,
        });

        const shortUrl = `${aidaEndpoint}/s/${shortCode}`;
        logger.info('Created shortened URL', { 
          shortCode, 
          urlType: finalOptions.urlType, 
          entityId: finalOptions.entityId,
          userId: finalOptions.userId,
          isPublic: !finalOptions.userId
        });
        
        return shortUrl;
      } catch (error: any) {
        // If unique constraint violation, try again with new code
        if (error.name === 'SequelizeUniqueConstraintError' && attempt < this.MAX_RETRIES - 1) {
          continue;
        }
        throw error;
      }
    }
    
    throw new Error('Failed to generate unique short code after maximum retries');
  }

  /**
   * Determine if a URL type should be public or user-specific
   */
  private shouldBeUserSpecific(urlType: UrlType): boolean {
    switch (urlType) {
      case UrlType.FEEDBACK:
      case UrlType.CONFIRMATION:
        case UrlType.TRANSCRIPT:
        case UrlType.PROJECT_INVITATION:
        case UrlType.RETENTION_EXTEND:
        return true; // These should be user-specific for security
      default:
        return false; // Default to user-specific for security
    }
  }

  /**
   * Validate URL access based on type and user context
   */
  private async validateUrlAccess(
    shortenedUrl: any, 
    userId?: string, 
    additionalContext?: any
  ): Promise<{ isValid: boolean; errorMessage?: string }> {
    // If URL has a userId set, it must match the requesting user
    if (shortenedUrl.userId && userId && shortenedUrl.userId !== userId) {
      return { isValid: false, errorMessage: 'Access denied: URL is user-specific' };
    }

    // Additional validation based on URL type
    switch (shortenedUrl.urlType) {
      case UrlType.FEEDBACK:
        // Feedback URLs should always be user-specific
        if (!shortenedUrl.userId) {
          return { isValid: false, errorMessage: 'Invalid feedback URL: missing user context' };
        }
        break;
      
      case UrlType.CONFIRMATION:
        // Confirmation URLs should be user-specific and typically one-time use
        if (!shortenedUrl.userId) {
          return { isValid: false, errorMessage: 'Invalid confirmation URL: missing user context' };
        }
        break;
      
      case UrlType.TRANSCRIPT:
        // Transcript URLs can be public but should still check resource access
        // TODO: Add resource-level access control here if needed
        break;
      
      case UrlType.PROJECT_INVITATION:
        // Project invitations can be shared but have their own access logic
        // TODO: Add project-level access control here if needed
        break;
    }

    return { isValid: true };
  }
  /**
   * Resolve a short code to original URL with security checks
   */
  public async resolveUrl(shortCode: string, userId?: string): Promise<UrlValidationResult> {
    try {
      const shortenedUrl = await models.ShortenedUrl.xFind1({ shortCode, isActive: true });
      
      if (!shortenedUrl) {
        return { isValid: false, errorMessage: 'URL not found or expired' };
      }

      // Check if URL has expired
      if (new Date() > shortenedUrl.expiresAt) {
        await models.ShortenedUrl.xUpdate({ id: shortenedUrl.id }, { isActive: false });
        return { isValid: false, errorMessage: 'URL has expired' };
      }

      // Validate access using enhanced access control
      const accessValidation = await this.validateUrlAccess(shortenedUrl, userId);
      if (!accessValidation.isValid) {
        return accessValidation;
      }

      // Increment click count
      await models.ShortenedUrl.xUpdate(
        { id: shortenedUrl.id },
        { clickCount: shortenedUrl.clickCount + 1 }
      );

      logger.info('URL accessed', { 
        shortCode, 
        userId, 
        urlType: shortenedUrl.urlType,
        clickCount: shortenedUrl.clickCount + 1,
        isUserSpecific: !!shortenedUrl.userId
      });

      return { isValid: true, originalUrl: shortenedUrl.originalUrl };
    } catch (error) {
      logger.error('Error resolving URL', { shortCode, error });
      return { isValid: false, errorMessage: 'Internal server error' };
    }
  }

  /**
   * Cleanup expired URLs (should be run as a scheduled job)
   */
  public async cleanupExpiredUrls(): Promise<number> {
    try {
      const result = await models.ShortenedUrl.xUpdate(
        { 
          expiresAt: { $lt: new Date() }, 
          isActive: true 
        },
        { isActive: false }
      );

      logger.info('Cleaned up expired URLs', { count: result });
      return result;
    } catch (error) {
      logger.error('Error cleaning up expired URLs', { error });
      return 0;
    }
  }

  /**
   * Get analytics for a specific entity or user
   */
  public async getAnalytics(entityId?: string, userId?: string, urlType?: UrlType) {
    const where: any = { isActive: true };
    
    if (entityId) where.entityId = entityId;
    if (userId) where.userId = userId;
    if (urlType) where.urlType = urlType;

    const urls = await models.ShortenedUrl.xFind(where);
    
    return {
      totalUrls: urls.length,
      totalClicks: urls.reduce((sum, url) => sum + url.clickCount, 0),
      urlsByType: urls.reduce((acc, url) => {
        acc[url.urlType] = (acc[url.urlType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  }

  /**
   * Verify the HMAC signature of a short code for security
   */
  public verifyShortCode(shortCode: string): boolean {
    if (shortCode.length !== this.SHORT_CODE_LENGTH) {
      return false;
    }
    
    // Extract base code and verification suffix
    const baseCode = shortCode.slice(0, -2);
    const providedSuffix = shortCode.slice(-2);
    
    // Generate expected HMAC signature
    const hmac = crypto.createHmac('sha256', this.SECRET_KEY);
    hmac.update(baseCode);
    const expectedSignature = hmac.digest('hex');
    const expectedSuffix = expectedSignature.slice(0, 2);
    
    // Compare using constant-time comparison to prevent timing attacks
    // Convert both to lowercase for case-insensitive comparison
    return crypto.timingSafeEqual(
      Buffer.from(providedSuffix.toLowerCase()),
      Buffer.from(expectedSuffix.toLowerCase())
    );
  }
}

export const urlShortenerService = new UrlShortenerService();
export { UrlType };
