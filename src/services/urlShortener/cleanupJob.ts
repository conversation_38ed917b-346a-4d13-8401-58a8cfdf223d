import { urlShortenerService } from '@/services/urlShortener';
import { createLogger } from '@/services/logger';

const logger = createLogger('UrlCleanupJob');

/**
 * Cleanup job for expired shortened URLs
 * Should be run as a scheduled job (e.g., daily via cron)
 */
export const cleanupExpiredUrls = async (): Promise<void> => {
  try {
    logger.info('Starting URL cleanup job...');
    
    const cleanedCount = await urlShortenerService.cleanupExpiredUrls();
    
    logger.info('URL cleanup job completed', { cleanedCount });
  } catch (error) {
    logger.error('Error during URL cleanup job', { error });
    throw error;
  }
};

// If run directly (for testing)
if (require.main === module) {
  cleanupExpiredUrls()
    .then(() => {
      console.log('Cleanup job completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Cleanup job failed:', error);
      process.exit(1);
    });
}
