import winston from "winston";
import { LoggingWinston } from "@google-cloud/logging-winston";
import { LogLevel } from "./types";
import { isGcpLoggingEnabled, isConsoleLoggingEnabled } from "./config";

// Winston log levels mapping (aligned with GCP severity levels)
// Note: GCP Logging Winston only supports standard levels, so we map 'fatal' to 'error' internally
const winstonLevels = {
  error: 0, // Maps to GCP ERROR (including fatal logs)
  warn: 1, // Maps to GCP WARNING
  info: 2, // Maps to GCP INFO
  debug: 3, // Maps to GCP DEBUG
};

// GCP severity level mapping for proper log categorization
const gcpSeverityMapping = {
  error: "ERROR",
  warn: "WARNING",
  info: "INFO",
  debug: "DEBUG",
};

// Winston colors for console output
const winstonColors = {
  error: "red",
  warn: "yellow",
  info: "cyan",
  debug: "gray",
};

winston.addColors(winstonColors);

// Create Winston logger instance
let logger: winston.Logger;

const createWinstonLogger = (): winston.Logger => {
  const transports: winston.transport[] = [];

  // Console transport for development
  if (isConsoleLoggingEnabled()) {
    transports.push(
      new winston.transports.Console({
        level: process.env.NODE_ENV === "production" ? "info" : "debug",
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(
            ({
              timestamp,
              level,
              message,
              logger,
              logLevel,
              stack,
              error,
              ...meta
            }) => {
              const loggerPrefix = logger ? `[${logger}] ` : "";

              // Enhanced metadata processing for console display
              let processedMeta = { ...meta };

              // Handle Error objects for better console display
              if (error instanceof Error) {
                processedMeta.error = {
                  name: error.name,
                  message: error.message,
                  stack: error.stack,
                };
              }

              // Look for other Error objects in meta
              Object.entries(processedMeta).forEach(([key, value]) => {
                if (value instanceof Error) {
                  processedMeta[key] = {
                    name: value.name,
                    message: value.message,
                    stack: value.stack,
                  };
                }
              });

              const metaStr = Object.keys(processedMeta).length
                ? ` ${JSON.stringify(processedMeta, null, 2)}`
                : "";

              // Include stack trace if present (for thrown errors)
              const stackStr = stack ? `\n${stack}` : "";

              return `${timestamp} ${level}: ${loggerPrefix}${message}${metaStr}${stackStr}`;
            }
          )
        ),
      })
    );
  }

  // Google Cloud Logging transport for production
  if (isGcpLoggingEnabled()) {
    try {
      console.log("Initializing Google Cloud Logging...");

      // Create a single transport for all levels instead of separate ones
      const loggingWinston = new LoggingWinston({
        logName: "aida-service",
        level: "debug", // Log all levels
        labels: {
          service: "aida-service",
          environment: process.env.NODE_ENV || "development",
        },
        // Add resource labels for better organization
        resource: {
          type: "cloud_run_revision",
          labels: {
            service_name: "aida-service",
            revision_name: process.env.K_REVISION || "unknown",
            configuration_name: "aida-service",
          },
        },
        // Let GCP handle structured logging natively - no custom string formatting
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }), // Capture stack traces
          // Transform the log entry structure for GCP
          winston.format((info) => {
            const { timestamp, level, message, stack, error, logger, ...meta } =
              info;

            // Map Winston levels to GCP severity
            // Handle fatal logs specially since they're mapped to error level
            let severity =
              gcpSeverityMapping[level as keyof typeof gcpSeverityMapping] ||
              level.toUpperCase();

            // Check if this is a fatal log (marked with severity in meta)
            if (meta.severity === "CRITICAL") {
              severity = "CRITICAL";
            }

            // Build the transformed log entry with proper typing
            // Preserve the logger namespace that was set by WinstonLogger
            const transformedInfo: any = {
              ...info,
              timestamp,
              severity,
              message,
              // Don't overwrite the logger field - preserve the namespace from WinstonLogger
              ...meta,
            };

            // Include stack trace if present
            if (stack) {
              transformedInfo.stack_trace = stack;
            }

            // Include error details if present
            if (error && error instanceof Error) {
              transformedInfo.error_details = {
                name: error.name,
                message: error.message,
                stack: error.stack,
              };
            }

            return transformedInfo;
          })()
        ),
      });

      transports.push(loggingWinston);
      console.log("✅ Google Cloud Logging initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize Google Cloud Logging:", error);
      // Continue with console logging only
    }
  } else {
    console.log("ℹ️ Google Cloud Logging disabled");
  }

  // Log the final configuration
  const consoleEnabled = isConsoleLoggingEnabled();
  const gcpEnabled = isGcpLoggingEnabled();

  console.log("📋 Logger Configuration:");
  console.log(
    `  Console Logging: ${consoleEnabled ? "✅ Enabled" : "❌ Disabled"}`
  );
  console.log(`  GCP Logging: ${gcpEnabled ? "✅ Enabled" : "❌ Disabled"}`);
  console.log(`  Environment: ${process.env.NODE_ENV || "development"}`);
  console.log("");

  return winston.createLogger({
    levels: winstonLevels,
    level: process.env.NODE_ENV === "production" ? "info" : "debug",
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.prettyPrint()
    ),
    transports,
    exitOnError: false,
  });
};

// Initialize logger
const getLogger = (): winston.Logger => {
  if (!logger) {
    logger = createWinstonLogger();
  }
  return logger;
};

// Create a logger wrapper that matches the existing API
export class WinstonLogger {
  private name?: string;
  private winstonLogger: winston.Logger;

  constructor(name?: string) {
    this.name = name;
    this.winstonLogger = getLogger();
  }

  private log(level: LogLevel, message: string, meta?: any) {
    const logData: any = {
      logger: this.name || "Application",
    };

    // Automatically detect and promote Error objects for proper GCP stack trace logging
    // Supports all these patterns:
    // - logger.error('message', error)                    -> Direct Error object
    // - logger.error('message', { error, context })      -> Error in meta.error
    // - logger.error('message', { someError, data })     -> Error in any meta property
    // - logger.error('message', { multiple, errors })    -> Multiple Error objects
    if (meta) {
      // Case 1: Direct Error object as meta (logger.error('message', error))
      if (meta instanceof Error) {
        logData.error = meta;
      }
      // Case 2: Error in meta.error property
      else if (meta.error instanceof Error) {
        const { error, ...restMeta } = meta;
        logData.error = error;
        Object.assign(logData, restMeta);
      }
      // Case 3: Regular meta object - look for any Error values
      else if (typeof meta === "object") {
        const errorKeys: string[] = [];
        const processedMeta: any = {};

        // Scan for Error objects in meta properties
        Object.entries(meta).forEach(([key, value]) => {
          if (value instanceof Error) {
            // Promote the first Error found to top-level for Winston
            if (!logData.error) {
              logData.error = value;
              errorKeys.push(key);
            } else {
              // Keep additional errors in meta with _error suffix
              processedMeta[`${key}_error_details`] = {
                name: value.name,
                message: value.message,
                stack: value.stack,
              };
            }
          } else {
            processedMeta[key] = value;
          }
        });

        Object.assign(logData, processedMeta);
      }
      // Case 4: Primitive values (string, number, etc.)
      else {
        logData.meta_value = meta;
      }
    }

    this.winstonLogger.log(level, message, logData);
  }

  debug = (message: string, meta?: any) => this.log("debug", message, meta);
  info = (message: string, meta?: any) => this.log("info", message, meta);
  warn = (message: string, meta?: any) => this.log("warn", message, meta);
  error = (message: string, meta?: any) => this.log("error", message, meta);
  fatal = (message: string, meta?: any) => {
    // Map fatal to error level for GCP compatibility, but add severity marker
    const fatalMeta = {
      ...meta,
      severity: "CRITICAL",
      logLevel: "fatal",
    };
    this.log("error", `[FATAL] ${message}`, fatalMeta);
  };

  stack = (err: unknown, level: LogLevel = "error", customMessage?: string) => {
    if (!err) return;

    const errorMessage =
      customMessage || (err instanceof Error ? err.message : String(err));

    // Log with the error as a top-level property for proper GCP formatting
    const logData: any = {
      logger: this.name || "Application",
    };

    if (err instanceof Error) {
      logData.error = err; // This will trigger the Winston error handling
    } else {
      logData.error_value = err; // For non-Error objects
    }

    this.winstonLogger.log(level, errorMessage, logData);
  };

  static create(name: string): WinstonLogger {
    return new WinstonLogger(name);
  }
}

// Export factory function for backward compatibility
export const createLogger = (name: string): WinstonLogger => {
  return WinstonLogger.create(name);
};
