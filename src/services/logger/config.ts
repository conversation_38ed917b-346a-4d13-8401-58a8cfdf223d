import { LogLevel, LogLevelConfig } from "./types";

export { LogLevel, LogLevelConfig };

export const LOG_LEVELS_CONFIG: Record<LogLevel, LogLevelConfig> = {
  debug: {
    level: "debug",
    logName: "aida-service-debug",
    description: "Detailed debug information for development",
    enabled: process.env.NODE_ENV !== "production",
  },
  info: {
    level: "info",
    logName: "aida-service-info",
    description: "General application information",
    enabled: true,
  },
  warn: {
    level: "warn",
    logName: "aida-service-warn",
    description: "Warning messages that don't break functionality",
    enabled: true,
  },
  error: {
    level: "error",
    logName: "aida-service-error",
    description: "Error messages that indicate problems",
    enabled: true,
  },
  fatal: {
    level: "fatal",
    logName: "aida-service-error", // Map to error for GCP compatibility
    description:
      "Fatal errors that require immediate attention (logged as ERROR with CRITICAL severity)",
    enabled: true,
  },
};

export const getEnabledLogLevels = (): LogLevel[] => {
  return Object.entries(LOG_LEVELS_CONFIG)
    .filter(([_, config]) => config.enabled)
    .map(([level]) => level as LogLevel);
};

export const getLogLevelConfig = (level: LogLevel): LogLevelConfig => {
  return LOG_LEVELS_CONFIG[level];
};

// Environment configuration
export const isGcpLoggingEnabled = (): boolean => {
  // Allow explicit override with environment variable
  if (process.env.GCP_LOGGING_ENABLED === "true") {
    return true;
  }

  // Default to true in production, false in development
  if (process.env.GCP_LOGGING_ENABLED === "false") {
    return false;
  }

  return process.env.NODE_ENV === "production";
};

export const isHttpLoggingEnabled = (): boolean => {
  return process.env.HTTP_LOGGING_ENABLED !== "false";
};

export const getHttpLoggingLevel = (): LogLevel => {
  const level = process.env.HTTP_LOGGING_LEVEL as LogLevel;
  return level && LOG_LEVELS_CONFIG[level] ? level : "info";
};

// Console logging configuration
export const isConsoleLoggingEnabled = (): boolean => {
  // Allow explicit override
  if (process.env.CONSOLE_LOGGING_ENABLED === "false") {
    return false;
  }

  if (process.env.CONSOLE_LOGGING_ENABLED === "true") {
    return true;
  }

  // Default behavior: disable console when GCP logging is enabled in development
  if (isGcpLoggingEnabled() && process.env.NODE_ENV !== "production") {
    return false;
  }

  // Always enable console in production (for debugging)
  return true;
};
