// Re-export types
export type { LogLevel, LogLevelConfig } from './types';

// Re-export Winston logger for backward compatibility
export { WinstonLogger as Log, createLogger } from './winston';

// Create default logger instance
import { WinstonLogger } from './winston';
import { LogLevel } from './types';
export const log = new WinstonLogger();

// Re-export SimpleLog type for backward compatibility
export type SimpleLog = Pick<WinstonLogger, LogLevel>;
