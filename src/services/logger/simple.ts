import CircularJson from "circular-json";

import type { LogLevel, SimpleLog } from ".";

const createSimpleLog =
  (l: LogLevel) =>
  (msg: string, condition: unknown = true) => {
    if (!condition) {
      return;
    }
    if (condition instanceof Error) {
      condition = condition.stack;
    } else if (typeof condition !== "boolean") {
      condition = CircularJson.stringify(condition);
    }
    if (typeof condition === "string") {
      msg = msg + "\n" + condition;
    }
    console[l](msg);
  };

// for non-nodejs env such as react native or browser

export const simpleLog: SimpleLog = {
  debug: createSimpleLog("debug"),
  info: createSimpleLog("info"),
  warn: createSimpleLog("warn"),
  error: createSimpleLog("error"),
  fatal: createSimpleLog("fatal"),
};
