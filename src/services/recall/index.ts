import {
  recallAiApiBaseUrlV1,
  recallAiApiBaseUrlV2,
  recallAiToken,
} from "@/config";
import { PickRequired } from "@/types";
import {
  AppCalendarEvent,
  BotChangeStatusCode,
  CalendarEventList,
  CreateBotBody,
  RBot,
  RecallCalendarDetail,
  ScheduleBotData,
} from "@/types/recall";
import type { AxiosInstance } from "axios";
import axios from "axios";
import type {
  Bot,
  BotEvent,
  CancelablePromise,
  TranscriptParagraph,
  TranscriptWord,
} from "recall-ai";
import { log } from "../logger";
import { qsStableStringify } from "@/utils";

type BotTranscipts = {
  speaker_id: number;
  words: Array<
    {
      language: string;
      confidence: number;
    } & TranscriptWord
  >;
} & Omit<TranscriptParagraph, "words">;

class RecallApiClient {
  private recallV1: AxiosInstance;
  private recallV2: AxiosInstance;

  constructor() {
    this.recallV1 = axios.create({
      baseURL: recallAiApiBaseUrlV1,
      headers: {
        authorization: `token ${recallAiToken}`,
        "content-type": "application/json",
      },
      timeout: 10000,
    });

    this.recallV2 = axios.create({
      baseURL: recallAiApiBaseUrlV2,
      headers: {
        authorization: `${recallAiToken}`,
        "content-type": "application/json",
      },
      timeout: 10000,
    });
  }

  createBot = (payload: PickRequired<Partial<CreateBotBody>, "meeting_url">) =>
    this.recallV1.post<Bot>("/bot", payload);

  retrieveBot = (id: string) => this.recallV1.get<RBot>(`/bot/${id}`).then((res) => res.data).catch((error) => {
    log.error(`Error retrieving bot ${id}: ${error}`);
    return null;
  });

  removeBotFromCall = (id: string) =>
    this.recallV1.post<RBot>(`/bot/${id}/leave_call`);

  getBotTranscipts = (botId: string) =>
    this.recallV1
      .get(`/bot/${botId}/transcript`)
      .then((res) => res.data) as CancelablePromise<Array<BotTranscipts>>;

  v2_retrieveCalendarById = (calendarId: string) =>
    this.recallV2
      .get<RecallCalendarDetail>(`calendars/${calendarId}`)
      .then((r) => r.data);
  v2_retrieveCalendarEventById = (calendarEventId: string) =>
    this.recallV2
      .get<AppCalendarEvent>(`calendar-events/${calendarEventId}`)
      .then((r) => r.data)
      .catch((e) => {
        log.error(`Error retrieving calendar event ${calendarEventId}: ${e}`);
        return null;
      });
  v2_retrieveCalendarEvents = (calendarId: string, updated_at__gte: string) => {
    const query = qsStableStringify(
      {
        calendar_id: calendarId,
        updated_at__gte: updated_at__gte,
      },
      {
        arrayFormat: "comma",
      }
    );
    return this.recallV2
      .get(`calendar-events?${query}`)
      .then((res) => res.data) as CancelablePromise<CalendarEventList>;
  };

  scheduleBot = (calendarEventId: string, requestBody: ScheduleBotData) =>
    this.recallV2
      .post(`/calendar-events/${calendarEventId}/bot`, {
        deduplication_key: requestBody.deduplication_key,
        bot_config: requestBody.bot_config,
      })
      .then((res) => res.data as AppCalendarEvent);

  botLeaveCall = (botId: string): Promise<void> =>
    this.recallV1.post(`/bot/${botId}/leave_call`);
}

export const removeBotFromCall = async (botId?: string | null | undefined) => {
try {
  if (!botId) {
    return;
  }

  const bot = await recallAiClient.retrieveBot(botId);
  if (!bot) {
    log.error(`Bot ${botId} not found`);
    return;
  }

  const lastBotStatus: BotEvent =
    bot.status_changes[bot.status_changes.length - 1];

  if (
    lastBotStatus?.code != BotChangeStatusCode.JoiningCall &&
    lastBotStatus?.code != BotChangeStatusCode.InWaitingRoom &&
    lastBotStatus?.code != BotChangeStatusCode.InCallNotRecording
  ) {
    log.error(`Bot ${botId} is not in call`);
    return;
  }

    const response = await recallAiClient.botLeaveCall(bot.id);
    log.info(`Bot ${botId} left call`);
    return response;
  } catch (error) {
    log.error(`Error removing bot from call ${botId}: ${error}`);
}
};

export const recallAiClient = new RecallApiClient();
