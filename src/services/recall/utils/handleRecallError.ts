import { RecallError } from '@/types/recall';
import { log } from '@/services/logger';

export const handleRecallError = (error: unknown): Error => {
  try {
    const recallError = error as RecallError;
    
    // Check if it's a Recall API error with response data
    if (recallError.response?.data?.errors) {
      const errorKeys = Object.keys(recallError.response.data.errors);
      if (errorKeys.length > 0) {
        const firstErrorKey = errorKeys[0];
        const errorMessage = recallError.response.data.errors[firstErrorKey][0];
        log.error(`Recall API Error: ${errorMessage}`);
        return new Error(errorMessage);
      }
    }

    // If it's not a structured Recall error, return the original error
    log.error('Unexpected Recall Error:', error);
    return error instanceof Error ? error : new Error('Unknown Recall error occurred');
  } catch (e) {
    // If error handling itself fails, return a generic error
    log.error('Error handling Recall error:', e);
    return new Error('Failed to process Recall error');
  }
}; 