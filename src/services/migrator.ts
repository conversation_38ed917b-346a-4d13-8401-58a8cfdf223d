import { QueryInterface, Sequelize, DataTypes } from "sequelize";
import { readdir } from "fs/promises";
import { join } from "path";
import { db } from "@/schemas";
import { log } from "@/services/logger";

interface Migration {
  up: (queryInterface: QueryInterface, sequelize: Sequelize) => Promise<void>;
  down: (queryInterface: QueryInterface, sequelize: Sequelize) => Promise<void>;
}

export class Migrator {
  private readonly migrationsPath = join(process.cwd(), "dist/migrations");

  async createMigrationsTable(): Promise<void> {
    const queryInterface = db.getQueryInterface();

    const tableExists = await queryInterface.tableExists("migrations");
    if (!tableExists) {
      await queryInterface.createTable("migrations", {
        name: {
          type: DataTypes.STRING,
          primaryKey: true,
          allowNull: false,
        },
        executed_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
        },
      });
      log.info("Created migrations table");
    }
  }

  async getExecutedMigrations(): Promise<string[]> {
    const [results] = await db.query(
      "SELECT name FROM migrations ORDER BY name"
    );
    return (results as any[]).map((row) => row.name);
  }

  async getPendingMigrations(): Promise<string[]> {
    const files = await readdir(this.migrationsPath);
    const migrationFiles = files
      .filter((file) => file.endsWith(".js"))
      .map((file) => file.replace(".js", ".ts")) // Convert back to .ts names for tracking
      .sort();

    const executed = await this.getExecutedMigrations();
    return migrationFiles.filter((file) => !executed.includes(file));
  }

  async runMigrations(): Promise<void> {
    await this.createMigrationsTable();

    const pending = await this.getPendingMigrations();

    if (pending.length === 0) {
      log.info("No pending migrations");
      return;
    }

    log.info(`Running ${pending.length} migration(s)...`);

    for (const migrationFile of pending) {
      await this.runMigration(migrationFile);
    }

    log.info("All migrations completed successfully");
  }

  private async runMigration(filename: string): Promise<void> {
    // Always use compiled .js files from dist/migrations
    const migrationPath = join(
      this.migrationsPath,
      filename.replace(".ts", ".js")
    );

    try {
      log.info(`Running migration: ${filename}`);

      // Dynamic import to load compiled migration
      const migration: Migration = await import(migrationPath);

      // Validate migration has required functions
      if (typeof migration.up !== "function") {
        throw new Error(`Migration ${filename} missing up() function`);
      }
      if (typeof migration.down !== "function") {
        throw new Error(`Migration ${filename} missing down() function`);
      }

      const queryInterface = db.getQueryInterface();

      // Run the migration in a transaction
      await db.transaction(async (transaction) => {
        await migration.up(queryInterface, db);

        // Record the migration as executed with explicit timestamp
        await db.query(
          "INSERT INTO migrations (name, executed_at) VALUES (?, NOW())",
          {
            replacements: [filename],
            transaction,
          }
        );
      });

      log.info(`Migration completed: ${filename}`);
    } catch (error) {
      log.error(`Migration failed: ${filename}`, error);
      throw error;
    }
  }

  async rollbackMigration(filename?: string): Promise<void> {
    await this.createMigrationsTable();

    const executed = await this.getExecutedMigrations();

    if (executed.length === 0) {
      log.info("No migrations to rollback");
      return;
    }

    // If no specific migration provided, rollback the last one
    const targetMigration = filename || executed[executed.length - 1];

    if (!executed.includes(targetMigration)) {
      throw new Error(`Migration ${targetMigration} was not executed`);
    }

    // Always use compiled .js files from dist/migrations
    const migrationPath = join(
      this.migrationsPath,
      targetMigration.replace(".ts", ".js")
    );

    try {
      log.info(`Rolling back migration: ${targetMigration}`);

      const migration: Migration = await import(migrationPath);

      // Validate migration has required functions
      if (typeof migration.up !== "function") {
        throw new Error(`Migration ${targetMigration} missing up() function`);
      }
      if (typeof migration.down !== "function") {
        throw new Error(`Migration ${targetMigration} missing down() function`);
      }

      const queryInterface = db.getQueryInterface();

      await db.transaction(async (transaction) => {
        await migration.down(queryInterface, db);

        // Remove from migrations table
        await db.query("DELETE FROM migrations WHERE name = ?", {
          replacements: [targetMigration],
          transaction,
        });
      });

      log.info(`Rollback completed: ${targetMigration}`);
    } catch (error) {
      log.error(`Rollback failed: ${targetMigration}`, error);
      throw error;
    }
  }
}
