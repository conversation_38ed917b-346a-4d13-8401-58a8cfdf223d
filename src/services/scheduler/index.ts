import schedule from 'node-schedule';
import { log } from '@/services/logger';
import { DATA_RETENTION_CLEANUP_SCHEDULE } from '@/config';
import { RetentionService } from '@/services/retention';

export class SchedulerService {
  private retentionService: RetentionService;

  constructor() {
    this.retentionService = new RetentionService();
  }

  public start() {
    // Schedule retention cleanup
    schedule.scheduleJob(DATA_RETENTION_CLEANUP_SCHEDULE, async () => {
      log.info('Running scheduled retention cleanup');
      try {
        await this.retentionService.run();
      } catch (error) {
        log.error('Error in scheduled retention cleanup:', error);
      }
    });

    // this.retentionService.run();

    log.info('Scheduler service started');
  }
} 