import type { Options as SequelizeOptions } from "sequelize";
import S, { Sequelize } from "sequelize";

import type { SimpleLog } from "@/services/logger";
import type { Db, Options as PrivateOptions } from "./Db";
import { extendDb } from "./extendDb";
import { logging } from "./logging";

export * from "sequelize";

export type Options = PrivateOptions & {
  logging?: boolean;
  benchmark?: boolean;
  log?: SimpleLog;
} & Required<
    Pick<
      SequelizeOptions,
      "dialect" | "host" | "username" | "password" | "database"
    >
  >;

export const createNodejsDb = (options: Options) => {
  if (!["mysql", "mariadb", "sqlite", "postgres"].includes(options.dialect)) {
    throw new Error(`Dialect ${options.dialect} is not supported`);
  }

  // Cloud Run optimized connection pool settings
  const isCloudRun = process.env.K_SERVICE !== undefined;
  const poolConfig = isCloudRun
    ? {
        // Cloud Run optimized settings
        max: 5, // Maximum number of connections in pool
        min: 0, // Minimum number of connections in pool
        acquire: 30000, // Maximum time to get connection (30s)
        idle: 10000, // Maximum time connection can be idle (10s)
        evict: 1000, // Check for idle connections every 1s
      }
    : {
        // Default settings for other environments
        max: 10,
        min: 0,
        acquire: 60000,
        idle: 10000,
      };

  const db = new Sequelize({
    ...options,
    pool: poolConfig,
    dialectOptions: {
      // Enable keep-alive for better connection stability
      keepAlive: true,
      keepAliveInitialDelayMillis: 0,
    },
    define: {
      freezeTableName: true,
      timestamps: true,
      updatedAt: false,
      paranoid: false,
    },
    logging: logging(options),
    benchmark: options.benchmark,
  }) as Db;

  extendDb(S, db, options);

  return db;
};
