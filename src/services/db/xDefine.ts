import { ulid } from "ulidx";

import type { Db } from "./Db";
import { extendModel } from "./extendModel";
import type { Attributes, Model } from "./Model";
import { ModelOptions } from "sequelize";

export const xDefine = <T extends Attributes>(
  S: any,
  db: Db,
  name: string,
  attrs: T,
  options?: ModelOptions
) => {
  Object.assign(attrs, {
    id: {
      type: "STRING",
      primaryKey: true,
      defaultValue: ulid,
      unique: true,
    },
  });
  Object.entries(attrs).forEach(([k, a]) => {
    if (!a.allowNull) {
      a.allowNull = false;
    }
    if (
      a.allowNull &&
      !k.endsWith("Id") &&
      !(a.type === "DATE" || a.type === "JSON") &&
      !db.$options?.ignoreAllowNull
    ) {
      db.$log?.warn(
        `Found allowNull=true in ${name}.${k} type=${a.type}, only allow null in id/date/json`
      );
    }
    a.type = S.DataTypes[a.type] as any;
  });
  const m = db.define(name, attrs, options) as any;
  extendModel(S, m);
  return m as Model<T>;
};
