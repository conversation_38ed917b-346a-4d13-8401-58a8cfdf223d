import type { Options } from ".";
import type { Db } from "./Db";
import { prepareAssociations } from "./prepareAssociations";

import { xDefine } from "./xDefine";

export const extendDb = (S: any, db: Db, options: Options) => {
  // override
  db.$options = options;
  db.xDefine = (...args) => xDefine(S, db, ...args);

  // prepare associations
  // must require all models before calling this
  db.prepareAssociations = () => prepareAssociations(db);

  // syntax highlight
  db.sql = String.raw;

  // log
  db.$log = options.log;
  db.S = S;
};
