import type { ModelOptions, Sequelize } from "sequelize";
import type { Literal } from "sequelize/types/utils";

import type { Attributes, Model, Models } from "./Model";
import type { SimpleLog } from "@/services/logger";

export type Options = {
  ignoreAllowNull?: boolean;
  ignoreAllAssociations?: boolean;
};

export interface Db extends Sequelize {
  // override
  $options: Options;
  models: Models;
  xDefine<T extends Attributes = any>(name: string, attrs: T, options?: ModelOptions): Model<T>;

  // prepare associations
  // must require all models before calling this
  prepareAssociations(): void;

  // raw sql quote identifiers
  q(...idens: string[]): string;
  qLiteral(...idens: string[]): Literal;
  // raw sql unquote identifiers
  uq(name: string): string;

  // syntax highlight
  sql: (typeof String)["raw"];

  $log?: SimpleLog;
  S: any;
}
