import { uniq } from "lodash";
import type { WhereOptions } from "sequelize";
import { ulid } from "ulidx";

import type { Model } from "./Model";
import { arrToMap, qsStableStringify } from "../../utils";
import { IntentionalError } from "../../utils/errors";

export const extendModel = (S: any, m: Model) => {
  //
  // CREATE

  m.xCreate = async (record) => {
    if (!record.id) {
      record.id = ulid();
    }
    const created = await m
      .create(record, { returning: true })
      .then((r) => r.toJSON());
    // sqlite might behave differently
    return created || m.xFind1ById(record.id);
  };
  m.xBulkCreate = async (records) => {
    if (!records.length) {
      return [];
    }
    records.forEach((r) => {
      if (!r.id) {
        r.id = ulid();
      }
    });
    // sqlite might behave differently
    if (m.sequelize.getDialect() === "sqlite") {
      return Promise.all(records.map((r) => m.xCreate(r)));
    }
    return m.bulkCreate(records).then((arr) => arr.map((r) => r.toJSON()));
  };
  m.xBulkUpsert = async (records, o = {}) => {
    if (!records.length) {
      return [];
    }
    // prepare options
    // get the values for each key
    const keys = o.keys || ["id"];
    const values = keys.map((k) => uniq(records.map((r) => r[String(k)])));
    // find existed rows in db based on those keys and their values
    const where: { [k: string]: any } = {};
    keys.forEach((k, i) => {
      where[k] = { [S.Op.in]: values[i] };
    });
    const existing = await m.xFind(where);
    // build key-id by using stringify of the value
    // then convert the existed rows into a map of their id
    const key = (r: any) =>
      qsStableStringify(arrToMap(keys, undefined, (k) => r[k]));
    const existingsMapId = arrToMap(existing, (r) => key(r), "id");
    // check if ignore duplicates and perform bulk create
    records.forEach((r) => {
      r.id = r.id || existingsMapId[key(r)];
    });
    const create = records.filter((r) => !existingsMapId[key(r)]);
    const update = records.filter((r) => existingsMapId[key(r)]);
    let upserted = await m.xBulkCreate(create);
    if (o.updateOnDuplicate && update.length) {
      await Promise.all(update.map((u) => m.xUpdateById(u.id as string, u)));
      const updated = await m.xFind({
        id: { [S.Op.in]: update.map((u) => u.id) },
      });
      upserted = upserted.concat(updated);
    }
    const upsertedMapId = arrToMap(upserted, (r) => key(r), "id");
    // then set again the new id from db to records
    records.forEach((r) => {
      r.id = r.id || upsertedMapId[key(r)];
    });
    // check if any record has no data returned from db
    const existingMap = arrToMap(existing, "id", (r) => r);
    const upsertedMap = arrToMap(upserted, "id", (r) => r);
    return records.map((r) => {
      const d = r.id && (existingMap[r.id] || upsertedMap[r.id]);
      if (!d) {
        const missing = !r.id ? "id" : "data";
        throw new Error(`Missing ${missing} for ${key(r)}`);
      }
      return d;
    });
  };

  //
  // READ

  m.xFind = (w, options) => {
    const where = (w || {}) as WhereOptions;
    return m
      .findAll({ ...options, where })
      .then((arr) => arr.map((r) => r.toJSON()));
  };
  m.xFindBy = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xFind(w);
  };
  m.xFindById = (id) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xFind(w);
  };

  m.xFind1 = (w) => {
    const where = (w || {}) as WhereOptions;
    return m.findOne({ where }).then((r) => r?.toJSON());
  };
  m.xFind1By = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xFind1(w);
  };
  m.xFind1ById = (id) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xFind1(w);
  };

  m.xMustFind1 = async (w) => {
    const r = await m.xFind1(w);
    return mustCheckReturn(r, m, w);
  };
  m.xMustFind1By = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xMustFind1(w);
  };
  m.xMustFind1ById = (id) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xMustFind1(w);
  };

  m.xExists = (w) => {
    const where = (w || {}) as WhereOptions;
    return m.findOne({ where }).then((i) => !!i);
  };
  m.xExistsBy = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xExists(w);
  };
  m.xExistsById = (id) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xExists(w);
  };

  m.xMustExists = async (w) => {
    const r = await m.xExists(w);
    mustCheckReturn(r, m, w);
  };
  m.xMustExistsBy = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xMustExists(w);
  };
  m.xMustExistsById = (id) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xMustExists(w);
  };

  m.xCount = (w) => {
    const where = (w || {}) as WhereOptions;
    return m.count({ where });
  };
  m.xCountBy = (k, v) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xCount(w);
  };
  m.xCountById = (ids) => m.xCountBy("id", ids);

  m.xSum = (colName, w) => {
    const where = (w || {}) as WhereOptions;
    return m
      .findAll({
        where,
        attributes: [[S.fn("sum", S.col(colName)), "sum"]],
      })
      .then((arr) => arr.map((r) => r.toJSON()))
      .then((r) => Number(r[0].sum) || 0);
  };

  //
  // UPDATE

  m.xUpdate = (w, data) => {
    const where = (w || {}) as WhereOptions;
    return m.update(data, { where }).then((r) => r[0]);
  };
  m.xUpdateBy = (k, v, data) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xUpdate(w, data);
  };
  m.xUpdateById = (id, data) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xUpdate(w, data);
  };

  //
  // DELETE (Soft Delete)

  m.xDestroy = (w, deletedByUserId) => {
    const where = (w || {}) as WhereOptions;
    const rawAttributes = m.getAttributes();
    
    // Check if the model has soft delete fields
    const hasDeletedAt = rawAttributes && rawAttributes.deletedAt;
    const hasDeletedBy = rawAttributes && rawAttributes.deletedBy;
    const hasDeleted = rawAttributes && rawAttributes.isDeleted;

    if (hasDeletedAt || hasDeletedBy || hasDeleted) {
      // Perform soft delete by updating the deletedAt and deletedBy fields
      const softDeleteData: any = {};
      if (hasDeletedAt) {
        softDeleteData.deletedAt = new Date();
      }
      if (hasDeletedBy && deletedByUserId) {
        softDeleteData.deletedBy = deletedByUserId;
      }
      if (hasDeleted) {
        softDeleteData.isDeleted = true;
      }
      return m.update(softDeleteData, { where }).then((r) => r[0]);
    } else {
      // Fallback to hard delete if model doesn't have soft delete fields
      return m.destroy({ where });
    }
  };
  m.xDestroyBy = (k, v, deletedByUserId) => {
    const w = mustBuildWhereBy(S, k, v);
    return m.xDestroy(w, deletedByUserId);
  };
  m.xDestroyById = (id, deletedByUserId) => {
    const w = mustBuildWhereBy(S, "id", id);
    return m.xDestroy(w, deletedByUserId);
  };
};

const mustBuildWhereBy = (
  S: any,
  k: string | number | symbol,
  v: unknown | unknown[],
) => {
  if (!v) {
    throw new Error(`Empty value in where by ${k as string}`);
  }
  const w = Array.isArray(v) ? { [S.Op.in]: v } : v;
  return { [k]: w };
};
const mustCheckReturn = <
  M extends Model,
  R extends M["$M"] | boolean | undefined,
>(
  r: R,
  m: M,
  w?: object,
) => {
  if (!r) {
    w = w || { where: true };
    const q = qsStableStringify(w, { delimiter: " " });
    throw new IntentionalError(
      "Data not found",
      `${m.name} ${q} was not found in database`,
    );
  }
  return r;
};
