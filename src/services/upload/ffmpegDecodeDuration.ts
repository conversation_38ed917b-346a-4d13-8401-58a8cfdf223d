import { log } from "@/services/logger";
import { execAsync } from "./utils";

export const ffmpegDecodeDuration = async (fileUrl: string) => {
  // Optimized: Use faster approach with better error handling and timeout
  // Get duration from multiple sources in one efficient call
  const cmd = `ffprobe -v error -select_streams v:0 -show_entries format=duration:stream=duration -of csv=p=0:s=x "${fileUrl}"`;

  try {
    const { stderr, stdout } = await execAsync(cmd);
    const output = stdout.trim();

    if (output) {
      // Parse the CSV output: format_duration x stream_duration
      const parts = output.split("x");

      // Try stream duration first (often more accurate for video files)
      if (parts[1] && parts[1] !== "N/A") {
        const streamDuration = parseFloat(parts[1]);
        if (
          !isNaN(streamDuration) &&
          isFinite(streamDuration) &&
          streamDuration > 0
        ) {
          return streamDuration;
        }
      }

      // Fallback to format duration
      if (parts[0] && parts[0] !== "N/A") {
        const formatDuration = parseFloat(parts[0]);
        if (
          !isNaN(formatDuration) &&
          isFinite(formatDuration) &&
          formatDuration > 0
        ) {
          return formatDuration;
        }
      }
    }

    log.warn(
      "[ffmpegDecodeDuration] No valid duration from optimized ffprobe, falling back to full decode"
    );
    return await decodeFullDuration(fileUrl);
  } catch (err) {
    log.error("[ffmpegDecodeDuration] Error getting duration:", {
      fileUrl,
      error: err,
    });
    return await decodeFullDuration(fileUrl);
  }
};

// Optimized fallback method for when quick duration check fails
const decodeFullDuration = async (fileUrl: string) => {
  // Use progress reporting for better performance monitoring and faster processing
  const cmd = `ffmpeg -hide_banner -loglevel error -progress pipe:2 -i "${fileUrl}" -f null -`;

  try {
    const { stderr } = await execAsync(cmd);

    // Parse progress output for more accurate duration extraction
    const progressLines = stderr.split("\n");
    let lastTimeStr = "";

    // Look for the last 'out_time' or 'time' entry which gives us the final duration
    for (const line of progressLines) {
      if (line.startsWith("out_time=") || line.startsWith("time=")) {
        lastTimeStr = line.split("=")[1];
      }
    }

    if (lastTimeStr) {
      // Parse time format: HH:MM:SS.microseconds or just seconds
      if (lastTimeStr.includes(":")) {
        const timeParts = lastTimeStr.split(":");
        if (timeParts.length >= 3) {
          const hours = parseInt(timeParts[0]) || 0;
          const minutes = parseInt(timeParts[1]) || 0;
          const seconds = parseFloat(timeParts[2]) || 0;
          return hours * 3600 + minutes * 60 + seconds;
        }
      } else {
        // Direct seconds format
        const duration = parseFloat(lastTimeStr);
        if (!isNaN(duration) && isFinite(duration)) {
          return duration;
        }
      }
    }

    // Legacy fallback: parse from stderr using regex (less reliable)
    const matches = Array.from(
      stderr.matchAll(/time=(\d+):(\d+):(\d+)\.(\d+)/g)
    );
    if (matches.length > 0) {
      const match = matches[matches.length - 1];
      const h = Number(match[1]);
      const m = Number(match[2]);
      const s = Number(match[3]);
      const ms = Number(match[4].padEnd(3, "0").substring(0, 3));
      return h * 60 * 60 + m * 60 + s + ms / 1000;
    }
  } catch (err) {
    log.error("[decodeFullDuration] Error:", { fileUrl, error: err });
  }

  return undefined;
};
