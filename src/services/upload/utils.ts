import { exec } from "child_process";
import { promisify } from "util";

// Enhanced execAsync with timeout support for better performance and reliability
const execAsyncBase = promisify(exec);

export const execAsync = (command: string, timeout: number = 60000) => {
  return execAsyncBase(command, {
    timeout,
    maxBuffer: 10 * 1024 * 1024, // 10MB buffer for large outputs
    encoding: "utf8",
  });
};

export const cleanLog = (...std: string[]) =>
  std
    .filter((s) => s)
    .map((s) => s.trim())
    .filter((s) => s)
    .join("\n") || true;
