import { log } from "@/services/logger";
import fs from "fs-extra";

import { cleanLog, execAsync } from "./utils";
import { gcsUpload } from "../storage";
import { apiDevUploadOutput, gcsResourceFolder } from "@/config";
import path from "path";

export const ffmpegCreateThumbnail = async (
  fileUrl: string,
  originalName: string,
  duration: number
): Promise<string | null> => {
  const { name: fileName } = path.parse(originalName);
  const uploadFileName = `${gcsResourceFolder}/${fileName}-thumbnail.png`;
  const pngFilePath = `${apiDevUploadOutput}/${fileName}.png`;

  // Optimized thumbnail generation: better seek position and faster encoding
  const seekTime =
    duration > 36 ? "00:00:09.000" : `00:00:${(duration / 4).toFixed(3)}`;

  // Enhanced command with better performance options:
  // - Use faster seeking (-ss before -i for input seeking)
  // - Optimize for single frame extraction
  // - Use faster pixel format and scaling
  const cmd = `ffmpeg -hide_banner -loglevel error -ss ${seekTime} -i "${fileUrl}" -vframes 1 -vf "scale=320:-1:flags=fast_bilinear" -f image2 -update 1 -y "${pngFilePath}"`;

  try {
    // Use shorter timeout for thumbnail generation (30 seconds should be enough)
    const { stderr, stdout } = await execAsync(cmd, 30000);
    log.debug(
      "[ffmpegCreateThumbnail] Command output:",
      cleanLog(stdout, stderr)
    );

    // Check if file exists and has valid size
    if (!fs.existsSync(pngFilePath)) {
      throw new Error("Thumbnail file was not created");
    }

    const stats = await fs.stat(pngFilePath);
    if (stats.size === 0) {
      throw new Error("Thumbnail file is empty");
    }

    const publicUrl = await gcsUpload(
      pngFilePath,
      uploadFileName,
      "image/png", // Use correct MIME type
      true
    );

    // Clean up local file
    await fs
      .rm(pngFilePath)
      .catch((err) =>
        log.error("[ffmpegCreateThumbnail] Error cleaning up thumbnail:", err)
      );

    return publicUrl;
  } catch (err) {
    log.error("[ffmpegCreateThumbnail] Error creating thumbnail:", {
      fileUrl,
      originalName,
      duration,
      error: err,
    });

    // Clean up any partial files
    await fs.rm(pngFilePath).catch(() => {});
  }

  return null;
};
