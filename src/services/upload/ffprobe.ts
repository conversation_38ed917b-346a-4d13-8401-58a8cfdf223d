import type { Resource } from "@/schemas/resource/Resource.model";
import { isVideo } from "@/utils";
import { log } from "@/services/logger";
import { ffmpegCreateThumbnail } from "./ffmpegCreateThumbnail";
import { ffmpegDecodeDuration } from "./ffmpegDecodeDuration";
import { cleanLog, execAsync } from "./utils";

export const ffprobe = async (
  fileUrl: string,
  originalName: string,
  mustGetDuration: boolean
) => {
  // Optimized command: get only essential format info and stream info in one call
  // Added stream info to get more metadata if available, removed unnecessary selectors
  const cmd = `ffprobe -hide_banner -loglevel error -show_error -show_format -show_streams -select_streams v:0 -print_format json -i "${fileUrl}"`;

  try {
    let { stderr, stdout } = await execAsync(cmd);
    stdout = stdout.trim();
    stderr = stderr.trim();

    // Early return if no valid output
    if (!stdout && !stderr) {
      throw new Error("No output from ffprobe");
    }

    const str = stdout.startsWith("{")
      ? stdout
      : stderr.startsWith("{")
      ? stderr
      : "";
    if (!str) {
      throw new Error("Invalid json output");
    }

    // Parse the complete ffprobe output
    const json: {
      format?: {
        duration?: string | number;
        size?: string;
        bit_rate?: string;
      };
      streams?: Array<{
        duration?: string | number;
        codec_type?: string;
      }>;
    } = JSON.parse(str);

    const r: Pick<Resource, "ffprobe"> &
      Partial<Pick<Resource, "duration" | "thumbnailUrl">> = {
      ffprobe: {
        format: {
          duration: json.format?.duration,
          size: json.format?.size,
          bit_rate: json.format?.bit_rate,
        },
      },
    };

    // Optimized duration extraction: try multiple sources in order of reliability
    let duration: number | undefined;

    // Try format duration first (most reliable)
    if (json.format?.duration) {
      const formatDuration = Number(json.format.duration);
      if (
        !isNaN(formatDuration) &&
        isFinite(formatDuration) &&
        formatDuration > 0
      ) {
        duration = formatDuration;
      }
    }

    // If format duration failed, try stream duration (fallback)
    if (!duration && json.streams?.length) {
      const videoStream =
        json.streams.find((s) => s.codec_type === "video") || json.streams[0];
      if (videoStream?.duration) {
        const streamDuration = Number(videoStream.duration);
        if (
          !isNaN(streamDuration) &&
          isFinite(streamDuration) &&
          streamDuration > 0
        ) {
          duration = streamDuration;
        }
      }
    }

    // Set duration if we found it
    if (duration) {
      r.duration = duration;
    } else if (mustGetDuration) {
      // Only use the expensive fallback if absolutely necessary
      r.duration = await ffmpegDecodeDuration(fileUrl);
    }

    // Generate thumbnail only if we have duration and it's a video
    // This can potentially run in parallel with other operations
    if (r.duration && isVideo(originalName)) {
      r.thumbnailUrl = await ffmpegCreateThumbnail(
        fileUrl,
        originalName,
        r.duration
      );
    }

    return r;
  } catch (err) {
    log.error("[ffprobe] Error processing file:", { fileUrl, error: err });
    throw err; // Re-throw to handle in the calling function
  }
};
