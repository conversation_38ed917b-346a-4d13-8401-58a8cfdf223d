import { agentTrackingService } from "./tracking.service";
import ConversationModel, {
  type Conversation,
} from "@/schemas/agent/Conversation.model";
import ChatMessageModel, {
  ChatUserRole,
} from "@/schemas/agent/ChatMessage.model";
import { getContextManager } from "./context";
import { FileProcessingValidator } from "./tools/FileProcessingValidator";
import { conversationImageService } from "./image/ConversationImageService";
import { AgentStreamService, createAgentStreamService } from "./stream";
import { conversationConversionService } from "./conversion.service";
import { createLogger } from "@/services/logger";
import type {
  RequestContext,
  FileProcessingReport,
  ProgressReporter,
} from "@/types/agent";
import type { CreateMessageRequest } from "@/validators/agent.validators";
import type { Part, Tool, GenerateContentResponse } from "@google/genai";
import { createErrorResponse, ApiResponse } from "@/utils/response";
import { v4 as uuidv4 } from "uuid";
import { getGoogleGenAI } from "@/config/googleGenAI";
import { agentUtilsService } from "./utils";
import { DEFAULT_MODEL } from "@/config/models";
import { FunctionCallingConfigMode } from "@google/genai";
import { RequestExecutor } from "./stream/RequestExecutor";

const logger = createLogger("AgentService");

// Standard errors for consistent handling
export class AgentError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode: number = 500
  ) {
    super(message);
    this.name = "AgentError";
  }
}

export class ValidationError extends AgentError {
  constructor(message: string) {
    super(message, "VALIDATION_ERROR", 400);
  }
}

export class NotFoundError extends AgentError {
  constructor(message: string) {
    super(message, "NOT_FOUND", 404);
  }
}

export class ProcessingError extends AgentError {
  constructor(message: string) {
    super(message, "PROCESSING_ERROR", 500);
  }
}

class AgentService {
  private streamService: AgentStreamService;
  private fileProcessingValidator: FileProcessingValidator;

  constructor() {
    // Initialize with multi-model RequestExecutor
    try {
      const requestExecutor = new RequestExecutor();
      this.streamService = new AgentStreamService(requestExecutor);
      logger.info("AgentService initialized with multi-provider support");
    } catch (error) {
      logger.warn("Failed to initialize multi-provider system, falling back to Google AI only", {
        error: error.message,
      });
      // Fall back to direct Google AI (no RequestExecutor)
      this.streamService = new AgentStreamService();
    }
    this.fileProcessingValidator = new FileProcessingValidator();
  }

  // ============================================================================
  // CONVERSATION MANAGEMENT
  // ============================================================================

  /**
   * Creates a new conversation for a user
   */
  async createConversation(
    userId: string,
    projectId?: string
  ): Promise<Conversation> {
    try {
      const conversation = await ConversationModel.create({
        createdById: userId,
        projectId,
      });

      logger.info("Conversation created successfully", {
        conversationId: conversation.get("id"),
        userId,
        projectId,
      });

      // Track conversation creation
      agentTrackingService.trackConversationCreated(
        userId,
        conversation.get("id") as string,
        projectId
      );

      return conversation.get({ plain: true });
    } catch (error) {
      logger.error("Failed to create conversation", {
        error,
        userId,
        projectId,
      });

      agentTrackingService.trackError(
        userId,
        "api",
        error instanceof Error
          ? error.message
          : "Unknown error in createConversation",
        { method: "createConversation" }
      );

      throw new ProcessingError("Failed to create conversation");
    }
  }

  // ============================================================================
  // MESSAGE PROCESSING
  // ============================================================================

  /**
   * Creates a message and generates a streaming AI response
   */
  async createMessageStream(
    conversationId: string,
    userId: string,
    body: CreateMessageRequest,
    requestId?: string,
    progressReporter?: ProgressReporter
  ): Promise<ApiResponse<ReadableStream | null>> {
    try {
      // Input validation
      this.validateMessageRequest(body);

      // Validate conversation and get context
      const conversationData = await this.validateAndGetConversation(
        conversationId,
        userId
      );

      // Create request context
      const requestContext: RequestContext = {
        userId,
        conversationId,
        projectId: conversationData.projectId,
        requestId,
        resourceIds: body.resources || [],
        preferences: body.preferences,
        requestedResources: body.resources || [],
        timestamp: Date.now(),
      };

      // Save user message first to get messageId (internal operation - no progress)
      const messageId = await this.saveUserMessage(conversationId, body);

      // File processing happens quickly - no need for progress during setup

      // Process resources and images
      const {
        resourceContext,
        availableTools,
        inlineDataParts,
        searchableFileIds,
        imageProcessingResult,
      } = await this.processResourcesAndImages(
        body.resources,
        body.images,
        userId,
        conversationData.projectId,
        conversationId,
        messageId,
        requestContext,
        progressReporter
      );

      // Log processing results
      if (imageProcessingResult) {
        logger.info("Image processing completed", {
          processedImages: imageProcessingResult.images.length,
          totalSize: imageProcessingResult.totalSize,
          errors: imageProcessingResult.errors.length,
          conversationId,
          messageId,
        });
      }

      // Reasoning progress is now shown during actual AI processing, not in setup phase

      // Generate streaming response (generateStreamingResponse will show "generating" progress)
      const stream = await this.streamService.generateStreamingResponse(
        conversationId,
        userId,
        body,
        resourceContext,
        availableTools,
        inlineDataParts,
        searchableFileIds,
        conversationData.projectId,
        body.preferences,
        requestId,
        progressReporter
      );

      return {
        success: true,
        message: "Streaming response initiated",
        data: stream,
        statusCode: 200,
      };
    } catch (error) {
      return this.handleError(error, conversationId, userId, body);
    }
  }

  // ============================================================================
  // CONVERSATION CONVERSION
  // ============================================================================

  /**
   * Convert a conversation to a resource in the system
   */
  async convertConversationToResource(
    conversationId: string,
    userId: string,
    exportFormat?: "markdown" | "docx" | "txt"
  ): Promise<any> {
    try {
      return await conversationConversionService.convertConversationToResource(
        conversationId,
        userId,
        exportFormat
      );
    } catch (error) {
      logger.error("Failed to convert conversation to resource", {
        error: error instanceof Error ? error.message : String(error),
        conversationId,
        userId,
        exportFormat,
      });
      throw new ProcessingError("Failed to convert conversation to resource");
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Validate message request input
   */
  private validateMessageRequest(body: CreateMessageRequest): void {
    // Check if user provided either resources or images
    const hasResources = body.resources && body.resources.length > 0;
    const hasImages = body.images && body.images.length > 0;

    if (!hasResources && !hasImages) {
      throw new ValidationError(
        "Please provide either files/resources to analyze or images to discuss."
      );
    }

    if (!body.message?.trim()) {
      throw new ValidationError("Message content is required.");
    }
  }

  /**
   * Validate conversation exists and user has access
   */
  private async validateAndGetConversation(
    conversationId: string,
    userId: string
  ): Promise<any> {
    const conversation = await ConversationModel.findOne({
      where: { id: conversationId, createdById: userId },
    });

    if (!conversation) {
      throw new NotFoundError("Conversation not found");
    }

    return conversation.toJSON();
  }

  /**
   * Process resources and images for context using the modular system
   */
  private async processResourcesAndImages(
    resources: string[] | undefined,
    images: any[] | undefined,
    userId: string,
    projectId: string | undefined,
    conversationId: string,
    messageId: string,
    requestContext: RequestContext,
    progressReporter?: ProgressReporter
  ): Promise<{
    resourceContext: string;
    availableTools: Tool[];
    inlineDataParts: Part[];
    processingReport: FileProcessingReport;
    searchableFileIds: string[];
    imageProcessingResult?: any;
  }> {
    logger.info("Processing resources and images", {
      resourceCount: resources?.length || 0,
      imageCount: images?.length || 0,
      userId,
      projectId,
      conversationId,
      messageId,
      requestId: requestContext.requestId,
    });

    // Process resources (existing logic)
    let resourceResult = {
      resourceContext: "",
      availableTools: [] as Tool[],
      inlineDataParts: [] as Part[],
      searchableFileIds: [] as string[],
      processingReport: {
        totalRequested: 0,
        processed: 0,
        searchable: 0,
        errors: [],
      } as FileProcessingReport,
    };

    if (resources?.length) {
      const contextManager = await getContextManager();

      const contextResult = await contextManager.buildContext(
        resources,
        userId,
        projectId,
        requestContext,
        progressReporter
      );

      // Validate all resources were processed
      const validationResult =
        this.fileProcessingValidator.validateAllResourcesProcessed(
          resources,
          contextResult.processedResources,
          contextResult.searchableFileIds
        );

      if (!validationResult.valid) {
        logger.error("File processing validation failed", {
          conversationId,
          requestId: requestContext.requestId,
          missingFiles: validationResult.missingFiles,
          requestedCount: resources.length,
        });

        throw new ProcessingError(
          `File processing incomplete: ${validationResult.message}`
        );
      }

      resourceResult = {
        resourceContext: contextResult.traditionalContext,
        availableTools: contextResult.availableTools,
        inlineDataParts: contextResult.inlineDataParts,
        searchableFileIds: contextResult.searchableFileIds,
        processingReport: {
          totalRequested: resources.length,
          processed: validationResult.processedCount || 0,
          searchable: validationResult.searchableCount || 0,
          errors: [],
        },
      };
    }

    // Process images (new logic)
    let imageProcessingResult: any = undefined;
    if (images?.length) {
      try {
        imageProcessingResult =
          await conversationImageService.processAndStoreImages(
            images,
            conversationId,
            messageId,
            userId
          );

        // Combine image inlineDataParts with resource inlineDataParts
        resourceResult.inlineDataParts = [
          ...resourceResult.inlineDataParts,
          ...imageProcessingResult.inlineDataParts,
        ];

        logger.info("Images processed successfully", {
          imageCount: images.length,
          processedCount: imageProcessingResult.images.length,
          errors: imageProcessingResult.errors.length,
          totalSize: imageProcessingResult.totalSize,
        });
      } catch (error) {
        logger.error("Image processing failed", {
          error: error instanceof Error ? error.message : String(error),
          imageCount: images.length,
          conversationId,
          messageId,
        });

        // Add image processing error to the report
        resourceResult.processingReport.errors.push(
          `Image processing failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    return {
      ...resourceResult,
      imageProcessingResult,
    };
  }

  /**
   * Save user message to database with image support
   */
  private async saveUserMessage(
    conversationId: string,
    body: CreateMessageRequest
  ): Promise<string> {
    const messageId = uuidv4();

    await ChatMessageModel.create({
      id: messageId,
      conversationId,
      role: ChatUserRole.USER,
      content: body.message,
      resources: body.resources || null,
      hasImages: (body.images?.length || 0) > 0,
      imageCount: body.images?.length || 0,
    });

    logger.info("User message saved", {
      messageId,
      conversationId,
      hasImages: (body.images?.length || 0) > 0,
      imageCount: body.images?.length || 0,
      resourceCount: body.resources?.length || 0,
    });

    return messageId;
  }

  /**
   * Create and execute AI streaming request
   */
  async executeStreamingRequest(
    model: string,
    contents: any[],
    systemInstructions: Part[],
    maxTokens: number,
    availableTools: Tool[] = []
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const ai = getGoogleGenAI();

    const requestConfig = {
      temperature: 0.3, // Optimized for RAG + document analysis
      topP: 0.95, // Nucleus sampling for adaptive vocabulary
      maxOutputTokens: maxTokens,
      systemInstruction: { parts: systemInstructions },
    };

    // Add tools and function calling configuration if tools are available
    if (availableTools.length > 0) {
      (requestConfig as any).tools = availableTools;
      (requestConfig as any).toolConfig = {
        functionCallingConfig: {
          mode: FunctionCallingConfigMode.AUTO,
        },
      };

      logger.info("Tools enabled for AI request", {
        toolCount: availableTools.length,
        toolNames: availableTools
          .map((t) => t.functionDeclarations?.map((f) => f.name) || [])
          .flat(),
      });
    } else {
      logger.info(
        "No tools provided for AI request - RAG search disabled or no searchable files"
      );
    }

    logger.info("Generating content stream", { model, requestConfig });

    return await ai.models.generateContentStream({
      model,
      contents,
      config: requestConfig,
    });
  }

  /**
   * Handle errors consistently with proper error types and user-friendly messages
   */
  private handleError(
    error: unknown,
    conversationId: string,
    userId: string,
    body: CreateMessageRequest
  ): ApiResponse<ReadableStream | null> {
    // Handle known error types
    if (error instanceof AgentError) {
      logger.error(`Agent error: ${error.code}`, {
        error: error.message,
        conversationId,
        userId,
        messageLength: body.message?.length || 0,
      });

      return createErrorResponse(error.message, error.statusCode);
    }

    // Handle unknown errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Unexpected error in agent service", {
      error: errorMessage,
      errorStack: error instanceof Error ? error.stack : undefined,
      conversationId,
      userId,
      messageLength: body.message?.length || 0,
    });

    // Track streaming error
    agentTrackingService.trackStreamingError(
      userId,
      conversationId,
      body.model || DEFAULT_MODEL,
      errorMessage,
      {
        messageLength: body.message.length,
        resourceCount: body.resources?.length || 0,
      }
    );

    // Handle specific error patterns with appropriate user messages
    if (errorMessage.includes("quota") || errorMessage.includes("limit")) {
      return createErrorResponse(
        "API quota exceeded. Please try again later.",
        429
      );
    }

    if (errorMessage.includes("auth") || errorMessage.includes("key")) {
      return createErrorResponse(
        "Authentication failed. Please contact support.",
        401
      );
    }

    if (errorMessage.includes("safety") || errorMessage.includes("policy")) {
      return createErrorResponse(
        "Content violates safety policies. Please modify your message.",
        400
      );
    }

    if (errorMessage.includes("model") || errorMessage.includes("not found")) {
      return createErrorResponse(
        "The requested AI model is not available. Please try again.",
        400
      );
    }

    if (errorMessage.includes("File processing incomplete")) {
      return createErrorResponse(
        "Some files could not be processed. Please try again or contact support.",
        400
      );
    }

    return createErrorResponse(
      "Failed to generate streaming AI response. Please try again.",
      500
    );
  }
}

export const agentService = new AgentService();
