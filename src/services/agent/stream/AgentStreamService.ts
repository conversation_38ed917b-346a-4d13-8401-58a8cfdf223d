import { createLogger } from "@/services/logger";
import { agentUtilsService } from "../utils";
import { agentTrackingService } from "../tracking.service";
import { TracingService } from "@/services/agent/tracing.service";
import { UsageTracker } from "@/services/billing/usage-tracker";
import { getModel } from "@/config/models";
import { getContextManager } from "../context";
import { Citation } from "../../pinecone/types";
import ChatMessageModel, {
  ChatUserRole,
} from "@/schemas/agent/ChatMessage.model";
import { calculateCost, validateUsage } from "../cost-calculator";
import type {
  CreateMessageRequest,
  IChatMessage,
  TokenUsage,
} from "@/validators/agent.validators";
import type {
  RequestContext,
  ToolCallRecord,
  UserPreferences,
  ProgressReporter,
} from "@/types/agent";
import type {
  FunctionCall,
  GenerateContentResponse,
  Part,
  Tool,
} from "@google/genai";
import { DEFAULT_MODEL } from "@/config/models";
import { getContinuationInstructions } from "../instructions";
import { getGoogleGenAI } from "@/config/googleGenAI";
import { SupportedModel } from "@/types/universal-ai";

const logger = createLogger("AgentStreamService");
const tracingService = TracingService.getInstance();

// Constants for stream configuration
const STREAM_TIMEOUT = 300000; // 5 minutes overall timeout

// Circuit breaker constants
const MAX_TOTAL_CHUNKS = 500;
const MAX_CONSECUTIVE_EMPTY_CHUNKS = 100;
const MAX_CONSECUTIVE_WHITESPACE_CHUNKS = 25;
const WHITESPACE_WARNING_THRESHOLD = 8;
const WHITESPACE_WARNING_INTERVAL = 8;
const WHITESPACE_ACCUMULATION_LIMIT = 15;
const HEARTBEAT_TIMEOUT = 30000; // 30 seconds

// RAG calculation constants
const ESTIMATED_TOKENS_PER_SNIPPET = 100;

// Stream configuration interface
interface StreamConfig {
  readonly timeout: number;
  readonly maxTotalChunks: number;
  readonly maxConsecutiveEmpty: number;
  readonly maxConsecutiveWhitespace: number;
  readonly whitespaceWarningThreshold: number;
  readonly whitespaceWarningInterval: number;
  readonly whitespaceAccumulationLimit: number;
  readonly heartbeatTimeout: number;
}

interface StreamContext {
  conversationId: string;
  userId: string;
  model: SupportedModel;
  accumulatedText: string;
  finishReason: string;
  chunkCount: number;
  textChunkCount: number;
  streamStartTime: number;
  streamTimeout: NodeJS.Timeout | null;
  consecutiveEmptyChunks: number;
  consecutiveWhitespaceChunks: number;
  // RAG tracking properties
  ragToolCalls: number;
  ragProcessingTime: number;
  ragResultsCount: number;
  ragSuccess: boolean;
  ragUsed: boolean;
  // Citations tracking (for streaming response only, not persisted)
  citations?: Citation[];
  // Add actual token usage from API response
  actualUsageMetadata?: {
    promptTokenCount?: number;
    candidatesTokenCount?: number;
    totalTokenCount?: number;
  };
  // Add effective preferences for system instructions
  effectivePreferences?: UserPreferences;
  // Add request ID for correlation with HTTP logs
  requestId?: string;
  // Add image tracking for token calculation
  imageCount?: number;
  imageDimensions?: Array<{ width: number; height: number }>;
  // Progress reporter for synthesis updates
  progressReporter?: ProgressReporter;
}

interface StreamProcessingResult {
  usage: TokenUsage;
  cost: number | null;
  success: boolean;
}

/**
 * Streaming request executor interface
 */
interface StreamingRequestExecutor {
  executeStreamingRequest(
    model: string,
    contents: any[],
    systemInstructions: Part[],
    maxTokens: number,
    availableTools: Tool[]
  ): Promise<AsyncGenerator<GenerateContentResponse>>;
}

/**
 * AgentStreamService - Handles all streaming logic with circuit breakers
 *
 * Responsible for:
 * - Stream processing with circuit breakers and whitespace protection
 * - Function call handling and continuation
 * - Stream error handling and recovery
 * - Analytics and logging for streaming operations
 */
export class AgentStreamService {
  private usageTracker = new UsageTracker();

  constructor(private requestExecutor?: StreamingRequestExecutor) {
    // Constructor simplified - optional dependency injection
  }

  /**
   * Execute streaming request - delegates to injected executor or fallback
   */
  private async executeStreamingRequest(
    model: SupportedModel,
    contents: any[],
    systemInstructions: Part[],
    maxTokens: number,
    availableTools: Tool[]
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    if (this.requestExecutor) {
      logger.info("Using multi-provider execution", {
        model,
        contentsCount: contents.length,
        maxTokens,
        toolsCount: availableTools.length,
      });

      try {
        return await this.requestExecutor.executeStreamingRequest(
          model,
          contents,
          systemInstructions,
          maxTokens,
          availableTools
        );
      } catch (error) {
        logger.error("Multi-provider execution failed", {
          model,
          error: error.message,
          toolsCount: availableTools.length,
        });

        // Check if this is a Google model before falling back
        const providerRegistry = new (
          await import("../provider-registry")
        ).ProviderRegistry();
        const googleModels = providerRegistry.getModelsByProvider("google");

        if (googleModels.includes(model)) {
          logger.info("Falling back to direct Google AI for Google model", {
            model,
          });
          return this.executeDirectGoogleStreaming(
            model,
            contents,
            systemInstructions,
            maxTokens,
            availableTools
          );
        } else {
          logger.error("Model not supported by any available provider", {
            model,
            availableModels: providerRegistry.getAvailableModels(),
          });
          throw new Error(
            `Model "${model}" is not supported by any available provider`
          );
        }
      }
    }

    // Check if this is a Google model before falling back
    const providerRegistry = new (
      await import("../provider-registry")
    ).ProviderRegistry();
    const googleModels = providerRegistry.getModelsByProvider("google");

    if (googleModels.includes(model)) {
      logger.warn(
        "No multi-provider executor available, using direct Google AI fallback",
        {
          model,
          toolsCount: availableTools.length,
        }
      );
      return this.executeDirectGoogleStreaming(
        model,
        contents,
        systemInstructions,
        maxTokens,
        availableTools
      );
    } else {
      logger.error("Model not supported by any available provider", {
        model,
        availableModels: providerRegistry.getAvailableModels(),
      });
      throw new Error(
        `Model "${model}" is not supported by any available provider`
      );
    }
  }

  /**
   * Direct Google AI streaming (fallback implementation)
   * Uses native Google GenAI API when multi-provider is unavailable
   */
  private async executeDirectGoogleStreaming(
    model: string,
    contents: any[],
    systemInstructions: Part[],
    maxTokens: number,
    availableTools: Tool[]
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const googleGenAI = getGoogleGenAI();

    const requestConfig: any = {
      temperature: 0.3,
      topP: 0.95,
      maxOutputTokens: maxTokens,
      systemInstruction: { parts: systemInstructions },
    };

    // Add tools if available
    if (availableTools.length > 0) {
      requestConfig.tools = availableTools;
      requestConfig.toolConfig = {
        functionCallingConfig: {
          mode: "AUTO",
        },
      };
    }

    try {
      const streamGenerator = await googleGenAI.models.generateContentStream({
        model,
        contents,
        config: requestConfig,
      });

      return streamGenerator;
    } catch (error) {
      logger.error("Direct Google streaming failed", {
        error: error.message,
        model,
        maxTokens,
      });
      throw error;
    }
  }

  /**
   * Generate streaming AI response
   */
  async generateStreamingResponse(
    conversationId: string,
    userId: string,
    body: CreateMessageRequest,
    resourceContext: string,
    availableTools: Tool[],
    inlineDataParts: Part[],
    searchableFileIds: string[],
    projectId?: string,
    effectivePreferences?: UserPreferences,
    requestId?: string,
    progressReporter?: ProgressReporter
  ): Promise<ReadableStream> {
    const model = (body.model || DEFAULT_MODEL) as SupportedModel;

    // Use effective preferences if provided, otherwise fall back to body preferences
    const preferencesToUse = effectivePreferences || body.preferences;

    // Create a modified body with effective preferences for system instructions
    const bodyWithEffectivePreferences = {
      ...body,
      preferences: preferencesToUse,
    };

    const systemInstructions = agentUtilsService.buildSystemInstructions(
      bodyWithEffectivePreferences.model as SupportedModel,
      bodyWithEffectivePreferences?.preferences?.instructions
    );
    const maxTokens = agentUtilsService.getMaxOutputTokens(
      body.model as SupportedModel,
      body.message
    );

    // Extract image information for token calculation
    const imageInfo =
      agentUtilsService.extractImageInfoFromInlineDataParts(inlineDataParts);

    // Get conversation history
    const history = await agentUtilsService.getConversationHistory(
      conversationId
    );
    const contents = agentUtilsService.formatHistoryForGenAI(
      history,
      body.message,
      resourceContext,
      inlineDataParts
    );

    this.logStreamingStart("initial", {
      conversationId,
      userId,
      model,
      messageLength: body.message.length,
      historyLength: history.length,
      systemInstructionLength: systemInstructions.length,
      hasCustomInstructions: !!preferencesToUse?.instructions,
      resourceCount: body.resources?.length || 0,
      hasResourceContext: !!resourceContext,
      inlineDataPartsCount: inlineDataParts.length,
      imageCount: imageInfo.imageCount,
      maxOutputTokens: maxTokens,
      toolsAvailable: availableTools.length,
      projectId: projectId,
      ragSearchEnabled: preferencesToUse?.ragSearchEnabled ?? true,
      requestId: requestId,
    });

    // Show generating progress right before AI model call - this is where the long wait begins
    progressReporter?.generating();

    const responseStream = await this.executeStreamingRequest(
      model,
      contents,
      systemInstructions,
      maxTokens,
      availableTools
    );

    return this.createProcessedStream(
      responseStream,
      conversationId,
      userId,
      bodyWithEffectivePreferences,
      history,
      resourceContext,
      systemInstructions,
      searchableFileIds,
      projectId,
      requestId,
      imageInfo,
      progressReporter
    );
  }

  /**
   * Create processed readable stream with proper error handling
   */
  createProcessedStream(
    responseStream: AsyncGenerator<GenerateContentResponse>,
    conversationId: string,
    userId: string,
    body: CreateMessageRequest,
    history: IChatMessage[],
    resourceContext: string,
    systemInstructions: Part[],
    searchableFileIds: string[],
    projectId?: string,
    requestId?: string,
    imageInfo?: {
      imageCount?: number;
      imageDimensions?: Array<{ width: number; height: number }>;
    },
    progressReporter?: ProgressReporter
  ): ReadableStream {
    const streamContext: StreamContext = {
      conversationId,
      userId,
      model: (body.model || DEFAULT_MODEL) as SupportedModel,
      accumulatedText: "",
      finishReason: "",
      chunkCount: 0,
      textChunkCount: 0,
      streamStartTime: Date.now(),
      streamTimeout: null,
      consecutiveEmptyChunks: 0,
      consecutiveWhitespaceChunks: 0,
      // Initialize RAG tracking
      ragToolCalls: 0,
      ragProcessingTime: 0,
      ragResultsCount: 0,
      ragSuccess: false,
      ragUsed: false,
      // Initialize citations for streaming (not persisted)
      citations: undefined,
      // Initialize actual usage metadata (will be populated from final chunk)
      actualUsageMetadata: undefined,
      // Initialize effective preferences
      effectivePreferences: body.preferences,
      requestId: requestId,
      imageCount: imageInfo?.imageCount,
      imageDimensions: imageInfo?.imageDimensions,
      // Add progress reporter to context for tool calls
      progressReporter: progressReporter,
    };

    // Bind methods to preserve 'this' context
    const processStreamChunks = (
      responseStream: AsyncGenerator<GenerateContentResponse>,
      controller: ReadableStreamDefaultController,
      context: StreamContext
    ) =>
      this.processStreamChunks(
        responseStream,
        controller,
        context,
        searchableFileIds,
        projectId
      );
    const finalizeStream = this.finalizeStream.bind(this);
    const sendCompletionSignal = this.sendCompletionSignal.bind(this);
    const handleStreamError = this.handleStreamError.bind(this);

    return new ReadableStream({
      async start(controller) {
        try {
          await processStreamChunks(responseStream, controller, streamContext);

          const result = await finalizeStream(
            streamContext,
            body,
            history,
            resourceContext,
            systemInstructions,
            projectId
          );

          sendCompletionSignal(
            controller,
            streamContext,
            result.success,
            streamContext.model
          );
        } catch (error) {
          logger.error("Stream processing error", {
            error: error instanceof Error ? error.message : String(error),
            conversationId,
            userId,
            stack: error instanceof Error ? error.stack : undefined,
          });
          handleStreamError(error, controller, streamContext);
        }
      },
    });
  }

  /**
   * Process individual stream chunks - enhanced version with better error handling
   */
  async processStreamChunks(
    responseStream: AsyncGenerator<GenerateContentResponse>,
    controller: ReadableStreamDefaultController,
    context: StreamContext,
    searchableFileIds: string[],
    projectId?: string
  ): Promise<void> {
    // Set up overall stream timeout
    context.streamTimeout = setTimeout(() => {
      logger.warn("Stream timeout: Overall stream timeout reached", {
        conversationId: context.conversationId,
        duration: Date.now() - context.streamStartTime,
        chunkCount: context.chunkCount,
        textChunkCount: context.textChunkCount,
      });
      this.clearStreamTimeout(context);
    }, STREAM_TIMEOUT);

    // Progress for analyzing is now shown during setup phase, not here
    // The AI model call and first chunk processing is where the real work happens

    try {
      // Add a heartbeat to detect if the stream is stuck
      let lastChunkTime = Date.now();
      let firstChunkReceived = false;
      let firstContentReceived = false;

      const heartbeatInterval = setInterval(() => {
        const timeSinceLastChunk = Date.now() - lastChunkTime;
        if (timeSinceLastChunk > HEARTBEAT_TIMEOUT) {
          // 30 seconds without a chunk
          logger.warn("Stream appears stuck - no chunks received", {
            conversationId: context.conversationId,
            timeSinceLastChunk,
            chunkCount: context.chunkCount,
            textChunkCount: context.textChunkCount,
          });
        }
      }, 30000);

      try {
        // Process chunks with enhanced error handling
        for await (const chunk of responseStream) {
          // Show analyzing progress when we get the first chunk - AI is now actively processing
          if (!firstChunkReceived && searchableFileIds.length > 0) {
            context.progressReporter?.analyzing(
              searchableFileIds.length,
              "simple"
            );
            firstChunkReceived = true;
          }
          lastChunkTime = Date.now(); // Update heartbeat

          try {
            context.chunkCount++;

            // Overall safety limit - prevent extremely long responses
            if (context.chunkCount > MAX_TOTAL_CHUNKS) {
              logger.error("Circuit breaker triggered: Too many total chunks", {
                conversationId: context.conversationId,
                chunkCount: context.chunkCount,
                textChunkCount: context.textChunkCount,
                accumulatedLength: context.accumulatedText.length,
                duration: Date.now() - context.streamStartTime,
              });

              context.finishReason = "CIRCUIT_BREAKER_TOTAL_CHUNKS";
              this.clearStreamTimeout(context);
              clearInterval(heartbeatInterval);
              break;
            }

            // Capture usage metadata from any chunk that has it (not just final chunk)
            if (chunk.usageMetadata && !context.actualUsageMetadata) {
              context.actualUsageMetadata = {
                promptTokenCount: chunk.usageMetadata.promptTokenCount,
                candidatesTokenCount: chunk.usageMetadata.candidatesTokenCount,
                totalTokenCount: chunk.usageMetadata.totalTokenCount,
              };
            }

            const candidate = chunk.candidates?.[0];

            // Check for function calls first
            if (candidate?.content?.parts) {
              for (const part of candidate.content.parts) {
                if (part.functionCall) {
                  logger.info("Function call detected in stream", {
                    conversationId: context.conversationId,
                    functionName: part.functionCall.name,
                    args: part.functionCall.args,
                  });

                  try {
                    const ragStartTime = Date.now();

                    // Show search progress to users - generic tool activity indication
                    context.progressReporter?.searching(
                      searchableFileIds.length
                    );

                    // Handle tool call using the new context manager
                    const contextManager = await getContextManager();

                    const functionResult = await contextManager.handleToolCall(
                      part.functionCall.name,
                      part.functionCall.args,
                      context.userId,
                      searchableFileIds,
                      projectId,
                      context.requestId
                    );

                    const ragProcessingTime = Date.now() - ragStartTime;

                    // Record tool result in conversation context
                    const toolResult: ToolCallRecord = {
                      toolName: part.functionCall.name,
                      args: part.functionCall.args,
                      result: functionResult,
                      timestamp: Date.now(),
                      processingTime: ragProcessingTime,
                    };

                    // Tool result tracking is handled by AgentTrackingService
                    // No need to record in ConversationContextManager

                    // Track RAG metrics for any tool that provides search/retrieval functionality
                    const isRagTool =
                      functionResult.metadata?.snippetCount !== undefined ||
                      functionResult.data?.citations !== undefined;

                    if (isRagTool) {
                      context.ragToolCalls++;
                      context.ragProcessingTime += ragProcessingTime;
                      context.ragUsed = true;
                      context.ragSuccess = functionResult.success;
                      context.ragResultsCount +=
                        functionResult.metadata?.snippetCount || 0;

                      // Trace RAG operation with requestId
                      if (context.requestId) {
                        try {
                          // TODO: Re-enable when traceRAGOperation method is available
                          // await tracingService.traceRAGOperation(
                          //   context.userId,
                          //   context.conversationId,
                          //   JSON.stringify(part.functionCall.args),
                          //   functionResult.success
                          //     ? functionResult.data?.citations || []
                          //     : [],
                          //   ragProcessingTime,
                          //   {
                          //     toolName: part.functionCall.name,
                          //     snippetCount:
                          //       functionResult.metadata?.snippetCount || 0,
                          //     success: functionResult.success,
                          //     error: functionResult.error || null,
                          //   },
                          //   context.requestId
                          // );
                        } catch (tracingError) {
                          logger.warn("Failed to trace RAG operation", {
                            error: tracingError,
                            conversationId: context.conversationId,
                            requestId: context.requestId,
                          });
                        }
                      }

                      // Log citation info from tool response (not tracked in context)
                      if (
                        functionResult.success &&
                        functionResult.data?.citations
                      ) {
                        context.citations = functionResult.data.citations;
                        logger.info(
                          "Citations extracted from RAG tool for streaming",
                          {
                            conversationId: context.conversationId,
                            citationCount:
                              functionResult.data.citations.length || 0,
                            toolName: part.functionCall.name,
                            requestId: context.requestId || "not-provided",
                          }
                        );
                      }
                    }

                    // Trace function call with requestId
                    if (context.requestId) {
                      try {
                        await tracingService.traceFunctionCall(
                          context.userId,
                          context.conversationId,
                          part.functionCall.name,
                          part.functionCall.args,
                          functionResult,
                          ragProcessingTime,
                          functionResult.success,
                          functionResult.error || null,
                          context.requestId
                        );
                      } catch (tracingError) {
                        logger.warn("Failed to trace function call", {
                          error: tracingError,
                          conversationId: context.conversationId,
                          functionName: part.functionCall.name,
                          requestId: context.requestId,
                        });
                      }
                    }

                    logger.info("Function call completed", {
                      conversationId: context.conversationId,
                      functionName: part.functionCall.name,
                      success: functionResult.success,
                      snippetCount: functionResult.metadata?.snippetCount || 0,
                      requestId: context.requestId || "not-provided",
                    });

                    // Show synthesis progress if we got search results
                    if (
                      functionResult.success &&
                      functionResult.metadata?.snippetCount > 0
                    ) {
                      context.progressReporter?.synthesizing(
                        functionResult.metadata.snippetCount
                      );
                    }

                    // Continue the conversation with the function result
                    await this.continueConversationWithFunctionResult(
                      part.functionCall,
                      functionResult,
                      context,
                      controller
                    );
                  } catch (error) {
                    logger.error("Function call failed", {
                      conversationId: context.conversationId,
                      functionName: part.functionCall.name,
                      error:
                        error instanceof Error ? error.message : String(error),
                    });

                    // Record failed tool result in conversation context
                    const failedToolResult: ToolCallRecord = {
                      toolName: part.functionCall.name,
                      args: part.functionCall.args,
                      result: {
                        success: false,
                        error:
                          error instanceof Error
                            ? error.message
                            : String(error),
                      },
                      timestamp: Date.now(),
                      processingTime: 0,
                    };

                    // Tool result tracking is handled by AgentTrackingService
                    // No need to record in ConversationContextManager

                    // Check if this was a RAG tool that failed
                    const wasRagTool =
                      part.functionCall.args?.query !== undefined ||
                      part.functionCall.name.toLowerCase().includes("search") ||
                      part.functionCall.name.toLowerCase().includes("rag");

                    if (wasRagTool) {
                      context.ragUsed = true;
                      context.ragSuccess = false;

                      // Track RAG error using dedicated method
                      agentTrackingService.trackRagError(
                        context.userId,
                        context.conversationId,
                        part.functionCall.name,
                        error instanceof Error ? error.message : String(error),
                        part.functionCall.args,
                        "retrieval_failed",
                        true,
                        0
                      );
                    }

                    // Send error as content (compatible with frontend)
                    const errorMessage =
                      error instanceof Error
                        ? `❌ Search temporarily unavailable (${error.message.substring(
                            0,
                            50
                          )}). I'll help with available information.`
                        : "❌ Search failed. I'll try to help based on available information.";
                    this.sendErrorToController(controller, errorMessage);
                  }
                }
              }
            }

            // Show reasoning progress when we get first actual content
            if (!firstContentReceived && candidate?.content?.parts) {
              for (const part of candidate.content.parts) {
                if (part.text && part.text.trim()) {
                  context.progressReporter?.reasoning("simple");
                  firstContentReceived = true;
                  break;
                }
              }
            }

            // Process text content
            const shouldContinue = await this.processChunkContent(
              candidate,
              context,
              controller,
              {
                enableCircuitBreaker: true,
                enableWhitespaceProtection: true,
              }
            );

            if (!shouldContinue) {
              this.clearStreamTimeout(context);
              clearInterval(heartbeatInterval);
              break;
            }

            // Handle finish reason AFTER processing text content
            if (candidate?.finishReason) {
              context.finishReason = candidate.finishReason;

              // Capture actual token usage from the final chunk
              if (chunk.usageMetadata) {
                context.actualUsageMetadata = {
                  promptTokenCount: chunk.usageMetadata.promptTokenCount,
                  candidatesTokenCount:
                    chunk.usageMetadata.candidatesTokenCount,
                  totalTokenCount: chunk.usageMetadata.totalTokenCount,
                };
              } else {
                logger.warn("No usage metadata in final chunk", {
                  conversationId: context.conversationId,
                  chunkKeys: chunk ? Object.keys(chunk) : [],
                  hasUsageMetadata: !!chunk?.usageMetadata,
                  requestId: context.requestId,
                });
              }

              this.clearStreamTimeout(context);
              clearInterval(heartbeatInterval);
              break; // Natural end of stream
            }

            // Force a micro-task yield to prevent blocking
            await new Promise((resolve) => setImmediate(resolve));
          } catch (chunkError) {
            logger.error("Error processing individual chunk", {
              error:
                chunkError instanceof Error
                  ? chunkError.message
                  : String(chunkError),
              conversationId: context.conversationId,
              chunkCount: context.chunkCount,
              chunkData: chunk
                ? JSON.stringify(chunk).substring(0, 200)
                : "null",
            });
            // Continue processing other chunks
          }
        }
      } finally {
        clearInterval(heartbeatInterval);
      }

      // Clean up accumulated whitespace if circuit breaker triggered
      if (context.finishReason === "CIRCUIT_BREAKER_WHITESPACE") {
        const originalLength = context.accumulatedText.length;
        context.accumulatedText = context.accumulatedText.trim();
        const cleanedLength = context.accumulatedText.length;
      }
    } catch (error) {
      logger.error("Error in processStreamChunks", {
        error: error instanceof Error ? error.message : String(error),
        conversationId: context.conversationId,
        chunkCount: context.chunkCount,
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error; // Re-throw to be handled by the caller
    } finally {
      this.clearStreamTimeout(context);
    }
  }

  /**
   * Extract and process text content from a chunk candidate
   */
  async processChunkContent(
    candidate:
      | { content?: { parts?: { text?: string }[] }; finishReason?: string }
      | undefined,
    context: StreamContext,
    controller: ReadableStreamDefaultController,
    options: {
      enableCircuitBreaker?: boolean;
      enableWhitespaceProtection?: boolean;
    } = {}
  ): Promise<boolean> {
    // Extract text from ALL parts, not just the first one
    let text = "";
    if (candidate?.content?.parts) {
      for (const part of candidate.content.parts) {
        if (part.text) {
          text += part.text;
        }
      }
    }

    // Process text content
    if (text) {
      context.textChunkCount++;

      if (options.enableWhitespaceProtection) {
        // Handle whitespace protection logic
        const shouldContinue = this.handleWhitespaceProtection(text, context);
        if (!shouldContinue) {
          return false;
        }
      } else {
        // Always accumulate text when whitespace protection is disabled
        context.accumulatedText += text;
      }

      // Send chunk to client
      const shouldSendChunk =
        !options.enableWhitespaceProtection ||
        context.consecutiveWhitespaceChunks <= WHITESPACE_WARNING_THRESHOLD;

      if (shouldSendChunk) {
        this.sendChunkToController(controller, text);
      }
    } else if (options.enableCircuitBreaker) {
      // Handle truly empty chunks (no text at all)
      context.consecutiveEmptyChunks++;
      context.consecutiveWhitespaceChunks = 0; // Reset whitespace counter

      // Circuit breaker for empty chunks
      if (context.consecutiveEmptyChunks > MAX_CONSECUTIVE_EMPTY_CHUNKS) {
        logger.error(
          "Circuit breaker triggered: Too many consecutive empty chunks",
          {
            conversationId: context.conversationId,
            consecutiveEmptyChunks: context.consecutiveEmptyChunks,
            chunkCount: context.chunkCount,
            textChunkCount: context.textChunkCount,
          }
        );

        context.finishReason = "CIRCUIT_BREAKER_EMPTY";
        return false;
      }
    }

    return true;
  }

  /**
   * Handle whitespace protection logic to prevent infinite loops
   */
  handleWhitespaceProtection(text: string, context: StreamContext): boolean {
    // Performance optimization: avoid creating new string with trim() for simple check
    const isWhitespaceOnly = text.length === 0 || /^\s*$/.test(text);

    if (isWhitespaceOnly) {
      context.consecutiveWhitespaceChunks++;
      context.consecutiveEmptyChunks = 0; // Reset empty counter

      // Circuit breaker for whitespace loops
      if (
        context.consecutiveWhitespaceChunks > MAX_CONSECUTIVE_WHITESPACE_CHUNKS
      ) {
        logger.error(
          "Circuit breaker triggered: Known Gemini API whitespace loop detected",
          {
            conversationId: context.conversationId,
            consecutiveWhitespaceChunks: context.consecutiveWhitespaceChunks,
            chunkCount: context.chunkCount,
            textChunkCount: context.textChunkCount,
            accumulatedLength: context.accumulatedText.length,
            lastChunkLength: text.length,
            duration: Date.now() - context.streamStartTime,
            issue:
              "This is a known issue with Gemini API for complex responses like tables",
          }
        );

        context.finishReason = "CIRCUIT_BREAKER_WHITESPACE";
        return false;
      }

      // Log suspicious patterns
      if (
        context.consecutiveWhitespaceChunks > WHITESPACE_WARNING_THRESHOLD &&
        context.consecutiveWhitespaceChunks % WHITESPACE_WARNING_INTERVAL === 0
      ) {
        logger.warn("Potential Gemini API whitespace loop detected", {
          conversationId: context.conversationId,
          consecutiveWhitespaceChunks: context.consecutiveWhitespaceChunks,
          chunkCount: context.chunkCount,
          accumulatedLength: context.accumulatedText.length,
          chunkLength: text.length,
          textSample: text.substring(0, 20),
          suggestion:
            "This is a known issue with Gemini API for complex responses",
        });
      }

      // Stop accumulating whitespace after 15 consecutive chunks
      if (
        context.consecutiveWhitespaceChunks <= WHITESPACE_ACCUMULATION_LIMIT
      ) {
        context.accumulatedText += text;
      } else {
        logger.info("Stopped accumulating whitespace to prevent memory bloat", {
          conversationId: context.conversationId,
          consecutiveWhitespaceChunks: context.consecutiveWhitespaceChunks,
          accumulatedLength: context.accumulatedText.length,
        });
      }
    } else {
      // Reset counters on meaningful content
      context.consecutiveWhitespaceChunks = 0;
      context.consecutiveEmptyChunks = 0;
      context.accumulatedText += text;
    }

    return true;
  }

  /**
   * Send a text chunk to the controller
   */
  sendChunkToController(
    controller: ReadableStreamDefaultController,
    text: string
  ): void {
    // Clean any AI-generated citation markers before sending
    const cleanedText = this.cleanCitationMarkers(text);

    // Optimize for common case: pre-build the JSON structure
    const chunkObject = {
      type: "content" as const,
      text: cleanedText,
      timestamp: Date.now(),
    };
    const chunkData = `data: ${JSON.stringify(chunkObject)}\n\n`;

    try {
      controller.enqueue(new TextEncoder().encode(chunkData));
    } catch (enqueueError) {
      if (enqueueError instanceof Error && enqueueError.name === "TypeError") {
        logger.warn("Stream closed during enqueue", {
          chunkLength: cleanedText.length,
        });
        return;
      }
      throw enqueueError;
    }
  }

  /**
   * Clean AI-generated citation markers from text content
   */
  private cleanCitationMarkers(text: string): string {
    if (!text) return text;

    // Remove various citation marker patterns that the AI might generate
    const citationPatterns = [
      /\[\^\d+\]/g, // [^1], [^2], [^123] - superscript-style citations
      /\[\^[\d,\s-]+\]/g, // [^1,2], [^1-3], [^1, 2, 3] - multiple citations
      /\[(?:\d+)\]/g, // [1], [2], [123] - simple numbered citations
    ];

    let cleanedText = text;

    for (const pattern of citationPatterns) {
      const originalLength = cleanedText.length;
      cleanedText = cleanedText.replace(pattern, "");
    }

    return cleanedText;
  }

  /**
   * Send error message to controller
   */
  sendErrorToController(
    controller: ReadableStreamDefaultController,
    message: string
  ): void {
    const errorData = `data: ${JSON.stringify({
      type: "content",
      text: message,
      timestamp: Date.now(),
    })}\n\n`;

    try {
      controller.enqueue(new TextEncoder().encode(errorData));
    } catch (error) {
      logger.error("Failed to send error to controller", {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Process stream chunks in a simplified way (for continuation streams)
   */
  async processSimpleStreamChunks(
    responseStream: AsyncGenerator<GenerateContentResponse>,
    context: StreamContext,
    controller: ReadableStreamDefaultController
  ): Promise<void> {
    for await (const chunk of responseStream) {
      const candidate = chunk.candidates?.[0];

      // Process text content without circuit breakers
      await this.processChunkContent(candidate, context, controller, {
        enableCircuitBreaker: false,
        enableWhitespaceProtection: false,
      });

      // Check for finish reason
      if (candidate?.finishReason) {
        context.finishReason = candidate.finishReason;
        break;
      }
    }
  }

  /**
   * Continue the conversation with the function result.
   * This involves sending a new API call to Google GenAI with the function result.
   */
  async continueConversationWithFunctionResult(
    functionCall: FunctionCall,
    functionResult: Record<string, unknown>,
    context: StreamContext,
    controller: ReadableStreamDefaultController
  ): Promise<void> {
    try {
      const model = context.model;

      // Get conversation history to continue the conversation
      const history = await agentUtilsService.getConversationHistory(
        context.conversationId
      );

      // Build the contents array for the continued conversation
      const contents: any[] =
        agentUtilsService.buildFunctionContinuationContents(
          history,
          functionCall,
          functionResult
        );

      this.logStreamingStart("continuation", {
        conversationId: context.conversationId,
        model,
        functionName: functionCall.name,
        functionSuccess: !!functionResult.success,
      });

      // Get the original user message from the last user message in history
      const lastUserMessage = history
        .filter((msg) => msg.role === "user")
        .pop();
      const originalUserMessage =
        lastUserMessage?.content ||
        "Continue the conversation based on the search results";

      // Build system instructions that preserve original context and prevent search loops
      const systemInstructions = agentUtilsService.buildSystemInstructions(
        model,
        context.effectivePreferences?.instructions
      );

      const continuationInstructions =
        getContinuationInstructions(originalUserMessage);

      const maxTokens = agentUtilsService.getMaxOutputTokens(model);

      const responseStream = await this.executeStreamingRequest(
        model,
        contents,
        [...systemInstructions, ...continuationInstructions],
        maxTokens,
        [] // No tools for continuation streams
      );

      // Use simplified stream processing for continuation
      await this.processSimpleStreamChunks(responseStream, context, controller);

      this.logStreamingCompletion("continuation", {
        conversationId: context.conversationId,
        accumulatedLength: context.accumulatedText.length,
        finishReason: context.finishReason,
      });
    } catch (error) {
      logger.error("Failed to continue conversation with function result", {
        conversationId: context.conversationId,
        error: error instanceof Error ? error.message : String(error),
      });

      // Send error as content (compatible with frontend)
      this.sendErrorToController(
        controller,
        "I encountered an error while processing your request. Please try again."
      );
    }
  }

  /**
   * Finalize stream processing
   */
  async finalizeStream(
    context: StreamContext,
    body: CreateMessageRequest,
    history: IChatMessage[],
    resourceContext: string,
    systemInstructions: Part[],
    projectId?: string
  ): Promise<StreamProcessingResult> {
    // Bind methods to preserve 'this' context
    const trackStreamingAnalytics = this.trackStreamingAnalytics.bind(this);
    const checkTruncationIssues = this.checkTruncationIssues.bind(this);

    // Calculate usage and cost using actual API response metadata
    const usage = agentUtilsService.calculateUsage(
      context.actualUsageMetadata,
      body.message, // fallback input
      context.accumulatedText, // fallback output
      context.imageCount // image count for token calculation
    );

    const validatedUsage = validateUsage(usage) ? usage : null;
    const cost = calculateCost(validatedUsage, body.model);

    // Clean accumulated text before saving
    const cleanedContent = this.cleanCitationMarkers(context.accumulatedText);

    // Save assistant message
    await ChatMessageModel.create({
      conversationId: context.conversationId,
      role: ChatUserRole.ASSISTANT,
      content: cleanedContent,
      model: body.model,
      usage: validatedUsage,
      cost,
      resources: body.resources || null,
    } as any);

    logger.info("Streaming AI response completed", {
      conversationId: context.conversationId,
      userId: context.userId,
      responseLength: cleanedContent.length,
      originalResponseLength: context.accumulatedText.length,
      inputTokens: validatedUsage?.inputTokens || 0,
      outputTokens: validatedUsage?.outputTokens || 0,
      totalTokens: validatedUsage?.totalTokens || 0,
      imageTokens: validatedUsage?.imageTokens || 0,
      cost,
      finishReason: context.finishReason,
      totalChunks: context.chunkCount,
      textChunks: context.textChunkCount,
      emptyChunks: context.chunkCount - context.textChunkCount,
      citationCount: context.citations?.length || 0,
      imageCount: context.imageCount || 0,
      requestId: context.requestId || "not-provided",
    });

    // Trace the main conversation with requestId
    if (context.requestId) {
      try {
        await tracingService.traceConversation(
          {
            userId: context.userId,
            conversationId: context.conversationId,
            model: body.model || DEFAULT_MODEL,
            projectId,
            input: body.message,
            output: context.accumulatedText,
            usage: validatedUsage || {
              inputTokens: 0,
              outputTokens: 0,
              totalTokens: 0,
            },
            cost: cost || 0,
            duration: Date.now() - context.streamStartTime,
            chunkCount: context.chunkCount,
            textChunkCount: context.textChunkCount,
            finishReason: context.finishReason,
            ragUsed: context.ragUsed,
            ragSuccess: context.ragSuccess,
            ragToolCalls: context.ragToolCalls,
            ragProcessingTime: context.ragProcessingTime,
            ragResultsCount: context.ragResultsCount,
            citations: context.citations,
            hasCustomInstructions: !!body.preferences?.instructions,
            customInstructions: body.preferences?.instructions,
            resourceCount: body.resources?.length || 0,
            historyLength: history.length,
          },
          context.requestId
        );
      } catch (tracingError) {
        logger.warn("Failed to trace conversation", {
          error: tracingError,
          conversationId: context.conversationId,
          requestId: context.requestId,
        });
      }
    }

    // Track analytics
    if (validatedUsage && cost !== null) {
      await trackStreamingAnalytics(
        context,
        body,
        history,
        validatedUsage,
        cost,
        projectId
      );
    }

    // Check for truncation issues
    checkTruncationIssues(context);

    return { usage, cost, success: true };
  }

  /**
   * Collect RAG metrics from stream context
   */
  collectRagMetrics(context: StreamContext) {
    if (!context.ragUsed) {
      return undefined;
    }

    // Calculate token savings estimate (rough approximation)
    const estimatedTokenSavings =
      context.ragResultsCount * ESTIMATED_TOKENS_PER_SNIPPET;

    // Calculate a more meaningful quality score
    let qualityScore = 0.2; // Base score for failed RAG
    if (context.ragSuccess) {
      if (context.ragResultsCount > 0) {
        // Good results found
        qualityScore = Math.min(0.9, 0.6 + context.ragResultsCount * 0.1);
      } else {
        // Successful query but no results (might indicate empty database or poor query)
        qualityScore = 0.4;
      }
    }

    return {
      enabled: context.ragUsed,
      success: context.ragSuccess,
      responseTimeMs: context.ragProcessingTime,
      estimatedTokenSavings,
      toolsUsed: context.ragToolCalls,
      qualityScore,
    };
  }

  /**
   * Collect RAG performance metrics from stream context
   */
  collectRagPerformanceMetrics(context: StreamContext) {
    if (!context.ragUsed) {
      return undefined;
    }

    return {
      processingTimeMs: context.ragProcessingTime,
      resultsCount: context.ragResultsCount,
      improvedResponse: context.ragSuccess && context.ragResultsCount > 0,
    };
  }

  /**
   * Track streaming analytics
   */
  async trackStreamingAnalytics(
    context: StreamContext,
    body: CreateMessageRequest,
    history: IChatMessage[],
    usage: TokenUsage,
    cost: number,
    projectId?: string
  ): Promise<void> {
    // Collect RAG metrics from context
    const ragMetrics = this.collectRagMetrics(context);
    const ragPerformance = this.collectRagPerformanceMetrics(context);

    // Track complete streaming session with comprehensive analytics
    agentTrackingService.trackStreamingComplete(
      context.userId,
      context.conversationId,
      context.model,
      usage,
      cost,
      body.message.length,
      body.resources?.length || 0,
      history.length,
      body.resources || [],
      projectId,
      ragMetrics,
      ragPerformance
    );

    // Track usage in database for billing
    try {
      const modelInfo = getModel(context.model);
      await this.usageTracker.trackUsage(
        modelInfo.provider,
        context.model,
        {
          inputTokens: usage.inputTokens || 0,
          outputTokens: usage.outputTokens || 0,
          totalTokens:
            usage.totalTokens ||
            (usage.inputTokens || 0) + (usage.outputTokens || 0),
          imageTokens: usage.imageTokens || 0,
        },
        {
          messageId: `msg_${Date.now()}_${Math.random()
            .toString(36)
            .substr(2, 9)}`, // Generate temporary messageId
          conversationId: context.conversationId,
          userId: context.userId,
          projectId,
          ragUsed: ragMetrics?.success || false,
          toolCalls: ragMetrics?.toolsUsed || 0,
          messageLength: body.message.length,
          contextLength: history.length,
          processingTimeMs: Date.now() - context.streamStartTime,
          finishReason: context.finishReason || "stop",
        }
      );
    } catch (error) {
      logger.error("Failed to track usage in database", {
        error: error.message,
        userId: context.userId,
        conversationId: context.conversationId,
        model: context.model,
      });
    }
  }

  /**
   * Check for truncation issues
   */
  checkTruncationIssues(context: StreamContext): void {
    if (context.finishReason === "MAX_TOKENS") {
      logger.warn("Response truncated due to token limit", {
        conversationId: context.conversationId,
        userId: context.userId,
        responseLength: context.accumulatedText.length,
        finishReason: context.finishReason,
        totalChunks: context.chunkCount,
        textChunks: context.textChunkCount,
      });
    } else if (context.finishReason === "SAFETY") {
      logger.warn("Response truncated due to safety concerns", {
        conversationId: context.conversationId,
        userId: context.userId,
        responseLength: context.accumulatedText.length,
        finishReason: context.finishReason,
      });
    } else if (context.finishReason === "STOP") {
      logger.info("Response completed naturally", {
        conversationId: context.conversationId,
        responseLength: context.accumulatedText.length,
        finishReason: context.finishReason,
        totalChunks: context.chunkCount,
        textChunks: context.textChunkCount,
        duration: Date.now() - context.streamStartTime,
      });
    } else if (context.finishReason === "CIRCUIT_BREAKER_WHITESPACE") {
      logger.error(
        "Response truncated due to excessive consecutive whitespace chunks",
        {
          conversationId: context.conversationId,
          userId: context.userId,
          responseLength: context.accumulatedText.length,
          finishReason: context.finishReason,
          totalChunks: context.chunkCount,
          textChunks: context.textChunkCount,
        }
      );
    } else if (context.finishReason === "CIRCUIT_BREAKER_EMPTY") {
      logger.error(
        "Response truncated due to excessive consecutive empty chunks",
        {
          conversationId: context.conversationId,
          userId: context.userId,
          responseLength: context.accumulatedText.length,
          finishReason: context.finishReason,
          totalChunks: context.chunkCount,
          textChunks: context.textChunkCount,
        }
      );
    } else if (context.finishReason === "CIRCUIT_BREAKER_TOTAL_CHUNKS") {
      logger.error("Response truncated due to excessive total chunks", {
        conversationId: context.conversationId,
        userId: context.userId,
        responseLength: context.accumulatedText.length,
        finishReason: context.finishReason,
        totalChunks: context.chunkCount,
        textChunks: context.textChunkCount,
      });
    }
  }

  /**
   * Send completion signal with cost and usage information
   */
  sendCompletionSignal(
    controller: ReadableStreamDefaultController,
    context: StreamContext,
    success: boolean,
    model?: string
  ): void {
    // Calculate usage and cost using actual API response metadata
    const usage = agentUtilsService.calculateUsage(
      context.actualUsageMetadata,
      undefined, // We don't have the original input here
      context.accumulatedText, // fallback output
      context.imageCount // image count for token calculation
    );

    const validatedUsage = validateUsage(usage) ? usage : null;
    const cost = calculateCost(validatedUsage, model);

    // Clean accumulated text for final response length calculation
    const cleanedContent = this.cleanCitationMarkers(context.accumulatedText);

    const completionData = {
      type: "completion",
      finishReason: context.finishReason,
      totalChunks: context.chunkCount,
      textChunks: context.textChunkCount,
      responseLength: cleanedContent.length,
      duration: Date.now() - context.streamStartTime,
      timestamp: Date.now(),
      success,
      // Add cost and usage information
      usage: validatedUsage,
      cost,
      // Add model information for context
      model: model || context.model,
      // Add citations for frontend display (not persisted)
      citations: context.citations || [],
    };

    controller.enqueue(
      new TextEncoder().encode(`data: ${JSON.stringify(completionData)}\n\n`)
    );
    controller.enqueue(new TextEncoder().encode("data: [DONE]\n\n"));
    controller.close();
  }

  /**
   * Handle stream processing errors
   */
  handleStreamError(
    error: Error,
    controller: ReadableStreamDefaultController,
    context: StreamContext
  ): void {
    this.clearStreamTimeout(context);

    logger.error("Error during streaming response", {
      error: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      conversationId: context.conversationId,
      userId: context.userId,
      chunkCount: context.chunkCount,
      textChunkCount: context.textChunkCount,
      accumulatedLength: context.accumulatedText.length,
      duration: Date.now() - context.streamStartTime,
    });

    try {
      controller.error(error);
    } catch (controllerError) {
      logger.error("Error closing controller", {
        controllerError:
          controllerError instanceof Error
            ? controllerError.message
            : String(controllerError),
      });
    }
  }

  /**
   * Utility methods
   */
  clearStreamTimeout(context: StreamContext): void {
    if (context.streamTimeout) {
      clearTimeout(context.streamTimeout);
      context.streamTimeout = null;
    }
  }

  /**
   * Log streaming operation start
   */
  logStreamingStart(
    type: "initial" | "continuation",
    context: {
      conversationId: string;
      userId?: string;
      model: string;
      functionName?: string;
      functionSuccess?: boolean;
      messageLength?: number;
      historyLength?: number;
      systemInstructionLength?: number;
      hasCustomInstructions?: boolean;
      resourceCount?: number;
      hasResourceContext?: boolean;
      inlineDataPartsCount?: number;
      maxOutputTokens?: number;
      toolsAvailable?: number;
      projectId?: string;
      ragSearchEnabled?: boolean;
      requestId?: string;
      imageCount?: number;
    }
  ): void {
    if (type === "initial") {
      logger.info("Generating streaming AI response", {
        conversationId: context.conversationId,
        userId: context.userId,
        model: context.model,
        messageLength: context.messageLength,
        historyLength: context.historyLength,
        systemInstructionLength: context.systemInstructionLength,
        hasCustomInstructions: context.hasCustomInstructions,
        resourceCount: context.resourceCount,
        hasResourceContext: context.hasResourceContext,
        inlineDataPartsCount: context.inlineDataPartsCount,
        maxOutputTokens: context.maxOutputTokens,
        toolsAvailable: context.toolsAvailable,
        projectId: context.projectId,
        ragSearchEnabled: context.ragSearchEnabled,
        requestId: context.requestId,
        imageCount: context.imageCount,
      });
    } else {
      logger.info("Continuing conversation with function result", {
        conversationId: context.conversationId,
        functionName: context.functionName,
        success: context.functionSuccess,
      });
    }
  }

  /**
   * Log streaming operation completion
   */
  logStreamingCompletion(
    type: "initial" | "continuation",
    context: {
      conversationId: string;
      accumulatedLength: number;
      finishReason: string;
      functionName?: string;
      requestId?: string;
    }
  ): void {
    if (type === "continuation") {
      logger.info("Function result conversation continuation completed", {
        conversationId: context.conversationId,
        accumulatedLength: context.accumulatedLength,
        finishReason: context.finishReason,
        requestId: context.requestId,
      });
    }
  }
}

// Export factory function with dependency injection
export const createAgentStreamService = (
  requestExecutor?: StreamingRequestExecutor
) => new AgentStreamService(requestExecutor);
