import { Part, GenerateContentResponse, Tool } from "@google/genai";
import { createLogger } from "@/services/logger";
import { ProviderRegistry } from "../provider-registry";

const logger = createLogger("RequestExecutor");
import {
  UniversalRequest,
  UniversalMessage,
  UniversalMessageContent,
  UniversalTool,
  UniversalToolCall,
  UniversalResponse,
  SupportedModel,
} from "@/types/universal-ai";

/**
 * RequestExecutor - Implements StreamingRequestExecutor interface
 * Routes requests to actual AI providers (OpenAI, Anthropic, Google)
 *
 * This provides TRUE multi-model support by calling the actual provider APIs
 * while maintaining the exact same interface and context logic for AgentStreamService.
 */
export class RequestExecutor {
  private providerRegistry: ProviderRegistry;
  private streamBuffer: string = "";

  constructor() {
    try {
      logger.info("Initializing multi-provider registry...");
      this.providerRegistry = new ProviderRegistry();
      logger.info("Multi-provider registry initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize provider registry", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Execute streaming request using actual multi-provider system
   */
  async executeStreamingRequest(
    model: SupportedModel,
    contents: any[],
    systemInstructions: Part[],
    maxTokens: number,
    availableTools: Tool[]
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    try {
      logger.info("Executing multi-provider streaming request", {
        model,
        contentsCount: contents.length,
        maxTokens,
        toolsCount: availableTools.length,
      });

      // Convert to universal format for provider routing
      const universalRequest: UniversalRequest = {
        model,
        messages: this.convertToUniversalMessages(contents),
        systemInstructions: systemInstructions
          .map((part) => part.text)
          .filter(Boolean),
        tools: this.convertToUniversalTools(availableTools),
        maxTokens,
        temperature: 0.3,
        stream: true,
      };

      // Route to actual provider (OpenAI, Anthropic, or Google)
      const providerStream = await this.providerRegistry.generateStream(
        universalRequest
      );

      // Convert provider stream back to AgentStream format for compatibility
      return this.convertProviderStreamToAgentFormat(providerStream);
    } catch (error) {
      logger.error("Failed to execute multi-provider streaming request", {
        error: error.message,
        model,
        maxTokens,
      });
      throw error;
    }
  }

  /**
   * Convert AgentStreamService format to Universal format
   */
  private convertToUniversalMessages(contents: any[]): UniversalMessage[] {
    return contents.map((content) => {
      if (!content.parts || content.parts.length === 0) {
        // Simple text content
        return {
          role: content.role || "user",
          content: content.text || "",
        };
      }

      // Handle multipart content (text + images + function calls)
      const messageContent: UniversalMessageContent[] = [];
      let hasMultipartContent = false;

      for (const part of content.parts) {
        if (part.text) {
          messageContent.push({
            type: "text",
            text: part.text,
          });
          hasMultipartContent = true;
        } else if (part.inlineData) {
          messageContent.push({
            type: "image",
            image: {
              mimeType: part.inlineData.mimeType,
              data: part.inlineData.data,
            },
          });
          hasMultipartContent = true;
        } else if (part.functionCall) {
          // For historical function calls, convert to descriptive text for context
          const args = part.functionCall.args || {};
          const argsStr = Object.keys(args).length > 0 ? ` with ${JSON.stringify(args)}` : '';
          const functionCallText = `I called the function ${part.functionCall.name}${argsStr}`;
          messageContent.push({
            type: "text", 
            text: functionCallText,
          });
          hasMultipartContent = true;
        } else if (part.functionResponse) {
          // For historical function responses, convert to descriptive text for context  
          const response = part.functionResponse.response;
          const responseStr = typeof response === 'object' ? JSON.stringify(response) : String(response);
          const responseText = `The function ${part.functionResponse.name} returned: ${responseStr}`;
          messageContent.push({
            type: "text",
            text: responseText,
          });
          hasMultipartContent = true;
        }
      }

      if (!hasMultipartContent) {
        // Fallback to text-only if no valid parts found
        return {
          role: content.role || "user", 
          content: "",
        };
      }

      // If only text content, flatten to string
      if (messageContent.length === 1 && messageContent[0].type === "text") {
        return {
          role: content.role || "user",
          content: messageContent[0].text || "",
        };
      }

      // Return multipart content
      return {
        role: content.role || "user",
        content: messageContent,
      };
    });
  }

  /**
   * Convert AgentStreamService tools to Universal format
   */
  private convertToUniversalTools(tools: Tool[]): UniversalTool[] {
    return tools
      .map((tool) => {
        const functionDecl = tool.functionDeclarations?.[0];
        if (!functionDecl?.name) {
          logger.warn("Skipping tool without valid function declaration", {
            tool: JSON.stringify(tool).substring(0, 100),
          });
          return null;
        }
        
        return {
          name: functionDecl.name,
          description: functionDecl.description || "",
          parameters: functionDecl.parameters || {},
        };
      })
      .filter((tool): tool is UniversalTool => tool !== null);
  }

  /**
   * Convert provider stream back to AgentStream format for AgentStreamService compatibility
   */
  private async *convertProviderStreamToAgentFormat(
    providerStream: ReadableStream
  ): AsyncGenerator<GenerateContentResponse> {
    const reader = providerStream.getReader();
    const decoder = new TextDecoder();
    
    // Reset buffer for new stream
    this.streamBuffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          // Process any remaining buffer content
          if (this.streamBuffer.trim()) {
            try {
              const universalResponse = JSON.parse(this.streamBuffer) as UniversalResponse;
              yield this.convertToAgentFormat(universalResponse);
            } catch (e) {
              logger.warn("Failed to parse final buffer content", {
                buffer: this.streamBuffer.substring(0, 100),
                error: e.message,
              });
            }
          }
          break;
        }

        // Decode the chunk and add to buffer
        const chunkText = decoder.decode(value, { stream: true });
        this.streamBuffer += chunkText;

        // Process complete lines from the buffer
        const lines = this.streamBuffer.split('\n');
        // Keep the last incomplete line in the buffer
        this.streamBuffer = lines.pop() || '';

        // Process each complete line
        for (const line of lines) {
          if (!line.trim()) continue;
          
          try {
            const universalResponse = JSON.parse(line) as UniversalResponse;

            yield this.convertToAgentFormat(universalResponse);
          } catch (parseError) {
            logger.warn("Failed to parse provider stream line as JSON", {
              error: parseError.message,
              line: line.substring(0, 100),
            });
          }
        }
      }
    } catch (error) {
      logger.error("Error in convertProviderStreamToAgentFormat", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Convert a universal response to AgentStream format
   */
  private convertToAgentFormat(universalResponse: UniversalResponse): GenerateContentResponse {
    const parts: Part[] = [];

    // Add text content if available
    if (universalResponse.content) {
      parts.push({ text: universalResponse.content });
    }

    // Add tool calls if available
    if (universalResponse.toolCalls && universalResponse.toolCalls.length > 0) {
      logger.info("Converting tool calls to AgentStream format", {
        toolCallsCount: universalResponse.toolCalls.length,
        provider: universalResponse.provider,
        model: universalResponse.model,
      });
      
      universalResponse.toolCalls.forEach((toolCall) => {
        parts.push({
          functionCall: {
            name: toolCall.name,
            args: toolCall.arguments,
          },
        });
      });
    }

    return {
      candidates: [
        {
          content: {
            parts: parts.length > 0 ? parts : [{ text: "" }],
          },
          finishReason: this.mapFinishReason(universalResponse.finishReason),
        },
      ],
      usageMetadata: universalResponse.usage
        ? {
            promptTokenCount: universalResponse.usage.inputTokens || 0,
            candidatesTokenCount: universalResponse.usage.outputTokens || 0,
            totalTokenCount: universalResponse.usage.totalTokens || 0,
          }
        : undefined,
      // Add required properties for GenerateContentResponse compatibility
      text: universalResponse.content || "",
      data: undefined,
      functionCalls: this.convertToGoogleFunctionCalls(universalResponse.toolCalls),
      executableCode: undefined,
      codeExecutionResult: undefined,
    } as GenerateContentResponse;
  }

  /**
   * Convert universal tool calls to Google function calls format
   */
  private convertToGoogleFunctionCalls(toolCalls?: UniversalToolCall[]): any {
    if (!toolCalls || toolCalls.length === 0) {
      return undefined;
    }

    return toolCalls.map((toolCall) => ({
      name: toolCall.name,
      args: toolCall.arguments,
    }));
  }

  /**
   * Map universal finish reason to AgentStream format
   */
  private mapFinishReason(reason: string): any {
    const reasonMap: Record<string, string> = {
      stop: "STOP",
      length: "MAX_TOKENS",
      content_filter: "SAFETY",
      error: "OTHER",
      continue: null, // ✅ FIX: Don't set finish reason for continuing chunks
    };
    return reasonMap[reason] !== undefined ? reasonMap[reason] : null;
  }
}
