import { Langfuse } from "langfuse-node";
import { createLogger } from "@/services/logger";
import type { TokenUsage } from "@/validators/agent.validators";
import type { Citation } from "@/services/pinecone/types";
import { LANGFUSE_CONFIG, validateLangFuseConfig } from "@/config/langfuse";

const logger = createLogger("TracingService");

// Enhanced metrics interface for comprehensive tracking
interface ConversationMetrics {
  // Basic conversation info
  userId: string;
  conversationId: string;
  model: string;
  projectId?: string;

  // Input/Output
  input: string;
  output: string;

  // Token usage and cost
  usage: TokenUsage;
  cost: number;

  // Performance metrics
  duration: number;
  chunkCount: number;
  textChunkCount: number;
  finishReason: string;

  // RAG metrics
  ragUsed: boolean;
  ragSuccess: boolean;
  ragToolCalls: number;
  ragProcessingTime: number;
  ragResultsCount: number;

  // Quality metrics
  citations?: Citation[];
  hasCustomInstructions: boolean;
  customInstructions?: string; // Add the actual custom instruction content
  resourceCount: number;
  historyLength: number;

  // Error tracking
  errors?: string[];
  warnings?: string[];
}

// Optimization: Batch configuration
interface BatchConfig {
  maxBatchSize: number;
  flushIntervalMs: number;
  maxRetries: number;
  retryDelayMs: number;
}

// Optimization: Metadata builder interfaces for better structure
interface BaseMetadata {
  // Core identifiers
  conversation_id?: string;
  user_id?: string;
  request_id?: string | null;
  trace_id?: string;

  // Service info
  service: string;
  operation: string;
  timestamp: string;
}

interface ConversationMetadata extends BaseMetadata {
  // Model and performance
  model: string;
  cost_usd: number;
  duration_ms: number;
  finish_reason: string;

  // Token usage
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;

  // Message and content metrics
  message_length: number;
  chunk_count: number;
  text_chunk_count: number;

  // Context information
  has_custom_instructions: boolean;
  custom_instructions?: string;

  // RAG information
  rag_enabled: boolean;
  rag_successful: boolean;
  rag_tool_calls: number;
  rag_processing_time_ms: number;
  rag_results_count: number;

  // Resource and citation information
  citation_count: number;
  resource_count: number;
  history_length: number;

  // Error tracking
  error_count: number;
  warning_count: number;

  // Computed performance metrics
  tokens_per_second: number;
  response_quality_score: number;

  // Additional fields
  project_id?: string;
}

interface FunctionCallMetadata extends BaseMetadata {
  // Function details
  function_name: string;
  operation: string;

  // Performance metrics
  processing_time_ms: number;
  success: boolean;

  // Size metrics
  args_size: number;
  result_size: number;

  // Error information
  error_message?: string | null;
  has_error: boolean;
  error_category?: number | null;
}

export class TracingService {
  private static instance: TracingService;
  private langfuse: Langfuse;
  private batchConfig: BatchConfig;
  private pendingOperations: Array<() => Promise<void>> = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private isFlushing = false;
  private errorCount = 0;
  private lastErrorTime = 0;

  private constructor() {
    try {
      // Validate configuration
      validateLangFuseConfig();

      // Optimization: Batch configuration
      this.batchConfig = {
        maxBatchSize: 10,
        flushIntervalMs: 5000, // 5 seconds
        maxRetries: 3,
        retryDelayMs: 1000,
      };

      // Log configuration details (without sensitive data)
      logger.info("Initializing LangFuse with optimized config", {
        baseUrl: LANGFUSE_CONFIG.baseUrl,
        publicKeyExists: !!LANGFUSE_CONFIG.publicKey,
        secretKeyExists: !!LANGFUSE_CONFIG.secretKey,
        batchSize: this.batchConfig.maxBatchSize,
        flushInterval: this.batchConfig.flushIntervalMs,
      });

      // Initialize LangFuse client
      this.langfuse = new Langfuse({
        publicKey: LANGFUSE_CONFIG.publicKey || "",
        secretKey: LANGFUSE_CONFIG.secretKey || "",
        baseUrl: LANGFUSE_CONFIG.baseUrl,
      });

      logger.info("LangFuse tracing initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize LangFuse", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  public static getInstance(): TracingService {
    if (!TracingService.instance) {
      try {
        TracingService.instance = new TracingService();
        logger.info("TracingService singleton instance created");
      } catch (error) {
        logger.error("Failed to create TracingService instance", {
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    }
    return TracingService.instance;
  }

  /**
   * Optimization: Create base metadata with common fields
   */
  private createBaseMetadata(
    operation: string,
    conversationId?: string,
    userId?: string,
    requestId?: string,
    traceId?: string
  ): BaseMetadata {
    return {
      conversation_id: conversationId,
      user_id: userId,
      request_id: requestId || null,
      trace_id: traceId,
      service: "aida-service",
      operation,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Optimization: Create conversation metadata efficiently
   */
  private createConversationMetadata(
    metrics: ConversationMetrics,
    requestId?: string,
    traceId?: string
  ): ConversationMetadata {
    const baseMetadata = this.createBaseMetadata(
      "ai_response",
      metrics.conversationId,
      metrics.userId,
      requestId,
      traceId
    );

    const tokensPerSecond =
      metrics.duration > 0
        ? Math.round(
            ((metrics.usage.totalTokens || 0) / (metrics.duration / 1000)) * 100
          ) / 100
        : 0;

    return {
      ...baseMetadata,
      model: metrics.model,
      cost_usd: metrics.cost,
      duration_ms: metrics.duration,
      finish_reason: metrics.finishReason,
      input_tokens: metrics.usage.inputTokens || 0,
      output_tokens: metrics.usage.outputTokens || 0,
      total_tokens: metrics.usage.totalTokens || 0,
      message_length: metrics.output.length,
      chunk_count: metrics.chunkCount,
      text_chunk_count: metrics.textChunkCount,
      has_custom_instructions: metrics.hasCustomInstructions,
      custom_instructions: metrics.customInstructions,
      rag_enabled: metrics.ragUsed,
      rag_successful: metrics.ragSuccess,
      rag_tool_calls: metrics.ragToolCalls,
      rag_processing_time_ms: metrics.ragProcessingTime,
      rag_results_count: metrics.ragResultsCount,
      citation_count: metrics.citations?.length || 0,
      resource_count: metrics.resourceCount,
      history_length: metrics.historyLength,
      error_count: metrics.errors?.length || 0,
      warning_count: metrics.warnings?.length || 0,
      tokens_per_second: tokensPerSecond,
      response_quality_score: this.getFinishReasonScore(metrics.finishReason),
      project_id: metrics.projectId,
    };
  }

  /**
   * Optimization: Create function call metadata efficiently
   */
  private createFunctionCallMetadata(
    conversationId: string,
    userId: string,
    functionName: string,
    sanitizedArgs: any,
    sanitizedResult: any,
    processingTime: number,
    success: boolean,
    error?: string,
    requestId?: string,
    traceId?: string
  ): FunctionCallMetadata {
    const baseMetadata = this.createBaseMetadata(
      "function_call",
      conversationId,
      userId,
      requestId,
      traceId
    );

    const argsSize = JSON.stringify(sanitizedArgs).length;
    const resultSize = success ? JSON.stringify(sanitizedResult).length : 0;

    return {
      ...baseMetadata,
      function_name: functionName,
      processing_time_ms: processingTime,
      success,
      args_size: argsSize,
      result_size: resultSize,
      error_message: error || null,
      has_error: !success,
      error_category: !success && error ? this.categorizeError(error) : null,
    };
  }

  /**
   * Optimization: Add operation to batch queue
   */
  private async addToBatch(operation: () => Promise<void>): Promise<void> {
    this.pendingOperations.push(operation);

    // Flush immediately if batch is full
    if (this.pendingOperations.length >= this.batchConfig.maxBatchSize) {
      await this.flushBatch();
    } else if (!this.flushTimer) {
      // Set timer for periodic flush
      this.flushTimer = setTimeout(
        () => this.flushBatch(),
        this.batchConfig.flushIntervalMs
      );
    }
  }

  /**
   * Optimization: Flush batch operations with retry logic
   */
  private async flushBatch(): Promise<void> {
    if (this.isFlushing || this.pendingOperations.length === 0) {
      return;
    }

    this.isFlushing = true;

    try {
      const operations = [...this.pendingOperations];
      this.pendingOperations = [];

      if (this.flushTimer) {
        clearTimeout(this.flushTimer);
        this.flushTimer = null;
      }

      // Execute all operations in parallel
      await Promise.allSettled(operations.map((op) => op()));

      // Reset error count on successful flush
      this.errorCount = 0;
    } catch (error) {
      this.errorCount++;
      this.lastErrorTime = Date.now();

      // Retry logic for failed operations
      if (this.errorCount < this.batchConfig.maxRetries) {
        setTimeout(() => this.flushBatch(), this.batchConfig.retryDelayMs);
      }
    } finally {
      this.isFlushing = false;
    }
  }

  /**
   * Optimization: Check if tracing should be skipped due to errors
   */
  private shouldSkipTracing(): boolean {
    const now = Date.now();
    const timeSinceLastError = now - this.lastErrorTime;

    // Skip tracing if too many recent errors
    if (
      this.errorCount >= this.batchConfig.maxRetries &&
      timeSinceLastError < 60000
    ) {
      return true;
    }

    return false;
  }

  /**
   * Create a comprehensive trace for a conversation with all available metrics
   */
  public async traceConversation(
    metrics: ConversationMetrics,
    requestId?: string
  ) {
    // Optimization: Skip tracing if too many errors
    if (this.shouldSkipTracing()) {
      logger.debug("Skipping tracing due to recent errors", { requestId });
      return;
    }

    // Optimization: Single consolidated log instead of multiple logs
    logger.info("Creating conversation trace", {
      conversationId: metrics.conversationId,
      userId: metrics.userId,
      model: metrics.model,
      duration: metrics.duration,
      cost: metrics.cost,
      requestId: requestId || "not-provided",
      hasCustomInstructions: metrics.hasCustomInstructions,
      ragUsed: metrics.ragUsed,
      citations: metrics.citations?.length || 0,
    });

    // Create unique trace ID for this specific AI response
    const traceId = requestId
      ? `conversation-${metrics.conversationId}-${requestId}`
      : `conversation-${metrics.conversationId}-${Date.now()}`;

    // Optimization: Create optimized metadata using the builder
    const metadata = this.createConversationMetadata(
      metrics,
      requestId,
      traceId
    );

    // Debug: Log the metadata being sent to verify structure
    logger.info("Creating Langfuse trace with metadata", {
      conversationId: metrics.conversationId,
      traceId: traceId,
      requestId: requestId,
      metadataPreview: {
        conversation_id: metadata.conversation_id,
        model: metadata.model,
        cost_usd: metadata.cost_usd,
        duration_ms: metadata.duration_ms,
        total_tokens: metadata.total_tokens,
        has_custom_instructions: metadata.has_custom_instructions,
        rag_enabled: metadata.rag_enabled,
      },
    });

    // Optimization: Add to batch instead of immediate execution
    await this.addToBatch(async () => {
      try {
        // Create individual trace within the session
        const trace = this.langfuse.trace({
          id: traceId,
          sessionId: metrics.conversationId,
          userId: metrics.userId,
          name: "AI Response",
          input: metrics.input,
          output: metrics.output,
          tags: [
            "ai-response",
            "conversation",
            metrics.model,
            metrics.projectId ? `project-${metrics.projectId}` : "no-project",
            metrics.ragUsed ? "rag-enabled" : "rag-disabled",
            metrics.hasCustomInstructions
              ? "custom-instructions"
              : "default-instructions",
            `finish-${metrics.finishReason.toLowerCase()}`,
            requestId ? `request-${requestId}` : "no-request-id",
          ].filter(Boolean),
          metadata,
        });

        // Create a generation span within the trace
        const generationId = requestId
          ? `llm-gen-${requestId}`
          : `llm-gen-${Date.now()}`;

        const generation = trace.generation({
          id: generationId,
          name: "ai_response_generation",
          model: metrics.model,
          input: metrics.input,
          output: metrics.output,
          usage: {
            input: metrics.usage.inputTokens || 0,
            output: metrics.usage.outputTokens || 0,
            total: metrics.usage.totalTokens || 0,
          },
          metadata: {
            ...metadata,
            generation_id: generationId,
          },
        });

        // Add performance scores
        generation.score({
          name: "response_time_ms",
          value: metrics.duration,
        });

        generation.score({
          name: "cost_usd",
          value: metrics.cost,
        });

        generation.score({
          name: "tokens_per_second",
          value: metadata.tokens_per_second,
        });

        generation.score({
          name: "finish_reason_score",
          value: metadata.response_quality_score,
        });

        // Add quality scores
        if (metrics.citations && metrics.citations.length > 0) {
          generation.score({
            name: "citation_count",
            value: metrics.citations.length,
          });
        }

        if (metrics.hasCustomInstructions) {
          generation.score({
            name: "has_custom_instructions",
            value: 1,
          });
        }

        await generation.end();

        // Debug: Log successful trace creation
        logger.info("Langfuse trace created successfully", {
          traceId: traceId,
          conversationId: metrics.conversationId,
          requestId: requestId,
        });
      } catch (error) {
        logger.warn("Failed to create conversation trace", {
          error: error instanceof Error ? error.message : String(error),
          conversationId: metrics.conversationId,
          requestId,
        });
      }
    });
  }

  /**
   * Trace function calls with detailed metrics
   */
  public async traceFunctionCall(
    userId: string,
    conversationId: string,
    functionName: string,
    functionArgs: any,
    functionResult: any,
    processingTime: number,
    success: boolean,
    error?: string,
    requestId?: string
  ) {
    // Optimization: Skip tracing if too many errors
    if (this.shouldSkipTracing()) {
      logger.debug("Skipping function call tracing due to recent errors", {
        requestId,
      });
      return;
    }

    // Create unique trace ID for this specific function call
    const traceId = requestId
      ? `func-${functionName}-${conversationId}-${requestId}`
      : `func-${functionName}-${conversationId}-${Date.now()}`;

    // Sanitize sensitive data
    const sanitizedArgs = this.sanitizeFunctionArgs(functionArgs);
    const sanitizedResult = success
      ? this.sanitizeFunctionResult(functionResult)
      : null;

    // Optimization: Create optimized metadata using the builder
    const funcMetadata = this.createFunctionCallMetadata(
      conversationId,
      userId,
      functionName,
      sanitizedArgs,
      sanitizedResult,
      processingTime,
      success,
      error,
      requestId,
      traceId
    );

    // Optimization: Add to batch instead of immediate execution
    await this.addToBatch(async () => {
      try {
        // Create individual trace within the session
        const trace = this.langfuse.trace({
          id: traceId,
          sessionId: conversationId,
          userId,
          name: `Function: ${functionName}`,
          input: JSON.stringify(sanitizedArgs),
          output: success
            ? JSON.stringify(sanitizedResult)
            : error || "Function call failed",
          tags: [
            "function-call",
            "conversation",
            "tool-execution",
            functionName,
            success ? "function-success" : "function-error",
            `function-${functionName}`,
            requestId ? `request-${requestId}` : "no-request-id",
          ].filter(Boolean),
          metadata: funcMetadata,
        });

        // Create a generation span within the trace
        const generationId = requestId
          ? `func-gen-${functionName}-${requestId}`
          : `func-gen-${functionName}-${Date.now()}`;

        const generation = trace.generation({
          id: generationId,
          name: `function_${functionName}_generation`,
          model: "function-system",
          input: JSON.stringify(sanitizedArgs),
          output: success
            ? JSON.stringify(sanitizedResult)
            : error || "Function call failed",
          metadata: {
            ...funcMetadata,
            generation_id: generationId,
          },
        });

        // Add performance scores
        generation.score({
          name: "function_processing_time_ms",
          value: processingTime,
        });

        generation.score({
          name: "function_success",
          value: success ? 1 : 0,
        });

        // Add function-specific metrics
        if (success && sanitizedResult) {
          generation.score({
            name: "function_result_size",
            value: funcMetadata.result_size,
          });
        }

        // Add error category if function failed
        if (!success && error) {
          generation.score({
            name: "function_error_category",
            value: funcMetadata.error_category,
          });
        }

        await generation.end();
      } catch (error) {
        logger.warn("Failed to create function call trace", {
          error: error instanceof Error ? error.message : String(error),
          conversationId,
          functionName,
          requestId,
        });
      }
    });
  }

  /**
   * Sanitize function arguments to remove sensitive data
   */
  private sanitizeFunctionArgs(args: any): any {
    if (!args || typeof args !== "object") {
      return args;
    }

    const sensitiveFields = [
      "password",
      "token",
      "secret",
      "key",
      "apiKey",
      "authorization",
    ];
    const sanitized: any = {};

    Object.entries(args).forEach(([key, value]) => {
      if (sensitiveFields.includes(key.toLowerCase())) {
        sanitized[key] = "[REDACTED]";
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = this.sanitizeFunctionArgs(value);
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }

  /**
   * Sanitize function results to remove sensitive data
   */
  private sanitizeFunctionResult(result: any): any {
    if (!result || typeof result !== "object") {
      return result;
    }

    const sensitiveFields = [
      "password",
      "token",
      "secret",
      "key",
      "apiKey",
      "authorization",
    ];
    const sanitized: any = {};

    Object.entries(result).forEach(([key, value]) => {
      if (sensitiveFields.includes(key.toLowerCase())) {
        sanitized[key] = "[REDACTED]";
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = this.sanitizeFunctionResult(value);
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }

  /**
   * Categorize error for scoring purposes
   */
  private categorizeError(error: string): number {
    const errorLower = error.toLowerCase();

    if (errorLower.includes("timeout") || errorLower.includes("time")) {
      return 0.3; // Timeout errors
    } else if (
      errorLower.includes("network") ||
      errorLower.includes("connection")
    ) {
      return 0.2; // Network errors
    } else if (
      errorLower.includes("auth") ||
      errorLower.includes("permission")
    ) {
      return 0.1; // Authentication/permission errors
    } else if (
      errorLower.includes("validation") ||
      errorLower.includes("invalid")
    ) {
      return 0.4; // Validation errors
    } else if (errorLower.includes("not found") || errorLower.includes("404")) {
      return 0.5; // Not found errors
    } else {
      return 0.2; // Unknown/other errors
    }
  }

  /**
   * Get a quality score based on finish reason
   */
  private getFinishReasonScore(finishReason: string): number {
    switch (finishReason) {
      case "STOP":
        return 1.0; // Perfect completion
      case "MAX_TOKENS":
        return 0.7; // Truncated but complete
      case "SAFETY":
        return 0.5; // Safety filter triggered
      case "CIRCUIT_BREAKER_TOTAL_CHUNKS":
      case "CIRCUIT_BREAKER_EMPTY":
      case "CIRCUIT_BREAKER_WHITESPACE":
        return 0.3; // Circuit breaker triggered
      default:
        return 0.5; // Unknown reason
    }
  }

  /**
   * Optimization: Force flush all pending operations
   */
  public async forceFlush(): Promise<void> {
    if (this.pendingOperations.length > 0) {
      logger.info("Force flushing pending operations", {
        operationCount: this.pendingOperations.length,
      });
      await this.flushBatch();
    }
  }

  /**
   * Optimization: Clean up resources
   */
  public async cleanup(): Promise<void> {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    await this.forceFlush();

    logger.info("TracingService cleanup completed");
  }

  /**
   * Optimization: Get current batch status
   */
  public getBatchStatus(): {
    pendingOperations: number;
    isFlushing: boolean;
    errorCount: number;
    lastErrorTime: number;
  } {
    return {
      pendingOperations: this.pendingOperations.length,
      isFlushing: this.isFlushing,
      errorCount: this.errorCount,
      lastErrorTime: this.lastErrorTime,
    };
  }

  /**
   * Get the LangFuse client for debugging
   */
  public getLangfuseClient() {
    return this.langfuse;
  }
}
