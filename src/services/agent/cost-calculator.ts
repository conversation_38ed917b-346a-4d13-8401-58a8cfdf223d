import type { TokenUsage } from "@/validators/agent.validators";
import { getModel, calculateTokenCost } from "@/config/models";
import { createLogger } from "@/services/logger";

const logger = createLogger("CostCalculator");

// Constants for validation
const MAX_REASONABLE_TOKENS = 1_000_000; // 1M tokens
const MAX_REASONABLE_COST = 100; // $100

/**
 * Detailed cost breakdown interface
 */
export interface CostBreakdown {
  inputCost: number;
  outputCost: number;
  totalCost: number;
  pricing: ReturnType<typeof getModelPricingInfo>;
}

/**
 * Cost efficiency metrics interface
 */
export interface CostEfficiencyMetrics {
  costPerInputToken: number;
  costPerOutputToken: number;
  tokensPerDollar: number;
  costPerMessageChar: number;
  modelCostEfficiency: "high" | "standard";
  estimatedFlashCost: number;
  estimatedProCost: number;
}

/**
 * Calculate the cost of an AI request based on token usage and model
 */
export function calculateCost(
  usage: TokenUsage | null,
  model: string
): number | null {
  if (!usage || !model) {
    logger.debug("Invalid usage or model provided", { usage, model });
    return null;
  }

  const { inputTokens = 0, outputTokens = 0 } = usage;

  if (inputTokens === 0 && outputTokens === 0) {
    logger.debug("No tokens used", { usage });
    return null;
  }

  // Validate reasonable token counts
  if (
    inputTokens > MAX_REASONABLE_TOKENS ||
    outputTokens > MAX_REASONABLE_TOKENS
  ) {
    logger.warn("Unusually high token count detected", {
      inputTokens,
      outputTokens,
      model,
    });
  }

  try {
    const cost = calculateTokenCost(inputTokens, outputTokens, model);

    // Validate reasonable cost
    if (cost > MAX_REASONABLE_COST) {
      logger.warn("Unusually high cost calculated", {
        cost,
        inputTokens,
        outputTokens,
        model,
      });
    }

    return cost;
  } catch (error) {
    logger.error("Failed to calculate cost", {
      error: error instanceof Error ? error.message : String(error),
      usage,
      model,
    });
    return null;
  }
}

/**
 * Get the current pricing for a model
 */
export function getModelPricingInfo(model: string) {
  try {
    return getModel(model).pricing;
  } catch (error) {
    logger.error("Failed to get model pricing", {
      error: error instanceof Error ? error.message : String(error),
      model,
    });
    throw error;
  }
}

/**
 * Validate that usage statistics are reasonable
 */
export function validateUsage(usage: TokenUsage): boolean {
  if (!usage) {
    return false;
  }

  const { inputTokens = 0, outputTokens = 0, totalTokens = 0 } = usage;

  // Basic sanity checks
  if (inputTokens < 0 || outputTokens < 0 || totalTokens < 0) {
    logger.warn("Negative token counts detected", { usage });
    return false;
  }

  // Check for reasonable token limits
  if (
    inputTokens > MAX_REASONABLE_TOKENS ||
    outputTokens > MAX_REASONABLE_TOKENS ||
    totalTokens > MAX_REASONABLE_TOKENS
  ) {
    logger.warn("Token counts exceed reasonable limits", { usage });
    return false;
  }

  // If totalTokens is provided, it should be approximately equal to input + output
  if (totalTokens > 0) {
    const calculatedTotal = inputTokens + outputTokens;
    const tolerance = Math.max(10, calculatedTotal * 0.1); // 10% tolerance or minimum 10 tokens

    if (Math.abs(totalTokens - calculatedTotal) > tolerance) {
      logger.warn("Total tokens don't match sum of input and output", {
        usage,
        calculatedTotal,
        difference: Math.abs(totalTokens - calculatedTotal),
        tolerance,
      });
      return false;
    }
  }

  return true;
}

/**
 * Calculate cost breakdown for detailed analysis
 */
export function calculateCostBreakdown(
  usage: TokenUsage,
  model: string
): CostBreakdown | null {
  if (!validateUsage(usage)) {
    logger.debug("Invalid usage provided for cost breakdown", { usage });
    return null;
  }

  try {
    const { inputTokens = 0, outputTokens = 0 } = usage;
    const pricing = getModel(model).pricing;

    const inputCost = (inputTokens / 1_000_000) * pricing.input;
    const outputCost = (outputTokens / 1_000_000) * pricing.output;
    const totalCost = inputCost + outputCost;

    return {
      inputCost,
      outputCost,
      totalCost,
      pricing,
    };
  } catch (error) {
    logger.error("Failed to calculate cost breakdown", {
      error: error instanceof Error ? error.message : String(error),
      usage,
      model,
    });
    return null;
  }
}

/**
 * Calculate cost efficiency metrics for analytics
 */
export function calculateCostEfficiencyMetrics(
  usage: TokenUsage,
  model: string
): CostEfficiencyMetrics | null {
  const breakdown = calculateCostBreakdown(usage, model);
  if (!breakdown) {
    return null;
  }

  try {
    const { inputTokens = 0, outputTokens = 0 } = usage;
    const totalTokens = inputTokens + outputTokens;
    const { totalCost } = breakdown;

    // Calculate per-token costs
    const costPerInputToken =
      inputTokens > 0 ? breakdown.inputCost / inputTokens : 0;
    const costPerOutputToken =
      outputTokens > 0 ? breakdown.outputCost / outputTokens : 0;

    // Calculate efficiency metrics
    const tokensPerDollar = totalCost > 0 ? totalTokens / totalCost : 0;
    const costPerMessageChar =
      usage.inputCharacters && usage.inputCharacters > 0
        ? totalCost / usage.inputCharacters
        : 0;

    // Determine model efficiency
    const modelCostEfficiency =
      model === "gemini-2.5-flash" ? "high" : "standard";

    // Calculate estimated costs for comparison
    const flashBreakdown = calculateCostBreakdown(usage, "gemini-2.5-flash");
    const proBreakdown = calculateCostBreakdown(usage, "gemini-2.5-pro");

    return {
      costPerInputToken,
      costPerOutputToken,
      tokensPerDollar,
      costPerMessageChar,
      modelCostEfficiency,
      estimatedFlashCost: flashBreakdown?.totalCost || 0,
      estimatedProCost: proBreakdown?.totalCost || 0,
    };
  } catch (error) {
    logger.error("Failed to calculate cost efficiency metrics", {
      error: error instanceof Error ? error.message : String(error),
      usage,
      model,
    });
    return null;
  }
}
