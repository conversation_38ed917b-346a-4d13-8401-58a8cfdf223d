/**
 * Progress Reporter - Provides meaningful progress updates to users
 * 
 * Based on research from modern AI tools (<PERSON>, <PERSON>, <PERSON>urs<PERSON>), users want to see:
 * - High-level thinking steps, not internal operations
 * - Clear indication of AI "working" on their request
 * - Estimated complexity and progress
 */

import { createLogger } from "@/services/logger";

const logger = createLogger("ProgressReporter");

export interface ProgressStep {
  type: 'progress'
  step: ProgressStepType
  message: string
  timestamp: number
  metadata?: {
    fileCount?: number
    searchResults?: number
    complexity?: 'simple' | 'complex'
    estimatedTime?: number
    details?: string
  }
}

export type ProgressStepType = 
  | 'analyzing'      // Analyzing user input and documents
  | 'searching'      // Searching through knowledge base
  | 'reasoning'      // AI thinking/reasoning process
  | 'generating'     // Generating the response
  | 'synthesizing'   // Combining information into final answer

/**
 * Context for time estimation
 */
interface EstimationContext {
  fileCount?: number;
  searchResults?: number;
  complexity?: 'simple' | 'complex';
}

export class ProgressReporter {
  private sendProgress: (step: ProgressStep) => void;

  constructor(sendProgress: (step: ProgressStep) => void) {
    this.sendProgress = sendProgress;
  }

  /**
   * Report document analysis progress
   */
  analyzing(fileCount: number, complexity: 'simple' | 'complex' = 'simple'): void {
    const message = 'Analyzing documents...';

    this.sendProgress({
      type: 'progress',
      step: 'analyzing',
      message,
      timestamp: Date.now(),
      metadata: {
        fileCount,
        complexity,
        estimatedTime: this.estimateTime('analyzing', { fileCount, complexity })
      }
    });

    logger.info("Progress: Analyzing", { fileCount, complexity });
  }

  /**
   * Report search/retrieval progress
   */
  searching(fileCount: number, searchQuery?: string): void {
    const message = 'Searching knowledge base...';

    this.sendProgress({
      type: 'progress',
      step: 'searching',
      message,
      timestamp: Date.now(),
      metadata: {
        fileCount,
        complexity: fileCount > 5 ? 'complex' : 'simple',
        estimatedTime: this.estimateTime('searching', { fileCount })
      }
    });

    logger.info("Progress: Searching", { fileCount, searchQuery: searchQuery?.substring(0, 50) });
  }

  /**
   * Report AI reasoning/thinking progress  
   */
  reasoning(complexity: 'simple' | 'complex' = 'simple', details?: string): void {
    const messages = {
      simple: 'Understanding your request...',
      complex: 'Analyzing this complex question...'
    };

    this.sendProgress({
      type: 'progress',
      step: 'reasoning',
      message: messages[complexity],
      timestamp: Date.now(),
      metadata: {
        complexity,
        details,
        estimatedTime: this.estimateTime('reasoning', { complexity })
      }
    });

    logger.info("Progress: Reasoning", { complexity, details });
  }

  /**
   * Report response generation progress
   */
  generating(): void {
    this.sendProgress({
      type: 'progress',
      step: 'generating',
      message: 'Generating response...',
      timestamp: Date.now(),
      metadata: {
        complexity: 'simple',
        estimatedTime: 2000 // Usually quick once we start generating
      }
    });

    logger.info("Progress: Generating response");
  }

  /**
   * Report information synthesis progress
   */
  synthesizing(searchResults: number): void {
    const message = 'Synthesizing information...';

    this.sendProgress({
      type: 'progress',
      step: 'synthesizing',
      message,
      timestamp: Date.now(),
      metadata: {
        searchResults,
        complexity: searchResults > 3 ? 'complex' : 'simple',
        estimatedTime: this.estimateTime('synthesizing', { searchResults })
      }
    });

    logger.info("Progress: Synthesizing", { searchResults });
  }

  /**
   * Estimate time remaining for each step type
   */
  private estimateTime(step: ProgressStepType, context: EstimationContext): number {
    switch (step) {
      case 'analyzing':
        return Math.min(3000, (context.fileCount || 1) * 500); // 500ms per file, max 3s
      case 'searching':
        return Math.min(5000, (context.fileCount || 1) * 800); // 800ms per file, max 5s  
      case 'reasoning':
        return context.complexity === 'complex' ? 8000 : 3000; // 3-8s for reasoning
      case 'generating':
        return 2000; // Usually 2s once generation starts
      case 'synthesizing':
        return Math.min(4000, (context.searchResults || 1) * 600); // 600ms per result, max 4s
      default:
        return 2000;
    }
  }
}

/**
 * SSE Response interface for progress reporting
 */
export interface SSEResponse {
  write: (data: string) => void;
}

/**
 * Create a progress reporter from SSE response stream
 */
export function createProgressReporter(res: SSEResponse): ProgressReporter {
  const sendProgress = (step: ProgressStep): void => {
    try {
      const data = `data: ${JSON.stringify(step)}\n\n`;
      res.write(data);
    } catch (error) {
      logger.error("Failed to send progress update", { 
        error: error instanceof Error ? error.message : String(error),
        step: step.step 
      });
    }
  };

  return new ProgressReporter(sendProgress);
}