import { createLogger } from "@/services/logger";
import { AgentUtilsService } from "../utils/AgentUtilsService";
import { models } from "@/schemas";
import type { ConversationImage } from "@/schemas/agent/ConversationImage.model";
import type { ImageData } from "@/validators/agent.validators";
import type { Part } from "@google/genai";
import {
  gcsUploadContent,
  generateSignedUrlForRead,
  deleteFilesByPrefix,
  deleteFiles,
} from "@/services/storage";
import { gcsConversationImagesFolder } from "@/config";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import crypto from "crypto";
import sharp from "sharp";
import { Op } from "sequelize";

const logger = createLogger("ConversationImageService");

export interface ProcessedConversationImage {
  id: string;
  originalData: ImageData;
  buffer: Buffer;
  optimizedBuffer?: Buffer;
  thumbnailBuffer?: Buffer;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
  inlineDataPart: Part;
  hash: string;
  gcsPath: string;
  thumbnailPath?: string;
}

export interface ConversationImageResult {
  images: ProcessedConversationImage[];
  storedImages: ConversationImage[];
  inlineDataParts: Part[];
  totalSize: number;
  errors: string[];
}

/**
 * ConversationImageService - Complete image handling for conversations
 *
 * Consolidates all image-related operations:
 * - Validation and processing
 * - GCS storage and database persistence
 * - Retrieval and URL generation
 * - Cleanup and lifecycle management
 */
export class ConversationImageService {
  /**
   * Process and store images for a conversation message
   */
  async processAndStoreImages(
    images: ImageData[],
    conversationId: string,
    messageId: string,
    createdById: string
  ): Promise<ConversationImageResult> {
    const result: ConversationImageResult = {
      images: [],
      storedImages: [],
      inlineDataParts: [],
      totalSize: 0,
      errors: [],
    };

    if (!images || images.length === 0) {
      return result;
    }

    // Validate rate limits and quotas
    await this.validateImageQuotas(images, conversationId, createdById);

    logger.info("Processing conversation images", {
      imageCount: images.length,
      conversationId,
      messageId,
      createdById,
    });

    // Process images in parallel with error handling
    const processingPromises = images.map(async (imageData, index) => {
      try {
        const processed = await this.processAndStoreSingleImage(
          imageData,
          conversationId,
          messageId,
          createdById,
          index
        );

        result.images.push(processed);
        result.storedImages.push(processed.conversationImage);
        result.inlineDataParts.push(processed.inlineDataPart);
        result.totalSize += processed.metadata.size;

        // Track successful image processing
        this.trackImageProcessingSuccess(
          processed,
          conversationId,
          createdById
        );
      } catch (error) {
        const errorMessage = `Image ${index + 1}: ${
          error instanceof Error ? error.message : String(error)
        }`;
        result.errors.push(errorMessage);

        // Track image processing failure
        this.trackImageProcessingError(
          error,
          imageData,
          conversationId,
          createdById,
          index
        );

        logger.error("Failed to process image", {
          imageIndex: index,
          error: errorMessage,
          filename: imageData.filename,
          conversationId,
          messageId,
          createdById,
        });
      }
    });

    await Promise.all(processingPromises);

    // Update message image counts only for successfully processed images
    if (result.storedImages.length > 0) {
      await this.updateMessageImageCounts(messageId);
    }

    logger.info("Conversation images processing completed", {
      totalImages: images.length,
      processedImages: result.images.length,
      errors: result.errors.length,
      totalSize: result.totalSize,
      successRate: `${Math.round(
        (result.images.length / images.length) * 100
      )}%`,
    });

    return result;
  }

  /**
   * Process and store a single image
   */
  private async processAndStoreSingleImage(
    imageData: ImageData,
    conversationId: string,
    messageId: string,
    createdById: string,
    index: number
  ): Promise<
    ProcessedConversationImage & { conversationImage: ConversationImage }
  > {
    const startTime = Date.now();

    try {
      // 1. Validate and process image
      this.validateImageData(imageData);
      const buffer = this.base64ToBuffer(imageData.data);
      await this.validateImageContent(buffer, imageData.mimeType);
      const metadata = await this.getImageMetadata(buffer);

      // 2. Generate hash and paths
      const hash = this.generateImageHash(buffer);

      // Check for duplicate images to avoid reprocessing
      const existingImage = await this.findDuplicateImages(
        hash,
        conversationId
      );
      if (existingImage) {
        logger.info("Duplicate image detected, reusing existing", {
          imageId: existingImage.id,
          hash,
          conversationId,
          messageId,
        });

        // Create new record pointing to same GCS paths but different message
        const duplicateRecord = await models.ConversationImage.create({
          id: uuidv4(),
          conversationId,
          messageId,
          createdById,
          originalFileName: imageData.filename || `image-${index + 1}.jpg`,
          mimeType: imageData.mimeType,
          fileSize: metadata.size,
          gcsPath: existingImage.gcsPath,
          thumbnailPath: existingImage.thumbnailPath,
          width: metadata.width,
          height: metadata.height,
          inlineDataHash: hash,
        });

        const duplicateRecordData = duplicateRecord.get({ plain: true });
        const processingTime = Date.now() - startTime;
        logger.info("Image processing completed (duplicate)", {
          processingTime,
          imageId: duplicateRecordData.id,
          isDuplicate: true,
        });

        return {
          id: duplicateRecordData.id,
          originalData: imageData,
          buffer,
          optimizedBuffer: undefined,
          thumbnailBuffer: undefined,
          metadata,
          inlineDataPart: this.createInlineDataPart(buffer, imageData.mimeType),
          hash,
          gcsPath: existingImage.gcsPath,
          thumbnailPath: existingImage.thumbnailPath,
          conversationImage: duplicateRecordData,
        };
      }

      const imageId = uuidv4();
      const filename = imageData.filename || `image-${index + 1}.jpg`;
      const gcsPath = this.generateImagePath(
        createdById,
        conversationId,
        messageId,
        filename
      );

      // 3. Optimize and generate thumbnail (in parallel for better performance)
      const [optimizedBuffer, thumbnailBuffer] = await Promise.all([
        this.optimizeImageIfNeeded(buffer, metadata, imageData.mimeType),
        this.generateThumbnail(buffer),
      ]);

      const thumbnailPath = thumbnailBuffer
        ? this.generateThumbnailPath(
            createdById,
            conversationId,
            messageId,
            filename
          )
        : undefined;

      // 4. Upload to GCS (in parallel)
      const bufferToUpload = optimizedBuffer || buffer;
      const uploadPromises = [
        gcsUploadContent(bufferToUpload, gcsPath, {
          contentType: imageData.mimeType,
          metadata: {
            uploadedAt: new Date().toISOString(),
            originalName: filename,
            conversationId,
            messageId,
            createdById,
          },
        }),
      ];

      if (thumbnailBuffer && thumbnailPath) {
        uploadPromises.push(
          gcsUploadContent(thumbnailBuffer, thumbnailPath, {
            contentType: "image/webp",
            metadata: {
              uploadedAt: new Date().toISOString(),
              originalName: filename,
              conversationId,
              messageId,
              createdById,
              type: "thumbnail",
            },
          })
        );
      }

      await Promise.all(uploadPromises);

      // 5. Create database record
      const conversationImageData = {
        id: imageId,
        conversationId,
        messageId,
        createdById,
        originalFileName: filename,
        mimeType: imageData.mimeType,
        fileSize: metadata.size,
        gcsPath,
        thumbnailPath,
        width: metadata.width,
        height: metadata.height,
        inlineDataHash: hash,
      };

      const conversationImage = await models.ConversationImage.create(
        conversationImageData
      );

      // 6. Create inline data part
      const inlineDataPart = this.createInlineDataPart(
        bufferToUpload,
        imageData.mimeType
      );

      const processingTime = Date.now() - startTime;
      logger.info("Image processed and stored successfully", {
        index,
        imageId,
        gcsPath,
        thumbnailPath,
        fileSize: metadata.size,
        dimensions: `${metadata.width}x${metadata.height}`,
        processingTime,
        optimized: !!optimizedBuffer,
        hasThumbnail: !!thumbnailBuffer,
      });

      return {
        id: imageId,
        originalData: imageData,
        buffer,
        optimizedBuffer,
        thumbnailBuffer,
        metadata,
        inlineDataPart,
        hash,
        gcsPath,
        thumbnailPath,
        conversationImage: conversationImage.get({ plain: true }),
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error("Image processing failed", {
        error: error instanceof Error ? error.message : String(error),
        processingTime,
        imageIndex: index,
        filename: imageData.filename,
      });
      throw error;
    }
  }

  /**
   * Get conversation images with signed URLs
   */
  async getConversationImagesWithUrls(
    conversationId: string,
    createdById?: string
  ): Promise<
    Array<
      ConversationImage & { signedUrl?: string; thumbnailSignedUrl?: string }
    >
  > {
    const whereClause: any = { conversationId };
    if (createdById) {
      whereClause.createdById = createdById;
    }

    const images = await models.ConversationImage.findAll({
      where: whereClause,
      order: [["createdAt", "ASC"]],
    });

    const imagesWithUrls = await Promise.all(
      images.map(async (image) => {
        const imageData = image.get({ plain: true });
        const signedUrl = await generateSignedUrlForRead(imageData.gcsPath);
        let thumbnailSignedUrl: string | undefined;
        if (imageData.thumbnailPath) {
          thumbnailSignedUrl = await generateSignedUrlForRead(
            imageData.thumbnailPath
          );
        }

        return {
          ...imageData,
          signedUrl,
          thumbnailSignedUrl,
        };
      })
    );

    return imagesWithUrls;
  }

  /**
   * Delete conversation images
   */
  async deleteConversationImages(
    conversationId: string,
    createdById?: string
  ): Promise<void> {
    const whereClause: any = { conversationId };
    if (createdById) {
      whereClause.createdById = createdById;
    }

    const images = await models.ConversationImage.findAll({
      where: whereClause,
    });

    if (images.length === 0) {
      logger.info("No images found to delete", { conversationId, createdById });
      return;
    }

    // Delete from GCS
    const filesToDelete = images.flatMap((image) => {
      const imageData = image.get({ plain: true });
      const files = [imageData.gcsPath];
      if (imageData.thumbnailPath) {
        files.push(imageData.thumbnailPath);
      }
      return files;
    });

    await deleteFiles(filesToDelete);

    // Delete from database
    await models.ConversationImage.destroy({ where: whereClause });

    logger.info("Conversation images deleted", {
      conversationId,
      createdById,
      deletedCount: images.length,
    });
  }

  /**
   * Delete all images for a user's conversation (using prefix deletion)
   */
  async deleteAllConversationImages(
    conversationId: string,
    createdById: string
  ): Promise<void> {
    const prefix = `${gcsConversationImagesFolder}/${createdById}/${conversationId}/`;

    // Delete from GCS using prefix
    await deleteFilesByPrefix(prefix);

    // Delete from database
    await models.ConversationImage.destroy({
      where: { conversationId, createdById },
    });

    logger.info("All conversation images deleted", {
      conversationId,
      createdById,
      prefix,
    });
  }

  // Private helper methods
  private validateImageData(imageData: ImageData): void {
    if (!imageData.data || typeof imageData.data !== "string") {
      throw new Error(
        "Invalid image data: data field is required and must be a string"
      );
    }

    if (
      !imageData.mimeType ||
      !AgentUtilsService.imageConfig.supportedMimeTypes.includes(
        imageData.mimeType as any
      )
    ) {
      throw new Error(`Unsupported MIME type: ${imageData.mimeType}`);
    }
  }

  private base64ToBuffer(data: string): Buffer {
    try {
      if (data.startsWith("data:")) {
        const [, base64Data] = data.split(",");
        if (!base64Data) throw new Error("Invalid data URL format");
        return Buffer.from(base64Data, "base64");
      }
      return Buffer.from(data, "base64");
    } catch (error) {
      throw new Error(
        `Failed to decode base64 image data: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  private async validateImageContent(
    buffer: Buffer,
    expectedMimeType: string
  ): Promise<void> {
    try {
      const image = sharp(buffer);
      const metadata = await image.metadata();

      const formatMimeTypeMap: Record<string, string> = {
        jpeg: "image/jpeg",
        jpg: "image/jpeg",
        png: "image/png",
        gif: "image/gif",
        webp: "image/webp",
        bmp: "image/bmp",
        tiff: "image/tiff",
      };

      if (
        metadata.format &&
        formatMimeTypeMap[metadata.format] !== expectedMimeType
      ) {
        throw new Error(
          `Image format mismatch: expected ${expectedMimeType}, got ${
            formatMimeTypeMap[metadata.format] || metadata.format
          }`
        );
      }
    } catch (error) {
      throw new Error(
        `Invalid image content: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  private async getImageMetadata(
    buffer: Buffer
  ): Promise<{ width: number; height: number; format: string; size: number }> {
    try {
      const image = sharp(buffer);
      const metadata = await image.metadata();

      if (!metadata.width || !metadata.height) {
        throw new Error("Unable to determine image dimensions");
      }

      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format || "unknown",
        size: buffer.length,
      };
    } catch (error) {
      throw new Error(
        `Failed to get image metadata: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  private generateImageHash(buffer: Buffer): string {
    return crypto.createHash("sha256").update(buffer).digest("hex");
  }

  private generateImagePath(
    createdById: string,
    conversationId: string,
    messageId: string,
    filename: string
  ): string {
    const timestamp = Date.now();
    const imageId = uuidv4().substring(0, 8);
    const ext = path.extname(filename);
    return `${gcsConversationImagesFolder}/${createdById}/${conversationId}/${messageId}_${timestamp}_${imageId}${ext}`;
  }

  private generateThumbnailPath(
    createdById: string,
    conversationId: string,
    messageId: string,
    filename: string
  ): string {
    const timestamp = Date.now();
    const imageId = uuidv4().substring(0, 8);
    return `${gcsConversationImagesFolder}/${createdById}/${conversationId}/thumbnails/${messageId}_${timestamp}_${imageId}_thumb.webp`;
  }

  private async optimizeImageIfNeeded(
    buffer: Buffer,
    metadata: { width: number; height: number; size: number },
    mimeType: string
  ): Promise<Buffer | undefined> {
    const recommendedDimension =
      AgentUtilsService.imageConfig.recommendedImageDimensions;
    const recommendedSize = AgentUtilsService.imageConfig.recommendedImageSize;

    const needsResize =
      Math.max(metadata.width, metadata.height) > recommendedDimension;
    const needsCompression = metadata.size > recommendedSize;

    if (!needsResize && !needsCompression) return undefined;

    try {
      let image = sharp(buffer);

      if (needsResize) {
        const scale =
          recommendedDimension / Math.max(metadata.width, metadata.height);
        const newWidth = Math.round(metadata.width * scale);
        const newHeight = Math.round(metadata.height * scale);
        image = image.resize(newWidth, newHeight, {
          fit: "inside",
          withoutEnlargement: true,
        });
      }

      if (mimeType === "image/jpeg")
        image = image.jpeg({ quality: 85, progressive: true });
      else if (mimeType === "image/png")
        image = image.png({ compressionLevel: 8 });
      else if (mimeType === "image/webp") image = image.webp({ quality: 85 });

      return await image.toBuffer();
    } catch (error) {
      logger.warn("Image optimization failed, using original", {
        error: error instanceof Error ? error.message : String(error),
      });
      return undefined;
    }
  }

  private async generateThumbnail(buffer: Buffer): Promise<Buffer | undefined> {
    try {
      return await sharp(buffer)
        .resize(
          AgentUtilsService.imageConfig.thumbnailMaxWidth,
          AgentUtilsService.imageConfig.thumbnailMaxHeight,
          { fit: "inside", withoutEnlargement: true }
        )
        .webp({ quality: AgentUtilsService.imageConfig.thumbnailQuality })
        .toBuffer();
    } catch (error) {
      logger.warn("Thumbnail generation failed", {
        error: error instanceof Error ? error.message : String(error),
      });
      return undefined;
    }
  }

  private createInlineDataPart(buffer: Buffer, mimeType: string): Part {
    return {
      inlineData: {
        mimeType,
        data: buffer.toString("base64"),
      },
    };
  }

  private async updateMessageImageCounts(messageId: string): Promise<void> {
    const imageCount = await models.ConversationImage.count({
      where: { messageId },
    });
    await models.ChatMessage.update(
      { hasImages: imageCount > 0, imageCount },
      { where: { id: messageId } }
    );
  }

  /**
   * Validate image quotas and rate limits
   */
  private async validateImageQuotas(
    images: ImageData[],
    conversationId: string,
    createdById: string
  ): Promise<void> {
    const config = AgentUtilsService.imageConfig;

    // Check per-message limit
    if (images.length > config.maxImagesPerMessage) {
      throw new Error(
        `Too many images: ${images.length}. Maximum ${config.maxImagesPerMessage} images per message allowed.`
      );
    }

    // Check total size limit
    const totalSize = images.reduce((sum, img) => sum + (img.size || 0), 0);
    const maxTotalSize = config.maxImageSize * images.length;
    if (totalSize > maxTotalSize) {
      throw new Error(
        `Total image size too large: ${Math.round(
          totalSize / (1024 * 1024)
        )}MB. Maximum ${Math.round(maxTotalSize / (1024 * 1024))}MB allowed.`
      );
    }

    // Check conversation image limit (optional - can be implemented based on requirements)
    try {
      const existingImageCount = await models.ConversationImage.count({
        where: { conversationId, createdById },
      });

      if (
        existingImageCount + images.length >
        config.maxImagesPerConversation
      ) {
        logger.warn("Conversation approaching image limit", {
          conversationId,
          createdById,
          existingCount: existingImageCount,
          newImages: images.length,
          limit: config.maxImagesPerConversation,
        });
      }
    } catch (error) {
      // Don't fail the request if quota check fails, just log
      logger.warn("Failed to check conversation image quota", {
        error: error instanceof Error ? error.message : String(error),
        conversationId,
        createdById,
      });
    }
  }

  /**
   * Track successful image processing
   */
  private trackImageProcessingSuccess(
    processed: ProcessedConversationImage,
    conversationId: string,
    createdById: string
  ): void {
    logger.info("Image processed successfully", {
      imageId: processed.id,
      conversationId,
      createdById,
      originalSize: processed.metadata.size,
      optimizedSize: processed.optimizedBuffer?.length,
      dimensions: `${processed.metadata.width}x${processed.metadata.height}`,
      format: processed.metadata.format,
      hasThumbnail: !!processed.thumbnailBuffer,
      processingTime: Date.now(), // Could be enhanced with actual processing time
    });
  }

  /**
   * Track image processing errors
   */
  private trackImageProcessingError(
    error: unknown,
    imageData: ImageData,
    conversationId: string,
    createdById: string,
    index: number
  ): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorType = this.categorizeImageError(errorMessage);

    logger.error("Image processing failed", {
      error: errorMessage,
      errorType,
      imageIndex: index,
      conversationId,
      createdById,
      filename: imageData.filename,
      mimeType: imageData.mimeType,
      size: imageData.size,
    });

    // Could integrate with external error tracking service here
    // Example: Sentry, DataDog, etc.
  }

  /**
   * Categorize image errors for better tracking
   */
  private categorizeImageError(errorMessage: string): string {
    if (errorMessage.includes("Invalid image data")) return "INVALID_DATA";
    if (errorMessage.includes("Unsupported MIME type"))
      return "UNSUPPORTED_FORMAT";
    if (errorMessage.includes("Image dimensions")) return "DIMENSION_ERROR";
    if (errorMessage.includes("Failed to decode base64"))
      return "ENCODING_ERROR";
    if (errorMessage.includes("Invalid image content")) return "CONTENT_ERROR";
    if (errorMessage.includes("Failed to upload")) return "UPLOAD_ERROR";
    if (errorMessage.includes("Failed to create")) return "DATABASE_ERROR";
    return "UNKNOWN_ERROR";
  }

  /**
   * Find existing images by hash and conversation ID to avoid reprocessing
   */
  private async findDuplicateImages(
    hash: string,
    conversationId: string
  ): Promise<any | null> {
    const result = await models.ConversationImage.findOne({
      where: {
        conversationId,
        inlineDataHash: hash,
      },
    });

    return result ? result.get({ plain: true }) : null;
  }

  /**
   * Health check method for monitoring image service performance
   */
  async healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    metrics: {
      totalImages: number;
      recentImages: number;
      averageProcessingTime?: number;
      errorRate?: number;
    };
  }> {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const [totalImages, recentImages] = await Promise.all([
        models.ConversationImage.count(),
        models.ConversationImage.count({
          where: {
            createdAt: {
              [Op.gte]: oneHourAgo,
            },
          },
        }),
      ]);

      const status = recentImages > 100 ? "degraded" : "healthy";

      return {
        status,
        metrics: {
          totalImages,
          recentImages,
        },
      };
    } catch (error) {
      logger.error("Health check failed", {
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        status: "unhealthy",
        metrics: {
          totalImages: 0,
          recentImages: 0,
        },
      };
    }
  }
}

// Export singleton instance
export const conversationImageService = new ConversationImageService();
