import { captureEvent, captureGroupEvent } from "@/services/posthog";
import { createLogger } from "@/services/logger";
import {
  calculateCostBreakdown,
  calculateCostEfficiencyMetrics,
} from "./cost-calculator";
import { getModel } from "@/config/models";
import type { TokenUsage } from "@/validators/agent.validators";

const logger = createLogger("AgentTrackingService");

// Cost tier thresholds as per specification
const COST_TIERS = {
  HIGH: 2.0,
  MEDIUM: 0.5,
  LOW: 0.1,
} as const;

type CostTier = "high" | "medium" | "low" | "minimal";

// Import RAG tracking types from consolidated types
import type {
  IRagMetrics,
  IRagPerformanceMetrics,
  RagErrorType,
} from "@/types/agent";

/**
 * Determine cost tier based on total cost
 */
function getCostTier(totalCost: number): CostTier {
  if (totalCost >= COST_TIERS.HIGH) return "high";
  if (totalCost >= COST_TIERS.MEDIUM) return "medium";
  if (totalCost >= COST_TIERS.LOW) return "low";
  return "minimal";
}

/**
 * Get session metadata for tracking
 */
function getSessionMetadata() {
  const now = new Date();
  return {
    sessionDate: now.toISOString().split("T")[0], // YYYY-MM-DD format
    sessionHour: now.getHours(), // 0-23
  };
}

export class AgentTrackingService {
  /**
   * Track AI usage events with comprehensive cost and token metrics
   */
  public trackAgentUsage(
    userId: string,
    model: string,
    usage: TokenUsage,
    cost: number,
    messageLength: number,
    fileCount: number = 0,
    conversationLength: number = 0,
    ragMetrics?: IRagMetrics
  ): void {
    try {
      const modelInfo = getModel(model);
      const costTier = getCostTier(cost);
      const sessionMetadata = getSessionMetadata();

      const costBreakdown = calculateCostBreakdown(usage, model);
      const costEfficiency = calculateCostEfficiencyMetrics(usage, model);

      // Prepare event properties
      const eventProps: any = {
        // Model identification
        ai_model: model,
        ai_model_display: modelInfo.name,
        ai_provider: modelInfo.provider, // Use actual provider from model config
        ai_action_type: "chat_message",

        // Token metrics
        tokens_input: usage.inputTokens || 0,
        tokens_output: usage.outputTokens || 0,
        tokens_total:
          usage.totalTokens ||
          (usage.inputTokens || 0) + (usage.outputTokens || 0),

        // Cost metrics
        cost_input_usd: costBreakdown?.inputCost || 0,
        cost_output_usd: costBreakdown?.outputCost || 0,
        cost_total_usd: cost,
        cost_per_token:
          cost / ((usage.inputTokens || 0) + (usage.outputTokens || 0)) || 0,

        // Context metrics
        message_length: messageLength,
        file_count: fileCount,
        conversation_length: conversationLength,

        // Session info
        user_id: userId,
        ...sessionMetadata,

        // Cost categorization
        cost_tier: costTier,

        // Cost efficiency metrics
        ...costEfficiency,
      };

      // Add RAG metrics if provided
      if (ragMetrics) {
        eventProps.rag_enabled = ragMetrics.enabled;
        eventProps.rag_success = ragMetrics.success;
        if (ragMetrics.responseTimeMs)
          eventProps.rag_response_time_ms = ragMetrics.responseTimeMs;
        if (ragMetrics.estimatedTokenSavings)
          eventProps.rag_estimated_token_savings =
            ragMetrics.estimatedTokenSavings;
        if (ragMetrics.toolsUsed)
          eventProps.rag_tools_used = ragMetrics.toolsUsed;
        if (ragMetrics.qualityScore)
          eventProps.rag_quality_score = ragMetrics.qualityScore;
      }

      // Track the main AI usage event
      captureEvent(userId, `${modelInfo.name} Chat Message` as any, eventProps);
    } catch (error) {
      logger.error("Failed to track AI usage", {
        error,
        userId,
        model,
        cost,
      });
    }
  }

  /**
   * Track conversation creation
   */
  public trackConversationCreated(
    userId: string,
    conversationId: string,
    projectId?: string
  ): void {
    try {
      const eventProps = {
        conversationId,
      };

      if (projectId) {
        captureGroupEvent(
          userId,
          "project",
          projectId,
          "Created conversation" as any,
          eventProps
        );
      } else {
        captureEvent(userId, "Created conversation" as any, eventProps);
      }
    } catch (error) {
      logger.error("Failed to track conversation creation", {
        error,
        userId,
        conversationId,
      });
    }
  }

  /**
   * Track conversation started (legacy compatibility)
   */
  public trackConversationStarted(
    userId: string,
    model: string,
    instructionCount: number = 0
  ): void {
    try {
      captureEvent(userId, "Started conversation" as any, {
        instructionCount,
        model,
      });
    } catch (error) {
      logger.error("Failed to track conversation started", {
        error,
        userId,
        model,
      });
    }
  }

  /**
   * Track message completion
   */
  public trackMessageCompleted(
    userId: string,
    conversationId: string,
    model: string,
    usage: TokenUsage,
    cost: number,
    resources: string[] = [],
    projectId?: string,
    ragPerformance?: IRagPerformanceMetrics
  ): void {
    try {
      const eventProps: any = {
        conversationId,
        model,
        usage: {
          inputTokens: usage.inputTokens || 0,
          outputTokens: usage.outputTokens || 0,
          totalTokens:
            usage.totalTokens ||
            (usage.inputTokens || 0) + (usage.outputTokens || 0),
          imageTokens: usage.imageTokens || 0,
        },
        cost,
        resources,
      };

      // Add RAG performance metrics if provided
      if (ragPerformance) {
        if (ragPerformance.processingTimeMs)
          eventProps.rag_processing_time_ms = ragPerformance.processingTimeMs;
        if (ragPerformance.resultsCount)
          eventProps.rag_results_count = ragPerformance.resultsCount;
        if (ragPerformance.improvedResponse !== undefined)
          eventProps.rag_improved_response = ragPerformance.improvedResponse;
      }

      if (projectId) {
        captureGroupEvent(
          userId,
          "project",
          projectId,
          "Message completed" as any,
          eventProps
        );
      } else {
        captureEvent(userId, "Message completed" as any, eventProps);
      }
    } catch (error) {
      logger.error("Failed to track message completion", {
        error,
        userId,
        conversationId,
      });
    }
  }

  /**
   * Track message sent (legacy compatibility)
   */
  public trackMessageSent(
    userId: string,
    message: string,
    usage: TokenUsage,
    model: string,
    contextIncluded: boolean = false,
    fullContentIncluded: boolean = false,
    activeResourceCount: number = 0,
    responseLength: number = 0
  ): void {
    try {
      captureEvent(userId, "Sent message" as any, {
        message,
        usage,
        contextIncluded,
        fullContentIncluded,
        activeResourceCount,
        responseLength,
        model,
      });
    } catch (error) {
      logger.error("Failed to track message sent", {
        error,
        userId,
        model,
      });
    }
  }

  /**
   * Track conversation stopped
   */
  public trackConversationStopped(userId: string): void {
    try {
      captureEvent(userId, "Stopped conversation" as any, {});
    } catch (error) {
      logger.error("Failed to track conversation stopped", {
        error,
        userId,
      });
    }
  }

  /**
   * Track streaming stopped
   */
  public trackStreamingStopped(userId: string): void {
    try {
      captureEvent(userId, "Stopped streaming" as any, {});
    } catch (error) {
      logger.error("Failed to track streaming stopped", {
        error,
        userId,
      });
    }
  }

  /**
   * Track model switch
   */
  public trackModelSwitched(
    userId: string,
    fromModel: string,
    toModel: string,
    messageCount: number = 0
  ): void {
    try {
      captureEvent(userId, "Switched model" as any, {
        fromModel,
        toModel,
        messageCount,
      });
    } catch (error) {
      logger.error("Failed to track model switch", {
        error,
        userId,
        fromModel,
        toModel,
      });
    }
  }

  /**
   * Track context updated with new resources
   */
  public trackContextUpdatedWithResources(
    userId: string,
    resources: string[],
    totalCount: number
  ): void {
    try {
      captureEvent(userId, "Updated context with new resources" as any, {
        resources: resources.join(", "),
        totalCount,
      });
    } catch (error) {
      logger.error("Failed to track context update with resources", {
        error,
        userId,
        resourceCount: resources.length,
      });
    }
  }

  /**
   * Track resources removed from context
   */
  public trackResourcesRemovedFromContext(
    userId: string,
    removedResources: string[],
    removedCount: number,
    remainingCount: number
  ): void {
    try {
      captureEvent(userId, "Removed resources from context" as any, {
        resources: removedResources.join(", "),
        removedCount,
        remainingCount,
      });
    } catch (error) {
      logger.error("Failed to track resources removed from context", {
        error,
        userId,
        removedCount,
      });
    }
  }

  /**
   * Track chat reset
   */
  public trackChatReset(userId: string): void {
    try {
      captureEvent(userId, "Reset chat" as any, {});
    } catch (error) {
      logger.error("Failed to track chat reset", {
        error,
        userId,
      });
    }
  }

  /**
   * Track streaming connection established
   */
  public trackStreamingStarted(
    userId: string,
    conversationId: string,
    model: string
  ): void {
    try {
      captureEvent(userId, "Streaming started" as any, {
        conversationId,
        model,
      });
    } catch (error) {
      logger.error("Failed to track streaming started", {
        error,
        userId,
        conversationId,
      });
    }
  }

  /**
   * Track when a user session starts (for legacy compatibility)
   */
  public trackSessionStarted(
    userId: string,
    sessionType: "agent_chat" | "general" = "agent_chat"
  ): void {
    try {
      captureEvent(userId, "Session started" as any, {
        sessionType,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error("Failed to track session started", {
        error,
        userId,
        sessionType,
      });
    }
  }

  /**
   * Track error events for debugging and monitoring
   */
  public trackError(
    userId: string,
    errorType: "validation" | "api" | "streaming" | "model" | "general" | "rag",
    errorMessage: string,
    context?: Record<string, any>,
    ragErrorDetails?: {
      ragErrorType: RagErrorType;
      fallbackUsed: boolean;
      retryCount: number;
    }
  ): void {
    try {
      const eventProps: any = {
        errorType,
        errorMessage,
        context,
        timestamp: new Date().toISOString(),
      };

      // Add RAG error details if provided
      if (ragErrorDetails) {
        eventProps.rag_error_type = ragErrorDetails.ragErrorType;
        eventProps.rag_fallback_used = ragErrorDetails.fallbackUsed;
        eventProps.rag_retry_count = ragErrorDetails.retryCount;
      }

      captureEvent(userId, "Agent error" as any, eventProps);
    } catch (error) {
      logger.error("Failed to track error", {
        error: error instanceof Error ? error.message : String(error),
        userId,
        errorType,
      });
    }
  }

  /**
   * Track complete streaming session with comprehensive analytics
   */
  public trackStreamingComplete(
    userId: string,
    conversationId: string,
    model: string,
    usage: TokenUsage,
    cost: number,
    messageLength: number,
    fileCount: number,
    conversationLength: number,
    resources: string[] = [],
    projectId?: string,
    ragMetrics?: IRagMetrics,
    ragPerformance?: IRagPerformanceMetrics
  ): void {
    try {
      // Track AI usage event with RAG metrics
      this.trackAgentUsage(
        userId,
        model,
        usage,
        cost,
        messageLength,
        fileCount,
        conversationLength,
        ragMetrics
      );

      // Track message completion with RAG performance
      this.trackMessageCompleted(
        userId,
        conversationId,
        model,
        usage,
        cost,
        resources,
        projectId,
        ragPerformance
      );

      // Track context usage if resources were used
      if (resources.length > 0) {
        this.trackContextUpdatedWithResources(
          userId,
          resources,
          resources.length
        );
      }
    } catch (error) {
      logger.error("Failed to track streaming session", {
        error,
        userId,
        conversationId,
        model,
      });
    }
  }

  /**
   * Track streaming errors with context
   */
  public trackStreamingError(
    userId: string,
    conversationId: string,
    model: string,
    errorMessage: string,
    context: {
      messageLength: number;
      resourceCount: number;
      [key: string]: any;
    }
  ): void {
    try {
      this.trackError(userId, "streaming", errorMessage, {
        conversationId,
        model,
        ...context,
      });
    } catch (error) {
      logger.error("Failed to track streaming error", {
        error: error instanceof Error ? error.message : String(error),
        userId,
        conversationId,
        errorMessage,
      });
    }
  }

  /**
   * Track RAG-specific errors with detailed context
   */
  public trackRagError(
    userId: string,
    conversationId: string,
    functionName: string,
    errorMessage: string,
    functionArgs: any,
    ragErrorType: RagErrorType = "retrieval_failed",
    fallbackUsed: boolean = true,
    retryCount: number = 0
  ): void {
    try {
      this.trackError(
        userId,
        "rag",
        errorMessage,
        {
          conversationId,
          functionName,
          args: functionArgs,
        },
        {
          ragErrorType,
          fallbackUsed,
          retryCount,
        }
      );
    } catch (error) {
      logger.error("Failed to track RAG error", {
        error: error instanceof Error ? error.message : String(error),
        userId,
        conversationId,
        functionName,
        ragErrorType,
      });
    }
  }
}

// Export singleton instance
export const agentTrackingService = new AgentTrackingService();
