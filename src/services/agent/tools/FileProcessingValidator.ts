import { ValidationResult } from "@/types/agent";
import { ProcessedResource } from "@/services/agent/context/TraditionalContextBuilder";
import { createLogger } from "@/services/logger";

const logger = createLogger("FileProcessingValidator");

export class FileProcessingValidator {
  validateAllResourcesProcessed(
    requestedResources: string[],
    processedResources: ProcessedResource[],
    searchableFileIds: string[]
  ): ValidationResult {
    const processedIds = new Set(processedResources.map((r) => r.resourceId));
    const searchableIds = new Set(searchableFileIds);
    const allHandledIds = new Set([...processedIds, ...searchableIds]);

    const missingFiles = requestedResources.filter(
      (id) => !allHandledIds.has(id)
    );

    // If files are missing, log for monitoring but don't fail validation
    if (missingFiles.length > 0) {
      logger.warn("Some requested files were not found - ignoring them", {
        requestedCount: requestedResources.length,
        processedCount: processedIds.size,
        searchableCount: searchableIds.size,
        missingFiles: missingFiles,
        missingCount: missingFiles.length,
      });
    }

    return {
      valid: true,
      processedCount: processedIds.size,
      searchableCount: searchableIds.size,
      totalRequested: requestedResources.length,
      missingFiles: missingFiles.length > 0 ? missingFiles : undefined,
      message:
        missingFiles.length > 0
          ? `${
              missingFiles.length
            } files ignored (not found): ${missingFiles.join(", ")}`
          : undefined,
    };
  }
}
