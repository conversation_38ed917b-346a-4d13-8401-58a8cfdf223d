/**
 * Tools System Exports
 *
 * Clean exports for the new tool system
 */

export { createToolRegistry, initializeToolRegistry } from "./ToolRegistry";
export type {
  RegistryTool,
  ToolCallArgs,
  ToolCallResult,
  ToolHealth,
} from "./ToolRegistry";
export { BaseTool } from "./BaseTool";
export { createToolExecutionContext } from "./ToolExecutionContext";
export type { ToolExecutionContext } from "./ToolExecutionContext";

// MCP (Model Context Protocol) support
export * from "./mcp";

// Export tool creation function
export { createAllTools } from "./providers";

// Re-export Google GenAI types for convenience
export type { Tool } from "@google/genai";
