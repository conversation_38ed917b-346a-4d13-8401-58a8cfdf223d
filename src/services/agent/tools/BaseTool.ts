/**
 * Base Tool Class - Makes creating new tools super simple
 *
 * Just extend this class and implement the required methods.
 * Registration happens automatically on import.
 */

import type { Tool } from "@google/genai";
import { createLogger } from "@/services/logger";
import type { ToolExecutionContext } from "./ToolExecutionContext";
import type {
  Regis<PERSON>Tool,
  ToolCallArgs,
  ToolCallResult,
} from "./ToolRegistry";
import { UserPreferences } from "@/types/agent";
import { FeatureFlagName } from "@/services/featureFlags";

/**
 * Context for tool enablement decisions
 */
export interface ToolEnablementContext {
  userId: string;
  projectId?: string;
  preferences?: UserPreferences;
  featureFlags: Record<FeatureFlagName, boolean>;
  searchableFileIds?: string[];
}

export abstract class BaseTool implements RegistryTool {
  protected logger = createLogger(this.constructor.name);

  // Tool metadata (override in subclass)
  abstract readonly name: string;
  abstract readonly description: string;

  /**
   * Determine if this tool should be enabled for the given context
   * Each tool implements its own enablement logic
   */
  abstract isEnabled(context: ToolEnablementContext): boolean;

  constructor() {
    // Note: Registration happens in subclass after properties are initialized
  }

  // ============================================================================
  // ABSTRACT METHODS (implement in subclass)
  // ============================================================================

  /**
   * Define the Google GenAI tool schema
   */
  abstract getToolDefinition(): Tool;

  /**
   * Execute the tool logic
   */
  abstract execute(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult>;

  /**
   * Check if tool is healthy and ready to use
   */
  abstract healthCheck(): Promise<boolean>;

  // ============================================================================
  // OPTIONAL LIFECYCLE HOOKS
  // ============================================================================

  /**
   * Initialize tool (override if needed)
   */
  async initialize(): Promise<void> {
    this.logger.info(`Initializing tool: ${this.name}`);
    // Default: no initialization needed
  }

  /**
   * Cleanup tool resources (override if needed)
   */
  async cleanup(): Promise<void> {
    this.logger.info(`Cleaning up tool: ${this.name}`);
    // Default: no cleanup needed
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Create a success result
   */
  protected createSuccessResult(
    data: unknown,
    metadata: Record<string, unknown> = {}
  ): ToolCallResult {
    return {
      success: true,
      data,
      metadata: {
        processingTime: 0, // Will be overridden by caller
        source: this.name,
        ...metadata,
      },
    };
  }

  /**
   * Create an error result
   */
  protected createErrorResult(
    error: string,
    metadata: Record<string, unknown> = {}
  ): ToolCallResult {
    return {
      success: false,
      error,
      metadata: {
        processingTime: 0, // Will be overridden by caller
        source: this.name,
        ...metadata,
      },
    };
  }

  /**
   * Validate required arguments
   */
  protected validateArgs(
    args: ToolCallArgs,
    required: string[]
  ): { valid: boolean; error?: string } {
    for (const field of required) {
      if (
        !(field in args) ||
        args[field] === undefined ||
        args[field] === null
      ) {
        return {
          valid: false,
          error: `Missing required argument: ${field}`,
        };
      }
    }
    return { valid: true };
  }

  /**
   * Log tool execution start
   */
  protected logStart(
    operation: string,
    context: ToolExecutionContext,
    args?: Record<string, unknown>
  ): void {
    this.logger.info(`${this.name}: ${operation}`, {
      requestId: context.requestId,
      query: args?.query ? String(args.query).substring(0, 100) : undefined,
    });
  }

  /**
   * Log tool execution completion
   */
  protected logComplete(
    operation: string,
    context: ToolExecutionContext,
    success: boolean,
    processingTime: number,
    metadata?: Record<string, unknown>
  ): void {
    this.logger.info(`${this.name}: completed`, {
      success,
      processingTime,
      requestId: context.requestId,
      ...metadata,
    });
  }

  /**
   * Log tool execution error
   */
  protected logError(
    operation: string,
    context: ToolExecutionContext,
    error: unknown,
    processingTime: number
  ): void {
    this.logger.error(`${this.name}: failed`, {
      error: error instanceof Error ? error.message : String(error),
      processingTime,
      requestId: context.requestId,
    });
  }
}
