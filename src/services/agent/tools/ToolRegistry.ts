/**
 * Tool Registry - Clean, extensible approach for managing LLM tools
 *
 * Design principles:
 * - Convention over configuration
 * - Runtime tool filtering with policies
 * - Auto-discovery of tools
 * - Built-in health monitoring
 * - Easy to extend
 */

import type { Tool } from "@google/genai";
import { createLogger } from "@/services/logger";
import type { ToolExecutionContext } from "./ToolExecutionContext";
import type { ToolEnablementContext } from "./BaseTool";

const logger = createLogger("ToolRegistry");

// ============================================================================
// CORE INTERFACES
// ============================================================================

export interface ToolCallArgs {
  readonly [key: string]: unknown;
}

export interface ToolCallResult {
  readonly success: boolean;
  readonly data?: unknown;
  readonly error?: string;
  readonly metadata?: {
    readonly processingTime: number;
    readonly source: string;
    readonly [key: string]: unknown;
  };
}

export interface ToolHealth {
  readonly available: boolean;
  readonly lastCheck: number;
  readonly errorRate: number;
  readonly avgLatency: number;
}

/**
 * Registry tool interface - each tool implements this
 */
export interface RegistryTool {
  readonly name: string;
  readonly description: string;

  // Dynamic enablement based on context
  isEnabled(context: ToolEnablementContext): boolean;

  // Google GenAI tool definition
  getToolDefinition(): Tool;

  // Tool execution
  execute(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult>;

  // Health check
  healthCheck(): Promise<boolean>;

  // Optional lifecycle hooks
  initialize?(): Promise<void>;
  cleanup?(): Promise<void>;
}

// ============================================================================
// TOOL REGISTRY
// ============================================================================

export class ToolRegistry {
  private tools = new Map<string, RegistryTool>();
  private toolHealth = new Map<string, ToolHealth>();
  private functionNameToToolName = new Map<string, string>(); // Maps function names to tool names
  private initialized = false;

  /**
   * Register a tool (called by each tool during import)
   */
  register(tool: RegistryTool): void {
    if (this.tools.has(tool.name)) {
      logger.warn(`Tool ${tool.name} already registered, overriding`);
    }

    this.tools.set(tool.name, tool);

    // Initialize health tracking
    this.toolHealth.set(tool.name, {
      available: false,
      lastCheck: 0,
      errorRate: 0,
      avgLatency: 0,
    });

    // Build function name → tool name mapping
    const toolDefinition = tool.getToolDefinition();
    const functionDeclarations = toolDefinition.functionDeclarations || [];

    functionDeclarations.forEach((funcDecl) => {
      this.functionNameToToolName.set(funcDecl.name, tool.name);
    });

    logger.info(`Tool registered: ${tool.name}`);
  }

  /**
   * Initialize all registered tools
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    logger.info("Initializing tool registry", {
      toolCount: this.tools.size,
    });

    // Initialize tools in parallel
    const initResults = await Promise.allSettled(
      Array.from(this.tools.values()).map(async (tool) => {
        try {
          await tool.initialize?.();
          await this.updateToolHealth(tool.name);
        } catch (error) {
          logger.error(`Failed to initialize tool: ${tool.name}`, {
            error: error instanceof Error ? error.message : String(error),
          });
          this.markToolUnavailable(tool.name);
        }
      })
    );

    const failedCount = initResults.filter(
      (r) => r.status === "rejected"
    ).length;
    const availableTools = Array.from(this.toolHealth.entries())
      .filter(([_, health]) => health.available)
      .map(([name]) => name);

    logger.info("Tool registry initialization complete", {
      totalTools: this.tools.size,
      availableTools: availableTools.length,
      failedTools: failedCount,
    });

    this.initialized = true;
  }

  /**
   * Get all registered tools (regardless of enabled state)
   * Used by policy service for runtime filtering
   */
  getAllRegisteredTools(): RegistryTool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools filtered by health status only
   * Used by ContextManager after policy filtering
   */
  getHealthyTools(tools: RegistryTool[]): Tool[] {
    return tools
      .filter((tool) => this.isToolHealthy(tool.name))
      .map((tool) => tool.getToolDefinition());
  }

  /**
   * Execute a tool call with proper validation
   * Supports both tool names and function names
   */
  async executeToolCall(
    toolOrFunctionName: string,
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult> {
    const startTime = Date.now();

    // Try to find tool by tool name first, then by function name
    let tool = this.tools.get(toolOrFunctionName);
    let actualToolName = toolOrFunctionName;

    if (!tool) {
      // Check if it's a function name that maps to a tool name
      const mappedToolName =
        this.functionNameToToolName.get(toolOrFunctionName);
      if (mappedToolName) {
        tool = this.tools.get(mappedToolName);
        actualToolName = mappedToolName;
      }
    }

    if (!tool) {
      return {
        success: false,
        error: `Tool not found: ${toolOrFunctionName} (checked both tool names and function names)`,
        metadata: {
          processingTime: Date.now() - startTime,
          source: "registry",
        },
      };
    }

    if (!this.isToolHealthy(actualToolName)) {
      return {
        success: false,
        error: `Tool unhealthy: ${actualToolName}`,
        metadata: {
          processingTime: Date.now() - startTime,
          source: "registry",
        },
      };
    }

    // Execute tool with error tracking
    try {
      logger.info(`Executing tool: ${actualToolName}`, {
        userId: context.userId,
        requestId: context.requestId,
      });

      const result = await tool.execute(args, context);
      const processingTime = Date.now() - startTime;

      // Update health metrics
      this.updateToolMetrics(actualToolName, processingTime, result.success);

      logger.info(`Tool completed: ${actualToolName}`, {
        success: result.success,
        processingTime,
      });

      return {
        ...result,
        metadata: {
          ...result.metadata,
          processingTime,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;

      // Update error metrics
      this.updateToolMetrics(actualToolName, processingTime, false);

      logger.error(`Tool execution failed: ${actualToolName}`, {
        error: error instanceof Error ? error.message : String(error),
        processingTime,
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : "Tool execution failed",
        metadata: {
          processingTime,
          source: actualToolName,
        },
      };
    }
  }

  /**
   * Get registry status for monitoring
   */
  getStatus() {
    const tools = Array.from(this.tools.entries()).map(([name, tool]) => ({
      name,
      description: tool.description,
      health: this.toolHealth.get(name),
    }));

    return {
      initialized: this.initialized,
      totalTools: this.tools.size,
      enabledTools: this.tools.size, // All registered tools are enabled (filtering happens at creation)
      healthyTools: tools.filter((t) => t.health?.available).length,
      tools,
    };
  }

  // ============================================================================
  // HEALTH MONITORING
  // ============================================================================

  private async updateToolHealth(toolName: string): Promise<void> {
    const tool = this.tools.get(toolName);
    if (!tool) return;

    try {
      const isHealthy = await tool.healthCheck();

      this.toolHealth.set(toolName, {
        ...this.toolHealth.get(toolName)!,
        available: isHealthy,
        lastCheck: Date.now(),
      });
    } catch (error) {
      logger.warn(`Health check failed for tool: ${toolName}`, {
        error: error instanceof Error ? error.message : String(error),
      });

      this.markToolUnavailable(toolName);
    }
  }

  private markToolUnavailable(toolName: string): void {
    this.toolHealth.set(toolName, {
      ...this.toolHealth.get(toolName)!,
      available: false,
      lastCheck: Date.now(),
    });
  }

  private isToolHealthy(toolName: string): boolean {
    const health = this.toolHealth.get(toolName);
    return health?.available ?? false;
  }

  private updateToolMetrics(
    toolName: string,
    processingTime: number,
    success: boolean
  ): void {
    const health = this.toolHealth.get(toolName);
    if (!health) return;

    // Simple exponential moving average for metrics
    const alpha = 0.1; // Smoothing factor
    const newErrorRate = success
      ? health.errorRate * (1 - alpha)
      : health.errorRate * (1 - alpha) + alpha;
    const newAvgLatency =
      health.avgLatency * (1 - alpha) + processingTime * alpha;

    this.toolHealth.set(toolName, {
      ...health,
      errorRate: newErrorRate,
      avgLatency: newAvgLatency,
    });
  }

  /**
   * Get health status of all tools (updates internal health cache)
   */
  async getHealthStatus(): Promise<Record<string, boolean>> {
    const healthStatus: Record<string, boolean> = {};

    for (const [toolName, tool] of this.tools.entries()) {
      try {
        const isHealthy = await tool.healthCheck();
        healthStatus[toolName] = isHealthy;

        // Update internal health cache
        this.toolHealth.set(toolName, {
          ...this.toolHealth.get(toolName)!,
          available: isHealthy,
          lastCheck: Date.now(),
        });
      } catch (error) {
        logger.warn(`Health check failed for tool: ${toolName}`, {
          error: error instanceof Error ? error.message : String(error),
        });
        healthStatus[toolName] = false;
        this.markToolUnavailable(toolName);
      }
    }

    return healthStatus;
  }

  /**
   * Cleanup all tools
   */
  async cleanup(): Promise<void> {
    logger.info("Cleaning up tool registry");

    await Promise.allSettled(
      Array.from(this.tools.values()).map(async (tool) => {
        try {
          await tool.cleanup?.();
        } catch (error) {
          logger.warn(`Failed to cleanup tool: ${tool.name}`, {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      })
    );

    this.tools.clear();
    this.toolHealth.clear();
    this.functionNameToToolName.clear();
    this.initialized = false;

    logger.info("Tool registry cleanup complete");
  }
}

// ============================================================================
// TOOL REGISTRY FACTORY
// ============================================================================

/**
 * Create a new tool registry instance
 * This replaces the singleton pattern for better testability
 */
export function createToolRegistry(): ToolRegistry {
  return new ToolRegistry();
}

/**
 * Initialize a tool registry with the provided tools
 */
export async function initializeToolRegistry(
  tools: RegistryTool[]
): Promise<ToolRegistry> {
  const registry = createToolRegistry();

  // Register all tools
  tools.forEach((tool) => registry.register(tool));

  // Initialize the registry
  await registry.initialize();

  return registry;
}
