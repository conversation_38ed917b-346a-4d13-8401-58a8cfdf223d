/**
 * Beings RAG Tool - Modern MCP Implementation
 *
 * Demonstrates the new registry-based approach for MCP tools.
 * Shows how easy it is to add new MCP tools without modifying core files.
 */

import type { Tool } from "@google/genai";
import { Type } from "@google/genai";
import type { Too<PERSON><PERSON>allArgs, ToolCallResult } from "../ToolRegistry";
import type { ToolExecutionContext } from "../ToolExecutionContext";
import type { ToolEnablementContext } from "../BaseTool";
import { MCPBaseTool, type MCPToolResult } from "../mcp/MCPBaseTool";
import { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
import {
  validateToolCallParams,
  sanitizeQuery,
  type BeingsToolArgs,
  callRetrieveRelevantChunks,
} from "@/services/beings";
import { registerMCPServer } from "../mcp/MCPRegistry";
import { MCPTransportType, type MCPServerConfig } from "../mcp/MCPClientManager";
import { beingsMCPAuthToken, beingsRAGBaseUrl } from "@/config";
import { createLogger } from "@/services/logger";
import { FEATURE_FLAGS } from "@/services/featureFlags";

const logger = createLogger("BeingsRAGTool");

/**
 * Modern Beings RAG tool using registry-based MCP configuration
 */
export class BeingsRAGTool extends MCPBaseTool {
  readonly name = "beings_rag";
  readonly description =
    "Semantic search across user documents using Beings MCP server";

  constructor() {
    super();

    // Register MCP server configuration
    this.registerBeingsMCPServer();
  }

  /**
   * Smart enablement logic for Beings RAG tool
   * This tool is enabled when:
   * 1. User wants RAG search
   * 2. Required environment variables are present
   * 3. ENABLE_BEINGS_MCP flag chooses Beings over Pinecone
   */
  isEnabled(context: ToolEnablementContext): boolean {
    // Must have RAG search enabled by user
    if (!context.preferences?.ragSearchEnabled) {
      return false;
    }

    // Only enable if there are actually searchable files available
    if (!context.searchableFileIds || context.searchableFileIds.length === 0) {
      return false;
    }

    return context.featureFlags[FEATURE_FLAGS.BEINGS_RAG_ENGINE];
  }

  /**
   * Register the Beings MCP server with the registry
   * This is how new MCP tools are added - zero core file modifications!
   */
  private registerBeingsMCPServer(): void {
    const serverConfig: MCPServerConfig = {
      name: "beings-rag",
      serverUrl: `${beingsRAGBaseUrl}/mcp`,
      transportType: MCPTransportType.STREAMABLE_HTTP,
      authToken: beingsMCPAuthToken,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 5000,
    };

    const toolConfigs = [
      {
        toolName: "retrieve_relevant_chunks",
        localName: "beings_rag",
        enabled: true,
        autoConnect: true,
        fallbackToHTTP: true,
      },
    ];

    registerMCPServer("beings-rag", { serverConfig, toolConfigs });
  }

  // ============================================================================
  // TOOL DEFINITION
  // ============================================================================

  getToolDefinition(): Tool {
    return {
      functionDeclarations: [
        {
          name: "retrieve_relevant_chunks",
          description:
            "Search for relevant information across uploaded documents using semantic search via MCP",
          parameters: {
            type: Type.OBJECT,
            properties: {
              query: {
                type: Type.STRING,
                description: "Search query to find relevant content",
              },
              userId: {
                type: Type.STRING,
                description: "User ID for access control",
              },
              projectId: {
                type: Type.STRING,
                description: "Project ID to scope the search (optional)",
              },
              fileIds: {
                type: Type.ARRAY,
                items: { type: Type.STRING },
                description:
                  "Specific file IDs to search within (use File IDs, not filenames)",
              },
              limit: {
                type: Type.NUMBER,
                description:
                  "Maximum number of results to return (default: 10)",
              },
            },
            required: ["query", "userId"],
          },
        },
      ],
    };
  }

  // ============================================================================
  // MCP IMPLEMENTATION
  // ============================================================================

  /**
   * Get required arguments for validation
   */
  protected getRequiredArgs(): string[] {
    return ["query", "userId", "projectId", "fileIds"];
  }

  /**
   * Validate MCP-specific arguments
   */
  protected validateMCPArgs(args: ToolCallArgs): {
    valid: boolean;
    error?: string;
  } {
    const { query, userId, projectId, fileIds } = args;

    // Use Beings-specific validation
    const beingsValidation = validateToolCallParams({
      query: String(query),
      userId: String(userId),
      projectId: projectId ? String(projectId) : undefined,
      fileIds: Array.isArray(fileIds) ? fileIds.map(String) : [],
    });

    if (!beingsValidation.valid) {
      return {
        valid: false,
        error: beingsValidation.error || "Invalid Beings MCP parameters",
      };
    }

    return { valid: true };
  }

  /**
   * Transform tool arguments to MCP format
   */
  protected transformArgsToMCP(args: ToolCallArgs): Record<string, any> {
    const { query, userId, projectId, fileIds, limit } = args;

    // Prepare MCP arguments with proper sanitization
    const mcpArgs = {
      query: sanitizeQuery(String(query)),
      user_id: String(userId), // MCP server expects snake_case
      project_id: projectId ? String(projectId) : undefined,
      file_ids: Array.isArray(fileIds) ? fileIds.map(String) : undefined,
      limit: limit ? Number(limit) : 10,
    };

    // Remove undefined values
    return Object.fromEntries(
      Object.entries(mcpArgs).filter(([_, value]) => value !== undefined)
    );
  }

  /**
   * Parse MCP server response to standard tool result
   */
  protected parseMCPResponse(mcpResult: CallToolResult): MCPToolResult {
    if (mcpResult.isError) {
      return {
        success: false,
        error: `MCP server error: ${JSON.stringify(mcpResult.content)}`,
        metadata: {
          mcpError: true,
          errorContent: mcpResult.content,
        },
      };
    }

    try {
      // Parse chunks from MCP response content
      const chunks = this.extractChunksFromMCPContent(mcpResult.content);

      if (chunks.length === 0) {
        return {
          success: true,
          data: {
            chunks: [],
            content: "No relevant chunks found for the search query",
            searchQuery: "", // Will be filled in by caller
          },
          metadata: {
            chunksFound: 0,
            source: "beings-mcp",
          },
        };
      }

      return {
        success: true,
        data: {
          chunks,
          content: `Found ${chunks.length} relevant chunks`,
          searchQuery: "", // Will be filled in by caller
        },
        metadata: {
          chunksFound: chunks.length,
          source: "beings-mcp",
        },
      };
    } catch (parseError) {
      return {
        success: false,
        error: `Failed to parse MCP response: ${
          parseError instanceof Error ? parseError.message : "Unknown error"
        }`,
        metadata: {
          parseError: true,
          rawContent: mcpResult.content,
        },
      };
    }
  }

  /**
   * Extract chunks from MCP response content
   */
  private extractChunksFromMCPContent(content: any[]): any[] {
    const chunks: any[] = [];

    for (const item of content) {
      if (item.type === "text" && item.text) {
        try {
          const parsed = JSON.parse(item.text);

          if (parsed.chunks && Array.isArray(parsed.chunks)) {
            chunks.push(...parsed.chunks);
          } else if (parsed.data?.chunks && Array.isArray(parsed.data.chunks)) {
            chunks.push(...parsed.data.chunks);
          } else if (parsed.id && parsed.content) {
            // Single chunk format
            chunks.push(parsed);
          }
        } catch (parseError) {
          logger.warn("Failed to parse MCP content item", {
            content: item.text?.substring(0, 200),
            error:
              parseError instanceof Error
                ? parseError.message
                : String(parseError),
          });
        }
      }
    }

    return chunks;
  }

  // ============================================================================
  // FALLBACK HANDLING
  // ============================================================================

  /**
   * Handle case when MCP server is unavailable
   * Provides graceful degradation by falling back to HTTP API
   */
  protected async handleMCPUnavailable(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult | null> {
    // Check if HTTP fallback is enabled
    const config = this.getMCPConfig();
    const registry = this.mcpRegistry.getToolConfig(this.name);
    const fallbackEnabled = registry?.toolConfig.fallbackToHTTP ?? false;

    logger.warn("Beings MCP server unavailable, attempting HTTP fallback", {
      serverName: config.serverName,
      userId: args.userId,
      fallbackEnabled,
    });

    if (!fallbackEnabled) {
      logger.warn("MCP server unavailable and HTTP fallback disabled", {
        serverName: config.serverName,
        userId: args.userId,
      });
      return null;
    }

    try {
      // Import HTTP-based function dynamically to avoid circular dependencies
      const searchArgs: BeingsToolArgs = {
        query: sanitizeQuery(String(args.query)),
        userId: String(args.userId),
        projectId: args.projectId ? String(args.projectId) : undefined,
        fileIds: Array.isArray(args.fileIds)
          ? args.fileIds.map(String)
          : undefined,
        limit: args.limit ? Number(args.limit) : undefined,
      };

      const result = await callRetrieveRelevantChunks(searchArgs);

      if (!result.success) {
        return null; // Let the main error handling take over
      }

      logger.info("HTTP fallback successful", {
        userId: args.userId,
        chunksFound: result.chunksCount || 0,
      });

      return this.createSuccessResult(result.data, {
        source: "beings-http-fallback",
        chunksFound: result.chunksCount || 0,
        searchQuery: searchArgs.query,
        fallbackUsed: true,
      });
    } catch (fallbackError) {
      logger.error("HTTP fallback also failed", {
        error:
          fallbackError instanceof Error
            ? fallbackError.message
            : String(fallbackError),
        userId: args.userId,
      });
      return null;
    }
  }
}
