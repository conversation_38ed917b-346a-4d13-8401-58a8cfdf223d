/**
 * Tool Initialization - Smart, Context-Driven Tool Creation
 *
 * Each tool implements its own enablement logic via isEnabled(context).
 * This approach scales to hundreds of tools without complex configuration.
 */

import type { RegistryTool } from "../ToolRegistry";
import { BeingsRAGTool } from "./BeingsRAGTool";
import { PineconeSearchTool } from "./PineconeSearchTool";
import { createLogger } from "@/services/logger";

const logger = createLogger("ToolProviders");

/**
 * All available tool classes - add new tools here
 */
const ALL_TOOL_CLASSES = [
  BeingsRAGTool,
  PineconeSearchTool,
  // Future tools just get added to this array:
];

/**
 * Create all available tools (without filtering)
 * Filtering happens per-request in ContextManager based on user context
 */
export async function createAllTools(): Promise<RegistryTool[]> {
  const tools: RegistryTool[] = [];

  // Create all tool instances - filtering happens per-request
  for (const ToolClass of ALL_TOOL_CLASSES) {
    try {
      const toolInstance = new ToolClass();
      tools.push(toolInstance);
    } catch (error) {
      logger.error(`Failed to create tool ${ToolClass.name}`, {
        error: error.message,
      });
    }
  }

  return tools;
}

// Re-export tool classes for external use
export { BeingsRAGTool, PineconeSearchTool };
