/**
 * Request-Scoped Tool Execution Context
 * 
 * Eliminates shared state and provides secure, per-request tool execution context.
 * This fixes the critical security vulnerability where file IDs could leak between users.
 */

export interface ToolExecutionContext {
  /** Unique identifier for this request */
  readonly requestId: string;
  
  /** User making the request */
  readonly userId: string;
  
  /** Project context (if applicable) */
  readonly projectId?: string;
  
  /** File IDs this user is allowed to access in this request */
  readonly allowedFileIds: readonly string[];
  
  /** When this request was initiated (for timeouts) */
  readonly requestStartTime: number;
  
  /** Maximum time this request can run (for timeouts) */
  readonly timeoutMs: number;
  
  /** User preferences for this request */
  readonly preferences?: {
    ragSearchEnabled?: boolean;
    instructions?: string;
  };
}

/**
 * Tool validation result
 */
export interface ToolValidationResult {
  readonly valid: boolean;
  readonly error?: string;
  readonly allowedFileIds?: readonly string[];
}

/**
 * Enhanced tool call arguments with validation
 */
export interface ValidatedToolCallArgs {
  readonly query: string;
  readonly userId: string;
  readonly projectId?: string;
  readonly fileIds?: readonly string[];
  readonly limit?: number;
  readonly [key: string]: unknown;
}

/**
 * Tool execution security validator
 */
export interface ToolSecurityValidator {
  /**
   * Validate that a tool call is allowed for this user/context
   */
  validateToolCall(
    toolName: string,
    args: unknown,
    context: ToolExecutionContext
  ): ToolValidationResult;
  
  /**
   * Validate file access permissions
   */
  validateFileAccess(
    requestedFileIds: string[],
    allowedFileIds: readonly string[],
    userId: string
  ): ToolValidationResult;
}

/**
 * Default security validator implementation
 */
export class DefaultToolSecurityValidator implements ToolSecurityValidator {
  validateToolCall(
    _toolName: string,
    args: unknown,
    context: ToolExecutionContext
  ): ToolValidationResult {
    // Basic validation - can be extended
    if (!context.userId) {
      return { valid: false, error: "User ID required" };
    }
    
    if (Date.now() - context.requestStartTime > context.timeoutMs) {
      return { valid: false, error: "Request timeout" };
    }
    
    // Validate file access if fileIds are requested
    if (args && typeof args === 'object' && 'fileIds' in args) {
      const fileIds = (args as any).fileIds;
      if (Array.isArray(fileIds)) {
        return this.validateFileAccess(fileIds, context.allowedFileIds, context.userId);
      }
    }
    
    return { valid: true };
  }
  
  validateFileAccess(
    requestedFileIds: string[],
    allowedFileIds: readonly string[],
    _userId: string
  ): ToolValidationResult {
    if (!requestedFileIds.length) {
      // No files requested - allow with current context files
      return { 
        valid: true, 
        allowedFileIds 
      };
    }
    
    // Check if all requested files are in allowed list
    const unauthorizedFiles = requestedFileIds.filter(id => !allowedFileIds.includes(id));
    
    if (unauthorizedFiles.length > 0) {
      return { 
        valid: false, 
        error: `Access denied to files: ${unauthorizedFiles.join(', ')}` 
      };
    }
    
    return { 
      valid: true, 
      allowedFileIds: requestedFileIds 
    };
  }
}

/**
 * Create tool execution context for a request
 */
export function createToolExecutionContext(
  requestId: string,
  userId: string,
  allowedFileIds: string[],
  options: {
    projectId?: string;
    timeoutMs?: number;
    preferences?: ToolExecutionContext['preferences'];
  } = {}
): ToolExecutionContext {
  return {
    requestId,
    userId,
    projectId: options.projectId,
    allowedFileIds: Object.freeze([...allowedFileIds]),
    requestStartTime: Date.now(),
    timeoutMs: options.timeoutMs || 30000, // 30 second default
    preferences: options.preferences,
  };
}