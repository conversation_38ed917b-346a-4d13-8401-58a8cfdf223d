/**
 * MCP Registry - Generic registration system for MCP servers
 * 
 * Enables easy addition of new MCP servers without modifying core files.
 * Each MCP tool registers its own server configuration independently.
 */

import { createLogger } from "@/services/logger";
import type { MCPServerConfig } from "./MCPClientManager";

const logger = createLogger("MCPRegistry");

/**
 * MCP Server Registration Interface
 * Each MCP tool provides this configuration
 */
export interface MCPServerRegistration {
  serverConfig: MCPServerConfig;
  toolConfigs: MCPToolRegistration[];
}

/**
 * Individual tool configuration within an MCP server
 */
export interface MCPToolRegistration {
  toolName: string; // Tool name on MCP server
  localName: string; // Local tool name (e.g., "beings_rag")
  enabled: boolean;
  autoConnect?: boolean;
  fallbackToHTTP?: boolean;
}

/**
 * Global registry for all MCP servers and their tools
 */
class MCPRegistry {
  private servers = new Map<string, MCPServerRegistration>();
  private initialized = false;

  /**
   * Register an MCP server with its tools
   */
  registerServer(serverName: string, registration: MCPServerRegistration): void {
    if (this.servers.has(serverName)) {
      logger.warn(`MCP server '${serverName}' already registered, overriding`);
    }

    this.servers.set(serverName, registration);
    
    logger.info("MCP server registered", {
      serverName,
      serverUrl: registration.serverConfig.serverUrl,
      toolCount: registration.toolConfigs.length,
      tools: registration.toolConfigs.map(t => t.localName),
    });
  }

  /**
   * Get all registered MCP servers
   */
  getAllServers(): Map<string, MCPServerRegistration> {
    return new Map(this.servers);
  }

  /**
   * Get specific server configuration
   */
  getServer(serverName: string): MCPServerRegistration | undefined {
    return this.servers.get(serverName);
  }

  /**
   * Get tool configuration by local tool name
   */
  getToolConfig(localToolName: string): {
    serverName: string;
    serverConfig: MCPServerConfig;
    toolConfig: MCPToolRegistration;
  } | null {
    for (const [serverName, registration] of this.servers.entries()) {
      const toolConfig = registration.toolConfigs.find(
        tool => tool.localName === localToolName
      );
      
      if (toolConfig) {
        return {
          serverName,
          serverConfig: registration.serverConfig,
          toolConfig,
        };
      }
    }
    
    return null;
  }

  /**
   * Get all registered MCP server configurations for client manager
   */
  getAllServerConfigs(): Record<string, MCPServerConfig> {
    const configs: Record<string, MCPServerConfig> = {};
    
    for (const [serverName, registration] of this.servers.entries()) {
      configs[serverName] = registration.serverConfig;
    }
    
    return configs;
  }

  /**
   * Initialize registry (call once on startup)
   */
  initialize(): void {
    if (this.initialized) return;

    logger.info("MCP Registry initialized", {
      serverCount: this.servers.size,
      servers: Array.from(this.servers.keys()),
    });

    this.initialized = true;
  }

  /**
   * Get registry status for monitoring
   */
  getStatus() {
    const servers = Array.from(this.servers.entries()).map(([name, reg]) => ({
      name,
      url: reg.serverConfig.serverUrl,
      toolCount: reg.toolConfigs.length,
      tools: reg.toolConfigs.map(t => ({
        local: t.localName,
        remote: t.toolName,
        enabled: t.enabled,
      })),
    }));

    return {
      initialized: this.initialized,
      serverCount: this.servers.size,
      servers,
    };
  }
}

// Singleton instance
const mcpRegistry = new MCPRegistry();

/**
 * Get the global MCP registry instance
 */
export function getMCPRegistry(): MCPRegistry {
  return mcpRegistry;
}

/**
 * Helper function to register an MCP server
 */
export function registerMCPServer(
  serverName: string, 
  registration: MCPServerRegistration
): void {
  mcpRegistry.registerServer(serverName, registration);
}