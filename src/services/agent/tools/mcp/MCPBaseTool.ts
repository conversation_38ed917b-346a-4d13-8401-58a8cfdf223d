/**
 * MCP Base Tool - Abstract base class for MCP-based tools
 *
 * Extends BaseTool to provide MCP-specific functionality while maintaining
 * the same interface and patterns as HTTP-based tools.
 */

import { BaseTool } from "../BaseTool";
import type { Tool<PERSON>allArgs, ToolCallResult } from "../ToolRegistry";
import type { ToolExecutionContext } from "../ToolExecutionContext";
import { getMCPClientManager, type MCPServerConfig } from "./MCPClientManager";
import { getMCPRegistry } from "./MCPRegistry";
import { CallToolResult } from "@modelcontextprotocol/sdk/types.js";

/**
 * Result from MCP tool execution with parsing utilities
 */
export interface MCPToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Abstract base class for tools that communicate via Model Context Protocol
 *
 * Provides:
 * - Automatic MCP server registration and connection management
 * - Standardized error handling and result parsing
 * - Health monitoring integration
 * - Consistent logging and metrics
 * - Graceful fallback when MCP server is unavailable
 */
export abstract class MCPBaseTool extends BaseTool {
  protected mcpClientManager = getMCPClientManager();
  protected mcpRegistry = getMCPRegistry();

  // Resolved at runtime from registry instead of hardcoded config
  private _resolvedConfig: {
    serverName: string;
    serverConfig: MCPServerConfig;
    toolName: string;
    autoConnect: boolean;
  } | null = null;

  constructor() {
    super();
  }

  // ============================================================================
  // ABSTRACT METHODS (to be implemented by subclasses)
  // ============================================================================

  /**
   * Transform tool call arguments to MCP format
   * Allows subclasses to modify arguments before sending to MCP server
   */
  protected abstract transformArgsToMCP(
    args: ToolCallArgs
  ): Record<string, any>;

  /**
   * Parse MCP response to tool result format
   * Handles MCP-specific response parsing and transformation
   */
  protected abstract parseMCPResponse(mcpResult: CallToolResult): MCPToolResult;

  /**
   * Validate MCP-specific arguments
   * Additional validation beyond standard BaseTool validation
   */
  protected validateMCPArgs(args: ToolCallArgs): {
    valid: boolean;
    error?: string;
  } {
    // Default implementation - subclasses can override
    return { valid: true };
  }

  // ============================================================================
  // MCP EXECUTION FRAMEWORK
  // ============================================================================

  /**
   * Execute tool via MCP protocol
   * Handles the complete MCP execution flow with error handling and metrics
   */
  async execute(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult> {
    const startTime = Date.now();
    const config = this.getMCPConfig();
    const functionName = config.toolName;

    this.logStart(functionName, context, args);

    // Standard argument validation
    const baseValidation = this.validateArgs(args, this.getRequiredArgs());
    if (!baseValidation.valid) {
      this.logError(
        functionName,
        context,
        baseValidation.error!,
        Date.now() - startTime
      );
      return this.createErrorResult(baseValidation.error!);
    }

    // MCP-specific validation
    const mcpValidation = this.validateMCPArgs(args);
    if (!mcpValidation.valid) {
      this.logError(
        functionName,
        context,
        mcpValidation.error!,
        Date.now() - startTime
      );
      return this.createErrorResult(mcpValidation.error!);
    }

    try {
      // Check MCP server connectivity
      const isConnected = await this.ensureMCPConnection();
      if (!isConnected) {
        const fallbackResult = await this.handleMCPUnavailable(args, context);
        if (fallbackResult) {
          return fallbackResult;
        }

        const errorMsg = `MCP server '${config.serverName}' is not available`;
        this.logError(functionName, context, errorMsg, Date.now() - startTime);
        return this.createErrorResult(errorMsg);
      }

      // Transform arguments for MCP
      const mcpArgs = this.transformArgsToMCP(args);

      // Execute via MCP
      const mcpResult = await this.mcpClientManager.callTool(
        config.serverName,
        config.toolName,
        mcpArgs
      );

      // Parse MCP response
      const parsedResult = this.parseMCPResponse(mcpResult);
      const processingTime = Date.now() - startTime;

      if (!parsedResult.success) {
        this.logError(
          functionName,
          context,
          parsedResult.error!,
          processingTime
        );
        return this.createErrorResult(
          parsedResult.error || "MCP tool execution failed",
          {
            processingTime,
            serverName: config.serverName,
            ...parsedResult.metadata,
          }
        );
      }

      // Log successful completion
      this.logComplete(functionName, context, true, processingTime, {
        serverName: config.serverName,
        mcpToolName: config.toolName,
        ...parsedResult.metadata,
      });

      return this.createSuccessResult(parsedResult.data, {
        processingTime,
        serverName: config.serverName,
        mcpToolName: config.toolName,
        source: "mcp",
        ...parsedResult.metadata,
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logError(functionName, context, error, processingTime);

      // Attempt graceful fallback
      const fallbackResult = await this.handleMCPError(error, args, context);
      if (fallbackResult) {
        return fallbackResult;
      }

      return this.createErrorResult(
        error instanceof Error ? error.message : "Unknown MCP error occurred",
        {
          processingTime,
          serverName: config.serverName,
        }
      );
    }
  }

  // ============================================================================
  // CONNECTION MANAGEMENT
  // ============================================================================

  /**
   * Ensure MCP server connection is established
   */
  protected async ensureMCPConnection(): Promise<boolean> {
    const config = this.getMCPConfig();
    const status = this.mcpClientManager.getConnectionStatus(config.serverName);

    if (status?.connected) {
      // Verify connection is still healthy
      return await this.mcpClientManager.checkServerHealth(config.serverName);
    }

    // Attempt to connect
    try {
      await this.mcpClientManager.connectToServer(config.serverName);
      return true;
    } catch (error) {
      this.logger.warn("Failed to establish MCP connection", {
        toolName: this.name,
        serverName: config.serverName,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Get resolved MCP configuration from registry
   */
  protected getMCPConfig() {
    if (!this._resolvedConfig) {
      const config = this.mcpRegistry.getToolConfig(this.name);
      if (!config) {
        throw new Error(
          `MCP configuration not found for tool: ${this.name}. Make sure to register the MCP server first.`
        );
      }

      this._resolvedConfig = {
        serverName: config.serverName,
        serverConfig: config.serverConfig,
        toolName: config.toolConfig.toolName,
        autoConnect: config.toolConfig.autoConnect ?? true,
      };
    }

    return this._resolvedConfig;
  }

  /**
   * Register MCP server configuration (automatically handled by registry)
   */
  protected registerMCPServer(): void {
    const config = this.getMCPConfig();
    this.mcpClientManager.registerServer(config.serverConfig);
  }

  // ============================================================================
  // FALLBACK AND ERROR HANDLING
  // ============================================================================

  /**
   * Handle case when MCP server is unavailable
   * Subclasses can override to provide fallback functionality
   */
  protected async handleMCPUnavailable(
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult | null> {
    const config = this.getMCPConfig();
    // Default: no fallback
    this.logger.warn("MCP server unavailable and no fallback implemented", {
      toolName: this.name,
      serverName: config.serverName,
    });
    return null;
  }

  /**
   * Handle MCP execution errors with intelligent fallback
   * Subclasses can override to provide additional error-specific fallbacks
   */
  protected async handleMCPError(
    error: unknown,
    args: ToolCallArgs,
    context: ToolExecutionContext
  ): Promise<ToolCallResult | null> {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Check if error is recoverable (network issues, timeouts, etc.)
    const isRecoverableError = this.isRecoverableError(errorMessage);

    if (isRecoverableError) {
      this.logger.warn("Recoverable MCP error, attempting fallback", {
        error: errorMessage,
        toolName: this.name,
        userId: args.userId,
      });

      return await this.handleMCPUnavailable(args, context);
    }

    // Non-recoverable error - let main error handling take over
    const config = this.getMCPConfig();
    this.logger.error("Non-recoverable MCP execution error", {
      toolName: this.name,
      serverName: config.serverName,
      error: errorMessage,
    });
    return null;
  }

  /**
   * Determine if an error is recoverable and worth falling back
   */
  protected isRecoverableError(errorMessage: string): boolean {
    const recoverablePatterns = [
      /timeout/i,
      /connection/i,
      /network/i,
      /unavailable/i,
      /refused/i,
      /reset/i,
    ];

    return recoverablePatterns.some((pattern) => pattern.test(errorMessage));
  }

  // ============================================================================
  // HEALTH AND LIFECYCLE
  // ============================================================================

  /**
   * Enhanced health check for MCP tools with fallback support
   * Subclasses can override this method to add tool-specific health checks
   */
  protected async checkMCPHealth(): Promise<boolean> {
    const config = this.getMCPConfig();

    try {
      // Check if MCP client manager is available
      if (!this.mcpClientManager) {
        return false;
      }

      // Check MCP server health
      return await this.mcpClientManager.checkServerHealth(config.serverName);
    } catch (error) {
      this.logger.warn("MCP tool health check failed", {
        toolName: this.name,
        serverName: config.serverName,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Enhanced health check with fallback availability testing
   * Override this method to customize overall health checking behavior
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Get tool configuration from registry
      const toolConfig = this.mcpRegistry.getToolConfig(this.name);
      if (!toolConfig?.toolConfig.enabled) {
        return false;
      }

      // Check MCP health first
      const mcpHealth = await this.checkMCPHealth();

      if (mcpHealth) {
        return true;
      }

      // MCP is unhealthy - check if fallback is available
      const fallbackEnabled = toolConfig.toolConfig.fallbackToHTTP;
      const hasServerUrl = !!toolConfig.serverConfig.serverUrl;
      const hasAuthToken = !!toolConfig.serverConfig.authToken;

      // If fallback is enabled and properly configured, tool is still healthy
      if (fallbackEnabled && hasServerUrl && hasAuthToken) {
        this.logger.info("MCP unhealthy but HTTP fallback available", {
          toolName: this.name,
          serverName: toolConfig.serverName,
        });
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error("Health check failed", {
        toolName: this.name,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Initialize MCP tool
   */
  async initialize(): Promise<void> {
    await super.initialize();

    try {
      const config = this.getMCPConfig();

      // Register MCP server
      this.registerMCPServer();

      // Auto-connect if configured
      if (config.autoConnect) {
        await this.ensureMCPConnection();
      }

      this.logger.info("MCP tool initialized successfully", {
        toolName: this.name,
        serverName: config.serverName,
        mcpToolName: config.toolName,
        autoConnect: config.autoConnect,
      });
    } catch (error) {
      this.logger.error("Failed to initialize MCP tool", {
        toolName: this.name,
        error: error instanceof Error ? error.message : String(error),
      });

      // Don't throw - allow tool to be registered as unhealthy
      // This enables graceful degradation
    }
  }

  /**
   * Cleanup MCP tool
   */
  async cleanup(): Promise<void> {
    await super.cleanup();

    try {
      const config = this.getMCPConfig();

      // Note: We don't disconnect the MCP server here as other tools might be using it
      // The MCPClientManager handles server lifecycle management
      this.logger.info("MCP tool cleaned up", {
        toolName: this.name,
        serverName: config.serverName,
      });
    } catch (error) {
      this.logger.error("Error during MCP tool cleanup", {
        toolName: this.name,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get required arguments for the tool
   * Subclasses should override to specify their required arguments
   */
  protected getRequiredArgs(): string[] {
    return [];
  }

  /**
   * Get MCP server connection status
   */
  protected getMCPServerStatus() {
    const config = this.getMCPConfig();
    return this.mcpClientManager.getConnectionStatus(config.serverName);
  }

  /**
   * Get available tools from MCP server
   */
  protected async getAvailableMCPTools() {
    const config = this.getMCPConfig();
    return await this.mcpClientManager.getAvailableTools(config.serverName);
  }
}
