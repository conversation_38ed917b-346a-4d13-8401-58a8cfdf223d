/**
 * MCP Client Manager - Central management for Model Context Protocol connections
 *
 * Provides a robust foundation for managing multiple MCP server connections
 * with connection pooling, health monitoring, and automatic reconnection.
 */

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import {
  CallToolRequest,
  CallToolResult,
  Tool as MCPTool,
} from "@modelcontextprotocol/sdk/types.js";
import { createLogger } from "@/services/logger";

const logger = createLogger("MCPClientManager");

export enum MCPTransportType {
  STDIO = "stdio",
  SSE = "sse",
  STREAMABLE_HTTP = "streamable-http",
}

export interface MCPServerConfig {
  name: string;
  serverUrl: string;
  transportType?: MCPTransportType; // Explicit transport type specification
  authToken?: string;
  headers?: Record<string, string>; // Custom headers for HTTP-based MCP servers
  timeout: number;
  command?: string; // MCP server command
  args?: string[]; // MCP server arguments
  env?: Record<string, string>; // Environment variables
  retryAttempts?: number;
  retryDelay?: number;
}

export interface MCPConnectionStatus {
  connected: boolean;
  lastConnected?: Date;
  lastError?: string;
  availableTools: string[];
  serverInfo?: any;
}

/**
 * Manages connections to multiple MCP servers
 * Provides connection pooling, health monitoring, and automatic reconnection
 */
export class MCPClientManager {
  private clients = new Map<string, Client>();
  private transports = new Map<
    string,
    StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport
  >();
  private configs = new Map<string, MCPServerConfig>();
  private connectionStatus = new Map<string, MCPConnectionStatus>();
  private reconnectTimers = new Map<string, NodeJS.Timeout>();

  /**
   * Register an MCP server configuration
   */
  registerServer(config: MCPServerConfig): void {
    this.configs.set(config.name, config);
    this.connectionStatus.set(config.name, {
      connected: false,
      availableTools: [],
    });

    logger.info("MCP server registered", {
      serverName: config.name,
      serverUrl: config.serverUrl,
    });
  }

  /**
   * Connect to a specific MCP server
   */
  async connectToServer(serverName: string): Promise<void> {
    const config = this.configs.get(serverName);
    if (!config) {
      throw new Error(`MCP server '${serverName}' not registered`);
    }

    const startTime = Date.now();

    try {
      logger.info("Connecting to MCP server", {
        serverName,
        serverUrl: config.serverUrl,
      });

      // Create transport based on configured transport type
      let transport;
      const transportType = this.determineTransportType(config);

      logger.info("Creating MCP transport", {
        serverName,
        transportType,
        serverUrl: config.serverUrl,
      });

      switch (transportType) {
        case "streamable-http":
          transport = this.createStreamableHttpTransport(config);
          break;
        case "sse":
          transport = this.createSSETransport(config);
          break;
        case "stdio":
          transport = this.createStdioTransport(config);
          break;
        default:
          throw new Error(`Unsupported transport type: ${transportType}`);
      }

      // Create client
      const client = new Client(
        {
          name: `aida-${serverName}-client`,
          version: "1.0.0",
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );

      // Connect
      await client.connect(transport);

      // List available tools
      const toolsResult = await client.listTools();
      const availableTools = toolsResult.tools?.map((tool) => tool.name) || [];

      // Store connection
      this.clients.set(serverName, client);
      this.transports.set(serverName, transport);
      this.connectionStatus.set(serverName, {
        connected: true,
        lastConnected: new Date(),
        availableTools,
        serverInfo: toolsResult,
      });

      const connectionTime = Date.now() - startTime;
      logger.info("MCP server connected successfully", {
        serverName,
        connectionTime,
        availableTools: availableTools.length,
      });
    } catch (error) {
      const connectionTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.connectionStatus.set(serverName, {
        connected: false,
        lastError: errorMessage,
        availableTools: [],
      });

      logger.warn("Failed to connect to MCP server", {
        serverName,
        error: errorMessage,
        connectionTime,
        serverUrl: config.serverUrl,
      });

      // Schedule reconnection if retry is configured
      if (config.retryAttempts && config.retryAttempts > 0) {
        this.scheduleReconnection(serverName, config.retryDelay || 5000);
      }

      throw error;
    }
  }

  /**
   * Execute a tool call on a specific MCP server
   */
  async callTool(
    serverName: string,
    toolName: string,
    args: Record<string, any>
  ): Promise<CallToolResult> {
    const client = this.clients.get(serverName);
    if (!client) {
      throw new Error(`MCP server '${serverName}' not connected`);
    }

    const status = this.connectionStatus.get(serverName);
    if (!status?.connected) {
      throw new Error(`MCP server '${serverName}' is not connected`);
    }

    if (!status.availableTools.includes(toolName)) {
      throw new Error(
        `Tool '${toolName}' not available on server '${serverName}'`
      );
    }

    const startTime = Date.now();

    try {
      // Execute MCP tool call

      const request: CallToolRequest = {
        method: "tools/call",
        params: {
          name: toolName,
          arguments: args,
        },
      };

      const result = await client.callTool(request.params);
      const executionTime = Date.now() - startTime;

      // MCP tool call completed

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      logger.error("MCP tool call failed", {
        serverName,
        toolName,
        error: error instanceof Error ? error.message : String(error),
        executionTime,
      });

      throw error;
    }
  }

  /**
   * Get available tools for a server
   */
  async getAvailableTools(serverName: string): Promise<MCPTool[]> {
    const client = this.clients.get(serverName);
    if (!client) {
      throw new Error(`MCP server '${serverName}' not connected`);
    }

    try {
      const result = await client.listTools();
      return result.tools || [];
    } catch (error) {
      logger.error("Failed to get available tools", {
        serverName,
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  /**
   * Check health of a specific server
   */
  async checkServerHealth(serverName: string): Promise<boolean> {
    const status = this.connectionStatus.get(serverName);
    if (!status?.connected) {
      return false;
    }

    const client = this.clients.get(serverName);
    if (!client) {
      return false;
    }

    try {
      // Ping server by listing tools
      await client.listTools();
      return true;
    } catch (error) {
      logger.warn("MCP server health check failed", {
        serverName,
        error: error instanceof Error ? error.message : String(error),
      });

      // Mark as disconnected
      this.connectionStatus.set(serverName, {
        ...status,
        connected: false,
        lastError: error instanceof Error ? error.message : String(error),
      });

      return false;
    }
  }

  /**
   * Get connection status for a server
   */
  getConnectionStatus(serverName: string): MCPConnectionStatus | undefined {
    return this.connectionStatus.get(serverName);
  }

  /**
   * Get connection status for all servers
   */
  getAllConnectionStatus(): Map<string, MCPConnectionStatus> {
    return new Map(this.connectionStatus);
  }

  /**
   * Disconnect from a specific server
   */
  async disconnectFromServer(serverName: string): Promise<void> {
    const client = this.clients.get(serverName);
    if (client) {
      try {
        await client.close();
        logger.info("MCP server disconnected", { serverName });
      } catch (error) {
        logger.error("Error disconnecting from MCP server", {
          serverName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Clear reconnection timer
    const timer = this.reconnectTimers.get(serverName);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(serverName);
    }

    // Clean up connections
    this.clients.delete(serverName);
    this.transports.delete(serverName);

    const status = this.connectionStatus.get(serverName);
    if (status) {
      this.connectionStatus.set(serverName, {
        ...status,
        connected: false,
      });
    }
  }

  /**
   * Connect to all registered servers
   */
  async connectToAllServers(): Promise<void> {
    const serverNames = Array.from(this.configs.keys());

    logger.info("Connecting to all MCP servers", {
      serverCount: serverNames.length,
    });

    const connectionPromises = serverNames.map(async (serverName) => {
      try {
        await this.connectToServer(serverName);
      } catch (error) {
        logger.warn("Failed to connect to MCP server during bulk connection", {
          serverName,
          error: error instanceof Error ? error.message : String(error),
        });
        // Don't throw - allow other servers to connect
      }
    });

    await Promise.all(connectionPromises);

    const connectedCount = Array.from(this.connectionStatus.values()).filter(
      (status) => status.connected
    ).length;

    logger.info("MCP bulk connection completed", {
      totalServers: serverNames.length,
      connectedServers: connectedCount,
    });
  }

  /**
   * Disconnect from all servers
   */
  async disconnectFromAllServers(): Promise<void> {
    const serverNames = Array.from(this.clients.keys());

    logger.info("Disconnecting from all MCP servers", {
      serverCount: serverNames.length,
    });

    const disconnectionPromises = serverNames.map((serverName) =>
      this.disconnectFromServer(serverName)
    );

    await Promise.all(disconnectionPromises);

    logger.info("All MCP servers disconnected");
  }

  /**
   * Schedule automatic reconnection for a server
   */
  private scheduleReconnection(serverName: string, delay: number): void {
    // Clear existing timer
    const existingTimer = this.reconnectTimers.get(serverName);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(async () => {
      logger.info("Attempting MCP server reconnection", { serverName, delay });

      try {
        await this.connectToServer(serverName);
      } catch (error) {
        logger.warn("MCP server reconnection failed", {
          serverName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }, delay);

    this.reconnectTimers.set(serverName, timer);
  }

  /**
   * Health check for all servers
   */
  async healthCheckAllServers(): Promise<Map<string, boolean>> {
    const serverNames = Array.from(this.clients.keys());
    const healthResults = new Map<string, boolean>();

    const healthPromises = serverNames.map(async (serverName) => {
      const isHealthy = await this.checkServerHealth(serverName);
      healthResults.set(serverName, isHealthy);
    });

    await Promise.all(healthPromises);
    return healthResults;
  }

  /**
   * Determine the appropriate transport type for a server configuration
   */
  private determineTransportType(config: MCPServerConfig): MCPTransportType {
    // If explicitly specified, use that
    if (config.transportType) {
      return config.transportType;
    }

    // Auto-detect based on URL pattern
    if (config.serverUrl.startsWith("http://") || config.serverUrl.startsWith("https://")) {
      // Default to SSE for HTTP servers unless otherwise specified
      return MCPTransportType.SSE;
    } else {
      // Default to stdio for non-HTTP servers
      return MCPTransportType.STDIO;
    }
  }

  /**
   * Create a Streamable HTTP transport
   */
  private createStreamableHttpTransport(config: MCPServerConfig) {
    const headers: Record<string, string> = {
      ...config.headers,
    };

    if (config.authToken) {
      headers.Authorization = `Bearer ${config.authToken}`;
    }

    return new StreamableHTTPClientTransport(new URL(config.serverUrl), {
      requestInit: { headers },
    });
  }

  /**
   * Create an SSE transport
   */
  private createSSETransport(config: MCPServerConfig) {
    const headers: Record<string, string> = {
      ...config.headers,
    };

    if (config.authToken) {
      headers.Authorization = `Bearer ${config.authToken}`;
    }

    return new SSEClientTransport(new URL(config.serverUrl), {
      requestInit: { headers },
    });
  }

  /**
   * Create a stdio transport
   */
  private createStdioTransport(config: MCPServerConfig) {
    return new StdioClientTransport({
      command: config.command || "mcp-server",
      args: config.args || [],
      env: {
        ...process.env,
        ...config.env,
        MCP_SERVER_URL: config.serverUrl,
        MCP_AUTH_TOKEN: config.authToken || "",
      },
    });
  }
}

// Singleton instance
let mcpClientManagerInstance: MCPClientManager | null = null;

/**
 * Get the singleton MCP client manager
 */
export function getMCPClientManager(): MCPClientManager {
  if (!mcpClientManagerInstance) {
    mcpClientManagerInstance = new MCPClientManager();
  }
  return mcpClientManagerInstance;
}

/**
 * Initialize MCP client manager (call on service startup)
 */
export async function initializeMCPClientManager(): Promise<void> {
  getMCPClientManager(); // Initialize the singleton

  // Register default servers here if needed
  // This will be server-specific in actual tools

  logger.info("MCP Client Manager initialized");
}

/**
 * Cleanup MCP client manager (call on service shutdown)
 */
export async function cleanupMCPClientManager(): Promise<void> {
  if (mcpClientManagerInstance) {
    await mcpClientManagerInstance.disconnectFromAllServers();
    mcpClientManagerInstance = null;
    logger.info("MCP Client Manager cleaned up");
  }
}
