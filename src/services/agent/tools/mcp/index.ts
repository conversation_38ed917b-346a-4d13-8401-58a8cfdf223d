/**
 * MCP (Model Context Protocol) Tool Foundation
 *
 * Provides comprehensive MCP support for the AIDA agent tools system
 *
 * ## Architecture Overview
 *
 * 1. **MCPRegistry** - Central registration system for MCP servers
 * 2. **Simple TypeScript Objects** - Clean configuration without complex builders
 * 3. **MCPClientManager** - Connection management and transport handling
 * 4. **MCPBaseTool** - Abstract base class for MCP tools
 *
 * ## Adding New MCP Tools
 *
 * 1. Extend MCPBaseTool
 * 2. Use simple TypeScript objects for clean server configuration
 * 3. Register with MCPRegistry in constructor
 * 4. Implement required abstract methods
 *
 * No core file modifications needed!
 */

// Core Registry System (New Architecture)
export { getMCPRegistry, registerMCPServer } from "./MCPRegistry";
export type { MCPServerRegistration, MCPToolRegistration } from "./MCPRegistry";

// Client Management
export {
  MCPClientManager,
  getMCPClientManager,
  initializeMCPClientManager,
  cleanupMCPClientManager,
} from "./MCPClientManager";
export type { MC<PERSON>erverConfig, MCPConnectionStatus } from "./MCPClientManager";

// Base Tool Class
export { MCPBaseTool } from "./MCPBaseTool";
export type { MCPToolResult } from "./MCPBaseTool";

// Re-export MCP SDK types for convenience
export type {
  CallToolRequest,
  CallToolResult,
  Tool as MCPTool,
} from "@modelcontextprotocol/sdk/types.js";
