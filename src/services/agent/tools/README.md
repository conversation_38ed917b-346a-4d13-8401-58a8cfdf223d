# AIDA Agent Tools System

**Clean, extensible tool architecture for AI agent interactions with native Google GenAI function calling.**

## Architecture Overview

```text
🌐 AI Agent Request
     ↓
📄 ContextManager (smart context-aware initialization)
     ↓
🏗️ createAllTools() (creates all tool instances)
     ↓
🔧 ToolRegistry (instance-based, not singleton)
     ↓
⚡ tool.isEnabled(context) (each tool decides its own enablement)
     ↓
✅ Smart Filtering (per-request, contextual)
     ↓
🎯 Native Google GenAI Function Calls
```

### Core Components

| Component              | Purpose                              | Pattern |
| ---------------------- | ------------------------------------ | ------- |
| **createAllTools()**   | Creates all available tool instances | Factory |
| **isEnabled(context)** | Smart per-tool enablement logic      | Method |
| **ToolRegistry**       | Instance-based tool management       | Instance |
| **BaseTool**           | Abstract base for HTTP tools         | Abstract |
| **MCPBaseTool**        | Abstract base for MCP tools          | Abstract |

### Initialization Flow

```typescript
// 1. ContextManager.initialize() is called
async initialize() {
  // 2. Create ALL tool instances
  const tools = await createAllTools(); // Creates BeingsRAGTool, PineconeSearchTool, etc.
  
  // 3. Initialize registry with tools
  this.toolRegistry = await initializeToolRegistry(tools);
}

// 4. Per-request filtering happens in getAvailableToolsForRequest()
const contextEnabledTools = allTools.filter(tool => tool.isEnabled(toolContext));
```

**Key Benefits:**
- 🧠 **Self-Governing**: Each tool decides its own enablement logic
- 🎯 **Context-Aware**: Tools get full user context for smart decisions
- 🧪 **Testable**: Easy to mock context for different scenarios
- 📦 **Infinitely Scalable**: Each tool is independent
- 🔍 **Debuggable**: Clear why each tool is enabled/disabled per request

## Adding New HTTP Tools

```typescript
// providers/MyHTTPTool.ts
import { BaseTool } from "../BaseTool";
import type { Tool } from "@google/genai";
import { Type } from "@google/genai";

export class MyHTTPTool extends BaseTool {
  readonly name = "my_http_tool";
  readonly description = "HTTP-based tool";

  isEnabled(context: ToolEnablementContext): boolean {
    // Your custom enablement logic here
    return context.featureFlags.ENABLE_MY_TOOL ?? false;
  }

  constructor() {
    super();
  }

  getToolDefinition(): Tool {
    return {
      functionDeclarations: [{
        name: "do_http_thing",
        description: "Performs HTTP action",
        parameters: {
          type: Type.OBJECT,
          properties: {
            query: { type: Type.STRING, description: "Query parameter" },
          },
          required: ["query"],
        },
      }],
    };
  }

  async execute(args, context) {
    // HTTP implementation
    const result = await this.callExternalAPI(args.query);
    return this.createSuccessResult(result);
  }

  async healthCheck() {
    return true; // Check your API connectivity
  }
}

```

## Adding New MCP Tools

```typescript
// providers/MyMCPTool.ts
import { MCPBaseTool } from "../mcp/MCPBaseTool";
import { registerMCPServer } from "../mcp/MCPRegistry";
import type { MCPServerConfig } from "../mcp/MCPClientManager";

export class MyMCPTool extends MCPBaseTool {
  readonly name = "my_mcp_tool";
  readonly description = "MCP-based tool";

  isEnabled(context: ToolEnablementContext): boolean {
    // Your custom enablement logic here
    return context.featureFlags.ENABLE_MY_MCP ?? false;
  }

  constructor() {
    super();
    this.registerMyMCPServer();
  }

  private registerMyMCPServer(): void {
    const serverConfig: MCPServerConfig = {
      name: "my-mcp-server",
      serverUrl: (process.env.MY_MCP_URL || "http://localhost:3001") + "/mcp",
      authToken: process.env.MY_MCP_TOKEN,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 5000,
    };

    const toolConfigs = [
      {
        toolName: "remote_function_name",
        localName: "my_mcp_tool",
        enabled: process.env.ENABLE_MY_MCP === "true",
        autoConnect: true,
        fallbackToHTTP: false,
      },
    ];

    registerMCPServer("my-mcp-server", { serverConfig, toolConfigs });
  }

  getToolDefinition(): Tool {
    return {
      functionDeclarations: [{
        name: "do_mcp_thing",
        description: "MCP-powered action",
        parameters: {
          type: Type.OBJECT,
          properties: {
            input: { type: Type.STRING, description: "Input data" },
          },
          required: ["input"],
        },
      }],
    };
  }

  protected transformArgsToMCP(args) {
    return { input: String(args.input) };
  }

  protected parseMCPResponse(mcpResult) {
    if (mcpResult.isError) {
      return { success: false, error: "MCP error" };
    }
    return { success: true, data: mcpResult.content };
  }

  async healthCheck() {
    return await this.checkMCPHealth();
  }
}

```

## Tool Registration

**Super Simple 3-Step Process:**

### Step 1: Create your tool class (shown above)

### Step 2: Implement smart enablement logic

```typescript
export class MyNewTool extends BaseTool {
  readonly name = "my_new_tool";
  readonly description = "My awesome new tool";

  /**
   * Smart enablement logic - each tool decides when it should be enabled
   */
  isEnabled(context: ToolEnablementContext): boolean {
    // Check user preferences
    if (!context.preferences?.allowMyFeature) {
      return false;
    }

    // Check environment variables
    if (!context.envVars.MY_API_KEY) {
      return false;
    }

    // Check feature flags
    if (!context.featureFlags.ENABLE_MY_TOOL) {
      return false;
    }

    // Add any complex logic here:
    // - User tier checks
    // - Time-based availability  
    // - Geographic restrictions
    // - A/B testing logic
    // - Dependencies on other tools
    
    return true;
  }
  
  // ... rest of your tool implementation
}
```

### Step 3: Add to tool class array

```typescript
// providers/index.ts
const ALL_TOOL_CLASSES = [
  BeingsRAGTool,
  PineconeSearchTool,
  MyNewTool,        // 👆 Just add your class here!
];
```

**That's it!** Each tool controls its own destiny. The system automatically:
- ✅ Checks each tool's enablement logic
- ✅ Only creates enabled tools
- ✅ Handles complex contextual logic
- ✅ Scales to hundreds of tools without complexity

## Smart Enablement Examples

See how our current tools implement intelligent enablement:

### Beings RAG Tool
```typescript
isEnabled(context: ToolEnablementContext): boolean {
  // Must have RAG search enabled by user
  if (!context.preferences?.ragSearchEnabled) return false;
  
  // Enabled based on global config (toggles between Beings vs Pinecone)
  return enableBeingsMCP;
}
```

### Pinecone Search Tool  
```typescript
isEnabled(context: ToolEnablementContext): boolean {
  // Must have RAG search enabled by user
  if (!context.preferences?.ragSearchEnabled) return false;
  
  // Opposite of Beings MCP (toggles between Pinecone vs Beings)
  return !enableBeingsMCP;
}
```

**Key Insight:** `ENABLE_BEINGS_MCP` is not about enabling one tool - it's about **choosing between RAG engines**. Each tool implements the correct side of the toggle!

## Environment Configuration

```bash
# HTTP Tool
MY_HTTP_API_URL=https://api.example.com

# MCP Tool
MY_MCP_URL=https://mcp-server.example.com
MY_MCP_TOKEN=your-auth-token
```

## Runtime Control

Tools are controlled via:

- **Feature flags**: `ENABLE_BEINGS_MCP=true/false` (selects RAG engine: BeingsRAG vs Pinecone)
- **User preferences**: `ragSearchEnabled: true/false` (enables/disables RAG search entirely)

### Control Logic:

No more hardcoded control logic! Each tool implements its own:

```typescript
// BeingsRAGTool.isEnabled()
if (!context.preferences?.ragSearchEnabled) return false;
return enableBeingsMCP; // Uses config value

// PineconeSearchTool.isEnabled() 
if (!context.preferences?.ragSearchEnabled) return false;
return !enableBeingsMCP; // Opposite of Beings
```

## Key Features

### 🏗️ **Architecture**

- ✅ **Smart Registration** - Tools register themselves and decide enablement
- ✅ **Instance-Based** - No singletons, better testing and isolation
- ✅ **No Configuration Files** - Pure TypeScript, no external config
- ✅ **Predictable Flow** - Clear sequence: create → register → filter per request

### 🚀 **Developer Experience**

- ✅ **Zero Core Modifications** - Add tools without touching registry/manager code
- ✅ **3-Step Addition** - Create tool class + implement isEnabled() + add to list = done!
- ✅ **Context-Rich** - Tools get full user context for smart decisions
- ✅ **Type Safety** - Full TypeScript support with proper interfaces

### 🌐 **Production Ready**

- ✅ **Health Monitoring** - Built-in health checks and metrics
- ✅ **MCP Protocol** - Model Context Protocol with HTTP fallback
- ✅ **Environment-Driven** - All config via environment variables
- ✅ **Smart Control** - Each tool implements contextual logic

The system is designed for **scale** and **simplicity** - adding new tools takes **2 minutes**, not hours.
