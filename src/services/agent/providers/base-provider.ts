// Base Provider - Universal Interface for All AI Providers

import {
  UniversalRequest,
  UniversalResponse,
  UniversalToolCall,
  TokenUsage,
  ModelCapabilities,
  ProviderType,
  SupportedModel,
  FinishReason,
} from "@/types/universal-ai";
import { createLogger } from "@/services/logger";

const logger = createLogger("BaseProvider");

// Define AIProvider interface here since it's the base
export interface AIProvider {
  readonly name: ProviderType;
  readonly supportedModels: string[];

  generateStream(request: UniversalRequest): Promise<ReadableStream>;
  calculateCost(modelId: SupportedModel, usage: TokenUsage): number;
  isHealthy(): Promise<boolean>;
  getModelCapabilities(modelId: SupportedModel): ModelCapabilities;
}

export abstract class BaseProvider implements AIProvider {
  abstract readonly name: ProviderType;
  abstract readonly supportedModels: string[];

  abstract generateStream(request: UniversalRequest): Promise<ReadableStream>;
  abstract calculateCost(modelId: SupportedModel, usage: TokenUsage): number;
  abstract getModelCapabilities(modelId: SupportedModel): ModelCapabilities;

  async isHealthy(): Promise<boolean> {
    try {
      // Basic health check - try to make a minimal request
      const testRequest: UniversalRequest = {
        model: this.supportedModels[0] as SupportedModel,
        messages: [{ role: "user", content: "test" }],
        maxTokens: 10,
        stream: true,
      };

      const stream = await this.generateStream(testRequest);

      // Cancel the stream immediately to avoid costs
      if (stream.cancel) {
        stream.cancel();
      }

      return true;
    } catch (error) {
      logger.warn(`Health check failed for ${this.name} provider`, {
        error: error.message,
        provider: this.name,
      });
      return false;
    }
  }

  protected createUniversalStream(
    nativeStream: any,
    model: SupportedModel,
    extractContent: (chunk: any) => string | null,
    extractUsage: (chunk: any) => TokenUsage | null,
    extractFinishReason: (chunk: any) => FinishReason | null,
    extractToolCalls: (chunk: any) => UniversalToolCall[] | null
  ): ReadableStream {
    const encoder = new TextEncoder();

    return new ReadableStream({
      async start(controller) {
        try {
          let totalUsage: TokenUsage | null = null;

          for await (const chunk of nativeStream) {
            const content = extractContent(chunk);
            const usage = extractUsage(chunk);
            const finishReason = extractFinishReason(chunk);
            const toolCalls = extractToolCalls(chunk);

            // Update total usage if provided
            if (usage) {
              totalUsage = usage;
            }

            // Send chunk if there's content or tool calls
            if (content || toolCalls) {
              const universalChunk: UniversalResponse = {
                provider: this.name,
                model,
                content: content || "",
                usage: totalUsage || {
                  inputTokens: 0,
                  outputTokens: 0,
                  totalTokens: 0,
                },
                finishReason: finishReason || "continue", // ✅ FIX: Don't default to "stop"
                metadata: { timestamp: new Date().toISOString() },
                toolCalls: toolCalls || undefined,
              };
              // Encode as Uint8Array for proper ReadableStream handling
              const chunkData = encoder.encode(
                JSON.stringify(universalChunk) + "\n"
              );
              controller.enqueue(chunkData);
            }

            // Only break on explicit termination conditions
            if (
              finishReason === "stop" ||
              finishReason === "length" ||
              finishReason === "content_filter"
            ) {
              // Send final chunk with finish reason before breaking
              if (finishReason === "stop" && !content && !toolCalls) {
                const finalChunk: UniversalResponse = {
                  provider: this.name,
                  model,
                  content: "",
                  usage: totalUsage || {
                    inputTokens: 0,
                    outputTokens: 0,
                    totalTokens: 0,
                  },
                  finishReason: "stop",
                  metadata: { timestamp: new Date().toISOString() },
                  toolCalls: undefined,
                };
                const chunkData = encoder.encode(
                  JSON.stringify(finalChunk) + "\n"
                );
                controller.enqueue(chunkData);
              }
              break;
            }
          }

          controller.close();
        } catch (error) {
          logger.error(`Stream error in ${this.name} provider`, {
            error: error.message,
            provider: this.name,
            model,
          });
          controller.error(error);
        }
      },
    });
  }

  protected validateModel(modelId: string): void {
    if (!this.supportedModels.includes(modelId)) {
      throw new Error(
        `Model ${modelId} not supported by ${
          this.name
        } provider. Supported models: ${this.supportedModels.join(", ")}`
      );
    }
  }

  protected logProviderCall(
    modelId: SupportedModel, 
    messageCount: number,
    additionalContext?: Record<string, any>
  ): void {
    logger.info(`${this.name} provider API call`, {
      provider: this.name,
      model: modelId,
      messageCount,
      timestamp: new Date().toISOString(),
      ...additionalContext,
    });
  }
}
