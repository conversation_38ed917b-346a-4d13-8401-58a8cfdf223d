// Google Provider - Real Vertex AI Integration

import { GoogleGenAI } from "@google/genai";
import { BaseProvider } from "./base-provider";
import {
  UniversalRequest,
  UniversalTool,
  UniversalToolCall,
  TokenUsage,
  ModelCapabilities,
  GoogleRequest,
  SupportedModel,
  FinishReason,
  UniversalMessageContent,
} from "@/types/universal-ai";
import { getGoogleGenAI, initializeGoogleGenAI } from "@/config/googleGenAI";
import { getModel } from "@/config/models";
import { createLogger } from "@/services/logger";

const logger = createLogger("GoogleProvider");

export class GoogleProvider extends BaseProvider {
  readonly name = "google" as const;
  readonly supportedModels = ["gemini-2.5-pro", "gemini-2.5-flash"];

  private client: GoogleGenAI;

  constructor() {
    super();
    try {
      this.client = getGoogleGenAI();
    } catch (error) {
      if (error.message.includes("not initialized")) {
        this.client = initializeGoogleGenAI();
      } else {
        throw new Error(
          `Failed to initialize Google AI client: ${error.message}`
        );
      }
    }
  }

  async generateStream(request: UniversalRequest): Promise<ReadableStream> {
    this.validateModel(request.model);
    this.logProviderCall(request.model, request.messages.length, {
      hasTools: !!request.tools?.length,
      maxTokens: request.maxTokens,
      temperature: request.temperature,
    });

    // Convert universal format to Google format
    const googleRequest = this.convertToGoogleFormat(request);

    // Build configuration object like the direct Google execution path
    const requestConfig: any = {
      temperature: request.temperature,
      topP: 0.95,
      ...(request.maxTokens && { maxOutputTokens: request.maxTokens }),
      ...(request.systemInstructions?.length && {
        systemInstruction: {
          parts: [{ text: request.systemInstructions.join("\n") }],
        },
      }),
    };

    // Add tools configuration if available
    if (googleRequest.tools && googleRequest.tools.length > 0) {
      requestConfig.tools = googleRequest.tools;
      requestConfig.toolConfig = {
        functionCallingConfig: {
          mode: "AUTO",
        },
      };
    }

    // Make actual Google AI API call
    const streamGenerator = await this.client.models.generateContentStream({
      model: request.model,
      contents: googleRequest.contents,
      config: requestConfig,
    });

    // Convert Google stream to universal format
    return this.createUniversalStream(
      streamGenerator,
      request.model,
      (chunk) => {
        // Extract ALL parts content, not just the first part
        const parts = chunk.candidates?.[0]?.content?.parts;
        if (!parts || parts.length === 0) return null;

        let allText = "";
        for (const part of parts) {
          if (part.text) {
            allText += part.text;
          }
        }
        return allText || null;
      },
      (chunk) => {
        if (chunk.usageMetadata) {
          return {
            inputTokens: chunk.usageMetadata.promptTokenCount || 0,
            outputTokens: chunk.usageMetadata.candidatesTokenCount || 0,
            totalTokens: chunk.usageMetadata.totalTokenCount || 0,
          };
        }
        return null;
      },
      (chunk) =>
        this.mapGoogleFinishReason(chunk.candidates?.[0]?.finishReason),
      (chunk) => this.extractGoogleToolCalls(chunk)
    );
  }

  calculateCost(modelId: SupportedModel, usage: TokenUsage): number {
    try {
      const model = getModel(modelId);
      return (
        (usage.inputTokens * model.pricing.input) / 1000 +
        (usage.outputTokens * model.pricing.output) / 1000
      );
    } catch (error) {
      // Fallback pricing if model not found
      logger.warn("Using fallback pricing for unknown model", {
        modelId,
        provider: this.name,
        error: error.message,
      });
      return (usage.inputTokens * 0.001 + usage.outputTokens * 0.002) / 1000;
    }
  }

  getModelCapabilities(modelId: SupportedModel): ModelCapabilities {
    const capabilities: Record<string, ModelCapabilities> = {
      "gemini-2.5-pro": {
        maxContextLength: 2000000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
      "gemini-2.5-flash": {
        maxContextLength: 1000000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
    };

    return (
      capabilities[modelId] || {
        maxContextLength: 1000000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      }
    );
  }

  private convertToGoogleFormat(request: UniversalRequest): GoogleRequest {
    return {
      contents: request.messages
        .filter((msg) => msg.role !== "system") // Google handles system messages separately
        .map((msg) => ({
          role: msg.role === "assistant" ? "model" : "user",
          parts: this.convertMessageToParts(msg.content),
        })),
      tools: request.tools?.map((tool) =>
        this.convertUniversalToolToGoogle(tool)
      ),
    };
  }

  /**
   * Convert Universal tool format to Google/Gemini tool format
   * Ensures proper format for Google Gemini API
   */
  private convertUniversalToolToGoogle(tool: UniversalTool) {
    const parameters = tool.parameters || {};

    // Handle different parameter formats
    let properties: Record<string, any> = {};
    let requiredFields: string[] = [];

    // Check if parameters has the nested structure with 'properties' and 'required'
    if (parameters.properties && typeof parameters.properties === "object") {
      // Extract properties from nested structure
      properties = this.convertSchemaPropertiesGoogle(parameters.properties);

      // Extract required fields
      if (Array.isArray(parameters.required)) {
        requiredFields = parameters.required;
      }
    } else {
      // Direct properties format
      Object.entries(parameters).forEach(([key, value]) => {
        if (key !== "required") {
          properties[key] = this.convertSchemaPropertyGoogle(value);
        }
      });

      // Handle required fields
      if (parameters.required && Array.isArray(parameters.required)) {
        requiredFields = parameters.required;
      }
    }

    // Google expects specific format with functionDeclarations array
    const googleParameters = {
      type: "OBJECT", // Google expects uppercase
      properties,
      ...(requiredFields.length > 0 && { required: requiredFields }),
    };

    logger.debug("Converted tool to Google format", {
      toolName: tool.name,
      originalParameters: parameters,
      convertedParameters: googleParameters,
      requiredFields,
    });

    return {
      functionDeclarations: [
        {
          name: tool.name,
          description: tool.description,
          parameters: googleParameters,
        },
      ],
    };
  }

  /**
   * Convert schema properties to Google/Gemini format
   */
  private convertSchemaPropertiesGoogle(
    properties: Record<string, any>
  ): Record<string, any> {
    const converted: Record<string, any> = {};

    Object.entries(properties).forEach(([key, value]) => {
      converted[key] = this.convertSchemaPropertyGoogle(value);
    });

    return converted;
  }

  /**
   * Convert a single schema property to Google/Gemini format
   */
  private convertSchemaPropertyGoogle(property: any): any {
    if (!property || typeof property !== "object") {
      return property;
    }

    const converted: any = {};

    // Convert type - Google expects UPPERCASE types
    if (property.type) {
      switch (property.type.toUpperCase()) {
        case "STRING":
          converted.type = "STRING";
          break;
        case "NUMBER":
        case "INTEGER":
          converted.type = "NUMBER";
          break;
        case "BOOLEAN":
          converted.type = "BOOLEAN";
          break;
        case "ARRAY":
          converted.type = "ARRAY";
          if (property.items) {
            converted.items = this.convertSchemaPropertyGoogle(property.items);
          }
          break;
        case "OBJECT":
          converted.type = "OBJECT";
          if (property.properties) {
            converted.properties = this.convertSchemaPropertiesGoogle(
              property.properties
            );
          }
          break;
        default:
          converted.type = property.type.toUpperCase(); // Google expects uppercase
      }
    }

    // Copy other properties (description, enum, etc.)
    Object.entries(property).forEach(([key, value]) => {
      if (key !== "type" && key !== "required") {
        converted[key] = value;
      }
    });

    return converted;
  }

  private convertMessageToParts(
    content: string | UniversalMessageContent[]
  ): any[] {
    if (typeof content === "string") {
      return [{ text: content }];
    }

    return content.map((item) => {
      if (item.type === "text") {
        return { text: item.text };
      } else if (item.type === "image" && item.image) {
        return {
          inlineData: {
            mimeType: item.image.mimeType,
            data: item.image.data,
          },
        };
      }
      return { text: "" }; // Fallback
    });
  }

  private extractGoogleToolCalls(chunk: any): UniversalToolCall[] | null {
    const functionCall = chunk.candidates?.[0]?.content?.parts?.find(
      (part: any) => part.functionCall
    )?.functionCall;

    if (!functionCall) {
      return null;
    }

    return [
      {
        id: functionCall.name + "_" + Date.now(), // Generate unique ID
        name: functionCall.name,
        arguments: functionCall.args || {},
        status: "complete" as const,
      },
    ];
  }

  private mapGoogleFinishReason(
    reason: string | null | undefined
  ): FinishReason | null {
    switch (reason) {
      case "STOP":
        return "stop";
      case "MAX_TOKENS":
        return "length";
      case "SAFETY":
        return "content_filter";
      case "RECITATION":
        return "content_filter";
      case "OTHER":
        return "error";
      default:
        return null;
    }
  }
}
