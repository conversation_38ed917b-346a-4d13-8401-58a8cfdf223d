// Anthropic Provider - Real API Integration

import Anthropic from "@anthropic-ai/sdk";
import sharp from "sharp";
import { BaseProvider } from "./base-provider";
import {
  UniversalRequest,
  UniversalTool,
  UniversalToolCall,
  TokenUsage,
  ModelCapabilities,
  AnthropicRequest,
  SupportedModel,
  FinishReason,
  UniversalMessageContent,
} from "@/types/universal-ai";
import { getModel } from "@/config/models";
import { createLogger } from "@/services/logger";

const logger = createLogger("AnthropicProvider");

export class AnthropicProvider extends BaseProvider {
  readonly name = "anthropic" as const;
  readonly supportedModels = [
    "claude-3-7-sonnet-20250219",
    "claude-sonnet-4-20250514",
    "claude-opus-4-20250514",
    "claude-opus-4-1-20250805",
  ];

  private client: Anthropic;
  private currentToolCall: {
    id: string;
    name: string;
    arguments: string;
  } | null = null;

  constructor() {
    super();
    if (!process.env.ANTHROPIC_API_KEY) {
      throw new Error(
        "ANTHROPIC_API_KEY environment variable is required for Anthropic provider"
      );
    }
    this.client = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  }

  async generateStream(request: UniversalRequest): Promise<ReadableStream> {
    this.validateModel(request.model);
    this.logProviderCall(request.model, request.messages.length, {
      hasTools: !!request.tools?.length,
      maxTokens: request.maxTokens,
      temperature: request.temperature,
    });

    // Convert universal format to Anthropic format
    const anthropicRequest = await this.convertToAnthropicFormat(request);

    // Make actual Anthropic API call with retry logic for overloaded errors
    const stream = await this.createAnthropicStreamWithRetry({
      model: request.model,
      messages: anthropicRequest.messages as any,
      max_tokens: request.maxTokens || 8192,
      temperature: request.temperature,
      system: request.systemInstructions?.join("\n"),
      tools: anthropicRequest.tools as any,
    });

    // Convert Anthropic stream to universal format
    return this.createUniversalStream(
      stream,
      request.model,
      (chunk) => {
        if (
          chunk.type === "content_block_delta" &&
          chunk.delta.type === "text_delta"
        ) {
          return chunk.delta.text;
        }
        return null;
      },
      (chunk) => {
        if (chunk.type === "message_stop" && chunk.usage) {
          return {
            inputTokens: chunk.usage.input_tokens || 0,
            outputTokens: chunk.usage.output_tokens || 0,
            totalTokens:
              (chunk.usage.input_tokens || 0) +
              (chunk.usage.output_tokens || 0),
          };
        }
        return null;
      },
      (chunk) => this.mapAnthropicFinishReason(chunk),
      (chunk) => this.extractAnthropicToolCalls(chunk)
    );
  }

  calculateCost(modelId: SupportedModel, usage: TokenUsage): number {
    try {
      const model = getModel(modelId);
      return (
        (usage.inputTokens * model.pricing.input) / 1000 +
        (usage.outputTokens * model.pricing.output) / 1000
      );
    } catch (error) {
      // Fallback pricing for Anthropic models
      logger.warn("Using fallback pricing for model", {
        modelId,
        provider: this.name,
        error: error.message,
      });
      const pricing: Record<string, { input: number; output: number }> = {
        "claude-3-7-sonnet-20250219": { input: 3.0, output: 15.0 },
        "claude-sonnet-4-20250514": { input: 3.0, output: 15.0 },
        "claude-opus-4-20250514": { input: 15.0, output: 75.0 },
        "claude-opus-4-1-20250805": { input: 15.0, output: 75.0 },
      };
      const modelPricing =
        pricing[modelId] || pricing["claude-3-7-sonnet-20250219"];
      return (
        (usage.inputTokens * modelPricing.input) / 1000 +
        (usage.outputTokens * modelPricing.output) / 1000
      );
    }
  }

  getModelCapabilities(modelId: SupportedModel): ModelCapabilities {
    const capabilities: Record<string, ModelCapabilities> = {
      "claude-3-7-sonnet-20250219": {
        maxContextLength: 500000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
      "claude-sonnet-4-20250514": {
        maxContextLength: 500000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
      "claude-opus-4-20250514": {
        maxContextLength: 500000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
      "claude-opus-4-1-20250805": {
        maxContextLength: 500000,
        supportsTools: true,
        supportsImages: true,
        supportsStreaming: true,
      },
    };

    return (
      capabilities[modelId] || {
        maxContextLength: 200000,
        supportsTools: true,
        supportsImages: false,
        supportsStreaming: true,
      }
    );
  }

  private async convertToAnthropicFormat(
    request: UniversalRequest
  ): Promise<AnthropicRequest> {
    const messages = await Promise.all(
      request.messages
        .filter((msg) => msg.role !== "system") // Anthropic handles system messages separately
        .map(async (msg) => ({
          role: msg.role === "assistant" ? "assistant" : "user",
          content: await this.convertMessageContent(msg.content),
        }))
    );

    return {
      messages: messages as Array<{ role: "user" | "assistant"; content: any }>,
      tools: request.tools?.map((tool) =>
        this.convertUniversalToolToAnthropic(tool)
      ) as Array<{
        name: string;
        description: string;
        input_schema: {
          type: "object";
          properties: Record<string, any>;
          required: string[];
        };
      }>,
    };
  }

  /**
   * Convert Universal tool format to Anthropic tool format
   * Ensures JSON Schema draft 2020-12 compliance
   */
  private convertUniversalToolToAnthropic(tool: UniversalTool) {
    const parameters = tool.parameters || {};

    // Handle different parameter formats
    let properties: Record<string, any> = {};
    let requiredFields: string[] = [];

    // Check if parameters has the nested structure with 'properties' and 'required'
    // This handles the Google Tool format that gets passed through
    if (parameters.properties && typeof parameters.properties === "object") {
      // Extract properties from nested structure (Google format)
      properties = this.convertSchemaProperties(parameters.properties);

      // Extract required fields
      if (Array.isArray(parameters.required)) {
        requiredFields = parameters.required;
      }
    } else {
      // Direct properties format - process each parameter
      Object.entries(parameters).forEach(([key, value]: [string, any]) => {
        if (key === "type" || key === "required") {
          // Skip these at the top level - they're handled separately
          return;
        }

        if (value && typeof value === "object") {
          // If the parameter definition includes a 'required' flag
          if (value.required === true) {
            requiredFields.push(key);
          }

          // Clean the property definition (remove our custom 'required' flag)
          const { required, ...cleanProperty } = value;
          properties[key] = this.convertSchemaProperty(cleanProperty);
        } else {
          // Simple parameter format
          properties[key] = value;
        }
      });

      // Handle required fields at the top level
      if (parameters.required && Array.isArray(parameters.required)) {
        requiredFields = parameters.required;
      }
    }

    const anthropicTool = {
      name: tool.name,
      description: tool.description,
      input_schema: {
        type: "object",
        properties,
        ...(requiredFields.length > 0 && { required: requiredFields }),
      },
    };

    logger.debug("Converted tool to Anthropic format", {
      toolName: tool.name,
      originalParameters: parameters,
      convertedSchema: anthropicTool.input_schema,
      requiredFields,
    });

    return anthropicTool;
  }

  /**
   * Convert schema properties to JSON Schema 2020-12 format
   */
  private convertSchemaProperties(
    properties: Record<string, any>
  ): Record<string, any> {
    const converted: Record<string, any> = {};

    Object.entries(properties).forEach(([key, value]) => {
      converted[key] = this.convertSchemaProperty(value);
    });

    return converted;
  }

  /**
   * Convert a single schema property to JSON Schema format for Anthropic
   */
  private convertSchemaProperty(property: any): any {
    if (!property || typeof property !== "object") {
      return property;
    }

    const converted: any = {};

    // Convert type - handle both Google Type enum values and string values
    if (property.type) {
      const typeStr =
        typeof property.type === "string"
          ? property.type.toUpperCase()
          : String(property.type).toUpperCase();

      switch (typeStr) {
        case "STRING":
          converted.type = "string";
          break;
        case "NUMBER":
        case "INTEGER":
          converted.type = "number";
          break;
        case "BOOLEAN":
          converted.type = "boolean";
          break;
        case "ARRAY":
          converted.type = "array";
          if (property.items) {
            converted.items = this.convertSchemaProperty(property.items);
          }
          break;
        case "OBJECT":
          converted.type = "object";
          if (property.properties) {
            converted.properties = this.convertSchemaProperties(
              property.properties
            );
          }
          break;
        default:
          // Default to lowercase for any other type
          converted.type = typeStr.toLowerCase();
      }
    }

    // Copy other properties (description, enum, etc.) but NOT items since we handle it above
    Object.entries(property).forEach(([key, value]) => {
      if (
        key !== "type" &&
        key !== "required" &&
        key !== "items" &&
        key !== "properties"
      ) {
        converted[key] = value;
      }
    });

    return converted;
  }

  private async convertMessageContent(
    content: string | UniversalMessageContent[]
  ): Promise<any> {
    if (typeof content === "string") {
      return content;
    }

    // Convert to Anthropic's multipart format
    return await Promise.all(
      content.map(async (item) => {
        if (item.type === "text") {
          return {
            type: "text",
            text: item.text,
          };
        } else if (item.type === "image" && item.image) {
          const processedImage = await this.processImageForAnthropic(
            item.image.mimeType,
            item.image.data
          );

          if (!processedImage) {
            logger.warn(
              `[AnthropicProvider] Unsupported image format: ${item.image.mimeType}, skipping image`
            );
            return {
              type: "text",
              text: "[Image format not supported by Claude]",
            };
          }

          return {
            type: "image",
            source: {
              type: "base64",
              media_type: processedImage.mimeType,
              data: processedImage.data,
            },
          };
        }
        return { type: "text", text: "" }; // Fallback
      })
    );
  }

  /**
   * Process and convert images to Anthropic-supported formats
   * Anthropic only supports: image/jpeg, image/png, image/gif, image/webp
   */
  private async processImageForAnthropic(
    mimeType: string,
    base64Data: string
  ): Promise<{ mimeType: string; data: string } | null> {
    const supportedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
    ];

    // If already supported, return as-is
    if (supportedTypes.includes(mimeType)) {
      return { mimeType, data: base64Data };
    }

    try {
      // Convert unsupported formats to supported ones
      let targetMimeType: string;
      switch (mimeType) {
        case "image/bmp":
        case "image/tiff":
        case "image/tif":
          // Convert to PNG for lossless quality
          targetMimeType = "image/png";
          logger.info(
            `[AnthropicProvider] Converting ${mimeType} to ${targetMimeType} for Claude compatibility`
          );
          break;

        case "image/jpg": // Common misspelling
          targetMimeType = "image/jpeg";
          logger.info(
            `[AnthropicProvider] Correcting MIME type from ${mimeType} to ${targetMimeType}`
          );
          return { mimeType: targetMimeType, data: base64Data }; // No conversion needed, just MIME type fix

        default:
          // Unsupported format
          logger.warn(
            `[AnthropicProvider] Unsupported image format: ${mimeType}`
          );
          return null;
      }

      // Actually convert the image data
      const buffer = Buffer.from(base64Data, "base64");
      let convertedBuffer: Buffer;

      if (targetMimeType === "image/png") {
        convertedBuffer = await sharp(buffer)
          .png({ compressionLevel: 8 })
          .toBuffer();
      } else if (targetMimeType === "image/jpeg") {
        convertedBuffer = await sharp(buffer).jpeg({ quality: 85 }).toBuffer();
      } else {
        // Fallback to original buffer if no specific conversion
        convertedBuffer = buffer;
      }

      const convertedBase64 = convertedBuffer.toString("base64");
      logger.info(
        `[AnthropicProvider] Successfully converted image from ${mimeType} to ${targetMimeType}`
      );

      return {
        mimeType: targetMimeType,
        data: convertedBase64,
      };
    } catch (error) {
      logger.error(
        `[AnthropicProvider] Failed to convert image from ${mimeType}:`,
        {
          error: error instanceof Error ? error.message : String(error),
        }
      );
      return null;
    }
  }

  /**
   * Create Anthropic stream with retry logic for overloaded/rate limit errors
   */
  private async createAnthropicStreamWithRetry(
    params: any,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(
          `[AnthropicProvider] Attempting stream creation (attempt ${attempt}/${maxRetries})`
        );

        // Make the actual API call
        return this.client.messages.stream(params);
      } catch (error: any) {
        lastError = error;

        // Check if this is a retryable error
        const isRetryable = this.isRetryableError(error);

        if (!isRetryable || attempt === maxRetries) {
          // Don't retry for non-retryable errors or if we've exhausted retries
          logger.error(
            `[AnthropicProvider] Stream creation failed (attempt ${attempt}/${maxRetries})`,
            {
              error: error.message,
              type: error.type,
              retryable: isRetryable,
              finalAttempt: attempt === maxRetries,
            }
          );

          // Throw user-friendly error for overloaded scenarios
          if (isRetryable && attempt === maxRetries) {
            throw new Error(
              `Claude is currently experiencing high demand. Please try again in a few moments.`
            );
          }

          throw error;
        }

        // Calculate exponential backoff delay
        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), 10000); // Max 10s

        logger.warn(
          `[AnthropicProvider] Retryable error occurred, waiting ${delay}ms before retry`,
          {
            error: error.message,
            attempt,
            nextDelay: delay,
            errorType: error.type || "unknown",
          }
        );

        // Wait before retry
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // This shouldn't be reached, but just in case
    throw lastError || new Error("Unknown error during stream creation");
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Parse error message if it's a JSON string
    let errorInfo = error;
    if (typeof error.message === "string") {
      try {
        errorInfo = JSON.parse(error.message);
      } catch {
        // Not JSON, use original error
      }
    }

    // Check for overloaded error
    if (errorInfo?.error?.type === "overloaded_error") {
      return true;
    }

    // Check for rate limit errors (429)
    if (error.status === 429 || errorInfo?.status === 429) {
      return true;
    }

    // Check for temporary server errors (5xx)
    if (error.status >= 500 && error.status < 600) {
      return true;
    }

    // Check for connection/network errors
    if (
      error.code === "ECONNRESET" ||
      error.code === "ETIMEDOUT" ||
      error.code === "ENOTFOUND"
    ) {
      return true;
    }

    // Not retryable
    return false;
  }

  private extractAnthropicToolCalls(chunk: any): UniversalToolCall[] | null {
    // Handle tool use start
    if (
      chunk.type === "content_block_start" &&
      chunk.content_block?.type === "tool_use"
    ) {
      const toolBlock = chunk.content_block;
      // Start accumulating a new tool call
      this.currentToolCall = {
        id: toolBlock.id || `anthropic_tool_${Date.now()}`,
        name: toolBlock.name || "",
        arguments: "",
      };
      // Don't return yet, wait for arguments
      return null;
    }

    // Handle tool argument deltas
    if (
      chunk.type === "content_block_delta" &&
      chunk.delta?.type === "input_json_delta"
    ) {
      if (this.currentToolCall && chunk.delta.partial_json) {
        // Accumulate the partial JSON
        this.currentToolCall.arguments += chunk.delta.partial_json;
      }
      return null;
    }

    // Handle tool use completion
    if (chunk.type === "content_block_stop" && this.currentToolCall) {
      // Ensure we have a valid tool name
      if (!this.currentToolCall.name) {
        logger.warn("Anthropic tool call completed without tool name");
        this.currentToolCall = null;
        return null;
      }

      // Try to parse and return the complete tool call
      try {
        const parsedArgs = this.currentToolCall.arguments
          ? JSON.parse(this.currentToolCall.arguments)
          : {};

        const completedCall: UniversalToolCall = {
          id: this.currentToolCall.id,
          name: this.currentToolCall.name,
          arguments: parsedArgs,
          status: "complete" as const,
        };

        logger.info("Anthropic tool call completed", {
          toolName: this.currentToolCall.name,
          toolId: this.currentToolCall.id,
          argumentsLength: this.currentToolCall.arguments.length,
        });

        // Clear the current tool call
        this.currentToolCall = null;

        return [completedCall];
      } catch (e) {
        // Failed to parse arguments, clear and return null
        logger.warn("Failed to parse Anthropic tool call arguments", {
          toolName: this.currentToolCall.name,
          arguments: this.currentToolCall.arguments.substring(0, 100),
          error: e.message,
        });
        this.currentToolCall = null;
        return null;
      }
    }

    return null;
  }

  private mapAnthropicFinishReason(chunk: any): FinishReason | null {
    if (chunk.type === "message_stop") {
      return "stop";
    }
    if (chunk.type === "content_block_stop") {
      return "stop";
    }
    if (chunk.type === "error") {
      return "error";
    }
    return null;
  }
}
