// Provider Registry - Smart Routing for Multi-Provider AI System

import {
  UniversalRequest,
  ProviderType,
  TokenUsage,
  SupportedModel,
} from "@/types/universal-ai";
import { AIProvider } from "./providers/base-provider";
import { AnthropicProvider } from "./providers/anthropic-provider";
import { GoogleProvider } from "./providers/google-provider";
import { createLogger } from "@/services/logger";

const logger = createLogger("ProviderRegistry");

export class ProviderRegistry {
  private providers: Map<ProviderType, AIProvider> = new Map();
  private modelProviderMap: Map<string, AIProvider> = new Map();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    const providerClasses = [GoogleProvider, AnthropicProvider];

    for (const ProviderClass of providerClasses) {
      try {
        const provider = new ProviderClass();
        this.providers.set(provider.name, provider);

        // Map models to provider using self-declared models
        provider.supportedModels.forEach((model) => {
          this.modelProviderMap.set(model, provider);
        });

        logger.info(`Initialized ${provider.name} provider`, {
          models: provider.supportedModels.length,
          providerName: provider.name,
          supportedModels: provider.supportedModels,
        });
      } catch (error) {
        logger.error(`Failed to initialize ${ProviderClass.name} provider`, {
          error: error.message,
          provider: ProviderClass.name,
          note: "This provider will be unavailable - check API keys and configuration",
        });
        // Don't throw - continue with other providers
      }
    }

    if (this.providers.size === 0) {
      throw new Error(
        "No AI providers could be initialized. Check API keys: ANTHROPIC_API_KEY, GOOGLE_APPLICATION_CREDENTIALS"
      );
    }

    logger.info("Provider registry initialized", {
      providersCount: this.providers.size,
      modelsCount: this.modelProviderMap.size,
      availableProviders: Array.from(this.providers.keys()),
      availableModels: Array.from(this.modelProviderMap.keys()),
    });
  }

  async generateStream(request: UniversalRequest): Promise<ReadableStream> {
    const provider = this.getProviderForModel(request.model);

    logger.info("Routing request to provider", {
      model: request.model,
      provider: provider.name,
      messageCount: request.messages.length,
      hasTools: !!request.tools?.length,
      maxTokens: request.maxTokens,
    });

    try {
      return await provider.generateStream(request);
    } catch (error) {
      logger.error("Provider failed to generate stream", {
        provider: provider.name,
        model: request.model,
        error: error.message,
        hasTools: !!request.tools?.length,
      });

      // If this is a Google model and the provider failed, we could fall back to direct Google AI
      // But for now, re-throw the error to be handled by the caller
      throw error;
    }
  }

  getProviderForModel(modelId: string): AIProvider {
    const provider = this.modelProviderMap.get(modelId);
    if (!provider) {
      const availableModels = Array.from(this.modelProviderMap.keys());
      throw new Error(
        `Model "${modelId}" is not supported. Available models: ${availableModels.join(
          ", "
        )}`
      );
    }
    return provider;
  }

  getAvailableProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }

  getAvailableModels(): string[] {
    return Array.from(this.modelProviderMap.keys());
  }

  getModelsByProvider(providerType: ProviderType): string[] {
    const provider = this.providers.get(providerType);
    return provider ? provider.supportedModels : [];
  }

  async getProviderHealth(): Promise<Record<ProviderType, boolean>> {
    const healthStatus: Record<string, boolean> = {};

    for (const [providerName, provider] of this.providers) {
      try {
        healthStatus[providerName] = await provider.isHealthy();
      } catch (error) {
        logger.error(`Health check failed for ${providerName}`, {
          error: error.message,
          provider: providerName,
        });
        healthStatus[providerName] = false;
      }
    }

    return healthStatus as Record<ProviderType, boolean>;
  }

  getSystemStatus() {
    return {
      providersInitialized: this.providers.size,
      totalModelsSupported: this.modelProviderMap.size,
      availableProviders: Array.from(this.providers.keys()),
      providerModelCounts: Array.from(this.providers.entries()).map(
        ([name, provider]) => ({
          provider: name,
          modelCount: provider.supportedModels.length,
          models: provider.supportedModels,
        })
      ),
    };
  }

  calculateCost(modelId: SupportedModel, usage: TokenUsage): number {
    const provider = this.getProviderForModel(modelId);
    return provider.calculateCost(modelId, usage);
  }
}
