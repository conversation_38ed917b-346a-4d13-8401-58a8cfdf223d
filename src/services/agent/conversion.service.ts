import ConversationModel from "@/schemas/agent/Conversation.model";
import ChatMessageModel from "@/schemas/agent/ChatMessage.model";
import { createNewResource } from "@/schemas/resource/utils";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { createLogger } from "@/services/logger";
import { agentTrackingService } from "./tracking.service";
import { gcsUploadContent, generateSignedUrlForRead } from "@/services/storage";
import { initiateRagSync } from "@/handlers/resources/createResourceHandler";
import { gcsResourceFolder } from "@/config";
import { format } from "date-fns";
import { conversationImageService } from "./image/ConversationImageService";
import { MarkdownConverterFactory, ConversionFormat } from "../conversion";
import * as fs from "fs-extra";
import path from "path";

const logger = createLogger("ConversationConversionService");

export type ConversationExportFormat = "markdown" | "docx" | "txt";

/**
 * Service responsible for converting conversations into resources
 */
export class ConversationConversionService {
  /**
   * Convert a conversation to a resource in the system
   */
  async convertConversationToResource(
    conversationId: string,
    userId: string,
    exportFormat: ConversationExportFormat = "markdown"
  ): Promise<any> {
    try {
      // Validate conversation exists and belongs to user
      const conversation = await ConversationModel.findOne({
        where: {
          id: conversationId,
          createdById: userId,
        },
      });

      if (!conversation) {
        throw new Error("Conversation not found or access denied");
      }

      const conversationData = conversation.get({ plain: true });

      // Get all messages from the conversation
      const messages = await ChatMessageModel.findAll({
        where: {
          conversationId,
        },
        order: [["createdAt", "ASC"]],
      });

      if (messages.length === 0) {
        throw new Error("No messages found in conversation");
      }

      // Get all images from the conversation with signed URLs
      const conversationImages =
        await conversationImageService.getConversationImagesWithUrls(
          conversationId,
          userId
        );

      const title = `Conversation at ${format(new Date(), "dd MMM yyyy")}`;

      // Generate content based on format
      const { content, fileName, contentType } =
        await this.generateConversationContent(
          messages,
          conversationImages,
          title,
          exportFormat
        );

      const gcsFilePath = `${gcsResourceFolder}/${fileName}`;

      // Upload the content to GCS first
      try {
        await gcsUploadContent(content, gcsFilePath, {
          contentType,
          metadata: {
            cacheControl: "public, max-age=3600",
            contentDisposition: `attachment; filename="${fileName}"`,
          },
        });

        logger.info("Successfully uploaded conversation content to GCS", {
          gcsFilePath,
          fileSize: content.length,
          conversationId,
          format: exportFormat,
        });
      } catch (uploadError) {
        logger.error("Failed to upload conversation markdown to GCS", {
          error:
            uploadError instanceof Error
              ? uploadError.message
              : String(uploadError),
          errorStack:
            uploadError instanceof Error ? uploadError.stack : undefined,
          gcsFilePath,
          conversationId,
        });
        throw new Error("Failed to upload conversation content to storage");
      }

      // Now create resource record with the uploaded file
      const { resource } = await createNewResource({
        gcsFilePath,
        fileName,
        fileSize: content.length,
        userId,
        title,
        uploadAction: IEUploadAction.UPLOAD,
        projectId: conversationData.projectId,
      });

      logger.debug("Processing file name", { fileName });

      // Initiate RAG sync
      await initiateRagSync(
        resource,
        fileName,
        gcsFilePath,
        content.length,
        userId,
        conversationData.projectId
      );

      logger.info("Conversation converted to resource successfully", {
        conversationId,
        resourceId: resource.id,
        userId,
        messageCount: messages.length,
        resourceTitle: title,
      });

      // Track conversion analytics
      agentTrackingService.trackError(
        userId,
        "general",
        "Conversation converted to resource",
        {
          conversationId,
          resourceId: resource.id,
          messageCount: messages.length,
          imageCount: conversationImages.length,
          exportFormat,
          success: true,
          resourceTitle: title,
        }
      );

      return resource;
    } catch (error) {
      logger.error("Failed to convert conversation to resource", {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        conversationId,
        userId,
      });

      agentTrackingService.trackError(
        userId,
        "general",
        error instanceof Error
          ? error.message
          : "Unknown error in convertConversationToResource",
        { conversationId }
      );

      throw error;
    }
  }

  /**
   * Generate conversation content in the specified format
   */
  private async generateConversationContent(
    messages: any[],
    conversationImages: any[],
    title: string,
    exportFormat: ConversationExportFormat
  ): Promise<{ content: Buffer; fileName: string; contentType: string }> {
    // First generate markdown (base format)
    const markdownContent = await this.formatConversationAsMarkdown(
      messages,
      conversationImages,
      title
    );

    switch (exportFormat) {
      case "markdown":
        return {
          content: Buffer.from(markdownContent, "utf8"),
          fileName: `${title}.md`,
          contentType: "text/markdown; charset=utf-8",
        };

      case "docx":
        return await this.convertToDocx(markdownContent, title);

      case "txt":
        return await this.convertToTxt(
          markdownContent,
          title,
          messages,
          conversationImages
        );

      default:
        throw new Error(`Unsupported export format: ${exportFormat}`);
    }
  }

  /**
   * Convert markdown content to DOCX format
   */
  private async convertToDocx(
    markdownContent: string,
    title: string
  ): Promise<{ content: Buffer; fileName: string; contentType: string }> {
    const tempDir = "/tmp/conversation-conversions";
    const tempFilePath = path.join(tempDir, `${title}.docx`);

    try {
      // Ensure temp directory exists
      await fs.ensureDir(tempDir);

      // Convert using the existing converter
      const converter = MarkdownConverterFactory.getConverter(
        ConversionFormat.DOCX
      );
      await converter.convert(markdownContent, tempFilePath);

      // Read the converted file
      const content = await fs.readFile(tempFilePath);

      // Clean up temp file
      await fs.remove(tempFilePath);

      return {
        content,
        fileName: `${title}.docx`,
        contentType:
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      };
    } catch (error) {
      // Clean up on error
      if (await fs.pathExists(tempFilePath)) {
        await fs.remove(tempFilePath);
      }
      throw new Error(`Failed to convert to DOCX: ${error.message}`);
    }
  }

  /**
   * Convert markdown content to TXT format
   */
  private async convertToTxt(
    markdownContent: string,
    title: string,
    messages?: any[],
    conversationImages?: any[]
  ): Promise<{ content: Buffer; fileName: string; contentType: string }> {
    try {
      // If we have the original messages and images, use specialized text formatting
      let textContent: string;
      if (messages && conversationImages) {
        textContent = await this.formatConversationAsText(
          messages,
          conversationImages,
          title
        );
      } else {
        // Fallback: convert from markdown using the existing converter
        const tempDir = "/tmp/conversation-conversions";
        const tempFilePath = path.join(tempDir, `${title}.txt`);

        // Ensure temp directory exists
        await fs.ensureDir(tempDir);

        // Convert using the existing converter
        const converter = MarkdownConverterFactory.getConverter(
          ConversionFormat.TXT
        );
        await converter.convert(markdownContent, tempFilePath);

        // Read the converted file
        const content = await fs.readFile(tempFilePath, "utf8");
        textContent = content;

        // Clean up temp file
        await fs.remove(tempFilePath);
      }

      return {
        content: Buffer.from(textContent, "utf8"),
        fileName: `${title}.txt`,
        contentType: "text/plain; charset=utf-8",
      };
    } catch (error) {
      throw new Error(`Failed to convert to TXT: ${error.message}`);
    }
  }

  /**
   * Format conversation messages as markdown
   */
  private async formatConversationAsMarkdown(
    messages: any[],
    conversationImages: any[],
    title: string
  ): Promise<string> {
    let markdown = `# ${title}\n\n`;
    markdown += `---\n\n`;

    // Format each message with just role and content
    for (const message of messages) {
      const messageData = message.get ? message.get({ plain: true }) : message;
      const role = messageData.role === "user" ? "**User**" : "**Assistant**";

      markdown += `${role}:\n\n`;
      markdown += `${messageData.content}\n\n`;

      // Add images if available for this message
      const messageImages = conversationImages.filter(
        (img) => img.messageId === messageData.id
      );

      if (messageImages.length > 0) {
        markdown += await this.formatImagesForMarkdown(messageImages);
      }

      markdown += `---\n\n`;
    }

    return markdown;
  }

  /**
   * Format images for markdown export
   */
  private async formatImagesForMarkdown(images: any[]): Promise<string> {
    if (images.length === 0) return "";

    let imageMarkdown = `**Images:**\n\n`;

    // Create a container div for horizontal layout
    imageMarkdown += `<div style="display: flex; flex-wrap: wrap; gap: 15px; margin: 15px 0;">\n`;

    for (const image of images) {
      const altText =
        image.originalFileName || `Image ${image.width}x${image.height}`;

      // Calculate display dimensions while maintaining aspect ratio
      const maxDisplayWidth = 250; // Max width for display in markdown
      const maxDisplayHeight = 200; // Max height for display in markdown

      let displayWidth = image.width;
      let displayHeight = image.height;

      // Scale down if image is too large
      if (displayWidth > maxDisplayWidth || displayHeight > maxDisplayHeight) {
        const widthRatio = maxDisplayWidth / displayWidth;
        const heightRatio = maxDisplayHeight / displayHeight;
        const scaleRatio = Math.min(widthRatio, heightRatio);

        displayWidth = Math.round(displayWidth * scaleRatio);
        displayHeight = Math.round(displayHeight * scaleRatio);
      }

      // Create image container with caption
      imageMarkdown += `  <div style="flex: 0 0 auto; text-align: center; max-width: 250px;">\n`;
      imageMarkdown += `    <img src="${image.signedUrl}" alt="${altText}" width="${displayWidth}" height="${displayHeight}" style="max-width: 250px; max-height: 200px; height: auto; border: 1px solid #ddd; border-radius: 4px;" />\n`;
      imageMarkdown += `    <div style="font-size: 12px; color: #666; margin-top: 5px; font-style: italic;">\n`;
      imageMarkdown += `      ${image.originalFileName}<br/>\n`;
      imageMarkdown += `      (${image.width}x${image.height}, ${Math.round(
        image.fileSize / 1024
      )}KB)\n`;
      imageMarkdown += `    </div>\n`;
      imageMarkdown += `  </div>\n`;
    }

    imageMarkdown += `</div>\n\n`;
    return imageMarkdown;
  }

  /**
   * Format images for plain text export
   */
  private formatImagesForText(images: any[]): string {
    if (images.length === 0) return "";

    let imageText = "Images:\n";

    for (const image of images) {
      imageText += `- ${image.originalFileName} (${image.width}x${
        image.height
      }, ${Math.round(image.fileSize / 1024)}KB)\n`;
      imageText += `  URL: ${image.signedUrl}\n`;
    }

    return imageText + "\n";
  }

  /**
   * Create a text-only version of the conversation for TXT export
   */
  private async formatConversationAsText(
    messages: any[],
    conversationImages: any[],
    title: string
  ): Promise<string> {
    let textContent = `${title}\n`;
    textContent += "=".repeat(title.length) + "\n\n";

    // Format each message
    for (const message of messages) {
      const messageData = message.get ? message.get({ plain: true }) : message;
      const role = messageData.role === "user" ? "User" : "Assistant";

      textContent += `${role}:\n`;
      textContent += `${messageData.content}\n\n`;

      // Add images if available for this message
      const messageImages = conversationImages.filter(
        (img) => img.messageId === messageData.id
      );

      if (messageImages.length > 0) {
        textContent += this.formatImagesForText(messageImages);
      }

      textContent += "-".repeat(50) + "\n\n";
    }

    return textContent;
  }
}

// Export singleton instance
export const conversationConversionService =
  new ConversationConversionService();
