import { DEFAULT_MODEL, getModel, isModelSupported } from "@/config/models";
import { getDefaultSystemInstructions } from "../instructions";
import ChatMessageModel from "@/schemas/agent/ChatMessage.model";
import type { IChatMessage, TokenUsage } from "@/validators/agent.validators";
import { createLogger } from "@/services/logger";
import type { Content, Part, FunctionCall } from "@google/genai";
import { SupportedModel } from "@/types/universal-ai";

const logger = createLogger("AgentUtilsService");

/**
 * AgentUtilsService - Pure utility functions for the Agent system
 *
 * Contains stateless utility methods that can be easily tested and reused.
 * No database operations or external service calls (except conversation history).
 */
export class AgentUtilsService {
  /**
   * Image configuration for conversation images
   *
   * This configuration defines limits, supported formats, and settings
   * for images embedded in agent conversations.
   */
  static readonly imageConfig = {
    // Size limits
    maxImageSize: 20 * 1024 * 1024, // 20MB per image (Gemini API limit)
    recommendedImageSize: 5 * 1024 * 1024, // 5MB recommended for performance
    maxImagesPerMessage: 16, // Gemini API limit
    recommendedImagesPerMessage: 5, // Recommended for better UX

    // Dimension limits
    maxImageDimensions: 3072, // 3072x3072 pixels (Gemini API limit)
    recommendedImageDimensions: 1024, // 1024x1024 for cost efficiency

    // Rate limiting
    maxImagesPerHour: 50, // Per user
    maxImagesPerConversation: 200, // Total per conversation
    maxImagesPerProject: 1000, // Per day

    // Supported formats (Gemini API compatible)
    supportedMimeTypes: [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/bmp",
      "image/tiff",
    ] as const,

    // File extensions
    supportedExtensions: [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".webp",
      ".bmp",
      ".tiff",
      ".tif",
    ] as const,

    // Thumbnail configuration
    thumbnailFormat: "image/webp" as const,
    thumbnailMaxWidth: 300,
    thumbnailMaxHeight: 300,
    thumbnailQuality: 80,
  } as const;

  /**
   * Get the maximum output tokens for a model and request
   */
  getMaxOutputTokens(model?: string, message?: string): number {
    const modelName = model || DEFAULT_MODEL;

    // Validate and get model info
    if (!isModelSupported(modelName)) {
      logger.warn("Invalid model specified, using default", {
        requestedModel: modelName,
        defaultModel: DEFAULT_MODEL,
      });
      const defaultInfo = getModel(DEFAULT_MODEL);
      // Use a reasonable default for max output tokens
      const maxOutputTokens = 8192; // Standard limit for most models
      const calculatedTokens = this.calculateTokensForRequest(
        maxOutputTokens,
        message
      );

      logger.info("Token allocation (default model)", {
        model: DEFAULT_MODEL,
        modelMaxTokens: maxOutputTokens,
        allocatedTokens: calculatedTokens,
        messagePreview: message?.substring(0, 100),
      });

      return calculatedTokens;
    }

    const modelInfo = getModel(modelName);
    // Use a reasonable default for max output tokens since we don't store this in the new model config
    const maxOutputTokens = 8192; // Standard limit for most models
    const calculatedTokens = this.calculateTokensForRequest(
      maxOutputTokens,
      message
    );

    logger.info("Token allocation", {
      model: modelName,
      modelMaxTokens: maxOutputTokens,
      allocatedTokens: calculatedTokens,
      messagePreview: message?.substring(0, 100),
    });

    return calculatedTokens;
  }

  /**
   * Calculate appropriate token limit based on model capacity and request complexity
   * @param modelMaxTokens - Maximum tokens supported by the model
   * @param message - The user message to analyze
   * @returns Appropriate token limit for the request
   */
  calculateTokensForRequest(modelMaxTokens: number, message?: string): number {
    // Check if message suggests need for longer response (table, detailed analysis, etc.)
    const needsLongerResponse =
      message &&
      (message.toLowerCase().includes("table") ||
        message.toLowerCase().includes("detailed") ||
        message.toLowerCase().includes("comprehensive") ||
        message.toLowerCase().includes("list all") ||
        message.toLowerCase().includes("compare") ||
        message.toLowerCase().includes("analyze") ||
        message.toLowerCase().includes("step by step") ||
        message.toLowerCase().includes("breakdown"));

    if (needsLongerResponse) {
      // Use full model capacity for complex requests
      return modelMaxTokens;
    }

    // For regular requests, use a more generous portion of model capacity
    // Most responses need more than 25% to be complete
    return Math.min(modelMaxTokens, Math.floor(modelMaxTokens * 0.75));
  }

  /**
   * Build system instructions from request
   */
  buildSystemInstructions(
    model: SupportedModel,
    userInstructions?: string
  ): Part[] {
    let systemInstructions = getDefaultSystemInstructions(
      model,
      userInstructions
    );

    return systemInstructions;
  }

  /**
   * Format history for GenAI
   */
  formatHistoryForGenAI(
    history: IChatMessage[],
    currentMessage: string,
    resourceContext?: string,
    inlineDataParts?: Part[]
  ): Content[] {
    const contents = this.buildContentsFromHistory(history);

    // Build current user message parts
    const userParts: Part[] = [];

    // Add resource context text if available
    if (resourceContext) {
      userParts.push({ text: resourceContext });
    }

    // Add current message
    userParts.push({ text: currentMessage });

    // Add inline data parts for direct AI analysis (PDFs, images, etc.)
    if (inlineDataParts && inlineDataParts.length > 0) {
      userParts.push(...inlineDataParts);
    }

    contents.push({
      role: "user",
      parts: userParts,
    });

    return contents;
  }

  /**
   * Build base contents array from conversation history
   */
  buildContentsFromHistory(history: IChatMessage[]): Content[] {
    const contents: Content[] = [];

    // Add conversation history
    for (const msg of history) {
      contents.push({
        role: msg.role === "user" ? "user" : "model",
        parts: [{ text: msg.content }],
      });
    }

    return contents;
  }

  /**
   * Build contents array for function continuation
   */
  buildFunctionContinuationContents(
    history: IChatMessage[],
    functionCall: FunctionCall,
    functionResult: Record<string, unknown>
  ): Content[] {
    const contents = this.buildContentsFromHistory(history);

    // Add the function call and response
    contents.push({
      role: "model",
      parts: [{ functionCall: functionCall }],
    });

    contents.push({
      role: "user",
      parts: [
        {
          functionResponse: {
            name: functionCall.name,
            response: functionResult,
          },
        },
      ],
    });

    return contents;
  }

  /**
   * Calculate token usage using actual API response metadata
   */
  calculateUsage(
    actualUsageMetadata?: {
      promptTokenCount?: number;
      candidatesTokenCount?: number;
      totalTokenCount?: number;
    },
    fallbackInput?: string,
    fallbackOutput?: string,
    imageCount?: number
  ): TokenUsage {
    // Use actual token counts from API response
    if (actualUsageMetadata) {
      logger.info("Using actual token counts from API response", {
        promptTokens: actualUsageMetadata.promptTokenCount || 0,
        candidatesTokens: actualUsageMetadata.candidatesTokenCount || 0,
        totalTokens: actualUsageMetadata.totalTokenCount || 0,
        imageCount: imageCount || 0,
        accuracy: "high",
      });

      return {
        inputTokens: actualUsageMetadata.promptTokenCount || 0,
        outputTokens: actualUsageMetadata.candidatesTokenCount || 0,
        totalTokens: actualUsageMetadata.totalTokenCount || 0,
        imageTokens: imageCount ? this.calculateImageTokens(imageCount) : 0,
      };
    }

    // Fallback: Estimate tokens using character count and image count
    if (fallbackInput || fallbackOutput || imageCount) {
      // Rough estimation: 1 token ≈ 4 characters for English text
      const inputTokens = fallbackInput
        ? Math.ceil(fallbackInput.length / 4)
        : 0;
      const outputTokens = fallbackOutput
        ? Math.ceil(fallbackOutput.length / 4)
        : 0;
      const imageTokens = imageCount
        ? this.calculateImageTokens(imageCount)
        : 0;
      const totalTokens = inputTokens + outputTokens + imageTokens;

      logger.warn("Using estimated token counts (fallback)", {
        accuracy: "estimated",
        inputLength: Math.min(fallbackInput?.length || 0, 10000), // Cap at 10k chars for logging
        outputLength: Math.min(fallbackOutput?.length || 0, 10000), // Cap at 10k chars for logging
        imageCount: imageCount || 0,
        estimatedInputTokens: inputTokens,
        estimatedOutputTokens: outputTokens,
        estimatedImageTokens: imageTokens,
        estimatedTotalTokens: totalTokens,
      });

      return {
        inputTokens,
        outputTokens,
        totalTokens,
        imageTokens,
        inputCharacters: fallbackInput?.length || 0,
        outputCharacters: fallbackOutput?.length || 0,
      };
    }

    // Last resort: return zero counts
    logger.warn("Usage metadata unavailable - returning zero counts", {
      accuracy: "unavailable",
    });

    return {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
      imageTokens: 0,
    };
  }

  /**
   * Calculate token usage for images based on Gemini API pricing
   *
   * According to Gemini API documentation:
   * - Images are charged based on their dimensions
   * - Base cost: 258 tokens per image
   * - Additional cost for high-resolution images
   */
  calculateImageTokens(
    imageCount: number,
    imageDimensions?: Array<{ width: number; height: number }>
  ): number {
    if (imageCount === 0) return 0;

    // Base token cost per image (Gemini API standard)
    const baseTokensPerImage = 258;

    if (!imageDimensions || imageDimensions.length === 0) {
      // Use base cost if no dimension info available
      return imageCount * baseTokensPerImage;
    }

    let totalTokens = 0;

    for (let i = 0; i < Math.min(imageCount, imageDimensions.length); i++) {
      const { width, height } = imageDimensions[i];
      totalTokens += this.calculateSingleImageTokens(width, height);
    }

    // For any remaining images without dimension info, use base cost
    const remainingImages = Math.max(0, imageCount - imageDimensions.length);
    totalTokens += remainingImages * baseTokensPerImage;

    return totalTokens;
  }

  /**
   * Calculate tokens for a single image based on its dimensions
   */
  private calculateSingleImageTokens(width: number, height: number): number {
    const baseTokens = 258;

    // Calculate total pixels
    const totalPixels = width * height;

    // Gemini API pricing tiers (approximate)
    if (totalPixels <= 512 * 512) {
      return baseTokens; // Small images
    } else if (totalPixels <= 1024 * 1024) {
      return Math.round(baseTokens * 1.5); // Medium images
    } else if (totalPixels <= 2048 * 2048) {
      return Math.round(baseTokens * 2.0); // Large images
    } else {
      return Math.round(baseTokens * 3.0); // Very large images
    }
  }

  /**
   * Get image count and dimensions from inlineDataParts for token calculation
   */
  extractImageInfoFromInlineDataParts(inlineDataParts: Part[]): {
    imageCount: number;
    imageDimensions: Array<{ width: number; height: number }>;
  } {
    const imageInfo = {
      imageCount: 0,
      imageDimensions: [] as Array<{ width: number; height: number }>,
    };

    for (const part of inlineDataParts) {
      if (part.inlineData && part.inlineData.mimeType?.startsWith("image/")) {
        imageInfo.imageCount++;
        // Note: We don't have dimension info from inlineDataParts directly
        // This would need to be passed separately or extracted from image metadata
      }
    }

    return imageInfo;
  }

  /**
   * Get conversation history from database
   */
  async getConversationHistory(
    conversationId: string
  ): Promise<IChatMessage[]> {
    const messages = await ChatMessageModel.findAll({
      where: { conversationId },
      order: [["createdAt", "ASC"]],
    });

    // Get plain objects before mapping
    return messages.map((msg) => {
      const plainMsg = msg.get({ plain: true });
      return {
        role: plainMsg.role as "user" | "assistant",
        content: plainMsg.content,
        model: plainMsg.model,
        usage: plainMsg.usage || undefined,
        cost: plainMsg.cost || undefined,
        resources: Array.isArray(plainMsg.resources)
          ? plainMsg.resources
          : undefined,
        hasImages: plainMsg.hasImages || false,
        imageCount: plainMsg.imageCount || 0,
      };
    });
  }
}

// Export singleton instance
export const agentUtilsService = new AgentUtilsService();
