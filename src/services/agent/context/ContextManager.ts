/**
 * Context Manager - Orchestrates all context building for the AI agent
 *
 * Clean separation of concerns:
 * - Traditional context building (immediate files)
 * - Dynamic tool management (searchable files)
 * - Context formatting and presentation
 */

import {
  initializeToolRegistry,
  type ToolRegistry,
} from "../tools/ToolRegistry";
import { createAllTools } from "../tools/providers";
import { TraditionalContextBuilder } from "./TraditionalContextBuilder";
import { createLogger } from "@/services/logger";
import type { Resource } from "@/schemas/resource/Resource.model";
import { RagSyncStatus } from "@/schemas/resource/Resource.model";
import type { Tool, Part } from "@google/genai";
import { RequestContext, UserPreferences, ProgressReporter } from "@/types/agent";
import { ProcessedResource } from "./TraditionalContextBuilder";
import { createToolExecutionContext } from "../tools/ToolExecutionContext";
import { initializeMCPClientManager } from "../tools";
import type { ToolEnablementContext } from "../tools/BaseTool";
import { getAllFeatureFlags } from "@/services/featureFlags";

const logger = createLogger("ContextManager");

export interface ContextResult {
  traditionalContext: string;
  availableTools: Tool[];
  inlineDataParts: Part[];
  contextSummary: {
    immediateFiles: number;
    searchableFiles: number;
    toolsEnabled: boolean;
    customInstructions: boolean;
  };
  processedResources: ProcessedResource[];
  searchableFileIds: string[];
}

export class ContextManager {
  private traditionalContextBuilder: TraditionalContextBuilder;
  private toolRegistry: ToolRegistry | null = null;

  constructor() {
    this.traditionalContextBuilder = new TraditionalContextBuilder();
  }

  /**
   * Initialize tool system - simple and configuration-driven
   */
  async initialize(): Promise<void> {
    // Initialize MCP client manager first
    await initializeMCPClientManager();

    // Create all tools from configuration and initialize registry
    const tools = await createAllTools();
    this.toolRegistry = await initializeToolRegistry(tools);

    const healthStatus = await this.toolRegistry.getHealthStatus();

    logger.info("Context manager initialized", {
      tools: tools.length,
      healthy: Object.entries(healthStatus).filter(([, healthy]) => healthy)
        .length,
    });
  }

  /**
   * Build complete context for the AI agent
   */
  async buildContext(
    resourceIds: string[],
    userId: string,
    projectId?: string,
    requestContext?: RequestContext,
    progressReporter?: ProgressReporter
  ): Promise<ContextResult> {
    const startTime = Date.now();

    // Get all resources with their sync status
    const resources = await this.traditionalContextBuilder.fetchResources(
      resourceIds,
      userId,
      projectId
    );

    // Separate resources by their availability method
    const resourcesByMethod = this.categorizeResources(
      resources,
      requestContext
    );

    // Get searchable file IDs for tool calls and validation
    const searchableFileIds = resourcesByMethod.searchable.map((r) => r.id);

    // Build traditional context for immediately available files (quick operation)

    const contextData =
      await this.traditionalContextBuilder.buildContextWithData(
        resourcesByMethod.immediate
      );

    // Extract inline data parts for Google GenAI
    const inlineDataParts = this.traditionalContextBuilder.getInlineDataParts(
      contextData.processedResources
    );

    // Show search progress for searchable files, not reasoning
    // (The reasoning step will be shown later in the agent service)

    // Get available tools based on preferences (RUNTIME FILTERING)
    const availableTools = await this.getAvailableToolsForRequest(
      requestContext,
      userId,
      searchableFileIds
    );

    const processingTime = Date.now() - startTime;

    // Enhance traditional context with preferences
    const enhancedTraditionalContext = this.enhanceContextWithPreferences(
      contextData.context,
      resourcesByMethod.searchable,
      requestContext?.preferences
    );

    const result: ContextResult = {
      traditionalContext: enhancedTraditionalContext,
      availableTools,
      inlineDataParts,
      contextSummary: {
        immediateFiles: resourcesByMethod.immediate.length,
        searchableFiles: resourcesByMethod.searchable.length,
        toolsEnabled: availableTools.length > 0,
        customInstructions: !!requestContext?.preferences?.instructions,
      },
      processedResources: contextData.processedResources,
      searchableFileIds: searchableFileIds,
    };

    logger.info("Context building completed", {
      immediateFiles: result.contextSummary.immediateFiles,
      searchableFiles: result.contextSummary.searchableFiles,
      toolsEnabled: result.contextSummary.toolsEnabled,
      processingTime,
      requestId: requestContext?.requestId,
    });

    return result;
  }

  /**
   * Handle tool calls from the AI agent with request-scoped security
   */
  async handleToolCall(
    toolName: string,
    args: any,
    userId: string,
    searchableFileIds: string[],
    projectId?: string,
    requestId?: string
  ): Promise<any> {
    // Always use the passed searchable file IDs for proper security filtering (request-scoped)
    const enhancedArgs = {
      ...args,
      userId,
      projectId,
      // Always use the correct resource IDs from current request context
      fileIds: searchableFileIds,
    };

    // Create secure, request-scoped execution context
    const executionContext = createToolExecutionContext(
      requestId || `req_${Date.now()}`,
      userId,
      searchableFileIds,
      {
        projectId,
        timeoutMs: 30000, // 30 second timeout
      }
    );
    if (!this.toolRegistry) {
      throw new Error(
        "Tool registry not initialized. Call initialize() first."
      );
    }
    return await this.toolRegistry.executeToolCall(
      toolName,
      enhancedArgs,
      executionContext
    );
  }

  /**
   * Categorize resources by their access method
   * Since upload restrictions ensure all files can be processed, we only have two categories:
   * - SYNCED files: Use MCP tools for vector search (when RAG is enabled)
   * - Non-synced files: Use traditional context processing
   * - When ragSearchEnabled is false: ALL files become immediate files
   */
  private categorizeResources(
    resources: Resource[],
    requestContext?: RequestContext
  ) {
    const immediate: Resource[] = [];
    const searchable: Resource[] = [];

    // When RAG search is disabled, treat all files as immediate
    if (!requestContext?.preferences?.ragSearchEnabled) {
      return { immediate: resources, searchable: [] };
    }

    for (const resource of resources) {
      if (resource.ragSyncStatus === RagSyncStatus.SYNCED) {
        searchable.push(resource);
      } else {
        // All non-synced files go to immediate processing
        // Upload restrictions ensure they can be processed
        immediate.push(resource);
      }
    }

    return { immediate, searchable };
  }

  /**
   * Get available tools based on request context and preferences (RUNTIME FILTERING)
   */
  private async getAvailableToolsForRequest(
    requestContext?: RequestContext,
    userId?: string,
    searchableFileIds?: string[]
  ): Promise<Tool[]> {
    if (!this.toolRegistry) {
      throw new Error(
        "Tool registry not initialized. Call initialize() first."
      );
    }

    // Get all registered tools (these were pre-filtered at creation time)
    const allTools = this.toolRegistry.getAllRegisteredTools();

    const featureFlags = await getAllFeatureFlags(userId);

    // Create context for runtime tool evaluation
    const toolContext: ToolEnablementContext = {
      userId: userId || "unknown",
      projectId: requestContext?.projectId,
      preferences: requestContext?.preferences || {},
      featureFlags,
      searchableFileIds: searchableFileIds || [],
    };

    // Re-evaluate each tool's enablement with current context
    const contextEnabledTools = allTools.filter((tool) =>
      tool.isEnabled(toolContext)
    );

    // Apply health filtering and convert to Tool definitions
    const healthyTools = this.toolRegistry.getHealthyTools(contextEnabledTools);

    logger.info("Tool filtering completed", {
      available: healthyTools.length,
      total: allTools.length,
      contextFiltered: contextEnabledTools.length,
      userId,
    });

    return healthyTools;
  }

  /**
   * Enhance traditional context with preferences
   */
  private enhanceContextWithPreferences(
    traditionalContext: string,
    searchableFiles: Resource[],
    preferences?: UserPreferences
  ): string {
    let enhancedContext = traditionalContext;

    // Add custom instructions at the beginning if provided
    if (preferences?.instructions) {
      const customInstructionsSection = [
        "=== CUSTOM INSTRUCTIONS ===",
        preferences.instructions,
        "",
        "Please follow these custom instructions when analyzing the files and responding to the user.",
        "",
      ].join("\n");

      enhancedContext = `${customInstructionsSection}\n${enhancedContext}`;
    }

    // Add searchable files section if RAG is enabled
    if (preferences?.ragSearchEnabled && searchableFiles.length > 0) {
      const searchableFilesSection =
        this.buildSearchableFilesSection(searchableFiles);
      enhancedContext = `${enhancedContext}\n\n${searchableFilesSection}`;
    }

    return enhancedContext;
  }

  /**
   * Build searchable files section for context
   */
  private buildSearchableFilesSection(searchableFiles: Resource[]): string {
    const searchableFilesSection = [
      "=== SEARCHABLE FILES ===",
      `The user has selected ${searchableFiles.length} file${
        searchableFiles.length > 1 ? "s" : ""
      } for analysis. These files are available through semantic search:`,
      "",
    ];

    searchableFiles.forEach((file, index) => {
      searchableFilesSection.push(`${index + 1}. ${file.name}`);
      searchableFilesSection.push(`   File ID: ${file.id}`);
      if (file.fileSize) {
        const sizeInMB = (file.fileSize / (1024 * 1024)).toFixed(1);
        searchableFilesSection.push(`   Size: ${sizeInMB}MB`);
      }
      searchableFilesSection.push(`   Status: Available via semantic search`);
      searchableFilesSection.push("");
    });

    searchableFilesSection.push(
      "These are the files the user wants you to analyze. Use semantic search to find relevant content for comparison, analysis, or any questions about these files.",
      "",
      "IMPORTANT: When calling search tools, always use the File ID (not the filename) in the fileIds parameter.",
      ""
    );

    return searchableFilesSection.join("\n");
  }

  /**
   * Get status of all context providers
   */
  async getStatus() {
    if (!this.toolRegistry) {
      return {
        toolRegistry: {
          available: false,
          error: "Tool registry not initialized",
        },
      };
    }

    const allTools = this.toolRegistry.getAllRegisteredTools();
    const healthStatus = await this.toolRegistry.getHealthStatus();

    return {
      toolRegistry: {
        available: true,
        totalRegisteredTools: allTools.length,
        registeredToolNames: allTools.map((t) => t.name),
        healthStatus,
        healthyTools: Object.entries(healthStatus).filter(
          ([, healthy]) => healthy
        ).length,
        note: "Runtime filtering by user preferences and feature flags",
      },
      traditionalContextBuilder: {
        available: true,
        supportedFileTypes: this.traditionalContextBuilder.getSupportedTypes(),
      },
    };
  }
}
