/**
 * Traditional Context Builder
 *
 * Handles building context for files that can be processed immediately
 * (transcripts, supported file types, etc.)
 *
 * Clean separation from dynamic tool-based context retrieval.
 */

import { models } from "@/schemas";
import { getFileCategory, isVideoOrAudio } from "@/utils/file";
import { createLogger } from "@/services/logger";
import { isUserAccessibleResource } from "@/handlers/resources/resource.service";
import { getInsightEngineByResourceId } from "@/schemas/insightEngine/utils";
import { getFileStream } from "@/services/storage";
import { getType } from "mime";
import type {
  Resource,
  RagSyncStatus,
} from "@/schemas/resource/Resource.model";
import type { Part } from "@google/genai";

const logger = createLogger("TraditionalContextBuilder");

export interface ProcessedResource {
  resourceId: string;
  name: string;
  category: "audio" | "video" | "text" | "image" | "unknown";
  content?: string;
  inlineData?: Part;
  metadata: {
    fileSize?: number;
    duration?: number;
    speakers?: string[];
    transcriptionStatus?: string;
    ragSyncStatus: RagSyncStatus;
    processingMethod: string;
  };
}

/**
 * Builds traditional context for immediately available files
 */
export class TraditionalContextBuilder {
  /**
   * Fetch resources with user access validation
   */
  async fetchResources(
    resourceIds: string[],
    userId: string,
    projectId?: string
  ): Promise<Resource[]> {
    if (!resourceIds || resourceIds.length === 0) {
      return [];
    }

    const resources = await models.Resource.findAll({
      where: {
        id: resourceIds,
        ...(projectId && { projectId }),
      },
    });

    // Validate user access for each resource
    const accessibleResources: Resource[] = [];

    for (const resource of resources) {
      const resourceData = resource.toJSON();
      const accessResult = await isUserAccessibleResource(userId, resourceData);

      if (accessResult.success && accessResult.data.canView) {
        accessibleResources.push(resourceData);
      } else {
        logger.warn("User access denied for resource", {
          resourceId: resourceData.id,
          userId,
          reason: accessResult.message,
        });
      }
    }

    return accessibleResources;
  }

  /**
   * Build traditional context for a list of resources
   */
  async buildContext(resources: Resource[]): Promise<string> {
    const contextData = await this.buildContextWithData(resources);
    return contextData.context;
  }

  /**
   * Build traditional context and return both context and processed resources
   */
  async buildContextWithData(resources: Resource[]): Promise<{
    context: string;
    processedResources: ProcessedResource[];
  }> {
    if (resources.length === 0) {
      return { context: "", processedResources: [] };
    }

    logger.info("Building traditional context", {
      resourceCount: resources.length,
    });

    const processedResources = await Promise.allSettled(
      resources.map((resource) => this.processResource(resource))
    );

    const validResources = processedResources
      .filter(
        (result): result is PromiseFulfilledResult<ProcessedResource | null> =>
          result.status === "fulfilled" && result.value !== null
      )
      .map((result) => result.value!);

    const context = this.formatContext(validResources);
    
    return {
      context,
      processedResources: validResources
    };
  }

  /**
   * Get list of supported file types
   */
  getSupportedTypes(): string[] {
    return [
      "Audio/Video (with transcripts)",
      "Text files (.txt, .md)",
      "Images (.jpg, .png, .gif, .webp)",
      "PDF documents",
      "Office documents (.docx, .xlsx, .pptx)",
      "HTML files",
    ];
  }

  /**
   * Extract inline data parts from processed resources
   */
  getInlineDataParts(resources: ProcessedResource[]): Part[] {
    return resources
      .filter(resource => resource.inlineData)
      .map(resource => resource.inlineData!);
  }

  /**
   * Process a single resource for traditional context
   */
  private async processResource(
    resource: Resource
  ): Promise<ProcessedResource | null> {
    const fileCategory = getFileCategory(resource.name) || "unknown";

    const baseResource: ProcessedResource = {
      resourceId: resource.id,
      name: resource.name,
      category: fileCategory as any,
      metadata: {
        fileSize: resource.fileSize,
        duration: resource.duration,
        ragSyncStatus: resource.ragSyncStatus,
        processingMethod: "Traditional",
      },
    };

    try {
      // Handle different file types for non-synced files
      if (isVideoOrAudio(resource.name)) {
        // Audio/Video: Get transcriptions from DB and convert to inline data
        return await this.processAudioVideoResource(resource, baseResource);
      } else {
        // All other file types: Embed directly as inline data (PDFs, images, documents, etc.)
        return await this.processFileAsInlineData(resource, baseResource);
      }
    } catch (error) {
      logger.error("Failed to process resource", {
        resourceId: resource.id,
        error: error instanceof Error ? error.message : String(error),
      });

      // On error, try to process as inline data anyway
      return await this.processFileAsInlineData(resource, baseResource);
    }
  }

  /**
   * Process audio/video resources (transcript-based)
   */
  private async processAudioVideoResource(
    resource: Resource,
    baseResource: ProcessedResource
  ): Promise<ProcessedResource> {
    try {
      const { resourceInInsightEngine: riie } =
        await getInsightEngineByResourceId(resource.id);

      if (riie?.id) {
        // Fetch transcriptions from the Transcription model
        const transcriptions = await models.Transcription.xFindBy(
          "resourceInInsightEngineId",
          riie.id
        );

        if (transcriptions && transcriptions.length > 0) {
          // Format transcription with speaker and timestamp information
          const transcriptionSegments = transcriptions.map((t: any) => {
            const startTime = this.formatTimestamp(t.startTime);
            const endTime = this.formatTimestamp(t.endTime);
            const speaker = t.nameFromRevAi || "Unknown Speaker";
            return `${speaker}: ${startTime}-${endTime}: ${t.content}`;
          });

          // Extract speaker information
          const speakers = Array.from(
            new Set(
              transcriptions.map((t) => t.nameFromRevAi || "Unknown Speaker")
            )
          ) as string[];

          const transcriptionText = transcriptionSegments.join("\n");
          baseResource.content = `[${resource.name}]\nTranscription available for AI analysis.`;
          baseResource.metadata.transcriptionStatus = "available";
          baseResource.metadata.speakers = speakers;

          // Convert transcription to inline data for Google GenAI
          baseResource.inlineData = {
            inlineData: {
              mimeType: "text/plain",
              data: Buffer.from(transcriptionText).toString('base64')
            }
          };

          logger.info("Audio/video resource processed with transcript as inline data", {
            resourceId: resource.id,
            transcriptLength: transcriptionText.length,
            segmentCount: transcriptionSegments.length,
            speakerCount: speakers.length,
          });
        } else {
          baseResource.content = `[Audio/Video file: ${resource.name}]\nTranscript not available.`;
          baseResource.metadata.transcriptionStatus =
            riie.status || "not_available";
        }
      } else {
        baseResource.content = `[Audio/Video file: ${resource.name}]\nTranscript not available.`;
        baseResource.metadata.transcriptionStatus = "not_available";
      }
    } catch (error) {
      logger.warn("Failed to get transcript for audio/video resource", {
        resourceId: resource.id,
        error: error instanceof Error ? error.message : String(error),
      });

      baseResource.content = `[Audio/Video file: ${resource.name}]\nTranscript processing failed.`;
      baseResource.metadata.transcriptionStatus = "failed";
    }

    return baseResource;
  }

  /**
   * Format timestamp for transcription display
   */
  private formatTimestamp(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  }

  /**
   * Process file as inline data (for all non-audio/video files)
   */
  private async processFileAsInlineData(
    resource: Resource,
    baseResource: ProcessedResource
  ): Promise<ProcessedResource> {
    if (this.canProcessAsInlineData(resource.name, resource.fileSize)) {
      try {
        // Create inline data for Google GenAI to analyze directly
        const inlineData = await this.createInlineDataFromResource(resource);
        if (inlineData) {
          baseResource.inlineData = inlineData;
          baseResource.content = `[${resource.name}]\nFile content available for direct AI analysis.`;
          baseResource.metadata.processingMethod = "InlineData";
          
          logger.info("Resource prepared for inline data analysis", {
            resourceId: resource.id,
            fileName: resource.name,
            mimeType: inlineData.inlineData?.mimeType
          });
        } else {
          baseResource.content = `[${resource.name}]\nFailed to prepare file for analysis.`;
          baseResource.metadata.processingMethod = "Error";
        }
      } catch (error) {
        logger.warn("Failed to create inline data for resource", {
          resourceId: resource.id,
          error: error instanceof Error ? error.message : String(error)
        });
        baseResource.content = `[${resource.name}]\nFile analysis preparation failed.`;
        baseResource.metadata.processingMethod = "Error";
      }
    } else {
      baseResource.content = `[${resource.name}]\nFile type not supported for direct analysis or file size too large.`;
      baseResource.metadata.processingMethod = "Unsupported";
      
      logger.info("Resource cannot be processed as inline data", {
        resourceId: resource.id,
        fileName: resource.name,
        reason: "File type not supported or size too large"
      });
    }

    return baseResource;
  }


  /**
   * Create inline data from resource for Google GenAI
   */
  private async createInlineDataFromResource(resource: Resource): Promise<Part | null> {
    try {
      // Get file stream from storage
      const fileStream = await getFileStream(resource.url);
      if (!fileStream) {
        logger.warn("Could not get file stream for resource", {
          resourceId: resource.id,
          url: resource.url
        });
        return null;
      }

      // Convert stream to buffer
      const chunks: Uint8Array[] = [];
      const reader = fileStream.getReader();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }
      } finally {
        reader.releaseLock();
      }

      // Create single buffer from chunks
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const buffer = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of chunks) {
        buffer.set(chunk, offset);
        offset += chunk.length;
      }

      // Get MIME type and ensure it's supported by Google GenAI
      const mimeType = this.getSupportedMimeType(resource.name);

      // Convert to base64
      const base64Data = Buffer.from(buffer).toString('base64');

      // Create Google GenAI inline data part
      const inlineData: Part = {
        inlineData: {
          mimeType,
          data: base64Data
        }
      };

      logger.info("Created inline data for resource", {
        resourceId: resource.id,
        fileName: resource.name,
        mimeType,
        dataSize: base64Data.length
      });

      return inlineData;
    } catch (error) {
      logger.error("Failed to create inline data for resource", {
        resourceId: resource.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Get supported MIME type for Google GenAI inline data
   */
  private getSupportedMimeType(filename: string): string {
    const ext = filename.toLowerCase().substring(filename.lastIndexOf("."));
    
    // Map file extensions to Google GenAI supported MIME types
    const supportedMimeTypes: Record<string, string> = {
      // Images (directly supported)
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg", 
      ".png": "image/png",
      ".gif": "image/gif",
      ".webp": "image/webp",
      
      // Text files (directly supported)
      ".txt": "text/plain",
      ".md": "text/plain",
      ".html": "text/html",
      ".htm": "text/html",
      
      // PDF (directly supported)
      ".pdf": "application/pdf",
      
      // Office documents (NOT supported - will be handled separately)
      // ".docx": "text/plain",
      // ".xlsx": "text/plain", 
      // ".pptx": "text/plain",
    };

    return supportedMimeTypes[ext] || "text/plain";
  }

  /**
   * Check if file can be processed as inline data
   */
  private canProcessAsInlineData(filename: string, fileSize?: number): boolean {
    // Conservative file size limit (Google GenAI has limits)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (fileSize && fileSize > maxSize) return false;

    // Check if we have a supported MIME type mapping
    const ext = filename.toLowerCase().substring(filename.lastIndexOf("."));
    const supportedExtensions = [
      ".txt", ".md", ".html", ".htm",    // Text files
      ".jpg", ".jpeg", ".png", ".gif", ".webp",  // Images  
      ".pdf",                           // PDF
      // Office docs temporarily disabled until proper text extraction is implemented
      // ".docx", ".xlsx", ".pptx",       
    ];

    return supportedExtensions.includes(ext);
  }

  /**
   * Format processed resources into context string
   */
  private formatContext(resources: ProcessedResource[]): string {
    if (resources.length === 0) {
      return "";
    }

    const contextSections: string[] = [
      "=== CURRENT CONTEXT ===",
      `Files: ${resources.length}`,
      "",
    ];

    for (const resource of resources) {
      contextSections.push(`## ${resource.name}`);

      // Add metadata info
      const metadataLines: string[] = [];
      metadataLines.push(`Type: ${resource.category}`);

      if (resource.metadata.fileSize) {
        const sizeInMB = (resource.metadata.fileSize / (1024 * 1024)).toFixed(
          1
        );
        metadataLines.push(`Size: ${sizeInMB}MB`);
      }

      if (resource.metadata.duration) {
        const duration = Math.round(resource.metadata.duration);
        metadataLines.push(`Duration: ${duration}s`);
      }

      if (resource.metadata.transcriptionStatus) {
        metadataLines.push(
          `Transcript: ${resource.metadata.transcriptionStatus}`
        );
      }

      metadataLines.push(`Method: ${resource.metadata.processingMethod}`);

      contextSections.push(metadataLines.join(" | "));

      // Add content
      if (resource.content) {
        contextSections.push("");
        contextSections.push(resource.content);
      }

      contextSections.push("");
    }

    const formattedContext = contextSections.join("\n");

    logger.info("Traditional context formatted", {
      resourceCount: resources.length,
      contextLength: formattedContext.length,
    });

    return formattedContext;
  }
}
