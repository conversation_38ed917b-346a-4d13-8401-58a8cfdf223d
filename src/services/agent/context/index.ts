/**
 * Context Service Exports
 *
 * Clean, simple exports for the new modular context system
 */

export { ContextManager } from "./ContextManager";
export { TraditionalContextBuilder } from "./TraditionalContextBuilder";
export type { ContextResult } from "./ContextManager";
export type { ProcessedResource } from "./TraditionalContextBuilder";

// Singleton instance for easy use across the application
import { ContextManager } from "./ContextManager";

let contextManagerInstance: ContextManager | null = null;

/**
 * Get the singleton context manager instance
 */
export const getContextManager = (): ContextManager => {
  if (!contextManagerInstance) {
    contextManagerInstance = new ContextManager();
  }
  return contextManagerInstance;
};

/**
 * Initialize the context manager (call once at startup)
 */
export const initializeContextManager = async (): Promise<void> => {
  const manager = getContextManager();
  await manager.initialize();
};
