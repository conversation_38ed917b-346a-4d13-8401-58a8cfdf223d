import { log } from "@/services/logger";
import { documentProcessorPubsubService } from "@/services/pubsub";
import { AidaPubsubTopic } from "@/config";
import { DocumentProcessorMessage, DocumentProcessingType, DocumentUpdateType } from "@/types";

/**
 * Validation error for document processor messages
 */
export class DocumentProcessorValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = "DocumentProcessorValidationError";
  }
}

/**
 * Validates required fields for upload messages
 */
function validateUploadMessage(message: Omit<DocumentProcessorMessage, "type">): void {
  const errors: string[] = [];

  if (!message.file_id || typeof message.file_id !== "string" || message.file_id.trim() === "") {
    errors.push("file_id is required and must be a non-empty string");
  }

  if (!message.user_id || typeof message.user_id !== "string" || message.user_id.trim() === "") {
    errors.push("user_id is required and must be a non-empty string");
  }

  if (!message.presigned_url || typeof message.presigned_url !== "string" || message.presigned_url.trim() === "") {
    errors.push("presigned_url is required and must be a non-empty string");
  }

  if (!message.project_id || typeof message.project_id !== "string" || message.project_id.trim() === "") {
    errors.push("project_id is required and must be a non-empty string");
  }

  // Validate metadata if provided
  if (message.metadata) {
    if (!message.metadata.filename || typeof message.metadata.filename !== "string" || message.metadata.filename.trim() === "") {
      errors.push("metadata.filename is required and must be a non-empty string when metadata is provided");
    }

    // Validate audio_video metadata if provided
    if (message.metadata.audio_video) {
      // Accept speakers as either an array (of strings) or a non-negative number (count)
      if (message.metadata.audio_video.speakers !== undefined) {
        const speakers = message.metadata.audio_video.speakers;
        if (
          !(
            (typeof speakers === "number" && speakers >= 0) ||
            (Array.isArray(speakers) && speakers.length > 0 && speakers.every(s => typeof s === "string"))
          )
        ) {
          errors.push("metadata.audio_video.speakers must be a non-negative number or an array of strings when provided");
        }
      }

      if (message.metadata.audio_video.audio_duration !== undefined && 
          (typeof message.metadata.audio_video.audio_duration !== "number" || message.metadata.audio_video.audio_duration < 0)) {
        errors.push("metadata.audio_video.audio_duration must be a non-negative number when provided");
      }
    }

    // Validate tags array if provided
    if (message.metadata.tags !== undefined) {
      if (!Array.isArray(message.metadata.tags)) {
        errors.push("metadata.tags must be an array when provided");
      } else if (message.metadata.tags.some(tag => typeof tag !== "string")) {
        errors.push("all metadata.tags must be strings when provided");
      }
    }
  }

  if (errors.length > 0) {
    throw new DocumentProcessorValidationError(`Upload message validation failed: ${errors.join(", ")}`);
  }
}

/**
 * Validates required fields for delete messages
 */
function validateDeleteMessage(file_id: string, user_id: string, project_id: string): void {
  const errors: string[] = [];

  if (!file_id || typeof file_id !== "string" || file_id.trim() === "") {
    errors.push("file_id is required and must be a non-empty string");
  }

  if (!user_id || typeof user_id !== "string" || user_id.trim() === "") {
    errors.push("user_id is required and must be a non-empty string");
  }

  if (!project_id || typeof project_id !== "string" || project_id.trim() === "") {
    errors.push("project_id is required and must be a non-empty string");
  }

  if (errors.length > 0) {
    throw new DocumentProcessorValidationError(`Delete message validation failed: ${errors.join(", ")}`);
  }
}

/**
 * Validates required fields for update messages
 */
function validateUpdateMessage(
  file_id: string,
  user_id: string,
  project_id: string,
  update_type: typeof DocumentUpdateType[keyof typeof DocumentUpdateType],
  metadata?: {
    original_name: string;
    new_name?: string;
    old_project_id?: string;
  }
): void {
  const errors: string[] = [];

  if (!file_id || typeof file_id !== "string" || file_id.trim() === "") {
    errors.push("file_id is required and must be a non-empty string");
  }

  if (!user_id || typeof user_id !== "string" || user_id.trim() === "") {
    errors.push("user_id is required and must be a non-empty string");
  }

  if (!project_id || typeof project_id !== "string" || project_id.trim() === "") {
    errors.push("project_id is required and must be a non-empty string");
  }

  if (!update_type || !Object.values(DocumentUpdateType).includes(update_type)) {
    errors.push("update_type is required and must be a valid DocumentUpdateType");
  }

  if (metadata) {
    if (!metadata.original_name || typeof metadata.original_name !== "string" || metadata.original_name.trim() === "") {
      errors.push("metadata.original_name is required and must be a non-empty string when metadata is provided");
    }

    // Validate specific fields based on update type
    if (update_type === DocumentUpdateType.NAME) {
      if (!metadata.new_name || typeof metadata.new_name !== "string" || metadata.new_name.trim() === "") {
        errors.push("metadata.new_name is required and must be a non-empty string for NAME updates");
      }
    }

    if (update_type === DocumentUpdateType.PROJECT_ID) {
      if (!metadata.old_project_id || typeof metadata.old_project_id !== "string" || metadata.old_project_id.trim() === "") {
        errors.push("metadata.old_project_id is required and must be a non-empty string for PROJECT_ID updates");
      }
    }
  }

  if (errors.length > 0) {
    throw new DocumentProcessorValidationError(`Update message validation failed: ${errors.join(", ")}`);
  }
}

/**
 * Service for document processing operations
 */
export class DocumentProcessorService {
  /**
   * Publish a document upload processing message
   * @param file_id File ID to process
   * @param user_id User ID who initiated the upload
   * @param presigned_url Presigned URL for file access
   * @param project_id Project ID where files belong
   * @returns Promise<string> Message ID
   * @throws DocumentProcessorValidationError if required fields are missing or invalid
   */
  static async publishUploadMessage(
    message: Omit<DocumentProcessorMessage, "type">
  ): Promise<string> {
    // Validate the message before publishing
    validateUploadMessage(message);
    
    return this.publishMessage({
      ...message,
      type: DocumentProcessingType.UPLOAD,
    });
  }

  /**
   * Publish a document deletion processing message
   * @param file_id File ID to delete
   * @param user_id User ID who initiated the deletion
   * @param project_id Project ID where files belong
   * @returns Promise<string> Message ID
   * @throws DocumentProcessorValidationError if required fields are missing or invalid
   */
  static async publishDeleteMessage(
    file_id: string,
    user_id: string,
    project_id: string
  ): Promise<string> {
    // Validate the parameters before publishing
    validateDeleteMessage(file_id, user_id, project_id);
    
    return this.publishMessage({
      file_id,
      user_id,
      presigned_url: "", // Not needed for deletion
      project_id,
      type: DocumentProcessingType.DELETE,
    });
  }

  /**
   * Publish a document update processing message
   * @param file_id File ID to update
   * @param user_id User ID who initiated the update
   * @param project_id Project ID where files belong
   * @param update_type Type of update (name, project_id, or both)
   * @param metadata Metadata for the update
   * @returns Promise<string> Message ID
   * @throws DocumentProcessorValidationError if required fields are missing or invalid
   */
  static async publishUpdateMessage(
    file_id: string,
    user_id: string,
    project_id: string,
    update_type: typeof DocumentUpdateType[keyof typeof DocumentUpdateType],
    metadata?: {
      original_name: string;
      new_name?: string;
      old_project_id?: string;
    }

  ): Promise<string> {
    // Validate the parameters before publishing
    validateUpdateMessage(file_id, user_id, project_id, update_type, metadata);
    
    return this.publishMessage({
      file_id,
      user_id,
      presigned_url: "", // Not needed for updates
      project_id,
      type: DocumentProcessingType.UPDATE,
      metadata: {
        filename: metadata?.original_name || "",
        custom:{
          old_project_id: metadata?.old_project_id,
          new_name: metadata?.new_name,
          update_type: update_type,
        }
      }
    });
  }

  /**
   * Publish a document processing message
   * @param message The document processing message
   * @returns Promise<string> Message ID
   */
  private static async publishMessage(message: DocumentProcessorMessage): Promise<string> {
    try {
      const messageId = await documentProcessorPubsubService.publishMessage(
        message,
        {
          type: AidaPubsubTopic.DOCUMENT_PROCESSOR,
          timestamp: new Date().toISOString(),
        }
      );

      log.info("Document processor message published", {
        messageId,
        topic: AidaPubsubTopic.DOCUMENT_PROCESSOR,
        type: message.type,
        fileCount: 1,
        userId: message.user_id,
        projectId: message.project_id,
      });

      return messageId;
    } catch (error) {
      log.error("Failed to publish document processor message", {
        error: error instanceof Error ? error.message : String(error),
        message: {
          ...message,
          presigned_url: message.presigned_url.substring(0, 50) + "...", // Log partial URL for security
        },
      });
      throw error;
    }
  }
} 