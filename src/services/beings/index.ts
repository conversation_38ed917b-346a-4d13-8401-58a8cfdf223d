/**
 * Beings RAG Service
 *
 * Implementation of the retrieve_relevant_chunks tool
 */

import { beingsMCPAuthToken, beingsRAGBaseUrl } from "@/config";
import { createLogger } from "@/services/logger";
import {
  BeingsToolArgs,
  BeingsChunk,
  BeingsRAGResponse,
  BeingsServiceResponse,
  BeingsMCPConfig,
} from "./types";

// ============================================================================
// BEINGS RAG CONFIGURATION
// ============================================================================
export const beingsMCPUrl = `${beingsRAGBaseUrl}/mcp`;
export const beingsRAGRetrieveUrl = `${beingsRAGBaseUrl}/retrieve`;
// Create dedicated logger for Beings service
const logger = createLogger("BeingsService");

const getBeingsMCPConfig = (): BeingsMCPConfig => {
  const authToken = beingsMCPAuthToken;
  const baseUrl = beingsRAGRetrieveUrl;
  const timeout = parseInt(process.env.BEINGS_TIMEOUT || "45000", 10); // Increased from 30s to 45s

  if (!authToken) {
    throw new Error("BEINGS_MCP_AUTH_TOKEN environment variable is required");
  }

  if (!baseUrl) {
    throw new Error("BEINGS_RAG_SERVICE_URL environment variable is required");
  }

  return {
    authToken,
    baseUrl,
    timeout,
  };
};

// ============================================================================
// VALIDATION & SANITIZATION
// ============================================================================

/**
 * Validate required parameters for tool calls
 */
export function validateToolCallParams(params: {
  query?: string;
  userId?: string;
  projectId?: string;
  fileIds?: string[];
}): { valid: boolean; error?: string } {
  const { query, userId, projectId, fileIds } = params;

  if (!query || !userId || !fileIds || fileIds.length === 0) {
    return {
      valid: false,
      error:
        "Missing required parameters: query, userId, and fileIds are required",
    };
  }

  if (typeof query !== "string" || query.trim().length === 0) {
    return {
      valid: false,
      error: "Query must be a non-empty string",
    };
  }

  if (typeof userId !== "string" || userId.trim().length === 0) {
    return {
      valid: false,
      error: "UserId must be a non-empty string",
    };
  }

  return { valid: true };
}

/**
 * Sanitize and prepare query for processing
 */
export function sanitizeQuery(query: string): string {
  return query.trim().substring(0, 1000); // Limit query length
}

/**
 * Create standardized log context for tool calls
 */
export function createLogContext(
  query: string,
  userId: string,
  projectId?: string,
  fileIds?: string[]
): {
  query: string;
  fullQuery: string;
  fileIds: number;
  fileIdsList: string[] | undefined;
  userId: string;
  projectId: string | undefined;
  timestamp: string;
} {
  return {
    query: query.substring(0, 50),
    fullQuery: query,
    fileIds: fileIds?.length || 0,
    fileIdsList: fileIds,
    userId,
    projectId,
    timestamp: new Date().toISOString(),
  };
}

// ============================================================================
// BEINGS RAG API CLIENT
// ============================================================================

class BeingsRAGClient {
  private config: BeingsMCPConfig;

  constructor() {
    this.config = getBeingsMCPConfig();
  }

  /**
   * Execute the retrieve_relevant_chunks tool
   * This IS the tool - it doesn't call another server, it executes the search logic
   */
  async callRetrieveRelevantChunks(
    args: BeingsToolArgs
  ): Promise<BeingsServiceResponse> {
    const startTime = Date.now();
    const { query, userId, projectId, fileIds } = args;

    // Validate required parameters
    const validation = validateToolCallParams({
      query,
      userId,
      projectId,
      fileIds,
    });
    if (!validation.valid) {
      logger.error("Invalid parameters for beings RAG search", {
        error: validation.error,
        query: query?.substring(0, 50),
        userId,
        projectId,
      });
      return {
        success: false,
        error: validation.error,
        metadata: {
          searchQuery: "",
          filesSearched: 0,
          source: "beings-rag",
          processingTime: Date.now() - startTime,
        },
      };
    }

    const sanitizedQuery = sanitizeQuery(query);
    const logContext = createLogContext(
      sanitizedQuery,
      userId,
      projectId,
      fileIds
    );

    try {
      // Perform the RAG search
      const chunks = await this.performBeingsRAGSearch({
        ...args,
        query: sanitizedQuery,
      });
      const processingTime = Date.now() - startTime;

      logger.info("RAG search completed successfully", {
        ...logContext,
        chunkCount: chunks.length,
        processing_time_ms: processingTime,
      });

      return {
        success: true,
        data: {
          chunks: chunks,
          content: `Found ${chunks.length} relevant chunks for: ${sanitizedQuery}`,
          searchQuery: sanitizedQuery,
        },
        chunksCount: chunks.length,
        metadata: {
          searchQuery: sanitizedQuery,
          filesSearched: fileIds?.length || "all",
          source: "beings-rag",
          processingTime,
        },
      };
    } catch (error) {
      return this.handleError(
        error,
        logContext,
        startTime,
        "retrieve_relevant_chunks"
      );
    }
  }

  /**
   * Perform the actual RAG search by calling the Beings API
   */
  private async performBeingsRAGSearch(
    args: BeingsToolArgs
  ): Promise<BeingsChunk[]> {
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.config.authToken}`,
    };

    // Build the request body
    const requestBody = {
      query: args.query,
      project_id: args.projectId,
      file_ids: args.fileIds,
      user_id: args.userId,
      limit: args.limit,
    };

    logger.debug("Making Beings RAG API request", {
      url: this.config.baseUrl,
      requestBody: {
        ...requestBody,
        query:
          args.query.substring(0, 100) + (args.query.length > 100 ? "..." : ""),
      },
    });

    try {
      const response = await fetch(this.config.baseUrl, {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Beings RAG API error: ${response.status} ${errorText}`
        );
      }

      const result: BeingsRAGResponse = await response.json();

      // Validate and parse response
      if (!result.success) {
        throw new Error(
          result.error || "Beings RAG API returned unsuccessful response"
        );
      }

      if (!result.data || !Array.isArray(result.data.chunks)) {
        throw new Error(
          "Invalid response format: missing or invalid chunks array"
        );
      }

      logger.debug("Beings RAG API response parsed successfully", {
        chunkCount: result.data.chunks.length,
        total: result.data.total,
      });

      return result.data.chunks;
    } catch (error) {
      logger.error("Error calling Beings RAG API", {
        error: error instanceof Error ? error.message : String(error),
        query: args.query,
        userId: args.userId,
        projectId: args.projectId,
        url: this.config.baseUrl,
      });
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Create consistent error response
   */
  private createErrorResponse(
    error: string,
    startTime: number
  ): BeingsServiceResponse {
    return {
      success: false,
      error,
      metadata: {
        searchQuery: "",
        filesSearched: 0,
        source: "beings-rag",
        processingTime: Date.now() - startTime,
      },
    };
  }

  /**
   * Handle errors consistently across methods
   */
  private handleError(
    error: unknown,
    logContext: Record<string, any>,
    startTime: number,
    operation: string
  ): BeingsServiceResponse {
    const processingTime = Date.now() - startTime;

    logger.error(`Beings RAG ${operation} error`, {
      ...logContext,
      processing_time_ms: processingTime,
    });
    logger.stack(error, "error");

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      metadata: {
        searchQuery: logContext.query || "",
        filesSearched:
          typeof logContext.fileIds === "number" ? logContext.fileIds : 0,
        source: "beings-rag",
        processingTime,
      },
    };
  }
}

// ============================================================================
// SINGLETON INSTANCE & EXPORTS
// ============================================================================

export const beingsRAGClient = new BeingsRAGClient();

/**
 * Call the retrieve_relevant_chunks tool
 * Direct access to the singleton instance method
 */
export const callRetrieveRelevantChunks =
  beingsRAGClient.callRetrieveRelevantChunks.bind(beingsRAGClient) as (
    args: BeingsToolArgs
  ) => Promise<BeingsServiceResponse>;

export { BeingsToolArgs };
