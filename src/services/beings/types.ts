// ============================================================================
// BEINGS RAG API TYPES
// ============================================================================

/**
 * Arguments for the retrieve_relevant_chunks tool
 */
export interface BeingsToolArgs {
  query: string;
  userId: string;
  projectId?: string;
  fileIds?: string[];
  limit?: number;
}

/**
 * Individual chunk returned by Beings RAG API
 */
export interface BeingsChunk {
  id: string;
  content: string;
  metadata: {
    file_id: string;
    filename: string;
    user_id: string;
    project_id: string;
    [key: string]: any;
  };
  score?: number;
}

/**
 * Raw response format from Beings RAG API
 */
export interface BeingsRAGResponse {
  success: boolean;
  data?: {
    chunks: BeingsChunk[];
    total?: number;
  };
  error?: string;
  chunksCount?: number;
  metadata?: {
    searchQuery: string;
    filesSearched: number | string;
    source: string;
    processingTime?: number;
  };
}

/**
 * Standardized service response format
 */
export interface BeingsServiceResponse {
  success: boolean;
  data?: {
    chunks: BeingsChunk[];
    content: string;
    searchQuery: string;
  };
  chunksCount?: number;
  metadata?: {
    searchQuery: string;
    filesSearched: number | string;
    source: string;
    processingTime?: number;
  };
  error?: string;
}

/**
 * Configuration for Beings RAG client
 */
export interface BeingsMCPConfig {
  authToken: string;
  baseUrl: string;
  timeout: number;
}
