import {
  gcpLocation,
  gcpProjectId,
  gcpPubsubTranscoderTopicName,
  getGcsServiceAccount,
} from "@/config";
import { log } from "@/services/logger";
import {
  protos,
  TranscoderServiceClient,
} from "@google-cloud/video-transcoder";
import { v4 as uuidv4 } from "uuid";

type JobConfig = protos.google.cloud.video.transcoder.v1.IJobConfig;
type JobMetadata = protos.google.cloud.video.transcoder.v1.IJob;
type JobResponse = [
  JobMetadata,
  protos.google.cloud.video.transcoder.v1.ICreateJobRequest | undefined,
  {} | undefined
];
type ListJobsResponseType =
  protos.google.cloud.video.transcoder.v1.IListJobsResponse;

class TranscoderService {
  private client: TranscoderServiceClient;
  private projectId: string;
  private location: string;

  constructor() {
    this.projectId = gcpProjectId;
    this.location = gcpLocation;
  }

  private async getClient() {
    if (!this.client) {
      const gcsServiceAccount = await getGcsServiceAccount();
      this.client = new TranscoderServiceClient({
        credentials: gcsServiceAccount,
      });
    }
    return this.client;
  }

  /**
   * Create a transcoding job
   * @param inputUri The URI of the input video in the format gs://bucket/file.mp4
   * @param outputUri The URI of the output location in the format gs://bucket/output/
   * @param config Optional job configuration
   * @returns The created job
   */
  async createJob(
    inputUri: string,
    outputUri: string,
    config?: JobConfig
  ): Promise<JobMetadata> {
    const client = await this.getClient();
    try {
      const jobId = `job-${uuidv4()}`;
      log.info(
        `Creating transcoding job with jobId: ${jobId} and projectId: ${this.projectId} and location: ${this.location}`
      );
      const parent = client.locationPath(this.projectId, this.location);
      log.info(`Parent: ${parent}`);

      const pubsubTopic = gcpPubsubTranscoderTopicName;

      // Default job config if none provided
      const defaultConfig: JobConfig = {
        inputs: [{ key: "input0", uri: inputUri }],
        output: { uri: outputUri },
        elementaryStreams: [
          {
            key: "video-stream0",
            videoStream: {
              h264: {
                heightPixels: 720,
                widthPixels: 1280,
                bitrateBps: 2500000,
                frameRate: 30,
              },
            },
          },
          {
            key: "audio-stream0",
            audioStream: {
              codec: "aac",
              bitrateBps: 64000,
            },
          },
        ],
        muxStreams: [
          {
            key: "sd",
            container: "mp4",
            elementaryStreams: ["video-stream0", "audio-stream0"],
            fileName: "output.mp4",
          },
        ],
        pubsubDestination: {
          topic: pubsubTopic,
        },
      };

      const jobConfig = config
        ? {
            ...config,
            pubsubDestination: {
              topic: pubsubTopic,
            },
          }
        : defaultConfig;

      // Create the job request
      const request = {
        parent,
        job: {
          inputUri,
          outputUri,
          config: jobConfig,
        },
        jobId,
      };

      log.info(
        `Creating transcoding job with request: ${JSON.stringify(request)}`
      );

      // Call the API
      const [job] = await client.createJob(request);

      log.info(`Created transcoding job: ${job.name}`);
      return job;
    } catch (error) {
      log.error(`Error creating transcoding job: ${error}`);
      throw error;
    }
  }

  /**
   * Get the status of a transcoding job
   * @param jobId The ID of the job
   * @returns The job details
   */
  async getJob(jobId: string): Promise<JobMetadata> {
    try {
      const client = await this.getClient();
      const name = client.jobPath(this.projectId, this.location, jobId);
      const [job] = await client.getJob({ name });
      return job;
    } catch (error) {
      log.error(`Error getting job ${jobId}: ${error}`);
      throw error;
    }
  }

  /**
   * List all transcoding jobs
   * @param pageSize The maximum number of jobs to return
   * @param pageToken The page token for pagination
   * @returns The list of jobs
   */
  async listJobs(
    pageSize = 50,
    pageToken?: string
  ): Promise<{ jobs: JobMetadata[]; nextPageToken?: string }> {
    try {
      const client = await this.getClient();
      const parent = client.locationPath(this.projectId, this.location);
      const request = {
        parent,
        pageSize,
        pageToken,
      };

      // The response is an array where the first element contains the jobs
      const [jobs, , response] = await client.listJobs(request);

      return {
        jobs: jobs || [],
        nextPageToken: response?.nextPageToken || undefined,
      };
    } catch (error) {
      log.error(`Error listing jobs: ${error}`);
      throw error;
    }
  }

  /**
   * Delete a transcoding job
   * @param jobId The ID of the job to delete
   */
  async deleteJob(jobId: string): Promise<void> {
    try {
      const client = await this.getClient();
      const name = client.jobPath(this.projectId, this.location, jobId);
      await client.deleteJob({ name });
      log.info(`Deleted job ${jobId}`);
    } catch (error) {
      log.error(`Error deleting job ${jobId}: ${error}`);
      throw error;
    }
  }
}

export const transcoderService = new TranscoderService();
