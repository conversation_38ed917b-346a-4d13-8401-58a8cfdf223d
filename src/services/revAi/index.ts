import { revAiToken } from "@/config";
import {
  RevAiApiClient,
  RevAiApiClientConfig,
  RevAiApiDeployment,
  RevAiApiDeploymentConfigMap,
  TopicExtractionClient,
} from "revai-node-sdk";

export const revAiClient = new RevAiApiClient({
  token: revAiToken,
  deploymentConfig: RevAiApiDeploymentConfigMap.get(RevAiApiDeployment.EU),
});

// @ts-ignore Have this as workaround for TopicExtractionClient since it's calling BaseApiClient. So can config different deployment
export const revAiTopicExtractionClient = new TopicExtractionClient({
  deploymentConfig: RevAiApiDeploymentConfigMap.get(RevAiApiDeployment.EU),
  token: revAiToken,
} as RevAiApiClientConfig);
