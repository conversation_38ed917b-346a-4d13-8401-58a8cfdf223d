import { Response } from "express";
import { v4 as uuidv4 } from "uuid";
import { Op } from "sequelize";

import { log } from "@/services/logger";
import {
  SSEConnectionModel,
  SSEConnection,
} from "@/schemas/sse/SSEConnection.model";
import { getRateLimiter } from "@/services/rate-limiter/rate-limiter.service";
import {
  getPubSubService,
  ProjectEvent,
  SSEEventType,
} from "@/services/sse/pubsub.service";
import { ServiceResponse } from "@/types";
import { IntentionalError } from "@/utils/errors";
import { models } from "@/schemas";

interface SSESubscriptionData {
  connectionId: string;
  channel: string;
  userId: string;
  projectId: string;
}

interface SSEConnectionInfo {
  connectionId: string;
  response: Response;
  lastActivity: Date;
  heartbeatInterval?: NodeJS.Timeout;
}

export class SSEService {
  private instanceId: string;
  private activeConnections: Map<string, SSEConnectionInfo> = new Map();
  private rateLimiter = getRateLimiter();
  private pubsubService = getPubSubService();

  constructor() {
    this.instanceId = this.pubsubService.getInstanceId();
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    try {
      await this.pubsubService.initialize();

      // Subscribe to cross-instance events (both global and project-specific)
      this.pubsubService.subscribe(
        "global",
        this.handleCrossInstanceEvent.bind(this)
      );

      // Cleanup stale connections periodically
      setInterval(() => {
        this.cleanupStaleConnections();
      }, 30000); // Every 30 seconds

      log.info("SSE Service initialized", { instanceId: this.instanceId });
    } catch (error) {
      log.error("Failed to initialize SSE Service", {
        error,
        instanceId: this.instanceId,
      });
    }
  }

  async subscribeToProject(
    userId: string,
    projectId: string,
    res: Response,
    userAgent?: string,
    ipAddress?: string
  ): Promise<ServiceResponse<SSESubscriptionData>> {
    try {
      // Rate limiting check
      const rateLimitResult = await this.rateLimiter.checkLimit(
        userId,
        "sse_connection"
      );
      if (!rateLimitResult.allowed) {
        return {
          success: false,
          message: `Rate limit exceeded. Try again after ${Math.ceil(
            (rateLimitResult.resetTime || 0 - Date.now()) / 60000
          )} minutes.`,
          statusCode: 429,
        };
      }

      // Validate project access
      const hasAccess = await this.validateProjectAccess(userId, projectId);
      if (!hasAccess) {
        return {
          success: false,
          message: "Access denied to project",
          statusCode: 403,
        };
      }

      // Check for existing active connections for this user/project
      const existingConnection = await SSEConnectionModel.findOne({
        where: {
          userId,
          projectId,
          instanceId: this.instanceId,
          isActive: true,
        },
      });

      if (existingConnection) {
        const existingConnectionData =
          existingConnection.toJSON<SSEConnection>();
        // Close existing connection before creating new one
        await this.unsubscribeConnection(existingConnectionData.connectionId);
      }

      const connectionId = uuidv4();
      const channel = `live-sync-project-${projectId}`;

      // Setup SSE response headers
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      });

      // Send initial connection event
      res.write(
        `data: ${JSON.stringify({
          type: SSEEventType.CONNECTED,
          connectionId,
          timestamp: new Date().toISOString(),
        })}\n\n`
      );

      // Store connection in database
      await SSEConnectionModel.create({
        id: uuidv4(),
        userId,
        projectId,
        channel,
        connectionId,
        instanceId: this.instanceId,
        userAgent: userAgent || "Unknown",
        ipAddress: ipAddress || "Unknown",
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        isActive: true,
      });

      // Store active connection locally
      const connectionInfo: SSEConnectionInfo = {
        connectionId,
        response: res,
        lastActivity: new Date(),
      };

      this.activeConnections.set(connectionId, connectionInfo);

      // Subscribe to project-specific cross-instance events
      const projectChannel = `project:${projectId}`;
      this.pubsubService.subscribe(
        projectChannel,
        this.handleCrossInstanceEvent.bind(this)
      );

      // Setup heartbeat
      connectionInfo.heartbeatInterval = setInterval(async () => {
        try {
          await this.sendHeartbeat(connectionId);
        } catch (error) {
          log.error("Heartbeat failed", {
            error,
            connectionId,
            instanceId: this.instanceId,
          });
          await this.unsubscribeConnection(connectionId);
        }
      }, 30000); // Every 30 seconds

      // Handle connection close
      res.on("close", async () => {
        await this.unsubscribeConnection(connectionId);
      });

      log.info("SSE connection established", {
        connectionId,
        userId,
        projectId,
        channel,
        instanceId: this.instanceId,
      });

      return {
        success: true,
        data: {
          connectionId,
          channel,
          userId,
          projectId,
        },
        statusCode: 200,
      };
    } catch (error) {
      log.error("Failed to create SSE subscription", {
        error,
        userId,
        projectId,
        instanceId: this.instanceId,
      });

      return {
        success: false,
        message: "Failed to establish SSE connection",
        statusCode: 500,
      };
    }
  }

  async broadcastToProject(
    projectId: string,
    event: Omit<ProjectEvent, "eventId" | "timestamp">
  ): Promise<ServiceResponse<void>> {
    try {
      const fullEvent: ProjectEvent = {
        ...event,
        eventId: uuidv4(),
        timestamp: new Date().toISOString(),
      };

      // Broadcast to local connections first
      await this.broadcastToLocalConnections(projectId, fullEvent);

      // Then broadcast to other instances via pub/sub
      await this.pubsubService.publishToAllInstances(fullEvent);

      log.info("Event broadcasted to project", {
        projectId,
        eventType: fullEvent.type,
        eventId: fullEvent.eventId,
        instanceId: this.instanceId,
      });

      return {
        success: true,
        statusCode: 200,
      };
    } catch (error) {
      log.error("Failed to broadcast event", {
        error,
        projectId,
        eventType: event.type,
        instanceId: this.instanceId,
      });

      return {
        success: false,
        message: "Failed to broadcast event",
        statusCode: 500,
      };
    }
  }

  async unsubscribeConnection(connectionId: string): Promise<void> {
    try {
      const connectionInfo = this.activeConnections.get(connectionId);

      if (connectionInfo) {
        // Clear heartbeat interval
        if (connectionInfo.heartbeatInterval) {
          clearInterval(connectionInfo.heartbeatInterval);
        }

        // Close response if still open
        try {
          if (!connectionInfo.response.closed) {
            connectionInfo.response.end();
          }
        } catch (error) {
          // Response might already be closed
        }

        // Remove from active connections
        this.activeConnections.delete(connectionId);
      }

      // Mark as inactive in database
      await SSEConnectionModel.update(
        { isActive: false },
        { where: { connectionId } }
      );

      log.info("SSE connection closed", {
        connectionId,
        instanceId: this.instanceId,
      });
    } catch (error) {
      log.error("Error closing SSE connection", {
        error,
        connectionId,
        instanceId: this.instanceId,
      });
    }
  }

  private async broadcastToLocalConnections(
    projectId: string,
    event: ProjectEvent
  ): Promise<void> {
    // Get active connections for this project on this instance
    const connections = await SSEConnectionModel.findAll({
      where: {
        projectId,
        instanceId: this.instanceId,
        isActive: true,
        lastHeartbeat: {
          [Op.gte]: new Date(Date.now() - 5 * 60 * 1000), // Active in last 5 minutes
        },
      },
    });

    for (const connection of connections) {
      const connectionData = connection.toJSON<SSEConnection>();
      try {
        await this.sendEventToConnection(connectionData.connectionId, event);

        // Update last heartbeat
        await connection.update({ lastHeartbeat: new Date() });
      } catch (error) {
        log.error("Failed to send event to connection", {
          error,
          connectionId: connectionData.connectionId,
          eventType: event.type,
          instanceId: this.instanceId,
        });

        // Mark connection as inactive
        await connection.update({ isActive: false });
        await this.unsubscribeConnection(connectionData.connectionId);
      }
    }
  }

  private async sendEventToConnection(
    connectionId: string,
    event: ProjectEvent
  ): Promise<void> {
    const connectionInfo = this.activeConnections.get(connectionId);
    if (!connectionInfo) {
      throw new Error("Connection not found in active connections");
    }

    const eventData = JSON.stringify(event);
    connectionInfo.response.write(`data: ${eventData}\n\n`);
    connectionInfo.lastActivity = new Date();
  }

  private async sendHeartbeat(connectionId: string): Promise<void> {
    const heartbeatEvent: ProjectEvent = {
      type: SSEEventType.HEARTBEAT,
      projectId: "",
      data: { timestamp: new Date().toISOString() },
      eventId: uuidv4(),
      timestamp: new Date().toISOString(),
    };

    await this.sendEventToConnection(connectionId, heartbeatEvent);

    // Update database heartbeat
    await SSEConnectionModel.update(
      { lastHeartbeat: new Date() },
      { where: { connectionId } }
    );
  }

  private async handleCrossInstanceEvent(event: ProjectEvent): Promise<void> {
    // This handles events from other instances
    await this.broadcastToLocalConnections(event.projectId, event);
  }

  private async validateProjectAccess(
    userId: string,
    projectId: string
  ): Promise<boolean> {
    try {
      // Check if user is project member
      const projectMember = await models.ProjectMember.findOne({
        where: {
          projectId,
          userId,
        },
      });

      if (projectMember) return true;

      // Check if user is project owner
      const project = await models.Project.findOne({
        where: {
          id: projectId,
          createdById: userId,
          deletedAt: null,
        },
      });
      if (project) return true;

      return false;
    } catch (error) {
      log.error("Error validating project access", {
        error,
        userId,
        projectId,
      });
      return false;
    }
  }

  private async cleanupStaleConnections(): Promise<void> {
    try {
      const staleThreshold = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago

      // Find stale connections in database
      const staleConnections = await SSEConnectionModel.findAll({
        where: {
          instanceId: this.instanceId,
          isActive: true,
          lastHeartbeat: {
            [Op.lt]: staleThreshold,
          },
        },
      });

      for (const connection of staleConnections) {
        const connectionData = connection.toJSON<SSEConnection>();
        await this.unsubscribeConnection(connectionData.connectionId);
      }

      if (staleConnections.length > 0) {
        log.info("Cleaned up stale connections", {
          count: staleConnections.length,
          instanceId: this.instanceId,
        });
      }
    } catch (error) {
      log.error("Error cleaning up stale connections", {
        error,
        instanceId: this.instanceId,
      });
    }
  }

  async getConnectionStats(): Promise<{
    activeLocal: number;
    activeGlobal: number;
    instanceId: string;
  }> {
    try {
      const activeLocal = this.activeConnections.size;

      const activeGlobalResult = await SSEConnectionModel.count({
        where: {
          isActive: true,
          lastHeartbeat: {
            [Op.gte]: new Date(Date.now() - 5 * 60 * 1000),
          },
        },
      });

      return {
        activeLocal,
        activeGlobal: activeGlobalResult,
        instanceId: this.instanceId,
      };
    } catch (error) {
      log.error("Error getting connection stats", {
        error,
        instanceId: this.instanceId,
      });
      return {
        activeLocal: this.activeConnections.size,
        activeGlobal: 0,
        instanceId: this.instanceId,
      };
    }
  }
}

// Singleton instance
let sseServiceInstance: SSEService | null = null;

export const initializeSSEService = (): SSEService => {
  sseServiceInstance = new SSEService();
  return sseServiceInstance;
};

export const getSSEService = (): SSEService => {
  if (!sseServiceInstance) {
    sseServiceInstance = new SSEService();
  }
  return sseServiceInstance;
};

export default SSEService;
