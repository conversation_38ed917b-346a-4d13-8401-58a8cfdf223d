import { PubSub, Topic, Subscription, Message } from "@google-cloud/pubsub";
import { log } from "@/services/logger";
import { getGcsServiceAccount } from "@/config";

export enum SSEEventType {
  // Connection events
  CONNECTED = "connected",
  HEARTBEAT = "heartbeat",

  // Resource events
  RESOURCE_CREATED = "resource.created",
  RESOURCE_UPDATED = "resource.updated",
  RESOURCE_DELETED = "resource.deleted",
  RESOURCE_MOVED_OUT_OF_PROJECT = "resource.moved-out-of-project", // In case resource is moved out of a project
  RESOURCE_MOVED_TO_PROJECT = "resource.moved-to-project", // In case resource is moved to a project


  // ACL events
  ACL_MEMBER_NEW_MEMBER = "acl.member.new-member",
  ACL_MEMBER_CHANGE_ROLE = "acl.member.change-role",
  ACL_MEMBER_REMOVED = "acl.member.removed",
  ACL_MEMBER_LEAVE = "acl.member.leave",
  ACL_MEMBER_ACCEPT_INVITATION = "acl.member.accept-invitation",

  // Session events
  SESSION_CREATED = "session.created",
  SESSION_STATUS_UPDATED = "session.status-updated",
  SESSION_DELETED = "session.deleted",

  // Project events
  PROJECT_DELETED = 'project.deleted',

  // Note events
  NOTE_CREATED = "note.created",
  NOTE_UPDATED = "note.updated",
  NOTE_DELETED = "note.deleted",
}

export interface ProjectEvent {
  type: SSEEventType;
  projectId: string;
  userId?: string;
  data: any;
  timestamp: string;
  eventId: string;
}

interface PubSubMessage {
  event: ProjectEvent;
  sourceInstanceId: string;
  timestamp: string;
}

type EventHandler = (event: ProjectEvent) => Promise<void>;

export class SSEPubSubService {
  private instanceId: string;
  private pubsub: PubSub | null = null;
  private topic: Topic | null = null;
  private subscription: Subscription | null = null;
  private subscribers: Map<string, EventHandler[]> = new Map();
  private isInitialized: boolean = false;
  private readonly topicName = "sse-events";
  private readonly subscriptionName = "sse-events-subscription";

  constructor(pubsubClient?: PubSub) {
    this.instanceId = this.generateInstanceId();
    this.pubsub = pubsubClient;
  }

  private generateInstanceId(): string {
    const hostname = process.env.HOSTNAME || "localhost";
    const processId = process.pid;
    const timestamp = Date.now();
    return `${hostname}-${processId}-${timestamp}`;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize PubSub client if not provided
      if (!this.pubsub) {
        const serviceAccount = await getGcsServiceAccount();
        this.pubsub = new PubSub({ credentials: serviceAccount });
        log.info("Created new PubSub client for SSE service", {
          instanceId: this.instanceId,
        });
      }

      if (this.pubsub) {
        await this.setupPubSub();
        this.isInitialized = true;
        log.info("SSE Pub/Sub service initialized", {
          instanceId: this.instanceId,
          topicName: this.topicName,
          subscriptionName: this.subscriptionName,
        });
      } else {
        log.warn(
          "Pub/Sub client not available, running in single-instance mode"
        );
      }
    } catch (error) {
      log.error("Failed to initialize Pub/Sub service", {
        error,
        instanceId: this.instanceId,
      });
      throw error;
    }
  }

  async publishToAllInstances(event: ProjectEvent): Promise<void> {
    try {
      if (!this.pubsub || !this.topic) {
        log.debug("Pub/Sub not available, skipping cross-instance broadcast");
        return;
      }

      const message: PubSubMessage = {
        event,
        sourceInstanceId: this.instanceId,
        timestamp: new Date().toISOString(),
      };

      const messageBuffer = Buffer.from(JSON.stringify(message));
      const messageId = await this.topic.publish(messageBuffer, {
        eventType: event.type,
        projectId: event.projectId,
        sourceInstanceId: this.instanceId,
      });

      log.debug("Published event to all instances", {
        eventType: event.type,
        projectId: event.projectId,
        eventId: event.eventId,
        messageId: messageId,
        instanceId: this.instanceId,
      });
    } catch (error) {
      log.error("Failed to publish event to all instances", {
        error,
        event: event.eventId,
        instanceId: this.instanceId,
      });
      // Don't throw error - local broadcasting should still work
    }
  }

  subscribe(channel: string, handler: EventHandler): void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, []);
    }

    this.subscribers.get(channel)!.push(handler);

    log.debug("Subscribed to channel", {
      channel,
      instanceId: this.instanceId,
      handlerCount: this.subscribers.get(channel)!.length,
    });
  }

  unsubscribe(channel: string, handler: EventHandler): void {
    const handlers = this.subscribers.get(channel);
    if (!handlers) return;

    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);

      if (handlers.length === 0) {
        this.subscribers.delete(channel);
      }

      log.debug("Unsubscribed from channel", {
        channel,
        instanceId: this.instanceId,
        remainingHandlers: handlers.length,
      });
    }
  }

  private async setupPubSub(): Promise<void> {
    try {
      if (!this.pubsub) {
        throw new Error("PubSub client not initialized");
      }

      // Get or create topic
      this.topic = this.pubsub.topic(this.topicName);
      const [topicExists] = await this.topic.exists();
      if (!topicExists) {
        await this.topic.create();
        log.info("Created SSE PubSub topic", { topicName: this.topicName });
      }

      // Get or create subscription with unique name per instance
      const instanceSubscriptionName = `${this.subscriptionName}-${this.instanceId}`;
      this.subscription = this.topic.subscription(instanceSubscriptionName);
      const [subscriptionExists] = await this.subscription.exists();
      if (!subscriptionExists) {
        await this.subscription.create({
          ackDeadlineSeconds: 60,
          enableMessageOrdering: false,
        });
        log.info("Created SSE PubSub subscription", { 
          subscriptionName: instanceSubscriptionName 
        });
      }

      // Set up message handler
      this.subscription.on("message", async (message: Message) => {
        try {
          const messageData = message.data.toString();
          const { event, sourceInstanceId }: PubSubMessage = JSON.parse(messageData);

          // Don't process events from our own instance
          if (sourceInstanceId === this.instanceId) {
            message.ack();
            return;
          }

          log.debug("Received cross-instance event", {
            eventType: event.type,
            projectId: event.projectId,
            eventId: event.eventId,
            sourceInstance: sourceInstanceId,
            currentInstance: this.instanceId,
          });

          // Notify local subscribers
          await this.notifyLocalSubscribers(event);
          message.ack();
        } catch (error) {
          log.error("Failed to process cross-instance event", {
            error,
            messageData: message.data.toString(),
            instanceId: this.instanceId,
          });
          message.nack();
        }
      });

      // Handle subscription errors
      this.subscription.on("error", (error) => {
        log.error("PubSub subscription error", {
          error,
          instanceId: this.instanceId,
        });
      });

      log.info("Pub/Sub subscription established", {
        instanceId: this.instanceId,
        topicName: this.topicName,
        subscriptionName: instanceSubscriptionName,
      });
    } catch (error) {
      log.error("Failed to setup Pub/Sub subscription", {
        error,
        instanceId: this.instanceId,
      });
      throw error;
    }
  }

  private async notifyLocalSubscribers(event: ProjectEvent): Promise<void> {
    const channels: string[] = ["global", `project:${event.projectId}`];

    for (const channel of channels) {
      const handlers = this.subscribers.get(channel);
      if (!handlers) continue;

      for (const handler of handlers) {
        try {
          await handler(event);
        } catch (error) {
          log.error("Event handler failed", {
            error,
            channel,
            eventType: event.type,
            eventId: event.eventId,
            instanceId: this.instanceId,
          });
        }
      }
    }
  }

  getInstanceId(): string {
    return this.instanceId;
  }

  async shutdown(): Promise<void> {
    try {
      if (this.subscription) {
        this.subscription.removeAllListeners();
        this.subscription.close();
        this.subscription = null;
      }

      if (this.pubsub && this.isInitialized) {
        await this.pubsub.close();
      }

      this.subscribers.clear();
      this.isInitialized = false;
      this.topic = null;
      this.pubsub = null;

      log.info("Pub/Sub service shutdown complete", {
        instanceId: this.instanceId,
      });
    } catch (error) {
      log.error("Error during Pub/Sub shutdown", {
        error,
        instanceId: this.instanceId,
      });
    }
  }
}

// Singleton instance
let pubsubInstance: SSEPubSubService | null = null;

export const initializePubSub = (pubsubClient?: any): SSEPubSubService => {
  pubsubInstance = new SSEPubSubService(pubsubClient);
  return pubsubInstance;
};

export const getPubSubService = (): SSEPubSubService => {
  if (!pubsubInstance) {
    pubsubInstance = new SSEPubSubService();
  }
  return pubsubInstance;
};

export default SSEPubSubService;
