import type { Bucket, UploadOptions } from "@google-cloud/storage";
import { Storage } from "@google-cloud/storage";
import { gcsBucket, getGcsServiceAccount } from "@/config";
import { log } from "@/services/logger";
import { storageCDNUrl } from "@/config";

let _bucket: Bucket | undefined;
let _storage: Storage | undefined;

const getStorage = async () => {
  if (!_storage) {
    const serviceAccount = await getGcsServiceAccount();
    _storage = new Storage({ credentials: serviceAccount });
  }
  return _storage;
};

const getBucket = async () => {
  if (!_bucket) {
    const storage = await getStorage();
    _bucket = storage.bucket(gcsBucket);
  }
  return _bucket;
};

export const gcsUpload = async (
  filePath: string,
  fileName: string,
  mineType?: string,
  shouldCache?: boolean
) => {
  const uploadOptions: UploadOptions = {
    destination: fileName,
    contentType: mineType,
  };

  if (shouldCache) {
    Object.assign(uploadOptions, {
      metadata: {
        cacheControl: "public, max-age=********",
        contentDisposition:
          "attachment" + (fileName ? `; filename="${fileName}"` : ""),
      },
    });
  }

  const bucket = await getBucket();
  await bucket.upload(filePath, uploadOptions);
  return fileName;
};

/**
 * Upload content directly to GCS without requiring a local file
 * @param content - Content to upload (string or Buffer)
 * @param fileName - Destination file name in GCS
 * @param options - Upload options including contentType and metadata
 * @returns The uploaded file name
 */
export const gcsUploadContent = async (
  content: string | Buffer,
  fileName: string,
  options?: {
    contentType?: string;
    metadata?: Record<string, any>;
  }
) => {
  const bucket = await getBucket();
  const file = bucket.file(fileName);

  const uploadOptions: any = {};

  if (options?.contentType) {
    uploadOptions.metadata = {
      contentType: options.contentType,
      ...options.metadata,
    };
  }

  // Create a write stream and pipe the content
  const stream = file.createWriteStream(uploadOptions);

  return new Promise<string>((resolve, reject) => {
    stream.on("error", (error) => {
      log.error(`Failed to upload content to ${fileName}:`, error);
      reject(error);
    });

    stream.on("finish", () => {
      log.info(`Successfully uploaded content to ${fileName}`);
      resolve(fileName);
    });

    // Write the content and end the stream
    stream.end(content);
  });
};

export const deleteFile = async (fileName: string) => {
  const bucket = await getBucket();
  if (!fileName) return;

  try {
    await bucket.file(fileName).delete();
  } catch (error) {
    log.error(`Failed to delete file: ${error.message}`);
  }
};

/**
 * Delete multiple files from GCS
 * @param fileNames - Array of file names to delete
 * @returns Promise resolving when all deletions are complete
 */
export const deleteFiles = async (fileNames: string[]): Promise<void> => {
  if (!fileNames || fileNames.length === 0) return;

  const bucket = await getBucket();

  await Promise.all(
    fileNames.map(async (fileName) => {
      try {
        await bucket.file(fileName).delete();
        log.info("File deleted successfully", { fileName });
      } catch (error) {
        log.error("Failed to delete file", {
          fileName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    })
  );
};

/**
 * Delete all files with a specific prefix (folder-like deletion)
 * @param prefix - File path prefix to delete
 * @returns Promise resolving when deletion is complete
 */
export const deleteFilesByPrefix = async (prefix: string): Promise<number> => {
  const bucket = await getBucket();

  try {
    const [files] = await bucket.getFiles({ prefix });

    if (files.length === 0) {
      log.info("No files found to delete", { prefix });
      return 0;
    }

    await Promise.all(
      files.map(async (file) => {
        try {
          await file.delete();
        } catch (error) {
          log.error("Failed to delete file", {
            fileName: file.name,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      })
    );

    log.info("Files deleted by prefix", {
      prefix,
      deletedCount: files.length,
    });

    return files.length;
  } catch (error) {
    log.error("Failed to delete files by prefix", {
      prefix,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
};

/**
 * Get the size of a file from Google Cloud Storage
 * @param fileName - The name of the file in GCS
 * @returns The size of the file in bytes, or undefined if the file doesn't exist or there's an error
 */
export const getFileSize = async (
  fileName: string
): Promise<number | undefined> => {
  if (!fileName) return undefined;

  const bucket = await getBucket();

  try {
    const [metadata] = await bucket.file(fileName).getMetadata();
    return Number(metadata.size);
  } catch (error) {
    log.error(`Failed to get file size for: ${fileName}`, error);
    return undefined;
  }
};

export const generateSignedUrlForRead = async (
  fileName: string,
  useCDNUrl: boolean = true
) => {
  if (!fileName) return;

  const bucket = await getBucket();
  const isCDNEnabled = storageCDNUrl !== undefined;
  try {
    const [signedUrl] = await bucket.file(fileName).getSignedUrl({
      version: "v4",
      action: "read",
      expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      ...(isCDNEnabled && useCDNUrl && {
        virtualHostedStyle: true,
        host: storageCDNUrl,
      }),
    });

    return signedUrl;
  } catch (error) {
    log.error(`Failed to generate signed URL for: ${fileName}`, error);
    return undefined;
  }
};

export const generateSignedUrlForUpload = async (
  fileName: string,
  folder?: string
) => {
  const bucket = await getBucket();

  const uploadFileName = folder ? `${folder}/${fileName}` : fileName;

  const [response] = await bucket
    .file(uploadFileName)
    .generateSignedPostPolicyV4({
      expires: Date.now() + 120 * 60 * 1000, // 2 hours
      fields: {
        "x-ignore-file-name": fileName,
      },
    });

  return response;
};

/**
 * Get a readable stream for a file from Google Cloud Storage
 * @param fileName - The name of the file in GCS
 * @returns A readable stream of the file content
 */
export const getFileStream = async (
  fileName: string
): Promise<ReadableStream | undefined> => {
  if (!fileName) return undefined;

  const bucket = await getBucket();

  try {
    const file = bucket.file(fileName);

    // Check if file exists first
    const [exists] = await file.exists();
    if (!exists) {
      log.error(`File not found in GCS: ${fileName}`);
      return undefined;
    }

    // Create a readable stream from the GCS file
    const gcsStream = file.createReadStream();

    // Convert Node.js readable stream to Web ReadableStream
    const webStream = new ReadableStream({
      start(controller) {
        gcsStream.on("data", (chunk) => {
          controller.enqueue(chunk);
        });

        gcsStream.on("end", () => {
          controller.close();
        });

        gcsStream.on("error", (error) => {
          log.error(`Error reading file stream from GCS: ${fileName}`, error);
          controller.error(error);
        });
      },

      cancel() {
        gcsStream.destroy();
      },
    });

    return webStream;
  } catch (error) {
    log.error(`Failed to get file stream for: ${fileName}`, error);
    return undefined;
  }
};

export const getTextFileFromStorage = async (fileName: string) => {
  const bucket = await getBucket();
  const file = bucket.file(fileName);
  const [content] = await file.download();
  return content.toString("utf8");
};
