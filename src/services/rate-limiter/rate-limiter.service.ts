import { log } from "@/services/logger";

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime?: number;
}

interface RateLimitConfig {
  limit: number;
  window: number; // in seconds
}

export class RateLimiterService {
  private redis: any; // TODO: Replace with proper Redis client type
  
  constructor(redisClient?: any) {
    this.redis = redisClient;
  }
  
  async checkLimit(
    userId: string, 
    action: string,
    customConfig?: RateLimitConfig
  ): Promise<RateLimitResult> {
    try {
      const config = customConfig || this.getConfigForAction(action);
      const key = `rate_limit:${action}:${userId}`;
      
      if (!this.redis) {
        // If Redis is not available, allow the request but log warning
        log.warn('Redis not available for rate limiting', { userId, action });
        return { allowed: true, remaining: config.limit };
      }
      
      const current = await this.redis.incr(key);
      
      if (current === 1) {
        await this.redis.expire(key, config.window);
      }
      
      const remaining = Math.max(0, config.limit - current);
      const allowed = current <= config.limit;
      
      // Get TTL for reset time
      const ttl = await this.redis.ttl(key);
      const resetTime = ttl > 0 ? Date.now() + (ttl * 1000) : undefined;
      
      log.debug('Rate limit check', {
        userId,
        action,
        current,
        limit: config.limit,
        allowed,
        remaining,
        resetTime,
      });
      
      return { allowed, remaining, resetTime };
    } catch (error) {
      log.error('Rate limiter error', { error, userId, action });
      // On error, allow the request but log the issue
      return { allowed: true, remaining: 0 };
    }
  }
  
  async resetLimit(userId: string, action: string): Promise<void> {
    try {
      if (!this.redis) return;
      
      const key = `rate_limit:${action}:${userId}`;
      await this.redis.del(key);
      
      log.debug('Rate limit reset', { userId, action });
    } catch (error) {
      log.error('Rate limit reset error', { error, userId, action });
    }
  }
  
  async getRemainingAttempts(userId: string, action: string): Promise<number> {
    try {
      if (!this.redis) return this.getConfigForAction(action).limit;
      
      const config = this.getConfigForAction(action);
      const key = `rate_limit:${action}:${userId}`;
      const current = await this.redis.get(key);
      
      return Math.max(0, config.limit - (parseInt(current || '0')));
    } catch (error) {
      log.error('Get remaining attempts error', { error, userId, action });
      return 0;
    }
  }
  
  private getConfigForAction(action: string): RateLimitConfig {
    const configs: Record<string, RateLimitConfig> = {
      'sse_connection': { limit: 5, window: 3600 }, // 5 connections per hour
      'sse_connection_per_minute': { limit: 10, window: 60 }, // 10 attempts per minute
      'sse_connection_per_hour': { limit: 100, window: 3600 }, // 100 attempts per hour
      'sse_heartbeat': { limit: 120, window: 3600 }, // 120 heartbeats per hour (2 per minute)
      'sse_broadcast': { limit: 1000, window: 3600 }, // 1000 broadcasts per hour
    };
    
    return configs[action] || { limit: 10, window: 3600 };
  }
}

// Singleton instance
let rateLimiterInstance: RateLimiterService | null = null;

export const initializeRateLimiter = (redisClient?: any): RateLimiterService => {
  rateLimiterInstance = new RateLimiterService(redisClient);
  return rateLimiterInstance;
};

export const getRateLimiter = (): RateLimiterService => {
  if (!rateLimiterInstance) {
    rateLimiterInstance = new RateLimiterService();
  }
  return rateLimiterInstance;
};

export default RateLimiterService; 
