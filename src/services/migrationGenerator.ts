import { QueryInterface, Sequelize, QueryTypes } from "sequelize";
import { db } from "@/schemas";
import { log } from "@/services/logger";

interface TableInfo {
  name: string;
  columns: ColumnInfo[];
  indexes: IndexInfo[];
  foreignKeys: ForeignKeyInfo[];
}

interface ColumnInfo {
  name: string;
  type: string;
  allowNull: boolean;
  defaultValue: any;
  primaryKey: boolean;
  autoIncrement: boolean;
  unique: boolean;
}

interface IndexInfo {
  name: string;
  fields: string[];
  unique: boolean;
}

interface ForeignKeyInfo {
  name: string;
  columnName: string;
  referencedTableName: string;
  referencedColumnName: string;
  onDelete?: string;
  onUpdate?: string;
}

export class MigrationGenerator {
  private queryInterface: QueryInterface;

  constructor() {
    this.queryInterface = db.getQueryInterface();
  }

  async getCurrentSchema(): Promise<TableInfo[]> {
    const allTables = await this.queryInterface.showAllTables();
    const schema: TableInfo[] = [];

    // Filter out system and internal tables
    const filteredTables = (allTables as string[]).filter((tableName) => {
      // Skip Sequelize's migration table
      if (tableName === "migrations") return false;

      // Skip PostgreSQL system tables and schemas
      if (tableName.startsWith("pg_")) return false;
      if (tableName.startsWith("information_schema")) return false;

      // Skip common system/internal tables
      const systemTables = [
        "spatial_ref_sys", // PostGIS
        "geography_columns", // PostGIS
        "geometry_columns", // PostGIS
        "raster_columns", // PostGIS
        "raster_overviews", // PostGIS
      ];
      if (systemTables.includes(tableName)) return false;

      return true;
    });

    log.info(`Processing ${filteredTables.length} application tables...`);

    for (const tableName of filteredTables) {
      try {
        const columns = await this.getTableColumns(tableName);
        const indexes = await this.getTableIndexes(tableName);
        const foreignKeys = await this.getTableForeignKeys(tableName);

        schema.push({
          name: tableName,
          columns,
          indexes,
          foreignKeys,
        });

        log.info(`✓ Processed table: ${tableName}`);
      } catch (error) {
        log.warn(`⚠️  Skipping table ${tableName}: ${error}`);
      }
    }

    return schema;
  }

  private async getTableColumns(tableName: string): Promise<ColumnInfo[]> {
    const attributes = await this.queryInterface.describeTable(tableName);

    return Object.entries(attributes).map(
      ([columnName, column]: [string, any]) => {
        // Clean up the default value
        let defaultValue = column.defaultValue;
        if (defaultValue === "NULL" || defaultValue === null) {
          defaultValue = null;
        }

        return {
          name: columnName,
          type: column.type,
          allowNull: column.allowNull,
          defaultValue,
          primaryKey: column.primaryKey || false,
          autoIncrement: column.autoIncrement || false,
          unique: column.unique || false,
        };
      }
    );
  }

  private async getTableIndexes(tableName: string): Promise<IndexInfo[]> {
    try {
      // Use direct PostgreSQL query instead of queryInterface.showIndex() to avoid duplicates
      const [results] = await db.query(
        `
        SELECT 
          i.relname as name,
          indisunique as is_unique,
          array_agg(a.attname ORDER BY c.ordinality) as columns
        FROM pg_index ix
        JOIN pg_class t ON t.oid = ix.indrelid
        JOIN pg_class i ON i.oid = ix.indexrelid
        JOIN pg_attribute a ON a.attrelid = t.oid
        JOIN unnest(ix.indkey) WITH ORDINALITY c(attnum, ordinality) ON c.attnum = a.attnum
        WHERE t.relname = ?
          AND NOT ix.indisprimary  -- Skip primary key indexes
          AND NOT i.relname LIKE 'pg_%'  -- Skip system indexes
          AND i.relname NOT LIKE '%_pkey'  -- Skip primary key constraint indexes
          AND i.relname NOT LIKE '%_constraint_%'  -- Skip constraint indexes
        GROUP BY i.relname, indisunique
        ORDER BY i.relname;
      `,
        {
          replacements: [tableName],
          type: QueryTypes.SELECT,
        }
      );

      return (results as any[]).map((row) => ({
        name: row.name,
        fields: row.columns,
        unique: row.is_unique,
      }));
    } catch (error) {
      log.warn(`Could not get indexes for table ${tableName}:`, error);
      return [];
    }
  }

  private async getTableForeignKeys(
    tableName: string
  ): Promise<ForeignKeyInfo[]> {
    try {
      // PostgreSQL-specific query to get foreign key constraints
      const [results] = await db.query(
        `
        SELECT 
          tc.constraint_name as name,
          kcu.column_name,
          ccu.table_name AS referenced_table_name,
          ccu.column_name AS referenced_column_name,
          rc.delete_rule as on_delete,
          rc.update_rule as on_update
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
          AND tc.table_schema = rc.constraint_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND tc.table_name = ?
      `,
        {
          replacements: [tableName],
          type: QueryTypes.SELECT,
        }
      );

      return (results as any[]).map((row) => ({
        name: row.name,
        columnName: row.column_name,
        referencedTableName: row.referenced_table_name,
        referencedColumnName: row.referenced_column_name,
        onDelete: row.on_delete !== "NO ACTION" ? row.on_delete : undefined,
        onUpdate: row.on_update !== "NO ACTION" ? row.on_update : undefined,
      }));
    } catch (error) {
      log.warn(`Could not get foreign keys for table ${tableName}:`, error);
      return [];
    }
  }

  compareSchemas(oldSchema: TableInfo[], newSchema: TableInfo[]): string {
    const migrations: string[] = [];
    const upMigrations: string[] = [];
    const downMigrations: string[] = [];

    // Find new tables
    const oldTableNames = oldSchema.map((t) => t.name);
    const newTableNames = newSchema.map((t) => t.name);

    const addedTables = newSchema.filter(
      (t) => !oldTableNames.includes(t.name)
    );
    const removedTables = oldSchema.filter(
      (t) => !newTableNames.includes(t.name)
    );

    // Generate create table migrations
    for (const table of addedTables) {
      upMigrations.push(this.generateCreateTable(table));
      downMigrations.unshift(
        `  // Only drop table if it exists
  const ${table.name.toLowerCase()}Exists = await queryInterface.tableExists("${
          table.name
        }");
  if (${table.name.toLowerCase()}Exists) {
    await queryInterface.dropTable("${table.name}");
  }`
      );
    }

    // Generate drop table migrations
    for (const table of removedTables) {
      upMigrations.push(
        `  // Only drop table if it exists
  const ${table.name.toLowerCase()}Exists = await queryInterface.tableExists("${
          table.name
        }");
  if (${table.name.toLowerCase()}Exists) {
    await queryInterface.dropTable("${table.name}");
  }`
      );
      downMigrations.unshift(this.generateCreateTable(table));
    }

    // Compare existing tables for all changes (columns, indexes, foreign keys)
    for (const newTable of newSchema) {
      const oldTable = oldSchema.find((t) => t.name === newTable.name);
      if (!oldTable) continue;

      // Compare columns (add/remove/modify)
      const columnChanges = this.compareTableColumns(oldTable, newTable);
      upMigrations.push(...columnChanges.up);
      downMigrations.unshift(...columnChanges.down);

      // Compare indexes
      const indexChanges = this.compareTableIndexes(oldTable, newTable);
      upMigrations.push(...indexChanges.up);
      downMigrations.unshift(...indexChanges.down);

      // Compare foreign keys
      const foreignKeyChanges = this.compareTableForeignKeys(
        oldTable,
        newTable
      );
      upMigrations.push(...foreignKeyChanges.up);
      downMigrations.unshift(...foreignKeyChanges.down);
    }

    return this.generateMigrationFile(upMigrations, downMigrations);
  }

  private generateCreateTable(table: TableInfo): string {
    const columns = table.columns
      .map((col) => {
        const attrs: string[] = [];
        attrs.push(`type: DataTypes.${this.mapSequelizeType(col.type)}`);

        if (col.primaryKey) attrs.push("primaryKey: true");
        if (col.autoIncrement) attrs.push("autoIncrement: true");
        if (!col.allowNull) attrs.push("allowNull: false");
        if (col.unique && !col.primaryKey) attrs.push("unique: true"); // Don't add unique to primary keys

        // Handle default values properly
        if (col.defaultValue !== null && col.defaultValue !== undefined) {
          let defaultVal = col.defaultValue;

          // Handle PostgreSQL-specific defaults
          if (typeof defaultVal === "string") {
            if (
              defaultVal.includes("nextval(") ||
              defaultVal.includes("gen_random_uuid()")
            ) {
              // Skip sequence and UUID defaults - they'll be handled by autoIncrement or DataTypes.UUIDV4
              return `    ${col.name}: {\n      ${attrs.join(
                ",\n      "
              )},\n    }`;
            } else if (
              defaultVal === "now()" ||
              defaultVal.includes("CURRENT_TIMESTAMP")
            ) {
              attrs.push(`defaultValue: DataTypes.NOW`);
            } else if (defaultVal === "true" || defaultVal === "false") {
              attrs.push(`defaultValue: ${defaultVal}`);
            } else {
              attrs.push(`defaultValue: ${JSON.stringify(defaultVal)}`);
            }
          } else {
            attrs.push(`defaultValue: ${JSON.stringify(defaultVal)}`);
          }
        }

        return `    ${col.name}: {\n      ${attrs.join(",\n      ")},\n    }`;
      })
      .join(",\n");

    // Include table existence check for safety
    let migration = `  // Check and create ${table.name} table
  const ${table.name.toLowerCase()}Exists = await queryInterface.tableExists("${
      table.name
    }");
  if (!${table.name.toLowerCase()}Exists) {
    await queryInterface.createTable("${table.name}", {
${columns},
    });
  }`;

    // Add indexes (only if there are any)
    if (table.indexes.length > 0) {
      for (const index of table.indexes) {
        migration += `\n  // Add index only if table was created
  if (!${table.name.toLowerCase()}Exists) {
    await queryInterface.addIndex("${table.name}", ${JSON.stringify(
          index.fields
        )}, {
      name: "${index.name}",
      unique: ${index.unique},
    });
  }`;
      }
    }

    // Add foreign keys (only if there are any)
    if (table.foreignKeys.length > 0) {
      for (const fk of table.foreignKeys) {
        migration += `\n  // Add foreign key only if table was created
  if (!${table.name.toLowerCase()}Exists) {
    await queryInterface.addConstraint("${table.name}", {
      fields: ["${fk.columnName}"],
      type: "foreign key",
      name: "${fk.name}",
      references: {
        table: "${fk.referencedTableName}",
        field: "${fk.referencedColumnName}",
      },${fk.onDelete ? `\n      onDelete: "${fk.onDelete}",` : ""}${
          fk.onUpdate ? `\n      onUpdate: "${fk.onUpdate}",` : ""
        }
    });
  }`;
      }
    }

    return migration;
  }

  private compareTableColumns(oldTable: TableInfo, newTable: TableInfo) {
    const up: string[] = [];
    const down: string[] = [];

    const oldColumns = oldTable.columns.map((c) => c.name);
    const newColumns = newTable.columns.map((c) => c.name);

    // Added columns
    const addedColumns = newTable.columns.filter(
      (c) => !oldColumns.includes(c.name)
    );
    for (const col of addedColumns) {
      const columnDef = this.generateColumnDefinition(col);
      up.push(`  // Add new column
  await queryInterface.addColumn("${newTable.name}", "${col.name}", {
${columnDef}
  });`);
      down.unshift(
        `  await queryInterface.removeColumn("${newTable.name}", "${col.name}");`
      );
    }

    // Removed columns
    const removedColumns = oldTable.columns.filter(
      (c) => !newColumns.includes(c.name)
    );
    for (const col of removedColumns) {
      up.push(
        `  // Remove column
  await queryInterface.removeColumn("${newTable.name}", "${col.name}");`
      );
      const columnDef = this.generateColumnDefinition(col);
      down.unshift(`  await queryInterface.addColumn("${newTable.name}", "${col.name}", {
${columnDef}
  });`);
    }

    // Modified columns (same name but different properties)
    const commonColumns = newTable.columns.filter((newCol) =>
      oldColumns.includes(newCol.name)
    );

    for (const newCol of commonColumns) {
      const oldCol = oldTable.columns.find((c) => c.name === newCol.name);
      if (!oldCol) continue;

      if (this.columnsAreDifferent(oldCol, newCol)) {
        const newColumnDef = this.generateColumnDefinition(newCol);
        const oldColumnDef = this.generateColumnDefinition(oldCol);

        up.push(`  // Modify column ${newCol.name}
  await queryInterface.changeColumn("${newTable.name}", "${newCol.name}", {
${newColumnDef}
  });`);

        down.unshift(`  // Revert column ${newCol.name}
  await queryInterface.changeColumn("${newTable.name}", "${newCol.name}", {
${oldColumnDef}
  });`);
      }
    }

    return { up, down };
  }

  private generateColumnDefinition(col: ColumnInfo): string {
    const attrs: string[] = [];
    attrs.push(`    type: DataTypes.${this.mapSequelizeType(col.type)}`);

    if (col.primaryKey) attrs.push("    primaryKey: true");
    if (col.autoIncrement) attrs.push("    autoIncrement: true");
    if (!col.allowNull) attrs.push("    allowNull: false");
    if (col.unique && !col.primaryKey) attrs.push("    unique: true");

    // Handle default values properly
    if (col.defaultValue !== null && col.defaultValue !== undefined) {
      let defaultVal = col.defaultValue;

      if (typeof defaultVal === "string") {
        if (
          defaultVal.includes("nextval(") ||
          defaultVal.includes("gen_random_uuid()")
        ) {
          // Skip sequence and UUID defaults
        } else if (
          defaultVal === "now()" ||
          defaultVal.includes("CURRENT_TIMESTAMP")
        ) {
          attrs.push("    defaultValue: DataTypes.NOW");
        } else if (defaultVal === "true" || defaultVal === "false") {
          attrs.push(`    defaultValue: ${defaultVal}`);
        } else {
          attrs.push(`    defaultValue: ${JSON.stringify(defaultVal)}`);
        }
      } else {
        attrs.push(`    defaultValue: ${JSON.stringify(defaultVal)}`);
      }
    }

    return attrs.join(",\n");
  }

  private columnsAreDifferent(oldCol: ColumnInfo, newCol: ColumnInfo): boolean {
    return (
      oldCol.type !== newCol.type ||
      oldCol.allowNull !== newCol.allowNull ||
      oldCol.defaultValue !== newCol.defaultValue ||
      oldCol.unique !== newCol.unique ||
      oldCol.primaryKey !== newCol.primaryKey ||
      oldCol.autoIncrement !== newCol.autoIncrement
    );
  }

  private compareTableIndexes(oldTable: TableInfo, newTable: TableInfo) {
    const up: string[] = [];
    const down: string[] = [];

    const oldIndexNames = oldTable.indexes.map((i) => i.name);
    const newIndexNames = newTable.indexes.map((i) => i.name);

    // Added indexes
    const addedIndexes = newTable.indexes.filter(
      (i) => !oldIndexNames.includes(i.name)
    );
    for (const index of addedIndexes) {
      up.push(`  // Add new index
  await queryInterface.addIndex("${newTable.name}", ${JSON.stringify(
        index.fields
      )}, {
    name: "${index.name}",
    unique: ${index.unique},
  });`);
      down.unshift(
        `  await queryInterface.removeIndex("${newTable.name}", "${index.name}");`
      );
    }

    // Removed indexes
    const removedIndexes = oldTable.indexes.filter(
      (i) => !newIndexNames.includes(i.name)
    );
    for (const index of removedIndexes) {
      up.push(`  // Remove index
  await queryInterface.removeIndex("${newTable.name}", "${index.name}");`);
      down.unshift(`  await queryInterface.addIndex("${
        newTable.name
      }", ${JSON.stringify(index.fields)}, {
    name: "${index.name}",
    unique: ${index.unique},
  });`);
    }

    // Modified indexes (same name but different properties)
    const commonIndexes = newTable.indexes.filter((newIdx) =>
      oldIndexNames.includes(newIdx.name)
    );

    for (const newIdx of commonIndexes) {
      const oldIdx = oldTable.indexes.find((i) => i.name === newIdx.name);
      if (!oldIdx) continue;

      if (this.indexesAreDifferent(oldIdx, newIdx)) {
        up.push(`  // Modify index ${newIdx.name}
  await queryInterface.removeIndex("${newTable.name}", "${newIdx.name}");
  await queryInterface.addIndex("${newTable.name}", ${JSON.stringify(
          newIdx.fields
        )}, {
    name: "${newIdx.name}",
    unique: ${newIdx.unique},
  });`);

        down.unshift(`  // Revert index ${newIdx.name}
  await queryInterface.removeIndex("${newTable.name}", "${newIdx.name}");
  await queryInterface.addIndex("${newTable.name}", ${JSON.stringify(
          oldIdx.fields
        )}, {
    name: "${oldIdx.name}",
    unique: ${oldIdx.unique},
  });`);
      }
    }

    return { up, down };
  }

  private indexesAreDifferent(oldIdx: IndexInfo, newIdx: IndexInfo): boolean {
    return (
      JSON.stringify(oldIdx.fields.sort()) !==
        JSON.stringify(newIdx.fields.sort()) || oldIdx.unique !== newIdx.unique
    );
  }

  private compareTableForeignKeys(oldTable: TableInfo, newTable: TableInfo) {
    const up: string[] = [];
    const down: string[] = [];

    const oldFkNames = oldTable.foreignKeys.map((fk) => fk.name);
    const newFkNames = newTable.foreignKeys.map((fk) => fk.name);

    // Added foreign keys
    const addedForeignKeys = newTable.foreignKeys.filter(
      (fk) => !oldFkNames.includes(fk.name)
    );
    for (const fk of addedForeignKeys) {
      up.push(`  // Add new foreign key
  await queryInterface.addConstraint("${newTable.name}", {
    fields: ["${fk.columnName}"],
    type: "foreign key",
    name: "${fk.name}",
    references: {
      table: "${fk.referencedTableName}",
      field: "${fk.referencedColumnName}",
    },${fk.onDelete ? `\n    onDelete: "${fk.onDelete}",` : ""}${
        fk.onUpdate ? `\n    onUpdate: "${fk.onUpdate}",` : ""
      }
  });`);
      down.unshift(
        `  await queryInterface.removeConstraint("${newTable.name}", "${fk.name}");`
      );
    }

    // Removed foreign keys
    const removedForeignKeys = oldTable.foreignKeys.filter(
      (fk) => !newFkNames.includes(fk.name)
    );
    for (const fk of removedForeignKeys) {
      up.push(`  // Remove foreign key
  await queryInterface.removeConstraint("${newTable.name}", "${fk.name}");`);
      down.unshift(`  await queryInterface.addConstraint("${newTable.name}", {
    fields: ["${fk.columnName}"],
    type: "foreign key",
    name: "${fk.name}",
    references: {
      table: "${fk.referencedTableName}",
      field: "${fk.referencedColumnName}",
    },${fk.onDelete ? `\n    onDelete: "${fk.onDelete}",` : ""}${
        fk.onUpdate ? `\n    onUpdate: "${fk.onUpdate}",` : ""
      }
  });`);
    }

    // Modified foreign keys (same name but different properties)
    const commonForeignKeys = newTable.foreignKeys.filter((newFk) =>
      oldFkNames.includes(newFk.name)
    );

    for (const newFk of commonForeignKeys) {
      const oldFk = oldTable.foreignKeys.find((fk) => fk.name === newFk.name);
      if (!oldFk) continue;

      if (this.foreignKeysAreDifferent(oldFk, newFk)) {
        up.push(`  // Modify foreign key ${newFk.name}
  await queryInterface.removeConstraint("${newTable.name}", "${newFk.name}");
  await queryInterface.addConstraint("${newTable.name}", {
    fields: ["${newFk.columnName}"],
    type: "foreign key",
    name: "${newFk.name}",
    references: {
      table: "${newFk.referencedTableName}",
      field: "${newFk.referencedColumnName}",
    },${newFk.onDelete ? `\n    onDelete: "${newFk.onDelete}",` : ""}${
          newFk.onUpdate ? `\n    onUpdate: "${newFk.onUpdate}",` : ""
        }
  });`);

        down.unshift(`  // Revert foreign key ${newFk.name}
  await queryInterface.removeConstraint("${newTable.name}", "${newFk.name}");
  await queryInterface.addConstraint("${newTable.name}", {
    fields: ["${oldFk.columnName}"],
    type: "foreign key",
    name: "${oldFk.name}",
    references: {
      table: "${oldFk.referencedTableName}",
      field: "${oldFk.referencedColumnName}",
    },${oldFk.onDelete ? `\n    onDelete: "${oldFk.onDelete}",` : ""}${
          oldFk.onUpdate ? `\n    onUpdate: "${oldFk.onUpdate}",` : ""
        }
  });`);
      }
    }

    return { up, down };
  }

  private foreignKeysAreDifferent(
    oldFk: ForeignKeyInfo,
    newFk: ForeignKeyInfo
  ): boolean {
    return (
      oldFk.columnName !== newFk.columnName ||
      oldFk.referencedTableName !== newFk.referencedTableName ||
      oldFk.referencedColumnName !== newFk.referencedColumnName ||
      oldFk.onDelete !== newFk.onDelete ||
      oldFk.onUpdate !== newFk.onUpdate
    );
  }

  private mapSequelizeType(type: string): string {
    // Normalize the type string - remove extra spaces and convert to uppercase
    const normalizedType = type.trim().toUpperCase();

    // Map database types to Sequelize DataTypes with proper handling
    const typeMap: Record<string, string> = {
      INTEGER: "INTEGER",
      BIGINT: "BIGINT",
      SMALLINT: "SMALLINT",
      SERIAL: "INTEGER", // PostgreSQL SERIAL -> INTEGER with autoIncrement
      BIGSERIAL: "BIGINT", // PostgreSQL BIGSERIAL -> BIGINT with autoIncrement
      VARCHAR: "STRING",
      "CHARACTER VARYING": "STRING",
      CHAR: "CHAR",
      CHARACTER: "CHAR",
      TEXT: "TEXT",
      BOOLEAN: "BOOLEAN",
      BOOL: "BOOLEAN",
      TIMESTAMP: "DATE",
      "TIMESTAMP WITH TIME ZONE": "DATE",
      "TIMESTAMP WITHOUT TIME ZONE": "DATE",
      TIMESTAMPTZ: "DATE",
      DATE: "DATEONLY",
      TIME: "TIME",
      "TIME WITH TIME ZONE": "TIME",
      "TIME WITHOUT TIME ZONE": "TIME",
      JSONB: "JSONB",
      JSON: "JSON",
      UUID: "UUID",
      DECIMAL: "DECIMAL",
      NUMERIC: "DECIMAL",
      REAL: "REAL",
      "DOUBLE PRECISION": "DOUBLE",
      FLOAT: "FLOAT",
      BYTEA: "BLOB",
      MONEY: "DECIMAL",
    };

    // Extract base type (remove length, precision, etc.)
    let baseType = normalizedType.split("(")[0].split("[")[0].trim();

    // Handle array types
    if (normalizedType.includes("[]")) {
      baseType = baseType.replace("[]", "");
      const elementType = typeMap[baseType] || "STRING";
      return `ARRAY(DataTypes.${elementType})`;
    }

    // Handle VARCHAR/CHARACTER VARYING with length
    if (
      (baseType === "VARCHAR" || baseType === "CHARACTER VARYING") &&
      normalizedType.includes("(")
    ) {
      const length = normalizedType.match(/\((\d+)\)/)?.[1];
      return length ? `STRING(${length})` : "STRING";
    }

    // Handle CHAR/CHARACTER with length
    if (
      (baseType === "CHAR" || baseType === "CHARACTER") &&
      normalizedType.includes("(")
    ) {
      const length = normalizedType.match(/\((\d+)\)/)?.[1];
      return length ? `CHAR(${length})` : "CHAR";
    }

    // Handle DECIMAL/NUMERIC with precision and scale
    if (
      (baseType === "DECIMAL" || baseType === "NUMERIC") &&
      normalizedType.includes("(")
    ) {
      const match = normalizedType.match(/\((\d+)(?:,\s*(\d+))?\)/);
      if (match) {
        const precision = match[1];
        const scale = match[2];
        return scale
          ? `DECIMAL(${precision}, ${scale})`
          : `DECIMAL(${precision})`;
      }
    }

    // Return mapped type or default to STRING
    return typeMap[baseType] || "STRING";
  }

  private generateMigrationFile(
    upMigrations: string[],
    downMigrations: string[]
  ): string {
    const upContent =
      upMigrations.length > 0
        ? upMigrations.join("\n  ")
        : "// No changes detected";

    const downContent =
      downMigrations.length > 0
        ? downMigrations.join("\n  ")
        : "// No rollback needed";

    return `import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  ${upContent}
}

export async function down(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  ${downContent}
}
`;
  }
}
