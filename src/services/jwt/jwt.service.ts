import { jwtSecret } from "@/config";
import { ProjectSharedLink } from "@/schemas/project/ProjectSharedLink.model";
import jwt from "jsonwebtoken";

export const verifyJwtToken = async <T>(token: string): Promise<T> => {
    const decoded = jwt.verify(token, jwtSecret);
    return decoded as T;
};

export const createJwtToken = async <T>(payload: T) => {
    const token = jwt.sign(payload as any, jwtSecret);
    return token;
};

export const createProjectSharedLinkToken = async (sharedLink: ProjectSharedLink) => {
    return createJwtToken({
        projectId: sharedLink.projectId,
        code: sharedLink.code,
        expiredAt: sharedLink.expiredAt,
        maxAccessCount: sharedLink.maxAccessCount,
    });
};

export const verifyProjectSharedLinkToken = async (token: string) => {
   try {
    const decoded = jwt.verify(token, jwtSecret);
    return decoded as ProjectSharedLink;
   } catch (error) {
    return null;
   }
};