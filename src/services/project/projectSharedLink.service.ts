import { ProjectSharedLink, ProjectSharedLinkModel } from "@/schemas/project/ProjectSharedLink.model";
import { ulid } from "ulidx";
import { createProjectSharedLinkToken, verifyProjectSharedLinkToken } from "../jwt/jwt.service";
import { createLogger } from "../logger";
import { Op } from "sequelize";

const logger = createLogger("projectSharedLinkService");


export const getProjectSharedLinkById = async (projectId: string) => {
    const projectSharedLink = await ProjectSharedLinkModel.xFind1({
        projectId,
    });

    if (!projectSharedLink) {
        return {
            success: false,
            message: "Project shared link not found",
            statusCode: 404,
        };
    }

    return {
        success: true,
        message: "Project shared link retrieved",
        statusCode: 200,
        data: projectSharedLink,
    };
}


interface CreateProjectSharedLinkPayload {
    projectId: string;
    expriedInDays?: number;
    maxAccessCount?: number;
    forceCreate?: boolean;
}
/**
 * Create a new project shared link
 * 
 * This function will create a new project shared link and return the token
 * 
 * The token will be used to access the project shared link
 * 
 * The token will be valid for 7 days
 * 
 * The token will be used to access the project shared link
 * 
 * @param projectId - The id of the project to create the shared link for
 * @returns The created project shared link
 */
export async function createProjectSharedLink({
    projectId,
    expriedInDays = 7,
    maxAccessCount = 30,
    forceCreate = false,
}: CreateProjectSharedLinkPayload) {
    let projectSharedLink: ProjectSharedLink | null = null;
    let message = "Project shared link created";

    logger.info(`Checking if project shared link already exists for project ${projectId}...`);
    projectSharedLink = await ProjectSharedLinkModel.xFind1({
        projectId,
        expiredAt: {
            [Op.gt]: new Date()
        }
    });

    if (!projectSharedLink || forceCreate) {
        logger.info(`No existing project shared link found, creating new one for project ${projectId}...`);
        projectSharedLink = await ProjectSharedLinkModel.xCreate({
            projectId,
            code: ulid(),
            expiredAt: new Date(Date.now() + expriedInDays * 24 * 60 * 60 * 1000),
            maxAccessCount,
        });
    } else {
        message = `Project shared link already exists, using existing one [${projectSharedLink.id}]`;
        logger.info(message);
    }

    const token = await createProjectSharedLinkToken(projectSharedLink);

    logger.info(`Project shared link created for project ${projectId}, token: ${token}`);

    return {
        success: true,
        message,
        statusCode: 200,
        data: {
            token,
            projectId: projectSharedLink.projectId,
            expiredAt: projectSharedLink.expiredAt,
            maxAccessCount: projectSharedLink.maxAccessCount,
        },
    };
};


interface VerifyProjectSharedLinkPayload {
    projectId: string;
    token: string;
}

/**
 * Verify a project shared link token
 * 
 * This function will verify a project shared link token and return the project shared link
 * 
 * @param token - The token to verify
 * @returns The verified project shared link
 */
export async function verifySharedLinkToken({ token }: VerifyProjectSharedLinkPayload) {
    logger.info(`Verifying project shared link token: ${token}...`);
    const verifiedToken = await verifyProjectSharedLinkToken(token);

    const projectSharedLink = await ProjectSharedLinkModel.xFind1({
        code: verifiedToken.code,
    });

    if (!projectSharedLink) {
        logger.warn(`Cannot verify project shared link, token: ${token}`);
        return {
            success: false,
            message: "Cannot verify project shared link",
            statusCode: 403,
        };
    }

    if (projectSharedLink.expiredAt < new Date()) {
        logger.warn(`Project shared link expired, token: ${token}`);
        return {
            success: false,
            message: "Project shared link expired",
            statusCode: 403,
        };
    }

    if (projectSharedLink.maxAccessCount <= projectSharedLink.currentAccessCount) {
        logger.warn(`Project shared link access limit reached, token: ${token}`);
        return {
            success: false,
            message: "Project shared link access limit reached",
            statusCode: 403,
        };
    }

    logger.info(`Project shared link verified, token: ${token}`);
    return {
        success: true,
        message: "Project shared link verified",
        statusCode: 200,
        data: projectSharedLink,
    };
}

export const getAllProjectSharedLinksByProjectId = async (projectId: string) => {
    const projectSharedLinks = await ProjectSharedLinkModel.xFind({
        projectId,
    });

    return {
        success: true,
        message: "Project shared links retrieved",
        statusCode: 200,
        data: projectSharedLinks,
    };
}

export const incrementProjectSharedLinkAccessCount = async (projectSharedLinkId: string) => {
    logger.info(`Incrementing project shared link access count for project shared link ${projectSharedLinkId}...`);

    const getProjectSharedLinkResult = await getProjectSharedLinkById(projectSharedLinkId);
    if (!getProjectSharedLinkResult.success || !getProjectSharedLinkResult.data) {
        logger.warn(`Cannot increment project shared link access count, project shared link ${projectSharedLinkId} not found`);
        return getProjectSharedLinkResult;
    }

    const projectSharedLink = getProjectSharedLinkResult.data;
    const updatedProjectSharedLink = await ProjectSharedLinkModel.xUpdate({
        currentAccessCount: projectSharedLink.currentAccessCount + 1,
    }, {
        id: projectSharedLinkId,
    });

    logger.info(`Project shared link access count incremented for project shared link ${projectSharedLinkId}`);

    return {
        success: true,
        message: "Project shared link access count incremented",
        statusCode: 200,
        data: updatedProjectSharedLink,
    };
}