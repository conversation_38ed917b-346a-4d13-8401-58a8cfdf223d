import { models } from "@/schemas";
import {
  ProjectMemberRole
} from "@/schemas/project/ProjectMember.model";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import { getResourceData } from "@/schemas/resource/utils";
import { log } from "@/services/logger";
import {
  GetProjectResourcesParams,
  ServiceResponse,
  ServiceResponseList
} from "@/types";
import { ResourceData } from "@/types/resources";
import {
  audioExts,
  documentExts,
  EnhancedFileType,
  getEnhancedFileType,
  imageExts,
  pdfExts,
  textFileExts,
  videoExts
} from "@/utils/file";
import { createSuccessResponse, createErrorResponse } from "@/utils/response";
import { Op } from "sequelize";

/**
 * Map file extension to enhanced file type for specific viewing modes
 * @deprecated Use getEnhancedFileType from utils/file directly
 */
const getFileType = (filename: string): EnhancedFileType => {
  return getEnhancedFileType(filename);
};

/**
 * Map TranscriptStatus to processing status
 */
const mapProcessingStatus = (status?: TranscriptStatus): 'completed' | 'processing' | 'failed' | 'pending' => {
  switch (status) {
    case TranscriptStatus.Completed:
      return 'completed';
    case TranscriptStatus.InProgress:
      return 'processing';
    case TranscriptStatus.Failed:
      return 'failed';
    case TranscriptStatus.UnTranscript:
    default:
      return 'pending';
  }
};

/**
 * Build file type WHERE conditions for database filtering using enhanced file types
 */
const buildFileTypeConditions = (fileType: string): any => {
  const allowedTypes = fileType.split(',').map(t => t.trim());
  const conditions: any[] = [];

  // Map enhanced file types to their extension maps
  const fileExtensionMaps = {
    video: videoExts,
    audio: audioExts,
    image: imageExts,
    pdf: pdfExts,
    document: documentExts,
    text: textFileExts,
  };

  allowedTypes.forEach(type => {
    const extensionMap = fileExtensionMaps[type];
    if (extensionMap) {
      // Extensions already include the dot (e.g., ".mp4", ".jpg")
      const extensionConditions = Object.keys(extensionMap).map(ext => ({
        name: { [Op.iLike]: `%${ext}` }
      }));
      conditions.push({ [Op.or]: extensionConditions });
    }
  });

  return conditions.length > 0 ? { [Op.or]: conditions } : null;
};

/**
 * Convert Sequelize sort field to database column
 */
const getSortField = (sortBy: string): string => {
  switch (sortBy) {
    case 'name':
      return 'name';
    case 'fileSize':
      return 'fileSize';
    case 'lastModified':
      return 'fileLastModified';
    case 'dateAdded':
    default:
      return 'createdAt';
  }
};

/**
 * Get the user permissions for a resource
 */
const getResourceUserPermissions = (
  resource: any,
  userId: string
) => {
  const projectId = resource.projectId;
  const project = resource.project;
  const isProjectOwner = project?.createdById === userId;

  if (!project || !projectId || isProjectOwner) {
    return {
      canView: true,
      canEdit: true,
      canComment: true,
    };
  }

  const projectMember = project?.members?.find(
    (member) => member.userId === userId
  );

  const memberRole = projectMember?.role;
  const canEdit = memberRole === ProjectMemberRole.EDITOR;
  const canComment = canEdit || memberRole === ProjectMemberRole.COMMENTER;

  return {
    canView: true,
    canEdit,
    canComment,
  };
};

/**
 * Get project resources with pagination and filtering
 */
export const getProjectResources = async (
  params: GetProjectResourcesParams
): Promise<ServiceResponseList<ResourceData & { type: EnhancedFileType; userPermissions: any }>> => {
  try {
    const {
      projectId,
      userId,
      page,
      limit,
      search,
      fileType,
      processingStatus,
      sortBy = 'dateAdded',
      sortOrder = 'desc'
    } = params;

    // Build database WHERE conditions
    const resourceWhere: any = {
      projectId,
      folderId: null, // Only get resources not in folders (root level)
      deletedAt: null,
    };

    // Add search filter at database level
    if (search) {
      resourceWhere.name = {
        [Op.iLike]: `%${search}%`
      };
    }

    // Add file type filter at database level
    if (fileType) {
      const fileTypeConditions = buildFileTypeConditions(fileType);
      if (fileTypeConditions) {
        resourceWhere[Op.and] = resourceWhere[Op.and] 
          ? [...resourceWhere[Op.and], fileTypeConditions]
          : [fileTypeConditions];
      }
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build sort order
    const sortField = getSortField(sortBy);
    const sortDirection = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    
    // Special handling for processingStatus sorting (needs to be done in JavaScript)
    const needsClientSideSort = sortBy === 'processingStatus' || processingStatus;
    const dbOrder: [string, string][] = needsClientSideSort ? [['createdAt', 'DESC']] : [[sortField, sortDirection]];

    // Execute query to get resources with project and member information
    const { rows: resources, count: totalCount } = await models.Resource.findAndCountAll({
      where: resourceWhere,
      limit: needsClientSideSort ? undefined : limit, 
      offset: needsClientSideSort ? undefined : offset,
      order: dbOrder,
      attributes: {
        exclude: ["ffprobe"],
      },
      include: [
        {
          model: models.Project,
          attributes: ["id", "name", "createdById"],
          as: "project",
          required: false,
          include: [
            {
              model: models.ProjectMember,
              attributes: ["id", "role", "userId"],
              as: "members",
              where: {
                userId,
              },
              required: false,
            },
          ],
        },
        {
          model: models.ProjectFolder,
          attributes: ["id", "name"],
          as: "folder",
          required: false,
        },
      ],
    });

    if (resources.length === 0) {
      return {
        success: true,
        message: 'Project resources retrieved successfully',
        statusCode: 200,
        data: {
          items: [],
          page,
          limit,
          total: 0
        },
      };
    }

    // Convert Sequelize objects to plain JSON to avoid circular references
    const plainResources = resources.map(resource => resource.toJSON());

    // Format resources using getResourceData and add user permissions
    let formattedResources = await Promise.all(
      plainResources.map(async (resourceJSON: any) => {
        // Create a clean resource object for getResourceData (without associations)
        const cleanResource = { ...resourceJSON };
        delete cleanResource.project;
        delete cleanResource.folder;
        
        // Get the full resource data
        const resourceData = await getResourceData(cleanResource);
        
        // Calculate user permissions using the original JSON with project info
        const userPermissions = getResourceUserPermissions(resourceJSON, userId);
        
        // Get file type using getEnhancedFileType from utils/file
        const type = getEnhancedFileType(resourceData.name);
        
        return {
          ...resourceData,
          type,
          userPermissions,
        };
      })
    );

    // Apply processing status filter if specified
    if (processingStatus) {
      formattedResources = formattedResources.filter(resource => 
        mapProcessingStatus(resource.transcriptionJobStatus) === processingStatus
      );
    }

    // Apply client-side sorting if needed
    if (sortBy === 'processingStatus') {
      const statusOrder = { 'completed': 4, 'processing': 3, 'failed': 2, 'pending': 1 };
      formattedResources.sort((a, b) => {
        const aValue = statusOrder[mapProcessingStatus(a.transcriptionJobStatus)] || 0;
        const bValue = statusOrder[mapProcessingStatus(b.transcriptionJobStatus)] || 0;
        
        if (sortDirection === 'ASC') {
          return aValue - bValue;
        } else {
          return bValue - aValue;
        }
      });
    }

    // Apply pagination if client-side filtering was used
    if (needsClientSideSort) {
      const totalFiltered = formattedResources.length;
      formattedResources = formattedResources.slice(offset, offset + limit);
      
      return {
        success: true,
        message: 'Project resources retrieved successfully',
        statusCode: 200,
        data: {
          items: formattedResources,
          page,
          limit,
          total: totalFiltered,
        },
      };
    }

    return {
      success: true,
      message: 'Project resources retrieved successfully',
      statusCode: 200,
      data: {
        items: formattedResources,
        page,
        limit,
        total: totalCount,
      },
    };

  } catch (error) {
    log.error('Failed to get project resources', { 
      error: error.message, 
      stack: error.stack,
      projectId: params.projectId,
      userId: params.userId 
    });

    return {
      success: false,
      message: 'Failed to retrieve project resources',
      statusCode: 500,
      data: {
        items: [],
        page: 1,
        limit: 10,
        total: 0
      }
    };
  }
}; 

/**
 * Get all resource IDs for a project without pagination
 * 
 * @param projectId The project ID
 * @returns A service response with an array of resource IDs
 */
export const getAllProjectResourceIds = async (
  projectId: string
): Promise<ServiceResponse<string[]>> => {
  try {
    // Find all resources in the project
    const resources = await models.Resource.findAll({
      where: { projectId },
      attributes: ['id']
    });

    // Extract just the IDs using get() method to safely access the id property
    const resourceIds = resources.map(resource => resource.get('id') as string);

    return createSuccessResponse(
      resourceIds,
      'Project resource IDs retrieved successfully',
      200
    );
  } catch (error) {
    log.error('Error getting project resource IDs', { error, projectId });
    
    return createErrorResponse(
      'Failed to retrieve project resource IDs',
      500
    );
  }
}; 
