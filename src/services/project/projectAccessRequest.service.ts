import { Project } from "@/schemas/project/Project.model";
import { ProjectAccessRequestModel, ProjectAccessRequestStatus } from "@/schemas/project/ProjectAccessRequest.model";
import * as emailService from "../email";
import { User } from '@firebase/auth'
import { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import { addProjectMember } from "./projectMember.service";
import { getProjectSharedLinkById } from "./projectSharedLink.service";
import { createLogger } from "../logger";
import { getFirebaseListOfUsersByIds, getFirebaseUserByEmail, getFirebaseUserById } from "../firebase";
import { fireBaseUserListToIndexMap } from "@/utils";
import { getProjectById } from "./project.service";
import { getSSEService } from "@/services/sse/sse.service";
import { SSEEventType } from "@/types";
import ProjectInvitationModel, { InvitationStatus } from "@/schemas/project/ProjectInvitation.model";
const logger = createLogger("projectAccessRequestService");

export const getProjectAccessRequestById = async (id: string) => {
    const projectAccessRequest = await ProjectAccessRequestModel.xFind1({
        id,
    });

    if (!projectAccessRequest) {
        return {
            success: false,
            message: "Project access request not found",
            statusCode: 404,
        };
    }

    return {
        success: true,
        message: "Project access request found",
        statusCode: 200,
        data: projectAccessRequest,
    };
}

interface CreateProjectAccessRequestPayload {
    requestedUser: User;
    approverUser: User;
    project: Partial<Project>;
    forceSendEmail?: boolean;
}
/**
 * Create a project access request, this will send an email to the approver user, return existing project access request if it already exists
 * @param project - The project to create the access request for
 * @param requestedUser - The user requesting access
 * @param approverUser - The user approving the access request
 * @param forceSendEmail - Whether to force send the email
 * @returns The created project access request
 */
export const createProjectAccessRequest = async ({ project, requestedUser, approverUser, forceSendEmail = true }: CreateProjectAccessRequestPayload) => {
    const getProjectSharedLinkResult = await getProjectSharedLinkById(project.id);

    if (!getProjectSharedLinkResult.success) {
        return getProjectSharedLinkResult;
    }

    const projectSharedLink = getProjectSharedLinkResult.data;

    logger.info("Checking if project access request already exists " + JSON.stringify({ projectId: project.id, sharedLinkId: projectSharedLink.id, requestedById: requestedUser.uid }));

    let ProjectAccessRequest = await ProjectAccessRequestModel.xFind1({
        projectId: project.id,
        sharedLinkId: projectSharedLink.id,
        requestedById: requestedUser.uid,
    });

    let message = "", isNew = false;

    if (!ProjectAccessRequest) {
        logger.info("Creating project access request " + JSON.stringify({ projectId: project.id, sharedLinkId: projectSharedLink.id, requestedById: requestedUser.uid }));
        ProjectAccessRequest = await ProjectAccessRequestModel.xCreate({
            projectId: project.id,
            sharedLinkId: projectSharedLink.id,
            requestedById: requestedUser.uid,
            status: ProjectAccessRequestStatus.PENDING,
            expiredAt: new Date(Date.now() + 48 * 24 * 60 * 60 * 1000), // --- [48 days]
        });
        message = `Project access request created [${ProjectAccessRequest.id}]`;
        logger.info(message);
    } else {
        message = `Project access request already exists [${ProjectAccessRequest.id}]`;
        // In case existing request was rejected, update the status to pending
        if (ProjectAccessRequest.status === ProjectAccessRequestStatus.REJECTED) {
            await ProjectAccessRequestModel.xUpdateById(ProjectAccessRequest.id, {
                status: ProjectAccessRequestStatus.PENDING,
            });
        }
        logger.info(message);
    }

    console.log('forceSendEmail', { forceSendEmail });

    if (isNew || forceSendEmail) {
        await emailService.sendProjectAccessRequestEmail({
            approverEmail: approverUser.email,
            projectName: project.name,
            projectId: project.id,
            requestId: ProjectAccessRequest.id,
            requesterEmail: requestedUser.email,
            requesterName: requestedUser.displayName || requestedUser.email,
            approverName: approverUser.displayName || approverUser.email,
        });
    }

    return {
        success: true,
        message,
        statusCode: 200,
        data: {...ProjectAccessRequest, isMember: false},
    };
};

export const approveProjectAccessRequest = async ({ ProjectAccessRequestId }: { ProjectAccessRequestId: string }) => {
    const ProjectAccessRequest = await ProjectAccessRequestModel.xFind1({
        id: ProjectAccessRequestId,
    });

    if (!ProjectAccessRequest) {
        return {
            success: false,
            message: "Project request access not found",
            statusCode: 404,
        };
    }

    await ProjectAccessRequestModel.xUpdate({
        status: ProjectAccessRequestStatus.APPROVED,
        approvedAt: new Date(),
    }, { id: ProjectAccessRequestId });


    const addProjectMemberResult = await addProjectMember({
        projectId: ProjectAccessRequest.projectId,
        userId: ProjectAccessRequest.requestedById,
        role: ProjectMemberRole.VIEWER,
    });

    if (!addProjectMemberResult.success) {
        return addProjectMemberResult;
    }

    return {
        success: true,
        message: "Project request access approved",
        statusCode: 200,
        data: ProjectAccessRequest,
    };
};


interface ResponseProjectAccessRequestPayload {
    id: string;
    isApproved: boolean;
}

export const responseProjectAccessRequest = async ({ id, isApproved }: ResponseProjectAccessRequestPayload) => {
    const getProjectAccessRequestResult = await getProjectAccessRequestById(id);

    if (!getProjectAccessRequestResult.success) {
        return getProjectAccessRequestResult;
    }

    const projectAccessRequest = getProjectAccessRequestResult.data;

    if (projectAccessRequest.status !== ProjectAccessRequestStatus.PENDING) {
        return {
            success: false,
            message: "Project access request is processed already",
            statusCode: 400,
        };
    }

    const updatedProjectAccessRequest = await ProjectAccessRequestModel.xUpdate({ id }, {
        status: isApproved ? ProjectAccessRequestStatus.APPROVED : ProjectAccessRequestStatus.REJECTED,
        approvedAt: isApproved ? new Date() : null,
        rejectedAt: isApproved ? null : new Date(),
        rejectedReason: isApproved ? null : "Rejected by the project owner",
    });

    if (!updatedProjectAccessRequest) {
        return {
            success: false,
            message: "Failed to update project access request",
            statusCode: 409,
        };
    }

    const project = await getProjectById(projectAccessRequest.projectId);

    const logPayload = {
        projectId: projectAccessRequest.projectId,
        userId: projectAccessRequest.requestedById,
        isApproved,
    }

    if (isApproved) {
        logger.info("Approved project access request, adding project member " + JSON.stringify(logPayload));
        const addProjectMemberResult = await addProjectMember({
            projectId: projectAccessRequest.projectId,
            userId: projectAccessRequest.requestedById,
            role: ProjectMemberRole.VIEWER,
        });

        if (!addProjectMemberResult.success) {
            return addProjectMemberResult;
        }

        const requestedUser = await getFirebaseUserById(projectAccessRequest.requestedById);

        if (!requestedUser) {
            return {
                success: false,
                message: "Requested user not found",
                statusCode: 404,
            };
        }

        const projectId = project.data.id;

        await emailService.sendProjectAccessRequestApprovedEmail({
            projectName: project.data.name,
            projectId: project.data.id,
            requesterEmail: requestedUser.email,
            userName: requestedUser.displayName,
        });

        // Broadcast acl.member.added event via SSE
        try {
            const sseService = getSSEService();
            await sseService.broadcastToProject(projectId, {
                type: SSEEventType.ACL_MEMBER_NEW_MEMBER,
                projectId,
                userId: projectAccessRequest.requestedById, // The acting user (who added the member)
                data: {
                    userId: requestedUser.uid,
                    userName: requestedUser.displayName || requestedUser.email,
                    role: ProjectMemberRole.VIEWER,
                    addedBy: projectAccessRequest.requestedById, // In context of this function, user is adding themselves or being added
                    addedAt: new Date(),
                },
            });
            
            logger.info('SSE broadcast sent for member addition', {
                projectId,
                userId: projectAccessRequest.requestedById,
                role: ProjectMemberRole.VIEWER,
            });
        } catch (error) {
            logger.error('Failed to broadcast acl.member.added event via SSE', {
                error,
                projectId,
                userId: projectAccessRequest.requestedById,
                role: ProjectMemberRole.VIEWER,
            });
        }

    } else {
        logger.info("Rejected project access request, sending email to the requested user " + JSON.stringify(logPayload));

        const getUserByEmailResult = await getFirebaseUserById(projectAccessRequest.requestedById);

        if (!getUserByEmailResult) {
            return {
                success: false,
                message: "Requested user not found",
                statusCode: 404,
            };
        }

        await emailService.sendProjectAccessRequestRejectedEmail({
            projectName: project.data.name,
            requesterEmail: getUserByEmailResult.email,
            userName: getUserByEmailResult.displayName,
        });
    }

    return {
        success: true,
        message: "Project access request approved and user has been added to project",
        statusCode: 200,
        data: projectAccessRequest,
    };
};

export const getProjectAccessRequestsByProjectId = async (projectId: string, status?: ProjectAccessRequestStatus) => {
    const projectAccessRequests = await ProjectAccessRequestModel.xFind({
        projectId,
        ...(status ? { status } : {})
    });

    const userIds = projectAccessRequests.map((projectAccessRequest) => projectAccessRequest.requestedById);
    const users = await getFirebaseListOfUsersByIds(userIds);
    const usersMap = fireBaseUserListToIndexMap(users);

    const projectAccessRequestsWithUser = projectAccessRequests.map((projectAccessRequest) => {
        const userIndex = usersMap[projectAccessRequest.requestedById];
        return {
            ...projectAccessRequest,
            user: users?.[userIndex]?.providerData?.[0] || null,
        };
    });

    // Sort by email A-Z
    projectAccessRequestsWithUser.sort((a, b) => {
        const emailA = a.user?.email || '';
        const emailB = b.user?.email || '';
        return emailA.localeCompare(emailB);
    });

    return {
        success: true,
        message: "Project access requests fetched",
        statusCode: 200,
        data: projectAccessRequestsWithUser,
    };
};

export const deleteProjectAccessRequestOfUser = async (projectId: string, userId: string) => {
    await ProjectAccessRequestModel.xDestroy({
        projectId,
        requestedById: userId
    });
}