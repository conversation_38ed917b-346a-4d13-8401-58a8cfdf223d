import { DEFAULT_PAGE_NUMBER } from "@/constants/pagination";
import { models } from "@/schemas";
import ProjectModel, { Project } from "@/schemas/project/Project.model";
import { ProjectFolder } from "@/schemas/project/ProjectFolder.model";
import {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";
import { Resource } from "@/schemas/resource/Resource.model";
import { getResourceData } from "@/schemas/resource/utils";
import { createLogger } from "@/services/logger";
import { ProjectData } from "@/types/project";
import { createErrorResponse, createSuccessResponse } from "@/utils/response";
import { Op } from "sequelize";
import { getFirebaseUserInfoById } from "@/services/firebase";

const logger = createLogger("ProjectService");

type ProjectDetails = Project & {
  resources: Resource[];
  folders: ProjectFolder[];
  members: ProjectMember[];
  userPermissions?: {
    canView: boolean;
    canEdit: boolean;
    canComment: boolean;
  };
};

export const getProjectById = async (projectId: string) => {
  const data = await ProjectModel.xFind1({
    id: projectId,
  });

  if (!data) {
    return {
      success: false,
      message: "Project not found",
      statusCode: 404,
    };
  }

  return {
    success: true,
    message: "Project found",
    statusCode: 200,
    data,
  };
};

export const verifyOwnedProject = async (userId: string, projectId: string) => {
  const data = await ProjectModel.xFind1({
    createdById: userId,
    id: projectId,
  });

  const statusCode = data ? 200 : 404;
  const message = data ? "Project found" : "Project not found";

  return {
    success: statusCode === 200,
    message,
    statusCode,
    data,
  };
};

export const getProjectDetails = async (
  projectId: string,
  userId: string,
  search?: string,
  limit?: number,
  offset?: number,
  sort?: string,
  sortOrder?: string
) => {
  const project = await ProjectModel.xFind1({
    id: projectId,
  });

  if (!project) {
    return {
      success: false,
      message: "Project not found",
      statusCode: 404,
    };
  }

  // Build search condition for resources
  const resourceSearchCondition = search
    ? {
        name: { [Op.iLike]: `%${search}%` },
      }
    : {};

  // Build order condition for resources
  const defaultSort = 'createdAt';
  const sortField = sort || defaultSort;
  const sortDirection = (sortOrder?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC');

  const projectDetailsRaw = await models.Project.findOne({
    where: {
      id: projectId,
    },
    include: [
      {
        model: models.ProjectFolder,
        as: "folders",
      },
      {
        model: models.Resource,
        as: "resources",
        attributes: {
          exclude: ["ffprobe"],
        },
        where: {
          ...resourceSearchCondition,
          folderId: null, // Only get root level resources
        },
        required: false,
        ...(limit && offset !== undefined ? { limit, offset } : {}),
        order: [[sortField, sortDirection]],
      },
      {
        model: models.ProjectMember,
        as: "members",
        attributes: ["id", "role", "userId"],
        where: {
          userId: userId,
        },
        required: false,
      },
    ],
  });

  const projectDetails = projectDetailsRaw.toJSON<ProjectDetails>();
  const userPermissions = getProjectUserPermissions(projectDetails, userId);

  // Get total count for pagination metadata
  const totalResourcesCount = await models.Resource.count({
    where: {
      projectId,
      ...resourceSearchCondition,
      folderId: null, // Only count root level resources
    },
  });

  const formattedResources = await Promise.all(
    projectDetails.resources.map(async (resource) => {
      let resourceData = await getResourceData(resource);
      return {
        ...resourceData,
        userPermissions,
      };
    })
  );

  const totalFilesSize = formattedResources.reduce(
    (acc, curr) => acc + (curr.fileSize ?? 0),
    0
  );

  const output = {
    ...projectDetails,
    resources: formattedResources,
    totalFilesCount: totalResourcesCount,
    totalFilesSize,
  } as ProjectData;

  return {
    success: true,
    message: "Project details retrieved successfully",
    statusCode: 200,
    data: output,
    meta: {
      total: totalResourcesCount,
      page: limit && offset !== undefined ? Math.floor(offset / limit) + DEFAULT_PAGE_NUMBER : DEFAULT_PAGE_NUMBER,
      limit: limit || totalResourcesCount,
    },
  };
};

export const getProjects = async (
  userId: string,
  accessibleProjects: string[]
) => {
  try {
    const projects = await models.Project.findAll({
      where: {
        [Op.or]: [
          {
            createdById: userId,
          },
          {
            id: {
              [Op.in]: accessibleProjects,
            },
          },
        ],
        deletedAt: null,
      },
      include: [
        {
          model: models.ProjectMember,
          attributes: ["id", "role", "userId"],
          as: "members",
          where: {
            userId: userId,
          },
          required: false,
        },
        {
          model: models.Resource,
          attributes: ["id", "fileSize"],
          as: "resources",
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    const output = projects.map((p) => {
      const project = p.toJSON<ProjectDetails>();
      const userPermissions = getProjectUserPermissions(project, userId);
      const totalFilesSize = project.resources.reduce(
        (acc, curr) => acc + (curr.fileSize ?? 0),
        0
      );
      const totalFilesCount = project.resources.length;

      return { ...project, totalFilesSize, totalFilesCount, userPermissions };
    });

    return {
      success: true,
      message: "Project details retrieved successfully",
      statusCode: 200,
      data: output,
    };
  } catch (error) {
    logger.error(`Failed to get projects: ${error.message}`);

    return {
      success: false,
      message: "Failed to get projects",
      statusCode: 500,
      data: [],
    };
  }
};

export const getProjectDefault = async (userId: string) => {
  try {
    const project = await models.Project.findOne({
      where: {
        isDefault: true,
        createdById: userId,
      },
    });

    if (!project) {
      return {
        success: false,
        message: "Project not found",
        statusCode: 404,
        data: null,
      };
    }
    return {
      success: true,
      message: "Project found",
      statusCode: 200,
      data: project.toJSON<Project>(),
    };
  } catch (error) {
    logger.error(`Failed to get project default: ${error.message}`);

    return {
      success: false,
      message: "Failed to get project default",
      statusCode: 500,
      data: null,
    };
  }
};

export const createProject = async (
  userId: string,
  name: string,
  description: string,
  isDefault?: boolean
) => {
  try {
    const project = await models.Project.xCreate({
      name,
      description,
      createdById: userId,
      isDefault: isDefault || false,
    });

    return {
      success: true,
      message: "Project created successfully",
      statusCode: 200,
      data: project,
    };
  } catch (error) {
    logger.error(`Failed to create project: ${error.message}`);

    return {
      success: false,
      message: "Failed to create project",
      statusCode: 500,
      data: null,
    };
  }
};

export const updateProject = async (
  projectId: string,
  userId: string,
  payload: Partial<Project>
) => {
  try {
    // First verify the project exists and user has permission
    const project = await ProjectModel.xFind1({
      id: projectId,
      createdById: userId,
    });

    if (!project) {
      return {
        success: false,
        message: "Project not found or you don't have permission to update it",
        statusCode: 404,
        data: null,
      };
    }

    // Update the project
    const updatedProject = await models.Project.update(payload, {
      where: {
        id: projectId,
        createdById: userId,
      },
      returning: true,
    });

    if (!updatedProject[0]) {
      return {
        success: false,
        message: "Failed to update project",
        statusCode: 500,
        data: null,
      };
    }

    return {
      success: true,
      message: "Project updated successfully",
      statusCode: 200,
      data: updatedProject[1][0],
    };
  } catch (error) {
    logger.error(`Failed to update project: ${error.message}`);

    return {
      success: false,
      message: "Failed to update project",
      statusCode: 500,
      data: null,
    };
  }
};

export const getProjectInfo = async (projectId: string, userId: string) => {
  const project = await ProjectModel.xFind1({
    id: projectId,
  });

  if (!project) {
    return createErrorResponse("ProjectNotFound", 404);
  }

  const projectInfoRaw = await models.Project.findOne({
    where: {
      id: projectId,
    },
    include: [
      {
        model: models.ProjectMember,
        as: "members",
        attributes: ["id", "role", "userId"],
        where: {
          userId: userId,
        },
        required: false,
      },
    ],
  });

  if (!projectInfoRaw) {
    return createErrorResponse("ProjectNotFound", 404);
  }

  const projectInfo = projectInfoRaw.toJSON<Project & { members: ProjectMember[] }>();
  const userPermissions = getProjectUserPermissions(projectInfo as ProjectDetails, userId);

  const createdBy = await getFirebaseUserInfoById(projectInfo.createdById);

  const output = {
    ...projectInfo,
    createdBy,
    userPermissions,
  };

  return createSuccessResponse(output, "ProjectInfoRetrieved");
};

/**************************************************
 *             PRIVATE FUNCTIONS
 *
 ***************************************************/

function getProjectUserPermissions(project: ProjectDetails, userId: string) {
  const isProjectOwner = project.createdById === userId;
  const projectMember = project.members.find(
    (member) => member.userId === userId
  );
  const memberRole = projectMember?.role;
  
  const canEdit = isProjectOwner || memberRole === ProjectMemberRole.EDITOR;
  const canComment = canEdit || memberRole === ProjectMemberRole.COMMENTER;
  
  return {
    canView: true,
    canEdit,
    canComment,
  };
}
