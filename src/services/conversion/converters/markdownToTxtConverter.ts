import * as fs from "fs-extra";
import path from "path";
import { IMarkdownConverter, ConversionFormat } from "../";
import { log } from "@/services/logger";

export class MarkdownToTxtConverter implements IMarkdownConverter {
  getSupportedFormat(): ConversionFormat {
    return ConversionFormat.TXT;
  }

  async convert(markdownContent: string, outputPath: string): Promise<string> {
    try {
      const plainText = this.markdownToPlainText(markdownContent);

      // Ensure output directory exists
      await fs.ensureDir(path.dirname(outputPath));

      // Write plain text to file
      await fs.writeFile(outputPath, plainText, "utf8");

      return outputPath;
    } catch (error) {
      log.error("Error converting markdown to TXT:", error);
      throw new Error(`Failed to convert markdown to TXT: ${error.message}`);
    }
  }

  private markdownToPlainText(markdown: string): string {
    let text = markdown;
    text = text.replace(/<[^>]+>/g, "");
    // Remove markdown headers (convert to plain text with spacing)
    text = text.replace(/^#{1,6}\s+(.+)$/gm, "$1\n" + "=".repeat(50) + "\n");

    // Remove bold and italic formatting
    text = text.replace(/\*\*(.*?)\*\*/g, "$1");
    text = text.replace(/\*(.*?)\*/g, "$1");

    // Remove inline code formatting
    text = text.replace(/`(.*?)`/g, "$1");

    // Convert links to plain text with URL
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, "$1 ($2)");

    // Remove image references
    text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, "[Image: $1]");

    // Clean up extra whitespace
    text = text.replace(/\n\s*\n\s*\n/g, "\n\n");
    text = text.trim();

    return text;
  }
}
