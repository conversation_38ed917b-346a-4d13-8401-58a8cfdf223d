import { Document, Packer, Paragraph, TextRun, HeadingLevel } from "docx";
import * as fs from "fs-extra";
import path from "path";
import { IMarkdownConverter, ConversionFormat } from "../";
import { log } from "@/services/logger";
import MarkdownIt from "markdown-it";
import TurndownService from "turndown";

export class MarkdownToDocxConverter implements IMarkdownConverter {
  getSupportedFormat(): ConversionFormat {
    return ConversionFormat.DOCX;
  }

  async convert(markdownContent: string, outputPath: string): Promise<string> {
    try {
      const doc = await this.parseMarkdownToDocx(markdownContent);
      const buffer = await Packer.toBuffer(doc);
      await fs.ensureDir(path.dirname(outputPath));
      await fs.writeFile(outputPath, buffer);
      return outputPath;
    } catch (error: any) {
      log.error("Error converting markdown to DOCX:", error);
      throw new Error(`Failed to convert markdown to DOCX: ${error.message}`);
    }
  }

  private async parseMarkdownToDocx(markdown: string): Promise<Document> {
    if (/<[a-z][\s\S]*>/i.test(markdown)) {
      const turndown = new TurndownService();
      markdown = turndown.turndown(markdown);
    }

    const md = new MarkdownIt();
    const tokens = md.parse(markdown, {});
    const children: Paragraph[] = [];

    let i = 0;
    let listLevel = 0;
    let listType: "bullet" | "numbered" | null = null;

    while (i < tokens.length) {
      const token = tokens[i];

      if (token.type === "heading_open") {
        const level = Number(token.tag.replace("h", ""));
        const content = tokens[i + 1].content;
        children.push(
          new Paragraph({
            heading: this.getHeadingLevel(level),
            children: this.parseInlineFormatting(content),
          })
        );
        i += 3;
        continue;
      }

      if (token.type === "paragraph_open") {
        const content = tokens[i + 1].content;
        children.push(
          new Paragraph({ children: this.parseInlineFormatting(content) })
        );
        i += 3;
        continue;
      }

      if (token.type.endsWith("_list_open")) {
        listLevel++;
        listType = token.type === "bullet_list_open" ? "bullet" : "numbered";
        i++;
        continue;
      }

      if (token.type.endsWith("_list_close")) {
        listLevel--;
        if (listLevel === 0) listType = null;
        i++;
        continue;
      }

      if (token.type === "list_item_open") {
        const content = tokens[i + 2].content;
        const props = { children: this.parseInlineFormatting(content) };
        if (listType === "bullet") {
          children.push(new Paragraph({ ...props, bullet: { level: listLevel - 1 } }));
        } else if (listType === "numbered") {
          children.push(
            new Paragraph({ ...props, numbering: { reference: "numbered-list", level: listLevel - 1 } })
          );
        }
        while (i < tokens.length && tokens[i].type !== "list_item_close") i++;
        i++;
        continue;
      }

      if (token.type === "blockquote_open") {
        let j = i + 1;
        while (j < tokens.length && tokens[j].type !== "blockquote_close") {
          if (tokens[j].type === "paragraph_open") {
            const content = tokens[j + 1].content;
            children.push(
              new Paragraph({
                style: "IntenseQuote",
                children: this.parseInlineFormatting(content),
              })
            );
            j += 3;
          } else {
            j++;
          }
        }
        i = j + 1;
        continue;
      }

      if (token.type === "fence") {
        children.push(
          new Paragraph({
            style: "Code",
            children: [new TextRun({ text: token.content, font: "Consolas" })],
          })
        );
        i++;
        continue;
      }

      i++;
    }

    return new Document({ sections: [{ children }] });
  }

  private getHeadingLevel(level: number) {
    switch (level) {
      case 1:
        return HeadingLevel.HEADING_1;
      case 2:
        return HeadingLevel.HEADING_2;
      case 3:
        return HeadingLevel.HEADING_3;
      case 4:
        return HeadingLevel.HEADING_4;
      case 5:
        return HeadingLevel.HEADING_5;
      default:
        return HeadingLevel.HEADING_6;
    }
  }

  private parseInlineFormatting(text: string): TextRun[] {
    const runs: TextRun[] = [];
    let remaining = text;
    const regex = /(\*\*([^*]+)\*\*|\*([^*]+)\*)/;

    while (remaining) {
      const match = remaining.match(regex);
      if (match) {
        const [full, , boldText, italicText] = match;
        const pos = match.index!;
        if (pos > 0) runs.push(new TextRun(remaining.slice(0, pos)));
        runs.push(
          new TextRun({
            text: boldText || italicText,
            bold: Boolean(boldText),
            italics: Boolean(italicText),
          })
        );
        remaining = remaining.slice(pos + full.length);
      } else {
        runs.push(new TextRun(remaining));
        break;
      }
    }

    return runs.length ? runs : [new TextRun(text)];
  }
}
