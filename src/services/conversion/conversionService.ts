import { models } from "@/schemas";
import { MarkdownConverterFactory, ConversionFormat } from "./";
import { log } from "@/services/logger";
import * as fs from "fs-extra";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { gcsUpload } from "@/services/storage";
import { gcsResourceFolder } from "@/config";
import { generateSignedUrlForRead } from "@/services/storage";
import { Storage } from "@google-cloud/storage";
import { gcsBucket, getGcsServiceAccount } from "@/config";
import { createErrorResponse, createSuccessResponse } from "@/utils/response";

export interface ConversionResult {
  success: boolean;
  message: string;
  statusCode: number;
  data?: {
    originalResourceId: string;
    convertedFormat: ConversionFormat;
    downloadUrl?: string;
    fileName: string;
  };
}

export interface ConversionBlobResult {
  success: boolean;
  message: string;
  statusCode: number;
  data?: {
    originalResourceId: string;
    convertedFormat: ConversionFormat;
    fileName: string;
    fileBuffer: Buffer;
    contentType: string;
  };
}

export class ConversionService {
  private static readonly TEMP_DIR = "/tmp/conversions";
  private static readonly SUPPORTED_MARKDOWN_EXTENSIONS = ['.md', '.markdown'];

  /**
   * Convert a markdown resource to the specified format and return as blob
   * @param resourceId - The ID of the resource to convert
   * @param format - The target conversion format
   * @param userId - The ID of the user requesting the conversion (reserved for future use)
   * @returns ConversionBlobResult with file buffer
   */
  static async convertMarkdownResourceToBlob(
    resourceId: string,
    format: ConversionFormat,
  ): Promise<ConversionBlobResult> {
    try {
      // 1. Fetch the resource from database
      const resource = await models.Resource.xFind1({ id: resourceId });
      if (!resource) {
        return createErrorResponse("Resource not found", 404);
      }

      // 2. Verify it's a markdown file
      if (!this.isMarkdownFile(resource.name)) {
        return createErrorResponse("Resource is not a markdown file. Only .md and .markdown files are supported.", 400);
      }

      // 3. Download the markdown file from GCS
      const markdownContent = await this.downloadMarkdownFromGCS(resource.url);

      // 4. Generate output paths
      const { tempFilePath, gcsFileName } = this.generateOutputPaths(resource.name, format);

      // 5. Ensure temp directory exists
      await fs.ensureDir(this.TEMP_DIR);

      // 6. Convert the markdown using the factory
      const converter = MarkdownConverterFactory.getConverter(format);
      await converter.convert(markdownContent, tempFilePath);

      // 7. Read the converted file as buffer
      const fileBuffer = await fs.readFile(tempFilePath);

      // 8. Clean up temp file
      await fs.remove(tempFilePath);

      return createSuccessResponse({
        originalResourceId: resourceId,
        convertedFormat: format,
        fileName: gcsFileName,
        fileBuffer,
        contentType: this.getContentType(format),
      }, `Successfully converted markdown to ${format.toUpperCase()}`);

    } catch (error) {
      log.error(`Failed to convert markdown resource ${resourceId} to blob:`, error);

      return createErrorResponse(`Conversion failed: ${error.message}`, 500);
    }
  }

  /**
   * Get supported conversion formats
   * @returns Array of supported conversion formats
   */
  static getSupportedFormats(): ConversionFormat[] {
    return MarkdownConverterFactory.getSupportedFormats();
  }

  /**
   * Check if a file is a markdown file based on its extension
   * @param fileName - Name of the file to check
   * @returns True if the file is a markdown file
   */
  private static isMarkdownFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return this.SUPPORTED_MARKDOWN_EXTENSIONS.includes(ext);
  }

  /**
   * Download markdown content from GCS
   * @param gcsPath - Path to the file in GCS
   * @returns Content of the markdown file as string
   */
  private static async downloadMarkdownFromGCS(gcsPath: string): Promise<string> {
    try {
      const serviceAccount = await getGcsServiceAccount();
      const storage = new Storage({ credentials: serviceAccount });
      const bucket = storage.bucket(gcsBucket);
      const file = bucket.file(gcsPath);

      const [exists] = await file.exists();
      if (!exists) {
        throw new Error(`File not found in GCS: ${gcsPath}`);
      }

      const [content] = await file.download();
      return content.toString('utf8');
    } catch (error) {
      throw new Error(`Failed to download file from GCS: ${error.message}`);
    }
  }

  /**
   * Generate temporary file path and GCS file name for the converted file
   * @param originalFileName - Original name of the file
   * @param format - Target conversion format
   * @returns Object containing temp file path and GCS file name
   */
  private static generateOutputPaths(originalFileName: string, format: ConversionFormat): {
    tempFilePath: string;
    gcsFileName: string;
  } {
    const baseName = path.parse(originalFileName).name;
    const timestamp = new Date().getTime();
    const uniqueId = uuidv4().substring(0, 8);

    const fileName = `${baseName}-converted-${timestamp}-${uniqueId}.${format}`;

    return {
      tempFilePath: path.join(this.TEMP_DIR, fileName),
      gcsFileName: fileName,
    };
  }

  /**
   * Get appropriate content type for the conversion format
   * @param format - Conversion format
   * @returns MIME type string
   */
  private static getContentType(format: ConversionFormat): string {
    switch (format) {
      case ConversionFormat.DOCX:
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case ConversionFormat.TXT:
        return "text/plain";
      default:
        return "application/octet-stream";
    }
  }
}
