import { MarkdownToDocxConverter } from "./converters/markdownToDocxConverter";
import { MarkdownToTxtConverter } from "./converters/markdownToTxtConverter";

export enum ConversionFormat {
  DOCX = "docx",
  TXT = "txt"
}

export interface IMarkdownConverter {
  convert(markdownContent: string, outputPath: string): Promise<string>;
  getSupportedFormat(): ConversionFormat;
}

export class MarkdownConverterFactory {
  private static converters: Map<ConversionFormat, IMarkdownConverter> = new Map();

  static getConverter(format: ConversionFormat): IMarkdownConverter {
    // Lazy initialization to avoid constructor issues
    if (!this.converters.has(format)) {
      switch (format) {
        case ConversionFormat.DOCX:
          this.converters.set(format, new MarkdownToDocxConverter());
          break;
        case ConversionFormat.TXT:
          this.converters.set(format, new MarkdownToTxtConverter());
          break;
        default:
          throw new Error(`Unsupported conversion format: ${format}`);
      }
    }

    const converter = this.converters.get(format);
    if (!converter) {
      throw new Error(`Failed to create converter for format: ${format}`);
    }
    return converter;
  }

  static getSupportedFormats(): ConversionFormat[] {
    return [ConversionFormat.DOCX, ConversionFormat.TXT];
  }
}

export { MarkdownToDocxConverter } from "./converters/markdownToDocxConverter";
export { MarkdownToTxtConverter } from "./converters/markdownToTxtConverter";
export { ConversionBlobResult } from "./conversionService";