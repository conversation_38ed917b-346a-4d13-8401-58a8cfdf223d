import { Op } from "sequelize";
import { models } from "@/schemas";
import { createLogger } from "@/services/logger";
import { transcoderService } from "@/services/transcoder";
import { createSpriteSheetPayload, getVttSrcPath, generateVTTTemplateByConfig, getEncodedVideoPath } from "@/utils/transcoding";
import { gcsBucket, gcsResourceFolder } from "@/config";
import { VideoProcessingType, VideoProcessingStatus } from "@/schemas/video/VideoProcessing.model";
import { ApiResponse, createSuccessResponse, createErrorResponse } from "@/utils/response";
import { GCS_URI_PREFIX } from "@/constants/storage";
import { isSpritesheetEligible } from "@/utils/file";
import { gcsUploadContent } from "@/services/storage";

const logger = createLogger("VideoProcessingService");

/**
 * Interface for spritesheet job creation response
 */
interface SpritesheetJobResponse extends ApiResponse {
  jobId?: string;
  vttPath?: string;
}

/**
 * Create a spritesheet generation job for a video resource
 * @param resourceId Resource ID to generate spritesheet for
 * @param fileName Original file name for validation
 * @param inputPath Path to the input video file
 * @param mimeType MIME type for validation
 * @returns Response with job details
 */
export const createSpritesheetJob = async (
  resourceId: string,
  fileName: string,
  inputPath: string,
  mimeType?: string
): Promise<SpritesheetJobResponse> => {
  try {
    if (!resourceId || !fileName || !inputPath) {
      return createErrorResponse('Missing required parameters', 400);
    }

    if (!isSpritesheetEligible(fileName, mimeType)) {
      return createErrorResponse(
        'File is not eligible for spritesheet generation. Only .mp4 and .mov files are supported.',
        415
      );
    }

    const resource = await models.Resource.xFind1ById(resourceId);
    if (!resource) {
      return createErrorResponse('Resource not found', 404);
    }

    const videoDurationSeconds = resource.duration || 300;
    
    if (!videoDurationSeconds || videoDurationSeconds <= 0) {
      return createErrorResponse(
        'Video duration is required for spritesheet generation. Resource may need to be processed first.',
        400
      );
    }

    const existingJob = await models.VideoProcessing.xFind1({
      resourceId,
      processingType: VideoProcessingType.SPRITESHEET,
      status: [VideoProcessingStatus.PENDING, VideoProcessingStatus.PROCESSING]
    });

    if (existingJob) {
      return createErrorResponse(
        'SpriteSheet generation already in progress for this resource',
        409
      );
    }

    // Construct URIs for the transcoding job
    const sourceGcsUri = `${GCS_URI_PREFIX}${gcsBucket}/${inputPath}`;
    const destinationFolderGcsUri = `${GCS_URI_PREFIX}${gcsBucket}/videos/${resourceId}/`;

    // Create spriteSheet config with dynamic sizing based on video duration
    const config = createSpriteSheetPayload(
      {
        inputUri: sourceGcsUri,
        outputUri: destinationFolderGcsUri,
        videoId: resourceId,
        durationInSeconds: videoDurationSeconds,
        hasAudio: true,
      }
    );


    // Create the transcoding job
    const job = await transcoderService.createJob(
      sourceGcsUri,
      destinationFolderGcsUri,
      config
    );

    // Extract job ID from the job name
    const jobId = job.name?.split("/").pop() || "";

    logger.info(
      `Creating VideoProcessing record for spriteSheet generation`,
      {
        jobId,
        resourceId,
        input: sourceGcsUri,
        output: destinationFolderGcsUri,
      }
    );

    // Create a record in the video processing table
    const videoProcessingJob = await models.VideoProcessing.xCreate({
      resourceId,
      processingType: VideoProcessingType.SPRITESHEET,
      status: VideoProcessingStatus.PROCESSING,
      jobId,
      inputUri: sourceGcsUri,
      outputUri: destinationFolderGcsUri,
      jobData: {
        transcoderJob: job,
        fileName,
        mimeType,
      },
      startedAt: new Date(),
    });

    const vttPath = getVttSrcPath(resourceId);

    return createSuccessResponse({
      jobId,
      job,
      videoProcessingJob,
      vttPath,
    }, 'SpriteSheet generation job created successfully', 201);
  } catch (error) {
    logger.error(`Failed to create spriteSheet job: ${error.message}`, error);
    
    return createErrorResponse(
      `Failed to create spriteSheet job: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      error
    );
  }
};

/**
 * Get video processing job by external job ID
 * @param jobId External transcoder job ID
 * @returns Service response with video processing job data
 */
export const getVideoProcessingJobById = async (
  jobId: string
): Promise<ApiResponse> => {
  try {
    
    if (!jobId) {
      return createErrorResponse('Job ID is required', 400);
    }

    const job = await models.VideoProcessing.xFind1({ jobId });
   
    if (!job) {
      return createErrorResponse('Video processing job not found', 404);
    }

    return createSuccessResponse(job, 'Video processing job retrieved successfully');
  } catch (error) {
    logger.error(`Failed to get video processing job: ${error.message}`, error);
    return createErrorResponse('Failed to retrieve video processing job', 500, error);
  }
};

/**
 * Update video processing job status and metadata
 * @param jobId External transcoder job ID
 * @param updateData Update data for the job
 * @returns Service response
 */
export const updateVideoProcessingJob = async (
  jobId: string,
  updateData: {
    status?: VideoProcessingStatus;
    jobData?: any;
    errorDetails?: any;
    retryCount?: number;
    retryReason?: string;
    completedAt?: Date;
    outputUri?: string;
  }
): Promise<ApiResponse> => {
  try {
    if (!jobId) {
      return createErrorResponse('Job ID is required', 400);
    }

    const job = await models.VideoProcessing.xFind1({ jobId });
    
    if (!job) {
      return createErrorResponse('Video processing job not found', 404);
    }

    await models.VideoProcessing.xUpdateById(job.id, updateData);

    logger.info(`Updated video processing job ${jobId}`, { 
      jobId, 
      updateData,
      resourceId: job.resourceId 
    });

    return createSuccessResponse(null, 'Video processing job updated successfully');
  } catch (error) {
    logger.error(`Failed to update video processing job: ${error.message}`, error);
    return createErrorResponse('Failed to update video processing job', 500, error);
  }
};

/**
 * Complete spriteSheet generation and update resource
 * @param jobId External transcoder job ID
 * @param outputUri URI where sprites and VTT were generated
 * @returns Service response
 */
export const completeSpriteSheetGeneration = async (
  jobId: string,
  outputUri: string
): Promise<ApiResponse> => {
  try {
    
    const jobResult = await getVideoProcessingJobById(jobId);
    
    if (!jobResult.success || !jobResult.data) {
      return createErrorResponse('Video processing job not found', 404);
    }

    const processingJob = jobResult.data;
    const resourceId = processingJob.resourceId;
    
    const resource = await models.Resource.xFind1ById(resourceId);
    if (!resource) {
      return createErrorResponse('Resource not found', 404);
    }

    const videoDurationSeconds = resource.duration || 300;
    
    const transcoderJob = processingJob.jobData?.transcoderJob;
    const spriteSheetConfig = transcoderJob?.config?.spriteSheets?.[0];
    
    if (!spriteSheetConfig) {
      return createErrorResponse('SpriteSheet configuration not found in job data', 400);
    }

    const itemWidth = spriteSheetConfig.spriteWidthPixels || 128;
    const itemHeight = spriteSheetConfig.spriteHeightPixels || 72;
    const columnCount = spriteSheetConfig.columnCount || 20;
    const rowCount = spriteSheetConfig.rowCount || 11;
    const intervalSeconds = parseInt(spriteSheetConfig.interval?.seconds || '10');
    const spriteSheetWidth = columnCount * itemWidth;
    const maxSprites = columnCount * rowCount;


    const vttContent = generateVTTTemplateByConfig({
      videoDurationSeconds,
      intervalSeconds,
      spriteSheetWidth,
      itemWidth,
      itemHeight,
      maxSprites,
      videoId: resourceId,
    });

    const vttFileName = getVttSrcPath(resourceId);
    
    try {
      await gcsUploadContent(
        vttContent,
        vttFileName,
        {
          contentType: 'text/vtt',
          metadata: {
            cacheControl: 'public, max-age=3600',
          },
        }
      );
    } catch (vttUploadError) {
      logger.error(`Failed to upload VTT file for resource ${resourceId}`, {
        error: vttUploadError,
        jobId,
        resourceId,
        vttFileName,
      });
    }

    await updateVideoProcessingJob(jobId, {
      status: VideoProcessingStatus.COMPLETED,
      completedAt: new Date(),
      outputUri,
    });

    const vttPath = getVttSrcPath(resourceId);
    const encodedVideoPath = getEncodedVideoPath(resourceId);
    
    try {
      await models.Resource.xUpdateById(resourceId, {
        isSpriteSheets: true,
        vttSrc: vttPath,
        transcodedUrl: encodedVideoPath,
      });
    } catch (updateError) {
      throw updateError;
    }

    logger.info(`Completed spritesheet generation for resource ${resourceId}`, {
      jobId,
      resourceId,
      vttPath,
      transcodedUrl: encodedVideoPath,
      outputUri,
      videoDurationSeconds,
    });

    return createSuccessResponse({
      resourceId,
      vttPath,
      transcodedUrl: encodedVideoPath,
      isSpriteSheets: true,
      videoDurationSeconds,
    }, 'SpriteSheet generation completed successfully');
  } catch (error) {
    logger.error(`Failed to complete spriteSheet generation: ${error.message}`, error);
    return createErrorResponse('Failed to complete spriteSheet generation', 500, error);
  }
};

/**
 * Handle spritesheet generation failure
 * @param jobId External transcoder job ID
 * @param errorDetails Error details from the transcoder
 * @returns Service response
 */
export const handleSpriteSheetFailure = async (
  jobId: string,
  errorDetails: any
): Promise<ApiResponse> => {
  try {
    const jobResult = await getVideoProcessingJobById(jobId);
    
    if (!jobResult.success || !jobResult.data) {
      return createErrorResponse('Video processing job not found', 404);
    }

    const processingJob = jobResult.data;
    const resourceId = processingJob.resourceId;
    const currentRetries = processingJob.retryCount || 0;
    const maxRetries = processingJob.maxRetries || 3;

    // Check if this is an audio-related failure
    const isAudioError = errorDetails?.message?.includes('audio track') || 
                        errorDetails?.message?.includes('AudioMissing');

    // If it's an audio error, try to create a new job without audio
    if (isAudioError) {
      logger.info(`Audio-related failure detected, retrying without audio for job ${jobId}`);
      
      try {
        const resource = await models.Resource.xFind1ById(resourceId);
        if (!resource) {
          return createErrorResponse('Resource not found for retry', 404);
        }

        // Get original job parameters
        const inputUri = processingJob.inputUri;
        const outputUri = processingJob.outputUri;
        const videoDurationSeconds = resource.duration || 300;

        // Create new spritesheet job without audio
        const configWithoutAudio = createSpriteSheetPayload({
          inputUri,
          outputUri,
          videoId: resourceId,
          durationInSeconds: videoDurationSeconds,
          hasAudio: false,
        });

        // Create the new transcoding job
        const newJob = await transcoderService.createJob(
          inputUri,
          outputUri,
          configWithoutAudio
        );

                 const newJobId = newJob.name?.split("/").pop() || "";

         // Mark the original job as failed
         await updateVideoProcessingJob(jobId, {
           status: VideoProcessingStatus.FAILED,
           retryReason: 'Missing audio track - retrying without audio',
           errorDetails,
           completedAt: new Date(),
         });

         // Create a new processing job record for the retry without audio
         await models.VideoProcessing.xCreate({
           resourceId,
           processingType: VideoProcessingType.SPRITESHEET,
           status: VideoProcessingStatus.PROCESSING,
           jobId: newJobId,
           inputUri,
           outputUri,
           jobData: {
             ...processingJob.jobData,
             hasAudio: false,
             transcoderJob: newJob,
             originalJobId: jobId,
           },
           retryCount: currentRetries + 1,
           retryReason: 'Missing audio track - retrying without audio',
           startedAt: new Date(),
         });

        logger.info(`Created new spritesheet job without audio. New job ID: ${newJobId}`, {
          originalJobId: jobId,
          newJobId,
          resourceId,
        });

        return createSuccessResponse({
          shouldRetry: true,
          retryCount: currentRetries + 1,
          newJobId,
          retryReason: 'Retrying without audio',
        }, 'SpriteSheet job retrying without audio');
      } catch (retryError) {
        logger.error(`Failed to create retry job without audio for ${jobId}`, retryError);
        // Fall through to normal retry logic
      }
    }

    // Check if we should retry normally
    if (currentRetries < maxRetries) {
      await updateVideoProcessingJob(jobId, {
        status: VideoProcessingStatus.RETRY,
        retryCount: currentRetries + 1,
        retryReason: errorDetails?.message || 'SpriteSheet generation failed',
        errorDetails,
      });

      logger.info(`Marking spriteSheet job for retry (${currentRetries + 1}/${maxRetries})`, {
        jobId,
        resourceId,
        errorDetails,
      });

      return createSuccessResponse({
        shouldRetry: true,
        retryCount: currentRetries + 1,
        maxRetries,
      }, 'SpriteSheet job marked for retry');
    } else {
      // Mark as permanently failed
      await updateVideoProcessingJob(jobId, {
        status: VideoProcessingStatus.FAILED,
        errorDetails,
        completedAt: new Date(),
      });

      logger.error(`SpriteSheet generation permanently failed for resource ${resourceId}`, {
        jobId,
        resourceId,
        errorDetails,
        finalRetryCount: currentRetries,
      });

      return createSuccessResponse({
        shouldRetry: false,
        retryCount: currentRetries,
        maxRetries,
      }, 'SpriteSheet generation permanently failed');
    }
  } catch (error) {
    logger.error(`Failed to handle spriteSheet failure: ${error.message}`, error);
    return createErrorResponse('Failed to handle spritesheet failure', 500, error);
  }
}; 
