/**
 * Transcript Utilities
 *
 * Utility functions for processing transcript data including timestamp extraction,
 * speaker identification, and time-based operations for video/audio transcripts.
 */

import { createLogger } from "@/services/logger";

const logger = createLogger("TranscriptUtils");

// ============================================================================
// TIMESTAMP UTILITIES
// ============================================================================

/**
 * Convert timestamp (HH:MM:SS) to seconds for video seeking
 */
export function convertTimestampToSeconds(timestamp: string): number {
  const parts = timestamp.split(":");
  if (parts.length === 3) {
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parseInt(parts[2], 10);
    return hours * 3600 + minutes * 60 + seconds;
  } else if (parts.length === 2) {
    const minutes = parseInt(parts[0], 10);
    const seconds = parseInt(parts[1], 10);
    return minutes * 60 + seconds;
  }
  return 0;
}

/**
 * Estimate timestamp based on position in response (fallback method)
 */
export function estimateTimestampFromPosition(
  position: number,
  content: string,
  audioDuration: number
): {
  timestamp: string;
  formatted: string;
  confidence: "low";
} | null {
  if (!content || position < 0 || !audioDuration) return null;

  // Estimate timestamp based on relative position in content
  const relativePosition = position / content.length;
  const estimatedSeconds = Math.floor(relativePosition * audioDuration);

  // Convert to HH:MM:SS format
  const hours = Math.floor(estimatedSeconds / 3600);
  const minutes = Math.floor((estimatedSeconds % 3600) / 60);
  const seconds = estimatedSeconds % 60;

  const timestamp = `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

  return {
    timestamp,
    formatted: timestamp,
    confidence: "low" as const,
  };
}

/**
 * Extract timestamp information from transcript content
 */
export function extractTimestampInfo(
  content: string,
  position: number
): {
  timestamp: string;
  formatted: string;
  confidence: "high" | "medium" | "low";
} | null {
  if (!content || position < 0) return null;

  // Look for timestamp patterns around the citation position
  const searchRadius = 1000; // Increased search radius for better timestamp detection
  const start = Math.max(0, position - searchRadius);
  const end = Math.min(content.length, position + searchRadius);
  const contextText = content.substring(start, end);

  // Debug: log the search context
  logger.debug("Timestamp extraction debug", {
    position,
    searchRadius,
    start,
    end,
    contextLength: contextText.length,
    contextSample: contextText.substring(0, 200),
    hasTimestampPattern: /\[\d{1,2}:\d{2}:\d{2}\]/.test(contextText),
  });

  // Transcript timestamp patterns - prioritize the exact format we use
  const timestampPatterns = [
    /\[(\d{2}:\d{2}:\d{2})\]/g, // [00:01:23] - exact transcript format
    /\[(\d{1,2}:\d{2}:\d{2})\]/g, // [0:01:23] - slight variation
    /\[(\d{1,2}:\d{2})\]/g, // [1:23] - shorter format
    /(\d{2}:\d{2}:\d{2})/g, // 00:01:23 - without brackets
    /(\d{1,2}:\d{2}:\d{2})/g, // 0:01:23 - without brackets
    /(\d{1,2}:\d{2})/g, // 1:23 - minimal format
  ];

  let bestMatch = null;
  let closestDistance = Infinity;
  let allMatches: Array<{
    pattern: string;
    match: string;
    index: number | undefined;
  }> = [];

  for (const pattern of timestampPatterns) {
    const matches = [...contextText.matchAll(pattern)];
    allMatches.push(
      ...matches.map((m) => ({
        pattern: pattern.source,
        match: m[1],
        index: m.index,
      }))
    );

    for (const match of matches) {
      const matchPosition = match.index || 0;
      const distance = Math.abs(matchPosition - searchRadius); // Distance from citation position
      if (distance < closestDistance) {
        closestDistance = distance;
        bestMatch = {
          timestamp: match[1],
          formatted: match[1],
          confidence:
            distance < 200 ? "high" : distance < 500 ? "medium" : "low",
        };
      }
    }
  }

  // Debug: log extraction results
  logger.debug("Timestamp extraction results", {
    position,
    allMatchesCount: allMatches.length,
    allMatches: allMatches.slice(0, 5), // First 5 matches
    bestMatch,
    closestDistance,
  });

  return bestMatch;
}

// ============================================================================
// SPEAKER UTILITIES
// ============================================================================

/**
 * Extract speaker information from transcript content
 */
export function extractSpeakerInfo(
  content: string,
  position: number
): {
  speaker: string;
  formatted: string;
} | null {
  if (!content || position < 0) return null;

  const searchRadius = 200;
  const start = Math.max(0, position - searchRadius);
  const end = Math.min(content.length, position + searchRadius);
  const contextText = content.substring(start, end);

  // Look for speaker patterns - prioritize the exact format we use
  const speakerPatterns = [
    /\]\s*(Speaker\s+\d+)\s*:/g, // "] Speaker 1:" - exact transcript format
    /(Speaker\s+\d+)\s*:/g, // "Speaker 1:" - direct match
    /\]\s*([A-Z][a-zA-Z]+(?:\s+\([A-Z]+\))?)\s*:/g, // "] John (SM):" - named speakers
    /([A-Z][a-zA-Z]+(?:\s+\([A-Z]+\))?)\s*:/g, // "John (SM):" - named speakers
    /\*\*([^*]+)\*\*\s*:/g, // "**Speaker Name**:" - markdown format
  ];

  for (const pattern of speakerPatterns) {
    const matches = [...contextText.matchAll(pattern)];
    if (matches.length > 0) {
      const speaker = matches[0][1];
      // Skip if it looks like a markdown header rather than a speaker
      if (
        speaker.includes("and") ||
        speaker.includes("of") ||
        speaker.includes("the") ||
        speaker.length > 30
      ) {
        continue;
      }
      return {
        speaker,
        formatted: speaker.replace(/\([^)]+\)/g, "").trim(),
      };
    }
  }

  return null;
}

// ============================================================================
// TEXT PROCESSING UTILITIES
// ============================================================================

/**
 * Extract text snippet from content around a specific position
 */
export function extractTextSnippet(
  position: number,
  content: string,
  radius: number = 100
): string {
  if (!content || position < 0 || position >= content.length) return "";

  const start = Math.max(0, position - radius);
  const end = Math.min(content.length, position + radius);
  let snippet = content.substring(start, end);

  // Clean up the snippet
  snippet = snippet.replace(/\s+/g, " ").trim();

  // Add ellipsis if truncated
  if (start > 0) snippet = "..." + snippet;
  if (end < content.length) snippet = snippet + "...";

  return snippet;
}

/**
 * Parse speakers from metadata
 */
export function parseSpeakers(speakers: any): string[] {
  if (!speakers) return [];

  if (Array.isArray(speakers)) {
    return speakers.map((s) => s.trim());
  }

  if (typeof speakers === "string") {
    return speakers.split(",").map((s) => s.trim());
  }

  return [];
}

/**
 * Create display name from filename
 */
export function createDisplayName(filename: string): string {
  return filename.replace(/\.(mp4|wav|mp3|m4a)$/i, "");
}
