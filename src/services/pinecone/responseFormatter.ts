/**
 * Response Formatter
 *
 * Handles formatting of Pinecone chat responses for LLM consumption,
 * including error handling and standardized response structures.
 */

import { ChatResponse } from "./types";

// ============================================================================
// RESPONSE FORMATTING
// ============================================================================

/**
 * Format chat response for natural LLM processing
 */
export function formatChatResponseForLLM(
  response: ChatResponse,
  query: string,
  fileIds?: string[]
): string {
  if (!response.message || !response.message.content) {
    return createNoResultsResponse(query, fileIds);
  }

  // Return only the clean response content - citations are handled separately by the frontend
  return response.message.content;
}

/**
 * Create a standardized "no results" response for failed searches
 */
export function createNoResultsResponse(
  query: string,
  fileIds?: string[]
): string {
  const fileContext =
    fileIds && fileIds.length > 0
      ? `in the ${fileIds.length} specified file${
          fileIds.length > 1 ? "s" : ""
        }`
      : "in your uploaded files";

  return [
    `I couldn't find any relevant information ${fileContext} for your query: "${query}"`,
    ``,
    `**Suggestions:**`,
    `- Try using different keywords or phrases`,
    `- Check if the information might be in files that aren't uploaded yet`,
    `- Be more specific about what you're looking for`,
    ``,
    `Let me know if you'd like me to search for something else or help in another way.`,
  ].join("\n");
}

// ============================================================================
// ERROR RESPONSE FORMATTING
// ============================================================================

/**
 * Create standardized error response for tool calls
 */
export function createErrorResponse(
  error: Error | string,
  query: string,
  projectId?: string
): {
  success: false;
  error: string;
  message: string;
  content: string;
  citations: any[];
} {
  const errorMessage = error instanceof Error ? error.message : String(error);

  return {
    success: false,
    error: errorMessage,
    message:
      "I encountered an error while searching through your files. Please try again.",
    content:
      "I apologize, but I couldn't search through your files right now. There was an error with the search system.",
    citations: [],
  };
}

/**
 * Create standardized success response for tool calls
 */
export function createSuccessResponse(
  content: string,
  citations: any[],
  query: string,
  metadata?: any
): {
  success: true;
  citationCount: number;
  message: string;
  content: string;
  citations: any[];
  metadata?: any;
} {
  return {
    success: true,
    citationCount: citations.length,
    message: `Generated response with ${citations.length} citation${
      citations.length > 1 ? "s" : ""
    } from your files.`,
    content,
    citations,
    metadata,
  };
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validate required parameters for tool calls
 */
export function validateToolCallParams(params: {
  query?: string;
  projectId?: string;
}): { valid: boolean; error?: string } {
  const { query } = params;

  if (!query) {
    return {
      valid: false,
      error: "Missing required parameters: query is required",
    };
  }

  if (typeof query !== "string" || query.trim().length === 0) {
    return {
      valid: false,
      error: "Query must be a non-empty string",
    };
  }

  return { valid: true };
}

/**
 * Sanitize and prepare query for processing
 */
export function sanitizeQuery(query: string): string {
  return query.trim().substring(0, 1000); // Limit query length
}

// ============================================================================
// LOGGING HELPERS
// ============================================================================

/**
 * Create standardized log context for tool calls
 */
export function createLogContext(
  query: string,
  projectId?: string,
  fileIds?: string[]
): {
  query: string;
  fullQuery: string;
  fileIds: number;
  fileIdsList: string[] | undefined;
  projectId: string | undefined;
  timestamp: string;
} {
  return {
    query: query.substring(0, 50),
    fullQuery: query,
    fileIds: fileIds?.length || 0,
    fileIdsList: fileIds,
    projectId,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create log context for response processing
 */
export function createResponseLogContext(
  query: string,
  citationCount: number,
  responseLength: number,
  projectId?: string
): {
  query: string;
  citationCount: number;
  responseLength: number;
  projectId: string | undefined;
} {
  return {
    query: query.substring(0, 50),
    citationCount,
    responseLength,
    projectId,
  };
}
