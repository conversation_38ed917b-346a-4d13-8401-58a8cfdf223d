/**
 * Pinecone Chat API Tools for Google GenAI Function Calling
 *
 * Defines the tools that expose Pinecone Assistant Chat API capabilities
 * to Google GenAI through the function calling mechanism.
 *
 * These are NOT MCP (Model Context Protocol) tools - they are Google GenAI
 * function calling tools that provide access to Pinecone's Chat API with citations.
 */

import { createLogger } from "@/services/logger";
import { queryPineconeChat } from "./index";
import { processCitations } from "./citationProcessor";
import {
  formatChatResponseForLLM,
  createNoResultsResponse,
  createErrorResponse,
  createSuccessResponse,
  validateToolCallParams,
  sanitizeQuery,
  createLogContext,
  createResponseLogContext,
} from "./responseFormatter";

const logger = createLogger("PineconeContextTools");

/**
 * Handle chat API tool calls from Google GenAI
 */
export async function handlePineconeChatToolCall(
  toolName: string,
  args: any
): Promise<any> {
  logger.info("Processing Pinecone chat tool call", { toolName });

  try {
    switch (toolName) {
      case "query_pinecone_chat":
        return await handleQueryPineconeChat(args);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  } catch (error) {
    logger.error("Context tool call failed", {
      toolName,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Handle query_pinecone_chat tool call - Optimized for natural LLM processing
 */
async function handleQueryPineconeChat(args: any): Promise<any> {
  const { query, fileIds, projectId } = args;

  // Validate required parameters
  const validation = validateToolCallParams({ query, projectId });
  if (!validation.valid) {
    logger.error("Invalid parameters for chat search", {
      error: validation.error,
      query: query?.substring(0, 50),
      projectId,
    });
    return {
      success: false,
      error: validation.error,
      message:
        "I need a search query to find relevant information in your files.",
    };
  }

  const sanitizedQuery = sanitizeQuery(query);

  try {
    // Query Pinecone Chat API
    const response = await queryPineconeChat({
      messages: [{ role: "user", content: sanitizedQuery }],
      fileIds,
      projectId,
    });

    logger.info(
      "Pinecone chat query response received",
      createResponseLogContext(
        sanitizedQuery,
        response.citations?.length || 0,
        response.message?.content?.length || 0,
        projectId
      )
    );

    if (!response.message || !response.message.content) {
      logger.warn("No relevant content found in Pinecone chat search", {
        query: sanitizedQuery.substring(0, 50),
        fileIds: fileIds?.length || 0,
        projectId,
      });

      return {
        success: true,
        citationCount: 0,
        message: "No relevant information found in your files for this query.",
        content: createNoResultsResponse(sanitizedQuery, fileIds),
        citations: [],
      };
    }

    // Format response for natural LLM processing
    const formattedContent = formatChatResponseForLLM(
      response,
      sanitizedQuery,
      fileIds
    );

    // Transform citations for client consumption with enhanced transcript information
    const structuredCitations = processCitations(response.citations || []);

    const result = createSuccessResponse(
      formattedContent,
      structuredCitations,
      sanitizedQuery,
      {
        searchQuery: sanitizedQuery,
        filesSearched: fileIds?.length || "all",
        citationCount: response.citations?.length || 0,
        finishReason: response.finish_reason,
        model: response.model,
        usage: response.usage,
      }
    );

    logger.info("Pinecone chat query completed successfully", {
      citationCount: result.citationCount,
      query: sanitizedQuery.substring(0, 50),
      projectId,
    });

    return result;
  } catch (error) {
    logger.error("Failed to query Pinecone chat", {
      error: error instanceof Error ? error.message : String(error),
      query: sanitizedQuery.substring(0, 100),
      projectId,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return createErrorResponse(error, sanitizedQuery, projectId);
  }
}

// Response formatting functions are now imported from responseFormatter.ts
