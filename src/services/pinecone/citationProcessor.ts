/**
 * Citation Processor
 *
 * Handles the transformation of raw Pinecone citations into enhanced,
 * frontend-ready citations with transcript metadata including timestamps,
 * speaker information, and rich text descriptions.
 */

import { createLogger } from "@/services/logger";
import { Citation, ChatCitation, ChatCitationReference } from "./types";
import {
  convertTimestampToSeconds,
  extractTimestampInfo,
  extractSpeakerInfo,
  parseSpeakers,
  createDisplayName,
} from "./transcriptUtils";

const logger = createLogger("CitationProcessor");

// ============================================================================
// CITATION PROCESSING
// ============================================================================

/**
 * Transform raw Pinecone citations into enhanced frontend-ready citations
 */
export function processCitations(citationsData: ChatCitation[]): Citation[] {
  if (!citationsData || citationsData.length === 0) {
    return [];
  }

  return citationsData.map((citation, index) => {
    logger.debug("Processing citation", {
      citationId: `citation-${index}`,
      position: citation.position,
      referenceCount: citation.references?.length || 0,
    });

    return {
      id: `citation-${index}`,
      position: citation.position,
      references: citation.references.map(
        (ref: ChatCitationReference, refIndex: number) => {
          return processCitationReference(ref, index, refIndex);
        }
      ),
    };
  });
}

/**
 * Process a single citation reference with transcript enhancements
 */
function processCitationReference(
  ref: ChatCitationReference,
  citationIndex: number,
  refIndex: number
): Citation["references"][number] {
  // Extract meaningful information from transcript metadata
  const isTranscript = ref.file.metadata?.is_transcript === true;
  const originalFilename =
    ref.file.metadata?.original_filename || ref.file.name;
  const speakers = parseSpeakers(ref.file.metadata?.speakers);
  const audioDuration = ref.file.metadata?.audio_duration;
  const transcriptSource = ref.file.metadata?.transcript_source;

  // Create a more descriptive display name
  const displayName = createDisplayName(originalFilename);

  // Use the highlight field from Pinecone citation data instead of extracting from AI response
  const contextSnippet = ref.highlight?.content || null;

  // Extract timestamp and speaker info directly from highlight content
  const timestampInfo =
    isTranscript && contextSnippet
      ? extractTimestampInfo(contextSnippet, 0)
      : null;

  const speakerInfo =
    isTranscript && contextSnippet
      ? extractSpeakerInfo(contextSnippet, 0)
      : null;

  // Convert timestamp to seconds for frontend video seeking
  const timestampSeconds = timestampInfo?.timestamp
    ? convertTimestampToSeconds(timestampInfo.timestamp)
    : undefined;

  // Create enhanced text description
  const textSnippet = createTextSnippet(
    isTranscript,
    contextSnippet,
    ref.pages
  );

  // Build the reference object with deduplicated metadata
  const {
    is_transcript,
    original_filename,
    audio_duration,
    transcript_source,
    ...cleanMetadata
  } = ref.file.metadata || {};

  const reference = {
    id: `ref-${citationIndex}-${refIndex}`,
    file: {
      id: ref.file.id,
      name: ref.file.name,
      displayName: displayName,
      metadata: {
        ...cleanMetadata,
        isTranscript,
        originalFilename,
        speakers,
        audioDuration,
        transcriptSource,
      },
    },
    pages: ref.pages,
    text: textSnippet,
  };

  // Add transcript-specific fields if this is a transcript
  if (isTranscript) {
    Object.assign(reference, {
      type: "transcript" as const,
      speakers: speakers,
      duration: audioDuration,
      source: transcriptSource,
    });

    // Add timestamp information if available
    if (timestampInfo) {
      Object.assign(reference, {
        timestamp: timestampInfo.timestamp,
        timestampFormatted: timestampInfo.formatted,
        timestampSeconds: timestampSeconds,
        timestampConfidence: timestampInfo.confidence || "medium",
      });
    }

    // Add speaker information if available
    if (speakerInfo) {
      Object.assign(reference, {
        currentSpeaker: speakerInfo.speaker,
      });
    }
  }

  return reference;
}

// ============================================================================
// TEXT DESCRIPTION HELPERS
// ============================================================================

/**
 * Create enhanced text description for citations
 */
function createTextSnippet(
  isTranscript: boolean,
  contextSnippet: string,
  pages: number[]
): string {
  if (isTranscript) {
    return contextSnippet;
  }
  // For non-transcript files, use simpler description
  return (
    contextSnippet ||
    (pages.length > 0 ? `Pages ${pages.join(", ")}` : "Referenced content")
  );
}

/**
 * Check if a context snippet contains structured patterns that might cause duplication
 * Updated to work with actual source content from Pinecone highlights
 */
function isStructuredPattern(snippet: string): boolean {
  if (!snippet) return false;

  // If the snippet looks like transcript content (has timestamps/speakers), don't treat as structured
  const transcriptPatterns = [
    /\[\d{2}:\d{2}:\d{2}\]/, // [00:10:01] timestamp pattern
    /Speaker\s+\d+:/, // Speaker 1: pattern
  ];

  // If it contains transcript patterns, it's actual source content - use it
  if (transcriptPatterns.some((pattern) => pattern.test(snippet))) {
    return false;
  }

  // Check for common structured patterns that cause duplication (from AI-generated content)
  const structuredPatterns = [
    /-\s*\*\*[^*]+\*\*\s*:/, // - **Title**: pattern anywhere in text
    /#+\s*\*\*[^*]+\*\*/, // ### **Title** pattern anywhere in text
    /\d+\.\s*\*\*[^*]+\*\*\s*:/, // 4. **Title**: pattern (numbered lists)
    /\*\*[^*]+\*\*\s*:/, // **Title**: pattern anywhere in text
    /---\s*#+/, // --- ### pattern (markdown dividers)
    /\*\*[^*]+\*\*\s*-/, // **Title** - pattern
  ];

  return structuredPatterns.some((pattern) => pattern.test(snippet));
}

// ============================================================================
// VALIDATION AND UTILITIES
// ============================================================================

/**
 * Validate citation data structure
 */
export function validateCitationData(citations: any[]): boolean {
  if (!Array.isArray(citations)) {
    logger.warn("Citations is not an array", {
      citationsType: typeof citations,
      isNull: citations === null,
      isUndefined: citations === undefined,
    });
    return false;
  }

  for (const citation of citations) {
    if (
      !citation.position ||
      !citation.references ||
      !Array.isArray(citation.references)
    ) {
      logger.warn("Invalid citation structure", { citation });
      return false;
    }

    for (const ref of citation.references) {
      if (!ref.file || !ref.file.id || !ref.file.name) {
        logger.warn("Invalid reference structure", { ref });
        return false;
      }
    }
  }

  return true;
}

/**
 * Get citation processing statistics
 */
export function getCitationStats(citations: Citation[]): {
  totalCitations: number;
  totalReferences: number;
  transcriptReferences: number;
  documentReferences: number;
  timestampedReferences: number;
} {
  const stats = {
    totalCitations: citations.length,
    totalReferences: 0,
    transcriptReferences: 0,
    documentReferences: 0,
    timestampedReferences: 0,
  };

  for (const citation of citations) {
    stats.totalReferences += citation.references.length;

    for (const ref of citation.references) {
      if (ref.type === "transcript") {
        stats.transcriptReferences++;
        if (ref.timestamp) {
          stats.timestampedReferences++;
        }
      } else {
        stats.documentReferences++;
      }
    }
  }

  return stats;
}
