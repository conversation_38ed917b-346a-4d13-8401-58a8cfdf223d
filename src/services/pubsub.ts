import { PubSub, Message } from "@google-cloud/pubsub";
import { log } from "@/services/logger";
import {
  AidaPubsubTopic,
  gcpPubsubSubscriptionName,
  gcpPubsubTopicName,
  getGcsServiceAccount,
  gcpPubsubTranscoderSubscriptionName,
  gcpPubsubTranscoderTopicName,
} from "@/config";
import { subcribersHandler } from "@/handlers/subcribers";


class PubSubService {
  private pubsub: PubSub | null = null;
  private topicName: string;
  private subscriptionName: string;

  constructor(
    topicName: string,
    subscriptionName: string
  ) {
    this.topicName = topicName;
    this.subscriptionName = subscriptionName;
  }

  private async getPubSub() {
    if (!this.pubsub) {
      const serviceAccount = await getGcsServiceAccount();
      this.pubsub = new PubSub({ credentials: serviceAccount });
    }
    return this.pubsub;
  }

  async publishMessage(data: any, attributes?: Record<string, string>) {
      const pubsub = await this.getPubSub();
      const messageBuffer = Buffer.from(JSON.stringify(data));
      const messageId = await pubsub
        .topic(this.topicName)
        .publish(messageBuffer, attributes);
          log.info(`Message ${messageId} published to topic ${this.topicName}`);
      return messageId;
  }

  async startListening(
    handler: (message: Message) => Promise<void>
  ) {
    const pubsub = await this.getPubSub();
    const subscription = pubsub.subscription(this.subscriptionName);

    subscription.on("message", handler);

    subscription.on("error", (error) => {
      log.error("Subscription error:", error);
    });

    log.info(`Started listening to subscription ${this.subscriptionName}`);
  }

  async createTopic(): Promise<void> {
    try {
      const [topic] = await this.pubsub.createTopic(this.topicName);
      log.info(`Topic ${topic.name} created successfully`);
    } catch (error) {
      log.error("Error creating topic:", error);
      throw error;
    }
  }

  async createSubscription(): Promise<void> {
    try {
      const [subscription] = await this.pubsub
        .topic(this.topicName)
        .createSubscription(this.subscriptionName);

      log.info(`Subscription ${subscription.name} created successfully`);
    } catch (error) {
      log.error("Error creating subscription:", error);
      throw error;
    }
  }
}

export const pubsubService = new PubSubService(gcpPubsubTopicName, gcpPubsubSubscriptionName);
export const transcoderPubsubService = new PubSubService(gcpPubsubTranscoderTopicName, gcpPubsubTranscoderSubscriptionName);
export const documentProcessorPubsubService = new PubSubService(AidaPubsubTopic.DOCUMENT_PROCESSOR, "");

export const initPubSub = async () => {
  pubsubService
    .startListening( async (message: Message) => {
      try {
        const data = JSON.parse(message.data.toString());
        const messageType = message.attributes?.type;
        if (messageType && messageType in subcribersHandler) {
          await subcribersHandler[messageType](data, message.attributes);
        } else {
          log.warn(`No handler found for message type: ${messageType}`);
        }
        message.ack();
      } catch (error) {
        log.error("Error processing message:", error);
        message.nack();
      }
    })
    .catch((err) => log.error("Error setting up PubSub subscription:", err));

  transcoderPubsubService
    .startListening(async (message) => {

      try {
        const data = JSON.parse(message.data.toString());
        const messageType = message.attributes?.type;
        const handler = subcribersHandler[AidaPubsubTopic.TRANSCODING_SYNC];
        if (handler) {
          await handler(data, message.attributes);
        } else {
          log.warn(`No handler found for message type: ${messageType}`);
        }
        message.ack();
      } catch (error) {
        log.error("Error processing message:", error);
        message.nack();
      }
     
      
    })
    .catch((err) => log.error("Error setting up PubSub subscription:", err));
};
