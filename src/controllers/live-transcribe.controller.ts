import { Request, Response } from "express";
import { speechmaticsApiKey } from "@/config";
import { createSpeechmaticsJWT } from "@speechmatics/auth";

export const createJwtHandler = async (req: Request, res: Response) => {
  const apiKey = speechmaticsApiKey;
  const { tokenType = "rt", ttl = 60 } = req.body;

  if (!apiKey) {
    throw new Error("Please set the SPEECHMATICS_API_KEY environment variable");
  }

  const jwt = await createSpeechmaticsJWT({
    type: tokenType,
    apiKey,
    ttl,
  });

  res.json({ jwt });
};
