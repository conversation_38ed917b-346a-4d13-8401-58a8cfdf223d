import type { Request, Response, NextFunction } from "express";

import { createConversationHandler } from "@/handlers/agent/createConversationHandler";
import {
  createConversationSchema,
  createMessageSchema,
} from "@/validators/agent.validators";
import type { CreateMessageRequest } from "@/validators/agent.validators";
import { createErrorResponse } from "@/utils/response";
import { log } from "@/services/logger";
import { agentTrackingService } from "@/services/agent/tracking.service";
import {
  createMessageStreamHandler,
  convertConversationToResourceHandler,
} from "@/handlers/agent";
import { createProgressReporter } from "@/services/agent/ProgressReporter";
import {
  getAllModels,
  getModel,
  isModelSupported,
  getModelsByProvider,
} from "@/config/models";
import { ProviderRegistry } from "@/services/agent/provider-registry";

export async function createConversation(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    createConversationSchema.parse(req.body);
    await createConversationHandler(req, res);
  } catch (error) {
    log.error("Validation error in createConversation:", error);
    res
      .status(400)
      .json(createErrorResponse("Invalid request body", 400, error));
    next(error);
  }
}

export async function convertConversationToResource(
  req: Request<
    { conversationId: string },
    any,
    { format?: "markdown" | "docx" | "txt" }
  >,
  res: Response,
  next: NextFunction
) {
  try {
    await convertConversationToResourceHandler(req, res);
  } catch (error) {
    log.error("Error in convertConversationToResource:", error);
    res
      .status(500)
      .json(createErrorResponse("Internal server error", 500, error));
    next(error);
  }
}

export async function createMessageStream(
  req: Request<{ conversationId: string }, any, CreateMessageRequest>,
  res: Response,
  next: NextFunction
) {
  try {
    createMessageSchema.parse(req.body);

    // Set headers for Server-Sent Events (improved based on best practices)
    res.setHeader("Content-Type", "text/event-stream; charset=utf-8");
    res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    res.setHeader("Pragma", "no-cache");
    res.setHeader("Expires", "0");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("X-Accel-Buffering", "no"); // Disable nginx buffering
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Cache-Control, Content-Type, Authorization"
    );
    res.setHeader("Access-Control-Expose-Headers", "Content-Type");

    // Send initial connection confirmation
    res.write('data: {"type":"connection","status":"connected"}\n\n');
    res.flushHeaders();

    // Create progress reporter for meaningful user updates
    const progressReporter = createProgressReporter(res);

    // Track streaming started event
    agentTrackingService.trackStreamingStarted(
      res.locals.user.id,
      req.params.conversationId,
      req.body.model
    );

    await createMessageStreamHandler(req, res, progressReporter);
  } catch (error) {
    log.error("Validation error in createMessageStream:", error);

    res
      .status(400)
      .json(createErrorResponse("Invalid request body", 400, error));
    next(error);
  }
}

/**
 * Get all available AI models organized by provider
 */
export async function getAvailableModels(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // Use the provider registry to get actually available models
    const providerRegistry = new ProviderRegistry();
    const availableModels = providerRegistry.getAvailableModels();

    // Get model details for available models only
    const models = availableModels.map((modelId) => getModel(modelId));

    const modelsByProvider = {
      anthropic: providerRegistry.getModelsByProvider("anthropic"),
      google: providerRegistry.getModelsByProvider("google"),
    };

    log.info("Available models requested", {
      userId: res.locals.user?.id,
      totalModels: models.length,
      availableModels,
    });

    res.json({
      success: true,
      message: "Available models retrieved",
      data: {
        models: modelsByProvider,
        all: models,
        total: models.length,
      },
      statusCode: 200,
    });
  } catch (error) {
    log.error("Error getting available models:", error);
    res
      .status(500)
      .json(createErrorResponse("Failed to get available models", 500, error));
    next(error);
  }
}

/**
 * Validate if a specific model is supported
 */
export async function validateModel(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const { modelId } = req.body;

    if (!modelId) {
      res.status(400).json(createErrorResponse("Model ID is required", 400));
      return;
    }

    const isSupported = isModelSupported(modelId);
    let modelInfo = null;

    if (isSupported) {
      try {
        modelInfo = getModel(modelId);
      } catch (error) {
        // Model lookup failed
        log.warn("Model lookup failed despite being supported", {
          modelId,
          error: error.message,
        });
      }
    }

    log.info("Model validation requested", {
      modelId,
      isSupported,
      userId: res.locals.user?.id,
    });

    res.json({
      success: true,
      message: isSupported ? "Model is supported" : "Model is not supported",
      data: {
        modelId,
        isSupported,
        model: modelInfo,
      },
      statusCode: 200,
    });
  } catch (error) {
    log.error("Error validating model:", error);
    res
      .status(500)
      .json(createErrorResponse("Failed to validate model", 500, error));
    next(error);
  }
}

/**
 * Get system status including model availability
 */
export async function getSystemStatus(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // Initialize provider registry for health checks
    const providerRegistry = new ProviderRegistry();

    const models = getAllModels();
    const modelCounts = {
      total: models.length,
      byProvider: {
        anthropic: getModelsByProvider("anthropic").length,
        google: getModelsByProvider("google").length,
      },
    };

    // Get provider health status
    const providerHealth = await providerRegistry.getProviderHealth();
    const systemStatus = providerRegistry.getSystemStatus();

    log.info("System status requested", {
      userId: res.locals.user?.id,
      modelCounts,
      providerHealth,
    });

    res.json({
      success: true,
      message: "Multi-provider system status retrieved",
      data: {
        status: "operational",
        models: modelCounts,
        providers: {
          health: providerHealth,
          available: systemStatus.availableProviders,
          details: systemStatus.providerModelCounts,
        },
        features: {
          multiProviderSupport: true,
          crossProviderFallbacks: true,
          resourceExhaustedFallback: true,
          enhancedBilling: true,
        },
        timestamp: new Date().toISOString(),
      },
      statusCode: 200,
    });
  } catch (error) {
    log.error("Error getting system status:", error);
    res
      .status(500)
      .json(createErrorResponse("Failed to get system status", 500, error));
    next(error);
  }
}
