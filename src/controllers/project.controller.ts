import { getFirebaseUserById } from "@/services/firebase";
import { Request, Response } from "express";
import * as invitationService from "@/services/project/projectInvitation.service";
import * as memberService from "@/services/project/projectMember.service";
import * as sharedLinkService from "@/services/project/projectSharedLink.service";
import * as accessRequestService from "@/services/project/projectAccessRequest.service";
import * as projectService from "@/services/project/project.service";
import * as resourceService from "@/handlers/resources/resource.service";
import { ProjectAccessRequestStatus } from "@/schemas/project/ProjectAccessRequest.model";
import { createLogger, log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Project } from "@/schemas/project/Project.model";
import { createErrorResponse } from "@/utils/response";
import { DEFAULT_PAGE_NUMBER, DEFAULT_PAGE_SIZE, DECIMAL_RADIX } from "@/constants/pagination";
import { gcsResourceFolder } from "@/config";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { createNewResource } from "@/schemas/resource/utils";
import { createTranscriptionsAndUpdateStatus } from "@/services/transcription/handleTranscriptSuccess";
import * as projectResourceService from "@/services/project/projectResource.service";
import { GetProjectResourcesParams, SSEEventType } from "@/types";
import { getSSEService } from "@/services/sse/sse.service";
import { InvitationStatus } from "@/schemas/project/ProjectInvitation.model";

const logger = createLogger("ProjectController");

export const inviteUserToProjectViaEmail = async (
  req: Request,
  res: Response
) => {
  const { email, role } = req.body;
  const { projectId } = req.params;
  const forceSendEmail = req.query.force === "true";

  const senderEmail = res.locals.user?.email;

  const projectInvitation = await invitationService.createProjectInvitation({
    projectId,
    email,
    role,
    senderEmail,
    forceSendEmail,
  });

  res.status(projectInvitation.statusCode).json(projectInvitation);

  return;
};

export const acceptInvitationToProject = async (
  req: Request,
  res: Response
) => {
  const { code } = req.body;
  const userId = res.locals.user?.id;

  const verifyInvitation = await invitationService.verifyProjectInvitation({
    code,
    userId,
    email: res.locals.user?.email?.toLowerCase(),
  });
  res.status(verifyInvitation.statusCode).json(verifyInvitation);
  return;
};

export const rejectInvitationToProject = async (
  req: Request,
  res: Response
) => {
  // TODO: Implement this, we dont have this feature yet, user just ignore the invitation email
};

export const getProjectInvitations = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const { status } = req.query;

  const projectInvitations = await invitationService.getProjectInvitations(
    projectId,
    status as InvitationStatus
  );

  res.status(projectInvitations.statusCode).json(projectInvitations);
  return;
};

export const getProjectMembers = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const projectMembers = await memberService.getProjectMembers(projectId);
  res.status(projectMembers.statusCode).json(projectMembers);
  return;
};

export const createProjectSharedLink = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const { expriedInDays, maxAccessCount } = req.body;
  const forceCreate = req.query.force === "true";

  const projectSharedLink = await sharedLinkService.createProjectSharedLink({
    projectId,
    expriedInDays,
    maxAccessCount,
    forceCreate,
  });
  res.status(projectSharedLink.statusCode).json(projectSharedLink);
  return;
};

export const verifyProjectSharedLink = async (req: Request, res: Response) => {
  const { token } = req.body;
  const { projectId } = req.params;
  const requestedUser: any = res.locals.user;
  requestedUser.uid = requestedUser.id; // TODO: res.local.user should be User firebase-auth-user type

  const forceSendEmail = req.query.force !== "false";

  const projectSharedLink = await sharedLinkService.verifySharedLinkToken({
    token,
    projectId,
  });

  if (!projectSharedLink.success) {
    res.status(projectSharedLink.statusCode).json(projectSharedLink);
    return;
  }

  const projectInvitationVerify = await invitationService.verifyProjectInvitationFromSharedLink({
    userId: requestedUser.uid,
    email: requestedUser.email,
    projectId,
  });

  const project = await projectService.getProjectById(projectId);

  if (!project.success) {
    res.status(project.statusCode).json(project);
    return;
  }

  const approverUser = await getFirebaseUserById(
    project.data.createdById
  ).catch((error) => {
    return null;
  });

  if (!approverUser) {
    res.status(404).json({
      success: false,
      message: "Approver user not found",
      statusCode: 404,
    });
    return;
  }

  if (approverUser.email === requestedUser.email) {
    res.status(409).json({
      success: false,
      message: "Approver user and requested user are the same",
    });
    return;
  }

  await sharedLinkService.incrementProjectSharedLinkAccessCount(projectId);

  const user = res.locals.user;
  // If user is already a member of the project, we don't need to create an access request

  const isMember = projectInvitationVerify.success;
  if(!isMember) {
    const accessRequest = await accessRequestService.createProjectAccessRequest({
      project: project.data,
      requestedUser: user,
      approverUser: approverUser,
      forceSendEmail,
    });

    // Broadcast acl.member.added event via SSE
    try {
      const sseService = getSSEService();
      await sseService.broadcastToProject(projectId, {
          type: SSEEventType.ACL_MEMBER_ACCEPT_INVITATION,
          projectId,
          userId: approverUser.uid,
          data: {
              userId: user.uid,
              userName: user.displayName || user.email,
          },
      });
      
      logger.info('SSE broadcast sent for member addition', {
          projectId,
          userId: user.uid,
      });
    } catch (error) {
        logger.error('Failed to broadcast acl.member.added event via SSE', {
            error,
            projectId,
            userId: user.uid,
        });
    }

    res.status(accessRequest.statusCode).json(accessRequest);
  } else {
    res.status(projectInvitationVerify.statusCode).json(projectInvitationVerify);
  }

  return;
};

export const getProjectSharedLinks = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const projectSharedLinks =
    await sharedLinkService.getAllProjectSharedLinksByProjectId(projectId);
  res.status(projectSharedLinks.statusCode).json(projectSharedLinks);
  return;
};

export const approveOrRejectAccessRequest = async (
  req: Request,
  res: Response
) => {
  const { accessRequestId } = req.params;
  const { isApproved } = req.body;
  const accessRequest = await accessRequestService.responseProjectAccessRequest(
    { id: accessRequestId, isApproved }
  );
  res.status(accessRequest.statusCode).json(accessRequest);
  return;
};

export const changeProjectMemberRole = async (req: Request, res: Response) => {
  const { memberUserId, projectId } = req.params;
  const { role } = req.body;
  const ownerUserId = res.locals.user?.id;

  const projectMember = await memberService.changeRoleOfProjectMember({
    projectId,
    memberUserId,
    role,
    ownerUserId,
  });

  res.status(projectMember.statusCode).json(projectMember);
  return;
};

/**
 * Change role of a single project invitation
 */
export const changeInvitationRole = async (req: Request, res: Response) => {
  try {
    const { projectId, inviteId } = req.params;
    const { role } = req.body;

    const result = await invitationService.changeInvitationRole(
      projectId,
      inviteId,
      role
    );

    res.status(result.statusCode).json(result);
    return;
  } catch (error) {
    log.error(`Error changing invitation role: ${error.message}`);
    const errorResponse = createErrorResponse("Internal server error", 500);
    res.status(errorResponse.statusCode).json(errorResponse);
    return;
  }
};

export const getProjectAccessRequests = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const { status } = req.query;
  const projectAccessRequests =
    await accessRequestService.getProjectAccessRequestsByProjectId(
      projectId,
      status as ProjectAccessRequestStatus
    );
  res.status(projectAccessRequests.statusCode).json(projectAccessRequests);
  return;
};

export const getMyProjectMembership = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const projectMembers = await memberService.getProjectMember(
    projectId,
    res.locals.user.id
  );
  res.status(projectMembers.statusCode).json(projectMembers);
  return;
};

export const deleteProjectMember = async (req: Request, res: Response) => {
  const { memberUserId, projectId } = req.params;
  const projectMember = await memberService.deleteProjectMember(
    projectId,
    memberUserId
  );

  // delete all invitations / requests to this project
  await invitationService.deleteProjectInvitationOfUser(
    projectId,
    memberUserId
  );
  await accessRequestService.deleteProjectAccessRequestOfUser(
    projectId,
    memberUserId
  );

  res.status(projectMember.statusCode).json(projectMember);
  return;
};

export const leaveProject = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const userId = res.locals.user?.id;

  try {
    const result = await memberService.leaveProject(projectId, userId);

    if (result.success) {
      // Clean up any pending invitations and access requests for this user
      await invitationService.deleteProjectInvitationOfUser(projectId, userId);
      await accessRequestService.deleteProjectAccessRequestOfUser(projectId, userId);
    }

    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error(`Failed to leave project ${projectId} for user ${userId}`, error);
    const errorResponse = createErrorResponse(
      "Failed to leave project",
      500
    );
    res.status(errorResponse.statusCode).json(errorResponse);
  }
};

export const getProjectDetails = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const userId = res.locals.user?.id;
  const pagination = res.locals.pagination;

  try {
    const result = await projectService.getProjectDetails(
      projectId,
      userId,
      pagination?.search,
      pagination?.limit,
      pagination?.offset,
      pagination?.sort,
      pagination?.sortOrder
    );

    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error(`Failed to get project ${projectId}`, error);
    throw new IntentionalError(`Failed to get project ${projectId}`);
  }
};

export const getProjectInfo = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const userId = res.locals.user?.id;

  try {
    const result = await projectService.getProjectInfo(projectId, userId);
    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error(`Failed to get project info ${projectId}`, error);
    throw new IntentionalError(`Failed to get project info ${projectId}`);
  }
};

export const getProjects = async (req: Request, res: Response) => {
  const userId = res.locals.user?.id;
  const accessibleProjects = res.locals.accessibleProjects;
  const result = await projectService.getProjects(userId, accessibleProjects);
  res.status(result.statusCode).json(result.data);
  return;
};

export const createProjectDefault = async (req: Request, res: Response) => {
  try {
    const currentUser = res.locals.user;
    const name = "Default";
    const description = "Default project";

    // Check if user has already has any default project
    const defaultProject = await projectService.getProjectDefault(
      currentUser.id
    );
    if (defaultProject.success) {
      res.status(200).json(defaultProject);
      return;
    }

    // If no default project, create one
    const project = await projectService.createProject(
      currentUser.id,
      name,
      description,
      true
    );

    // Update all resources to have the default projectId
    await resourceService.backfillUserResourcesWithDefaultProject(
      currentUser.id,
      project.data.id
    );

    res.json(project);
  } catch (error) {
    log.error(`Failed to create project: ${error.message}`);
    throw new IntentionalError("Failed to create project");
  }
};

/**
 * Ensure all user resources have a project by assigning them to default project
 * This endpoint will find or create a default project and assign all orphaned resources to it
 */
export const ensureAllResourcesHaveProject = async (req: Request, res: Response) => {
  try {
    const currentUser = res.locals.user;
    
    // Get or create default project
    let defaultProject = await projectService.getProjectDefault(currentUser.id);
    
    if (!defaultProject.success) {
      // Create default project if it doesn't exist
      const project = await projectService.createProject(
        currentUser.id,
        "Default",
        "Default project for user resources",
        true
      );
      defaultProject = project;
    }

    // Update all resources to have the default projectId
    const result = await resourceService.backfillUserResourcesWithDefaultProject(
      currentUser.id,
      defaultProject.data.id
    );

    res.json({
      success: true,
      message: "All resources now have projects assigned",
      data: {
        defaultProject: defaultProject.data,
        resourcesUpdated: result
      }
    });
  } catch (error) {
    log.error(`Failed to ensure all resources have project: ${error.message}`);
    throw new IntentionalError("Failed to ensure all resources have project");
  }
};

export const updateProject = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const currentUser = res.locals.user;
  const payload = req.body as Partial<Project>;

  try {
    const result = await projectService.updateProject(
      projectId,
      currentUser.id,
      payload
    );
    res.json(result);
  } catch (error) {
    log.error(`Failed to update project ${projectId}`, error);
    throw new IntentionalError(`Failed to update project ${projectId}`);
  }
};

export const resendInvitation = async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const { email } = req.body;
    const senderEmail = res.locals.user?.email;

    if (!senderEmail) {
      const errorResponse = createErrorResponse("Unauthorized", 401);
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    const result = await invitationService.resendExpiredInvitation({
      projectId,
      email,
      senderEmail
    });

    res.status(result.statusCode).json(result);
    return;
  } catch (error) {
    log.error(`Error resending invitation: ${error.message}`);
    const errorResponse = createErrorResponse("Internal server error", 500);
    res.status(errorResponse.statusCode).json(errorResponse);
    return;
  }
};

export const cancelProjectInvitation = async (req: Request, res: Response) => {
  try {
    const { projectId, inviteId } = req.params;
    const canceledByUserId = res.locals.user?.id;
    const canceledByUserEmail = res.locals.user?.email;

    if (!canceledByUserId || !canceledByUserEmail) {
      const errorResponse = createErrorResponse("Unauthorized", 401);
      res.status(errorResponse.statusCode).json(errorResponse);
      return;
    }

    const result = await invitationService.cancelProjectInvitation({
      inviteId,
      projectId,
      canceledByUserId,
      canceledByUserEmail
    });

    res.status(result.statusCode).json(result);
    return;
  } catch (error) {
    log.error(`Error canceling invitation: ${error.message}`);
    const errorResponse = createErrorResponse("Internal server error", 500);
    res.status(errorResponse.statusCode).json(errorResponse);
    return;
  }
};

export const createProjectResource = async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const { uploadedFileName, fileName, fileSize, fileLastModified, transcript } = req.body;
  const currentUser = res.locals.user;
  const filePath = `${gcsResourceFolder}/${uploadedFileName}`;
  try {
    if (!fileName) {
      res.status(400).json({ message: "Missing file name" });
      return;
    }

    if (!uploadedFileName) {
      res.status(400).json({ message: "Missing uploaded file name" });
      return;
    }

    const resourceData: Parameters<typeof createNewResource>[0] = {
      gcsFilePath: filePath,
      fileLastModified,
      fileName,
      fileSize,
      userId: currentUser.id,
      title: fileName,
      uploadAction: IEUploadAction.UPLOAD,
      projectId,
      transcribe: false
    };

    const { resource, insightEngine, resourceInIE } = await createNewResource(resourceData);

    await createTranscriptionsAndUpdateStatus(transcript, resourceInIE, resource);

    res.status(200).json(resource);
  } catch (error) {
    log.error(`[createProjectResource] Failed to create resource`, error);
    throw new IntentionalError("Failed to create resource");
  }
  // filepath is a url to the file
  // transcript is an array of strings, each string is a line of the transcript
}

/**
 * Get project resources with pagination and filtering
 */
export const getProjectResources = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId } = req.params;
    const userId = res.locals.user?.id;
    const query = req.query;

    if (!userId) {
      res.status(401).json(createErrorResponse("Unauthorized"));
      return;
    }

    const params: GetProjectResourcesParams = {
      projectId,
      userId,
      page: parseInt(String(query.page || DEFAULT_PAGE_NUMBER.toString()), DECIMAL_RADIX),
      limit: parseInt(String(query.limit || DEFAULT_PAGE_SIZE.toString()), DECIMAL_RADIX),
      search: query.search as string,
      fileType: query.fileType as string,
      processingStatus: query.processingStatus as string,
      sortBy: query.sortBy as any,
      sortOrder: query.sortOrder as 'asc' | 'desc',
    };

    const result = await projectResourceService.getProjectResources(params);
    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error('Failed to get project resources', { 
      error: error.message, 
      projectId: req.params.projectId,
      userId: res.locals.user?.id 
    });
    res.status(500).json(createErrorResponse("Failed to retrieve project resources"));
  }
};

/**
 * Get all resource IDs for a project without pagination
 * Returns array of resource IDs that belong to the project
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<void>}
 */
export const getAllProjectResourceIds = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId } = req.params;

    const result = await projectResourceService.getAllProjectResourceIds(projectId);
    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error('Error getting all project resource IDs', { 
      error: error.message, 
      projectId: req.params.projectId,
      userId: res.locals.user?.id 
    });
    const errorResponse = createErrorResponse('Failed to get project resource IDs', 500);
    res.status(errorResponse.statusCode).json(errorResponse);
  }
};
