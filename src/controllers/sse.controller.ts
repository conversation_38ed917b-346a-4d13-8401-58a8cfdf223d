import { Request, Response } from 'express';
import { subscribeToProjectHandler } from '@/handlers/sse/subscribeToProjectHandler';
import { log } from '@/services/logger';
import { createErrorResponse } from '@/utils/response';

/**
 * SSE Controller - Subscribe to project events
 * Establishes Server-Sent Events connection for real-time project updates
 */
export const subscribeToProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId } = req.params;
    const user = res.locals.user;
    const userAgent = req.get('User-Agent');
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';

    // Validate required data
    if (!user) {
      res.status(401).json(createErrorResponse('Authentication required'));
      return;
    }

    if (!projectId) {
      res.status(400).json(createErrorResponse('Project ID is required'));
      return;
    }

    const result = await subscribeToProjectHandler(
      { projectId },
      user,
      res,
      userAgent,
      ipAddress
    );

    // If the handler failed before establishing SSE connection, send error response
    if (!result.success && !res.headersSent) {
      res.status(result.statusCode).json(createErrorResponse(result.message || 'Failed to establish SSE connection'));
    }
  } catch (error) {
    log.error('SSE controller error', {
      error,
      projectId: req.params.projectId,
      userId: res.locals.user?.id,
    });

    if (!res.headersSent) {
      res.status(500).json(createErrorResponse('Internal server error'));
    }
  }
}; 
