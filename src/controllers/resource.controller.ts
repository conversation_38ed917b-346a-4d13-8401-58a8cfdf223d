import { Request, Response } from "express";
import * as resourceService from "../handlers/resources/resource.service";
import { getResourceData } from "@/schemas/resource/utils";

export const getResourceById = async (req: Request, res: Response) => {
    const { id } = req.params;

    const result = await resourceService.getResourceById(id, res.locals.user?.id);

    if (!result.success) {
        res.status(result.statusCode).json({ message: result.message });
        return;
    }

    const resourceData = await getResourceData(result.data) as any;
    resourceData.userPermissions = res.locals.resourceUserPermissions;
    res.status(result.statusCode).json(resourceData);
    return;
}

export const getAllUserResources = async (req: Request, res: Response) => {
    const currentUser = res.locals.user;
    const accessibleProjects = res.locals.accessibleProjects;
    const result = await resourceService.getAllUserResources(currentUser.id, accessibleProjects);
    res.status(result.statusCode).json(result.data);
    return;
}

/**
 * Get transcoding status for multiple resources
 * @param req Express request
 * @param res Express response
 */
export const getResourcesTranscodingStatus = async (req: Request, res: Response): Promise<void> => {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids)) {
        res.status(400).json({ 
            success: false,
            message: "Invalid request: 'ids' must be an array of resource IDs",
            statusCode: 400
        });
        return;
    }
    
    // Validate that all IDs are strings
    if (!ids.every(id => typeof id === 'string' && id.trim().length > 0)) {
        res.status(400).json({
            success: false,
            message: "Invalid request: All resource IDs must be non-empty strings",
            statusCode: 400
        });
        return;
    }
    
    // Limit the number of IDs to prevent performance issues
    const MAX_IDS = 100;
    if (ids.length > MAX_IDS) {
        res.status(400).json({
            success: false,
            message: `Too many resource IDs. Maximum allowed is ${MAX_IDS}`,
            statusCode: 400
        });
        return;
    }
    
    const result = await resourceService.getResourcesTranscodingStatus(ids);

    res.status(result.statusCode).json(result);
}
