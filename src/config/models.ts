// Import centralized types
import { ProviderType, SupportedModel } from "@/types/universal-ai";

export interface ModelConfig {
  id: SupportedModel;
  name: string;
  provider: ProviderType;
  pricing: {
    input: number; // Cost per 1M input tokens (USD)
    output: number; // Cost per 1M output tokens (USD)
  };
  fallback?: SupportedModel; // Fallback model ID for resource exhausted errors
}

// Default model configuration
export const DEFAULT_MODEL: SupportedModel = "gemini-2.5-flash"; // Fast, cost-effective default

export const MODELS: Record<SupportedModel, ModelConfig> = {
  // Anthropic Models (Latest 4)
  "claude-3-7-sonnet-20250219": {
    id: "claude-3-7-sonnet-20250219",
    name: "Claude 3.7 Sonnet",
    provider: "anthropic",
    pricing: { input: 3.0, output: 15.0 },
    fallback: "gemini-2.5-pro",
  },
  "claude-sonnet-4-20250514": {
    id: "claude-sonnet-4-20250514",
    name: "<PERSON> Sonnet 4",
    provider: "anthropic",
    pricing: { input: 3.0, output: 15.0 },
    fallback: "gemini-2.5-pro",
  },
  "claude-opus-4-20250514": {
    id: "claude-opus-4-20250514",
    name: "Claude Opus 4",
    provider: "anthropic",
    pricing: { input: 15.0, output: 75.0 },
    fallback: "gemini-2.5-pro",
  },
  "claude-opus-4-1-20250805": {
    id: "claude-opus-4-1-20250805",
    name: "Claude Opus 4.1",
    provider: "anthropic",
    pricing: { input: 15.0, output: 75.0 },
    fallback: "gemini-2.5-pro",
  },

  // Google Models (Latest)
  "gemini-2.5-flash": {
    id: "gemini-2.5-flash",
    name: "Gemini 2.5 Flash",
    provider: "google",
    pricing: { input: 1.0, output: 3.5 },
    fallback: "claude-3-7-sonnet-20250219",
  },
  "gemini-2.5-pro": {
    id: "gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "google",
    pricing: { input: 2.5, output: 15.0 },
    fallback: "claude-sonnet-4-20250514",
  },
};

/**
 * Get model configuration by ID
 * @param modelId - Model identifier
 * @returns Model configuration
 * @throws Error if model not found
 */
export function getModel(modelId: string): ModelConfig {
  const model = MODELS[modelId as SupportedModel];
  if (!model) {
    throw new Error(
      `Model ${modelId} not found. Available: ${Object.keys(MODELS).join(", ")}`
    );
  }
  return model;
}

/**
 * Get fallback model for resource exhausted recovery
 * @param modelId - Current model that failed
 * @returns Fallback model ID or null if no fallback available
 */
export function getFallbackModel(modelId: string): SupportedModel | null {
  try {
    const model = getModel(modelId);
    return model.fallback || null;
  } catch {
    return null; // Invalid model ID
  }
}

/**
 * Check if a model is supported
 * @param modelId - Model identifier to check
 * @returns True if model is supported
 */
export function isSupportedModel(modelId: string): modelId is SupportedModel {
  return modelId in MODELS;
}

/**
 * Get all available models
 * @returns Array of all model configurations
 */
export function getAllModels(): ModelConfig[] {
  return Object.values(MODELS);
}

/**
 * Get models by provider
 * @param provider - Provider to filter by
 * @returns Array of models from the specified provider
 */
export function getModelsByProvider(
  provider: ModelConfig["provider"]
): ModelConfig[] {
  return getAllModels().filter((model) => model.provider === provider);
}

/**
 * Get default model
 * @returns Default model configuration
 */
export function getDefaultModel(): ModelConfig {
  return MODELS["gemini-2.5-flash"];
}

/**
 * Validate if model ID is supported
 * @deprecated Use isSupportedModel instead for better type safety
 */
export function isModelSupported(modelId: string): boolean {
  return isSupportedModel(modelId);
}

/**
 * Calculate the cost of an AI request based on token usage and model
 * @param inputTokens - Number of input tokens
 * @param outputTokens - Number of output tokens
 * @param modelId - The AI model used
 * @returns Cost in USD, or null if calculation is not possible
 */
export function calculateTokenCost(
  inputTokens: number,
  outputTokens: number,
  modelId: string
): number | null {
  if (inputTokens < 0 || outputTokens < 0) {
    return null;
  }

  let pricing;
  try {
    pricing = getModel(modelId).pricing;
  } catch {
    return null;
  }

  // Convert to per-million calculations
  const inputCost = (inputTokens / 1_000_000) * pricing.input;
  const outputCost = (outputTokens / 1_000_000) * pricing.output;

  return inputCost + outputCost;
}
