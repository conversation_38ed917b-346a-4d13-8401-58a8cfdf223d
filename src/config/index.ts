import path from "path";
import { getEnvConfig, getSecret } from "./utils";

export const apiNodeEnv = getEnvConfig("NODE_ENV") as
  | "local"
  | "development"
  | "staging"
  | "production";
export const apiServerOrigin = getEnvConfig("SERVER_ORIGIN") as string;
export const aidaEndpoint = getEnvConfig("AIDA_ENDPOINT") as string;

export const gcpProjectId = getEnvConfig("GCP_PROJECT_ID") as string;
export const gcpLocation = getEnvConfig("GCP_LOCATION") as string;
export const gcpRecaptchaSitekey = getEnvConfig(
  "GCP_RECAPTCHA_SITEKEY"
) as string;
export const gcsBucket = getEnvConfig("GCS_BUCKET") as string;
export const gcsTempFolderForDocumentProcessor = getEnvConfig(
  "GCS_TEMP_FOLDER_FOR_DOCUMENT_PROCESSOR"
) as string;

export const dbHost = getEnvConfig("DB_HOST") as string;
export const dbUsername = getEnvConfig("DB_USERNAME") as string;
export const dbPassword = getEnvConfig("DB_PASSWORD") as string;
export const dbDatabase = getEnvConfig("DB_DATABASE") as string;
export const revAiToken = getEnvConfig("REV_AI_TOKEN") as string;
export const recallAiToken = getEnvConfig("RECALL_API_KEY") as string;
export const recallAiApiBaseUrlV1 = getEnvConfig(
  "RECALL_API_BASE_URL_V1"
) as string;
export const recallAiApiBaseUrlV2 = getEnvConfig(
  "RECALL_API_BASE_URL_V2"
) as string;
export const recallSystemCalendarEmail = getEnvConfig(
  "RECALL_SYSTEM_CALENDAR_EMAIL"
) as string;
export const speechmaticsApiKey = getEnvConfig(
  "SPEECHMATICS_API_KEY"
) as string;

export const gcsResourceFolder = "resources";
export const gcsConversationImagesFolder = "conversation-images";

// For local development, we use the `src/uploads` folder to store the uploaded files
// While for other environments, we use the `/tmp/aida-uploads` folder for Cloud Run compatibility
export const apiDevUploadOutput =
  apiNodeEnv === "local"
    ? path.join(__dirname, "../uploads")
    : "/tmp/aida-uploads";

export const gcpPubsubTopicName = getEnvConfig(
  "GCP_PUBSUB_TOPIC_NAME"
) as string;
export const gcpPubsubSubscriptionName = getEnvConfig(
  "GCP_PUBSUB_SUBSCRIPTION_NAME"
) as string;

export const gcpPubsubTranscoderTopicName = getEnvConfig(
  "GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME"
) as string;
export const gcpPubsubTranscoderSubscriptionName = getEnvConfig(
  "GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME"
) as string;

export const gcpPubsubDocumentProcessorTopicName = getEnvConfig(
  "GCP_PUBSUB_DOCUMENT_PROCESSOR_TOPIC_NAME",
  "document-processor"
) as string;

export const aidaApiKey = getEnvConfig(
  "AIDA_API_KEY",
  "8IXtyR898g8OplVd9frruCzcf7sqXQINeH5GaUp7l28Knd03aFmpF3bXuhLTFcFX"
) as string;

// SendGrid configuration
export const sendgridApiKey = getEnvConfig("SENDGRID_API_KEY") as string;
export const sendgridFromEmail = getEnvConfig("SENDGRID_FROM_EMAIL") as string;
export const sendgridFromName = getEnvConfig("SENDGRID_FROM_NAME") as string;

export const jwtSecret = getEnvConfig("JWT_SECRET") as string;

export const webAppUrl = getEnvConfig("WEB_APP_URL") as string;

export const AidaPubsubTopic = {
  CLOUDLAB_RESOURCE_SYNC: "media-sync",
  TRANSCODING_SYNC: "transcoding-sync",
  DOCUMENT_PROCESSOR: gcpPubsubDocumentProcessorTopicName,
} as const;

export const geminiModel = getEnvConfig("GEMINI_MODEL") as string;
export const googleGenerativeAIApiKey = getEnvConfig(
  "GOOGLE_GENERATIVE_AI_API_KEY",
  "AIzaSyBZf-TOv3XYUgSOX5AvWX7v_nZTlR5ZE1s"
) as string;

export const posthogKey = getEnvConfig("POSTHOG_KEY") as string;
export const posthogHost = getEnvConfig("POSTHOG_HOST") as string;

// Beings MCP Server Configuration
export const beingsRAGBaseUrl = getEnvConfig(
  "BEINGS_RAG_SERVICE_URL"
) as string;
export const beingsMCPAuthToken = getEnvConfig(
  "BEINGS_MCP_AUTH_TOKEN"
) as string;

// Data Retention Settings
export const DATA_RETENTION_DAYS = parseInt(
  getEnvConfig("DATA_RETENTION_DAYS", "28")
);
export const DATA_RETENTION_REMINDER_DAYS = parseInt(
  getEnvConfig("DATA_RETENTION_REMINDER_DAYS", "7")
); // Send reminder 7 days before deletion
export const DATA_RETENTION_CLEANUP_SCHEDULE = getEnvConfig(
  "DATA_RETENTION_CLEANUP_SCHEDULE",
  "0 0 * * *"
); // Run daily at midnight

export const defaultTimezone = getEnvConfig("DEFAULT_TIMEZONE") as string;

// SpriteSheet generation feature flag
export const enableSpriteSheetGeneration =
  getEnvConfig("ENABLE_SPRITE_SHEET_GEN", "false").toLowerCase() === "true";

export const pineconeApiKey = getEnvConfig("PINECONE_API_KEY") as string;
export const pineconeAssistantHost = getEnvConfig(
  "PINECONE_ASSISTANT_HOST"
) as string;
export const pineconeAssistantName = getEnvConfig(
  "PINECONE_ASSISTANT_NAME"
) as string;

export const getGcsServiceAccount = async () => {
  if (apiNodeEnv === "local") {
    const fs = require("fs");
    const serviceAccountPath = path.join(__dirname, "./gcs-dev.json");
    const serviceAccountJson = fs.readFileSync(serviceAccountPath, "utf8");
    return JSON.parse(serviceAccountJson);
  }
  return getSecret("gcs-service-account");
};

// URL Shortening Configuration
export const urlShortenerConfig = {
  defaultExpirationDays: parseInt(
    getEnvConfig("URL_DEFAULT_EXPIRY_DAYS", "30")
  ),
  maxClicksPerHour: parseInt(getEnvConfig("URL_MAX_CLICKS_PER_HOUR", "100")),
  shortCodeLength: parseInt(getEnvConfig("URL_SHORT_CODE_LENGTH", "7")),
  enableShortening: getEnvConfig("URL_SHORTENING_ENABLED", "true") === "true",
  secretKey: getEnvConfig("URL_SHORTENER_SECRET_KEY", "default-secret-key"),
};

export const storageCDNUrl = getEnvConfig("STORAGE_CDN_URL") as string;
