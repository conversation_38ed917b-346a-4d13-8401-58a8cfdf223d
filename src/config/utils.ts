import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import dotenv from "dotenv";

dotenv.config(); // for local variables override

export const getEnvConfig = (key: string, defaultValue?: string) => {
  const value = process.env[key];
  if (value !== undefined) {
    return value;
  }
  return defaultValue;
};

export const getSecret = async (secretKey: string) => {
  const client = new SecretManagerServiceClient();
  const gcpProjectId = process.env.GCP_PROJECT_ID;
  const secretName = `projects/${gcpProjectId}/secrets/${secretKey}/versions/latest`;

  try {
    const [version] = await client.accessSecretVersion({ name: secretName });
    const payload = version.payload?.data?.toString();
    if (!payload) {
      throw new Error(`Failed to get secret ${secretKey} from Secret Manager`);
    }
    return JSON.parse(payload);
  } catch (error) {
    console.error(
      `Error fetching secret ${secretKey} from Secret Manager:`,
      error
    );
    throw error;
  }
};
