import { GoogleGenAI } from "@google/genai";

import { createLogger } from "@/services/logger";
import { gcpProjectId } from ".";

const logger = createLogger("GoogleGenAI");

// Initialize the Google GenAI client
let genAIClient: GoogleGenAI | null = null;

export function initializeGoogleGenAI(): GoogleGenAI {
  if (!genAIClient) {
    genAIClient = new GoogleGenAI({
      vertexai: true,
      project: gcpProjectId,
      location: "europe-west1",
    });

    logger.info("🚀 Google GenAI initialized successfully");
  }

  return genAIClient;
}

/**
 * Get the configured Google GenAI client
 * Throws an error if not initialized
 */
export function getGoogleGenAI(): GoogleGenAI {
  if (!genAIClient) {
    throw new Error(
      "Google GenAI not initialized. Call initializeGoogleGenAI() first."
    );
  }
  return genAIClient;
}
