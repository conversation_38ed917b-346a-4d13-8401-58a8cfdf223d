---
description: 
globs: 
alwaysApply: false
---
# Cloud Deployment Standards

## Objective
Ensure reliable, scalable, and secure deployment on Google Cloud Platform following cloud-native best practices.

## Context
- Google App Engine deployment
- Google Cloud Storage for file management
- Pub/Sub for asynchronous messaging
- Secret Manager for configuration
- Cloud SQL for PostgreSQL database

## Rules

### Environment Configuration
- **Use environment variables** for all configuration values
- **Never commit secrets** to version control
- **Use Google Secret Manager** for sensitive configuration
- **Implement proper environment separation** (dev, staging, prod)

✅ Good Configuration Pattern:
```typescript
// config/index.ts
export const dbHost = process.env.DB_HOST || 'localhost';
export const dbUsername = process.env.DB_USERNAME || 'dev_user';
export const dbPassword = process.env.DB_PASSWORD; // Required, no default
export const openaiApiKey = process.env.OPENAI_API_KEY; // From Secret Manager

// Validate required environment variables
const requiredEnvVars = ['DB_PASSWORD', 'OPENAI_API_KEY', 'FIREBASE_PROJECT_ID'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Required environment variable ${envVar} is not set`);
  }
}
```

### App Engine Configuration
- **Configure proper scaling** in app.yaml
- **Set appropriate instance classes** based on workload
- **Use health checks** for service monitoring
- **Implement graceful shutdown** handling

✅ Good app.yaml Structure:
```yaml
runtime: nodejs20
env: standard
instance_class: F4

automatic_scaling:
  min_idle_instances: 1
  max_idle_instances: automatic
  min_pending_latency: automatic
  max_pending_latency: automatic

health_check:
  enable_health_check: true
  check_interval_sec: 60
  timeout_sec: 30
  healthy_threshold: 2
  unhealthy_threshold: 3

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 1

readiness_check:
  path: "/ready"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 1
```

### Cloud Storage Integration
- **Use signed URLs** for secure file access
- **Implement proper bucket policies** for security
- **Organize files** with consistent naming conventions
- **Handle storage errors** gracefully

✅ Good Storage Service Pattern:
```typescript
export const uploadToGCS = async (
  file: Express.Multer.File, 
  destination: string
): Promise<string> => {
  try {
    const bucket = storage.bucket(process.env.GCS_BUCKET_NAME!);
    const fileName = `${Date.now()}-${file.originalname}`;
    const fileUpload = bucket.file(`${destination}/${fileName}`);

    const stream = fileUpload.createWriteStream({
      metadata: {
        contentType: file.mimetype,
        metadata: {
          uploadedAt: new Date().toISOString(),
          originalName: file.originalname,
        },
      },
    });

    return new Promise((resolve, reject) => {
      stream.on('error', (error) => {
        log.error('GCS upload failed', { error, fileName });
        reject(new IntentionalError('File upload failed', 500));
      });

      stream.on('finish', () => {
        const publicUrl = `gs://${process.env.GCS_BUCKET_NAME}/${destination}/${fileName}`;
        resolve(publicUrl);
      });

      stream.end(file.buffer);
    });
  } catch (error) {
    log.error('GCS service error', { error });
    throw new IntentionalError('Storage service unavailable', 503);
  }
};
```

### Pub/Sub Integration
- **Use typed message handlers** for Pub/Sub topics
- **Implement proper error handling** and retry logic
- **Use dead letter queues** for failed messages
- **Monitor message processing** performance

✅ Good Pub/Sub Handler Pattern:
```typescript
export const handleMediaSyncMessage = async (message: any) => {
  try {
    const data = JSON.parse(message.data.toString());
    log.info('Processing media sync message', { messageId: message.id, data });

    await processMediaSync(data);
    message.ack();
    
    log.info('Media sync completed', { messageId: message.id });
  } catch (error) {
    log.error('Media sync failed', { 
      error: error.message, 
      messageId: message.id,
      retryCount: message.deliveryAttempt 
    });

    // Don't ack failed messages to trigger retry
    if (message.deliveryAttempt < 3) {
      message.nack();
    } else {
      // Send to dead letter queue after max retries
      message.ack();
      await sendToDeadLetterQueue(message);
    }
  }
};
```

### Database Connectivity
- **Use connection pooling** for database connections
- **Implement proper connection handling** for cloud environments
- **Handle connection timeouts** gracefully
- **Monitor database performance**

### Logging & Monitoring
- **Use structured logging** compatible with Cloud Logging
- **Implement proper log levels** (debug, info, warn, error)
- **Include correlation IDs** for request tracking
- **Set up alerting** for critical errors

✅ Good Logging Pattern:
```typescript
import { log } from '@/services/logger';

export const processRequest = async (req: Request, res: Response) => {
  const correlationId = req.headers['x-correlation-id'] || generateId();
  
  log.info('Request started', {
    correlationId,
    method: req.method,
    path: req.path,
    userId: res.locals.user?.id,
  });

  try {
    const result = await businessLogic(req.body);
    
    log.info('Request completed', {
      correlationId,
      duration: Date.now() - startTime,
      statusCode: 200,
    });
    
    res.json(result);
  } catch (error) {
    log.error('Request failed', {
      correlationId,
      error: error.message,
      stack: error.stack,
    });
    
    res.status(500).json(createErrorResponse('Internal server error'));
  }
};
```

### Performance Optimization
- **Implement caching strategies** where appropriate
- **Use async operations** for I/O bound tasks
- **Optimize database queries** to reduce latency
- **Monitor resource usage** and scaling metrics

### Security in Cloud Environment
- **Use IAM roles** instead of service account keys when possible
- **Implement proper network security** with VPC if needed
- **Use HTTPS only** for all external communications
- **Regularly rotate secrets** and API keys

### Deployment Process
- **Use automated deployment** scripts
- **Implement proper testing** before deployment
- **Use blue-green or rolling deployments** for zero downtime
- **Have rollback procedures** ready

✅ Good Deployment Script Pattern:
```bash
#!/bin/bash
set -e

echo "Starting deployment..."

# Run tests
npm test

# Build application
npm run build

# Deploy to App Engine
gcloud app deploy app.yaml --quiet --promote

# Verify deployment
curl -f https://your-app.appspot.com/health || {
  echo "Health check failed, rolling back..."
  gcloud app versions list --limit=2 --sort-by=~version.createTime --format="value(version.id)" | tail -1 | xargs gcloud app services set-traffic default --splits
  exit 1
}

echo "Deployment completed successfully"
```

## Exceptions
- Development environments may use local configurations
- Emergency deployments may skip some validation steps
- Third-party service integrations may require specific configurations
