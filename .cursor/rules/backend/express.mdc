---
description: 
globs: 
alwaysApply: false
---
# Express.js REST API Standards

## Objective
Ensure consistent, secure, and maintainable Express.js REST API development with proper middleware usage and request handling.

## Context
- Express.js 4.x with TypeScript
- Firebase Authentication integration
- Middleware-based architecture for cross-cutting concerns
- RESTful endpoint design
- Cloud deployment considerations

## Rules

### Route Definition
- **Use Express Router** for modular route organization
- **Apply middleware in correct order**: auth → validation → business logic
- **Use descriptive route paths** following REST conventions
- **Group related routes** in dedicated router files

✅ Good:
```typescript
// routes/project.routes.ts
const router = express.Router();

router.post('/projects', 
  checkAuthMiddleware, 
  validateProjectCreation,
  createProject
);

router.get('/projects/:id', 
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  getProjectDetails
);

export { router as projectRouter };
```

### Controller Structure
- **Keep controllers thin** - delegate business logic to handlers/services
- **Handle HTTP-specific concerns** (request parsing, response formatting)
- **Use proper HTTP status codes** following REST standards
- **Always handle errors** and return consistent responses

✅ Good:
```typescript
export const createProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const projectData = req.body;
    const user = res.locals.user;
    
    const result = await createProjectHandler(projectData, user);
    res.status(result.statusCode).json(result);
  } catch (error) {
    log.error('Project creation failed', { error, userId: res.locals.user?.id });
    res.status(500).json(createErrorResponse('Internal server error'));
  }
};
```

### Middleware Patterns
- **Authentication middleware** should set `res.locals.user`
- **Validation middleware** should validate and sanitize input
- **Error middleware** should be applied last in the chain
- **Use async error handling** with express-async-errors

✅ Good Middleware Structure:
```typescript
// Authentication
export const checkAuthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = extractToken(req);
    const user = await verifyFirebaseToken(token);
    res.locals.user = user;
    next();
  } catch (error) {
    res.status(401).json(createErrorResponse('Unauthorized'));
  }
};

// Validation
export const validateProjectCreation = (req: Request, res: Response, next: NextFunction) => {
  const { error } = projectCreateSchema.validate(req.body);
  if (error) {
    return res.status(400).json(createErrorResponse(error.message));
  }
  next();
};
```

### Request/Response Handling
- **Validate all inputs** before processing
- **Use typed request/response interfaces** when possible
- **Handle query parameters** with proper defaults
- **Implement pagination** for list endpoints

✅ Good:
```typescript
interface ProjectListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
}

export const getProjects = async (req: Request<{}, {}, {}, ProjectListQuery>, res: Response) => {
  const { page = 1, limit = 20, search, status } = req.query;
  const userId = res.locals.user.id;
  
  const result = await projectService.getProjects({ 
    userId, 
    page: Number(page), 
    limit: Number(limit), 
    search, 
    status 
  });
  
  res.json(result);
};
```

### Error Handling
- **Use centralized error handler** for unhandled errors
- **Return consistent error format** across all endpoints
- **Log errors with context** for debugging
- **Use appropriate HTTP status codes**

✅ Good Error Response:
```typescript
interface ErrorResponse {
  success: false;
  message: string;
  statusCode: number;
  details?: any;
}

// Centralized error handler
export const expressErrorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  log.error('Unhandled error', { 
    error: err.message, 
    stack: err.stack, 
    path: req.path,
    method: req.method 
  });
  
  res.status(500).json(createErrorResponse('Internal server error'));
};
```

### Security Practices
- **Always use authentication** for protected routes
- **Validate user permissions** before data access
- **Sanitize input data** to prevent injection attacks
- **Use CORS properly** for cross-origin requests
- **Rate limiting** for public endpoints

### HTTP Status Codes
- `200` - Success with data
- `201` - Resource created
- `204` - Success without data
- `400` - Bad request / validation error
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not found
- `409` - Conflict
- `500` - Internal server error

## Exceptions
- Internal routes (`/_internal_*`) may have different auth requirements
- Callback handlers from external services may skip standard validation
- Legacy endpoints may maintain existing patterns during migration
