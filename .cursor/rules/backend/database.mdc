---
description: 
globs: 
alwaysApply: false
---
# Database & Sequelize ORM Standards

## Objective
Maintain consistent, efficient, and secure database access patterns using Sequelize ORM with PostgreSQL.

## Context
- PostgreSQL database with Sequelize ORM
- Custom database service layer with extended functionality
- Cloud-based deployment with connection pooling
- Multi-tenant data isolation requirements

## Rules

### Model Definition
- **Use consistent naming**: PascalCase for models, snake_case for database columns
- **Define proper associations** in separate association files
- **Include timestamps** for audit trails
- **Use proper data types** and constraints

✅ Good Model Structure:
```typescript
// schemas/project/Project.model.ts
export const Project = db.xDefine('Project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255],
    },
  },
  createdById: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'created_by_id',
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_deleted',
  },
}, {
  tableName: 'projects',
  timestamps: true,
  paranoid: true,
});
```

### Service Layer Patterns
- **Encapsulate all database operations** in service layers
- **Use transactions** for multi-table operations
- **Implement proper error handling** with meaningful messages
- **Return consistent response format** with ServiceResponse

✅ Good Service Pattern:
```typescript
// services/project/project.service.ts
export const createProject = async (
  data: ProjectCreateData, 
  userId: string
): Promise<ServiceResponse<Project>> => {
  const transaction = await db.transaction();
  
  try {
    const project = await Project.create({
      ...data,
      createdById: userId,
    }, { transaction });

    await ProjectMember.create({
      projectId: project.id,
      userId,
      role: 'owner',
    }, { transaction });

    await transaction.commit();
    
    return {
      success: true,
      data: project,
      statusCode: 201,
    };
  } catch (error) {
    await transaction.rollback();
    log.error('Failed to create project', { error, userId, data });
    
    return {
      success: false,
      message: 'Failed to create project',
      statusCode: 500,
    };
  }
};
```

### Query Optimization
- **Use eager loading** for related data when needed
- **Implement pagination** for list queries
- **Use proper indexes** in model definitions
- **Avoid N+1 queries** with include statements

✅ Good Query Patterns:
```typescript
// Efficient pagination with includes
export const getProjects = async (params: GetProjectsParams) => {
  const { userId, page, limit, search } = params;
  const offset = (page - 1) * limit;

  const where: any = {
    '$ProjectMembers.userId$': userId,
    isDeleted: false,
  };

  if (search) {
    where.name = { [Op.iLike]: `%${search}%` };
  }

  const { rows, count } = await Project.findAndCountAll({
    where,
    include: [{
      model: ProjectMember,
      where: { userId },
      attributes: ['role'],
    }],
    limit,
    offset,
    order: [['createdAt', 'DESC']],
  });

  return {
    success: true,
    data: rows,
    meta: {
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
    },
    statusCode: 200,
  };
};
```

### Association Management
- **Define associations** in dedicated association files
- **Use proper foreign key constraints** 
- **Handle cascading deletes** appropriately
- **Implement soft deletes** where needed

✅ Good Association Pattern:
```typescript
// services/db/Associations.ts
export const setupProjectAssociations = () => {
  Project.hasMany(ProjectMember, {
    foreignKey: 'projectId',
    as: 'members',
  });

  Project.hasMany(Resource, {
    foreignKey: 'projectId',
    as: 'resources',
  });

  ProjectMember.belongsTo(Project, {
    foreignKey: 'projectId',
    as: 'project',
  });
};
```

### Transaction Management
- **Use transactions** for operations affecting multiple tables
- **Always commit or rollback** transactions
- **Handle transaction errors** properly
- **Keep transactions short** to avoid locks

### Data Validation
- **Validate at model level** with Sequelize validators
- **Implement business validation** in service layer
- **Sanitize input data** before database operations
- **Use proper data types** and constraints

✅ Good Validation:
```typescript
// Model validation
name: {
  type: DataTypes.STRING,
  allowNull: false,
  validate: {
    notEmpty: true,
    len: [1, 255],
    is: /^[a-zA-Z0-9\s\-_]+$/,
  },
},

// Service validation
export const validateProjectData = (data: any): ProjectCreateData => {
  const schema = Joi.object({
    name: Joi.string().trim().min(1).max(255).required(),
    description: Joi.string().max(1000).optional(),
  });

  const { error, value } = schema.validate(data);
  if (error) {
    throw new IntentionalError(error.message, 400);
  }
  
  return value;
};
```

### Performance Considerations
- **Use connection pooling** properly configured
- **Implement query timeouts** for long-running operations
- **Monitor slow queries** and add indexes as needed
- **Use database-level constraints** for data integrity

### Migration Strategy
- **Use Sequelize migrations** for schema changes
- **Version control migrations** with descriptive names
- **Test migrations** on staging before production
- **Include rollback procedures** for complex changes

## Exceptions
- Legacy models may use different naming conventions during migration
- Performance-critical queries may use raw SQL with proper typing
- Batch operations may require custom transaction handling
