---
description: 
globs: 
alwaysApply: false
---
# Projects Domain Rules

## Objective
Ensure consistent implementation of project management business logic, including collaboration features, access control, and resource management.

## Context
- Multi-tenant project collaboration platform
- Role-based access control (owner, admin, member)
- Project invitations and shared links
- Resource management within projects
- Audit trails and activity tracking

## Rules

### Project Access Control
- **Implement proper role hierarchy**: owner > admin > member
- **Validate user permissions** before any project operation
- **Use middleware for permission checks** in routes
- **Audit all permission changes** with proper logging

✅ Good Permission Check:
```typescript
export const checkProjectPermission = async (
  projectId: string, 
  userId: string, 
  requiredRole: ProjectRole = 'member'
): Promise<boolean> => {
  const membership = await ProjectMember.findOne({
    where: { projectId, userId, isActive: true }
  });

  if (!membership) return false;

  const roleHierarchy = { owner: 3, admin: 2, member: 1 };
  return roleHierarchy[membership.role] >= roleHierarchy[requiredRole];
};
```

### Invitation Management
- **Generate unique invitation codes** with expiration
- **Send email notifications** for invitations
- **Track invitation status** (pending, accepted, rejected, expired)
- **Prevent duplicate invitations** for same email/project

✅ Good Invitation Flow:
```typescript
export const createProjectInvitation = async (params: InvitationParams) => {
  const { projectId, email, role, senderEmail } = params;

  // Check for existing invitation
  const existingInvitation = await ProjectInvitation.findOne({
    where: { 
      projectId, 
      email: email.toLowerCase(), 
      status: 'pending',
      expiresAt: { [Op.gt]: new Date() }
    }
  });

  if (existingInvitation && !params.forceSendEmail) {
    return {
      success: false,
      message: 'Invitation already sent',
      statusCode: 409,
    };
  }

  const invitation = await ProjectInvitation.create({
    projectId,
    email: email.toLowerCase(),
    role,
    code: generateInvitationCode(),
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    senderEmail,
  });

  await sendInvitationEmail(invitation);
  
  return {
    success: true,
    data: invitation,
    statusCode: 201,
  };
};
```

### Resource Association
- **Associate resources with projects** through proper foreign keys
- **Implement resource access control** based on project membership
- **Handle resource cleanup** when projects are deleted
- **Track resource usage** for analytics

### Project Lifecycle
- **Soft delete projects** to maintain data integrity
- **Archive inactive projects** after specified period
- **Handle member cleanup** when projects are deleted
- **Maintain audit trail** of project changes

✅ Good Project Deletion:
```typescript
export const deleteProject = async (projectId: string, userId: string) => {
  const transaction = await db.transaction();
  
  try {
    // Verify ownership
    const isOwner = await checkProjectPermission(projectId, userId, 'owner');
    if (!isOwner) {
      throw new IntentionalError('Only project owners can delete projects', 403);
    }

    // Soft delete project
    await Project.update(
      { isDeleted: true, deletedAt: new Date() },
      { where: { id: projectId }, transaction }
    );

    // Archive resources
    await Resource.update(
      { isArchived: true },
      { where: { projectId }, transaction }
    );

    // Log deletion
    log.info('Project deleted', { projectId, userId });

    await transaction.commit();
    return { success: true, statusCode: 200 };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
```

### Shared Link Management
- **Generate secure, time-limited tokens** for shared access
- **Track access attempts** and usage analytics
- **Implement access count limits** if specified
- **Allow revocation** of shared links

### Collaboration Features
- **Track member activity** within projects
- **Implement proper notification system** for project events
- **Handle concurrent access** to project resources
- **Maintain version history** for important changes

### Data Integrity
- **Validate project data** before persistence
- **Ensure referential integrity** across related entities
- **Handle cascading updates** properly
- **Implement optimistic locking** for concurrent modifications

✅ Good Data Validation:
```typescript
export const validateProjectUpdate = (data: any): ProjectUpdateData => {
  const schema = Joi.object({
    name: Joi.string().trim().min(1).max(255).optional(),
    description: Joi.string().max(1000).allow('').optional(),
    settings: Joi.object({
      isPublic: Joi.boolean().optional(),
      allowGuestAccess: Joi.boolean().optional(),
    }).optional(),
  });

  const { error, value } = schema.validate(data);
  if (error) {
    throw new IntentionalError(error.message, 400);
  }

  return value;
};
```

### Performance Optimization
- **Implement pagination** for project lists
- **Use proper indexes** on frequently queried fields
- **Cache project permissions** for active users
- **Optimize member queries** with joins

### Security Requirements
- **Sanitize all user inputs** to prevent injection
- **Validate user permissions** on every operation
- **Log security-relevant events** for audit
- **Implement rate limiting** on sensitive operations

## Exceptions
- System administrators may bypass standard permission checks
- Bulk operations may require different validation patterns
- Legacy projects may have different data structures during migration
