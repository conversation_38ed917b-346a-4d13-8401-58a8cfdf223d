---
description: 
globs: 
alwaysApply: false
---
# Resources Domain Rules

## Objective
Ensure reliable and secure handling of media resources including file uploads, transcoding, transcription, and storage management.

## Context
- Multi-format media support (audio, video, documents)
- Google Cloud Storage for file persistence
- Transcoding pipeline for media optimization
- AI-powered transcription and analysis
- Project-based resource organization

## Rules

### File Upload Management
- **Validate file types and sizes** before processing
- **Generate unique filenames** to prevent conflicts
- **Implement upload progress tracking** for large files
- **Handle upload failures** gracefully with cleanup

✅ Good Upload Handler:
```typescript
export const uploadResourceHandler = async (req: Request, res: Response) => {
  const { projectId, title, description } = req.body;
  const file = req.file;

  if (!file) {
    return res.status(400).json(createErrorResponse('No file provided'));
  }

  // Validate project access
  const hasAccess = await checkProjectPermission(projectId, res.locals.user.id);
  if (!hasAccess) {
    return res.status(403).json(createErrorResponse('Access denied'));
  }

  const uploadResult = await resourceService.uploadResource({
    file,
    projectId,
    title,
    description,
    uploadedBy: res.locals.user.id,
  });

  res.status(uploadResult.statusCode).json(uploadResult);
};
```

### Resource Lifecycle Management
- **Track resource status** (uploading, processing, ready, failed)
- **Implement proper cleanup** for failed uploads
- **Handle resource versioning** when files are updated
- **Maintain audit trail** of resource operations

✅ Good Resource Status Tracking:
```typescript
export const updateResourceStatus = async (
  resourceId: string, 
  status: ResourceStatus,
  metadata?: any
) => {
  const transaction = await db.transaction();
  
  try {
    await Resource.update(
      { 
        status, 
        metadata: { ...metadata, updatedAt: new Date() },
        processedAt: status === 'ready' ? new Date() : null 
      },
      { where: { id: resourceId }, transaction }
    );

    // Log status change
    log.info('Resource status updated', { 
      resourceId, 
      status, 
      previousStatus: metadata?.previousStatus 
    });

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
```

### Media Processing Pipeline
- **Queue processing jobs** asynchronously
- **Handle different media formats** appropriately
- **Implement retry logic** for failed processing
- **Track processing progress** and duration

### Transcription Integration
- **Support multiple transcription providers** (Rev.ai, Speechmatics)
- **Handle webhook callbacks** securely
- **Process transcription results** consistently
- **Implement fallback providers** for reliability

✅ Good Transcription Handler:
```typescript
export const handleTranscriptionWebhook = async (req: Request, res: Response) => {
  const { jobId, status, transcript } = req.body;
  
  try {
    // Verify webhook authenticity
    const isValid = await verifyWebhookSignature(req);
    if (!isValid) {
      return res.status(401).json(createErrorResponse('Invalid signature'));
    }

    const resource = await Resource.findOne({ 
      where: { transcriptionJobId: jobId } 
    });

    if (!resource) {
      log.warn('Transcription webhook for unknown job', { jobId });
      return res.status(404).json(createErrorResponse('Job not found'));
    }

    if (status === 'completed') {
      await processTranscriptionSuccess(resource.id, transcript);
    } else if (status === 'failed') {
      await processTranscriptionFailure(resource.id, req.body.error);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    log.error('Transcription webhook processing failed', { error, jobId });
    res.status(500).json(createErrorResponse('Processing failed'));
  }
};
```

### Storage Management
- **Organize files** with consistent folder structure
- **Implement proper access controls** using signed URLs
- **Handle storage quota** and usage monitoring
- **Implement file cleanup** for deleted resources

### Resource Access Control
- **Verify project membership** before allowing access
- **Generate time-limited signed URLs** for secure access
- **Log access events** for audit purposes
- **Handle different permission levels** (view, download, edit)

✅ Good Access Control:
```typescript
export const getResourceAccessUrl = async (
  resourceId: string, 
  userId: string,
  accessType: 'view' | 'download' = 'view'
): Promise<ServiceResponse<string>> => {
  // Verify resource exists and user has access
  const resource = await Resource.findOne({
    where: { id: resourceId },
    include: [{
      model: Project,
      include: [{
        model: ProjectMember,
        where: { userId, isActive: true }
      }]
    }]
  });

  if (!resource) {
    return {
      success: false,
      message: 'Resource not found or access denied',
      statusCode: 404,
    };
  }

  // Generate signed URL
  const signedUrl = await storage.getSignedUrl({
    bucket: process.env.GCS_BUCKET_NAME!,
    fileName: resource.storagePath,
    expiresIn: 3600, // 1 hour
    action: accessType === 'download' ? 'read' : 'read',
  });

  // Log access
  log.info('Resource access granted', { 
    resourceId, 
    userId, 
    accessType,
    projectId: resource.projectId 
  });

  return {
    success: true,
    data: signedUrl,
    statusCode: 200,
  };
};
```

### Performance Optimization
- **Implement thumbnail generation** for video resources
- **Use appropriate storage classes** for different access patterns
- **Cache frequently accessed metadata**
- **Optimize file transfer** with streaming

### Error Recovery
- **Implement automatic retry** for transient failures
- **Provide manual retry options** for failed processing
- **Clean up partial uploads** and temporary files
- **Notify users** of processing failures

## Exceptions
- System administrators may access resources across projects
- Bulk operations may require different validation patterns
- Emergency recovery procedures may bypass normal access controls
