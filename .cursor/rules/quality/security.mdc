---
description:
globs:
alwaysApply: false
---
# Security Standards

## Objective
Implement comprehensive security measures to protect user data, prevent unauthorized access, and ensure secure API operations.

## Context
- Firebase Authentication integration
- Multi-tenant application with role-based access
- Cloud deployment on Google App Engine
- External API integrations (OpenAI, SendGrid, etc.)
- File upload and media processing capabilities

## Rules

### Authentication & Authorization
- **Always verify Firebase tokens** before processing requests
- **Use consistent authentication middleware** across protected routes
- **Implement proper role-based access control** (RBAC)
- **Never trust client-side data** for permission decisions

✅ Good Authentication Pattern:
```typescript
export const checkAuthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    if (!token) {
      return res.status(401).json(createErrorResponse('Missing authentication token'));
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    const user = await getUserFromFirebase(decodedToken.uid);
    
    res.locals.user = user;
    next();
  } catch (error) {
    log.error('Authentication failed', { error: error.message, path: req.path });
    res.status(401).json(createErrorResponse('Invalid authentication token'));
  }
};
```

### Input Validation & Sanitization
- **Validate all inputs** at the controller/handler level
- **Use strong type checking** with TypeScript interfaces
- **Sanitize data** before database operations
- **Implement request size limits** to prevent DoS

✅ Good Input Validation:
```typescript
export const validateResourceUpload = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    title: Joi.string().trim().min(1).max(255).required(),
    description: Joi.string().max(1000).optional().allow(''),
    projectId: Joi.string().uuid().required(),
    fileType: Joi.string().valid('audio', 'video', 'document').required(),
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    return res.status(400).json(createErrorResponse(error.message));
  }

  req.body = value; // Use sanitized data
  next();
};
```

### Data Protection
- **Never log sensitive information** (passwords, tokens, PII)
- **Use parameterized queries** to prevent SQL injection
- **Encrypt sensitive data** at rest when required
- **Implement proper data retention policies**

✅ Good Logging Practice:
```typescript
// ❌ Avoid - logs sensitive data
log.info('User login', { email: user.email, password: req.body.password });

// ✅ Good - logs only necessary info
log.info('User login attempt', { 
  userId: user.id, 
  userAgent: req.headers['user-agent'],
  ip: req.ip 
});
```

### API Security
- **Use HTTPS only** for all communications
- **Implement proper CORS policies** 
- **Rate limit sensitive endpoints** 
- **Validate request origins** for sensitive operations

✅ Good CORS Configuration:
```typescript
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://app.aida.com', 'https://admin.aida.com']
    : true,
  credentials: true,
  optionsSuccessStatus: 200,
}));
```

### File Upload Security
- **Validate file types and sizes** before processing
- **Scan uploaded files** for malware when possible
- **Use secure file storage** with proper access controls
- **Generate unique filenames** to prevent conflicts

✅ Good File Upload Validation:
```typescript
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  const allowedMimeTypes = [
    'audio/mpeg', 'audio/wav', 'audio/mp4',
    'video/mp4', 'video/quicktime', 'video/x-msvideo',
    'application/pdf', 'application/msword'
  ];

  const maxFileSize = 100 * 1024 * 1024; // 100MB

  if (!req.file) {
    return res.status(400).json(createErrorResponse('No file uploaded'));
  }

  if (!allowedMimeTypes.includes(req.file.mimetype)) {
    return res.status(400).json(createErrorResponse('Unsupported file type'));
  }

  if (req.file.size > maxFileSize) {
    return res.status(400).json(createErrorResponse('File too large'));
  }

  next();
};
```

### External API Security
- **Store API keys securely** using environment variables or secret management
- **Implement proper error handling** to avoid information disclosure
- **Use timeouts** for external API calls
- **Validate responses** from external services

✅ Good External API Pattern:
```typescript
export const callExternalAPI = async (data: any): Promise<any> => {
  try {
    const response = await axios.post(EXTERNAL_API_URL, data, {
      headers: {
        'Authorization': `Bearer ${process.env.EXTERNAL_API_KEY}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds
    });

    return response.data;
  } catch (error) {
    // Don't expose internal error details
    log.error('External API call failed', { 
      error: error.message,
      status: error.response?.status 
    });
    throw new IntentionalError('External service unavailable', 503);
  }
};
```

### Database Security
- **Use parameterized queries** to prevent SQL injection
- **Implement proper access controls** at database level
- **Audit sensitive database operations**
- **Use database connection pooling** securely

### Error Handling Security
- **Never expose internal system details** in error messages
- **Log security events** for monitoring
- **Implement consistent error responses**
- **Use proper HTTP status codes**

✅ Good Error Response:
```typescript
export const createErrorResponse = (message: string, details?: any) => {
  // Log internal details but don't expose them
  if (details) {
    log.error('Internal error details', details);
  }

  return {
    success: false,
    message, // Only user-safe message
    statusCode: 500,
  };
};
```

### Session Management
- **Use secure session handling** with Firebase
- **Implement proper token expiration**
- **Handle token refresh** securely
- **Log authentication events**

### Monitoring & Alerting
- **Monitor for suspicious activities**
- **Set up alerts for security events**
- **Track failed authentication attempts**
- **Monitor for unusual API usage patterns**

## Exceptions
- Internal system routes may have different authentication requirements
- Development environment may have relaxed security for testing
- Emergency access procedures may bypass normal security checks with proper logging
