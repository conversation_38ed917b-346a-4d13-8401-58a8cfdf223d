---
description:
globs:
alwaysApply: true
---

# TypeScript Standards

## Objective

Ensure consistent, type-safe, and maintainable TypeScript code across the AIDA backend service.

## Context

- Node.js 20+ backend service
- Express.js REST API
- Sequelize ORM with PostgreSQL
- Path aliases using `@/` for `src/`
- Cloud deployment on Google App Engine

## Rules

### Type Safety

- **Always use explicit return types** for functions, especially async functions
- **Use strict TypeScript settings** and avoid `any` type unless absolutely necessary
- **Prefer interfaces over types** for object shapes, use types for unions/intersections
- **Use proper generic constraints** when creating reusable functions/classes

✅ Good:

```typescript
export const getProjectById = async (
  id: string
): Promise<ServiceResponse<Project>> => {
  // implementation
};

interface ProjectCreateRequest {
  name: string;
  description?: string;
}
```

❌ Avoid:

```typescript
export const getProjectById = async (id) => {
  // implementation - missing return type and parameter type
};

type ProjectCreateRequest = {
  name: any;
  description: any;
};
```

### Import Organization

- **Use path aliases** (`@/`) consistently for internal imports
- **Group imports** in order: external libraries, internal modules, types
- **Use named imports** instead of default imports when possible
- **Avoid barrel exports** that create circular dependencies

✅ Good:

```typescript
import express from "express";
import { Request, Response } from "express";

import { projectService } from "@/services/project/project.service";
import { log } from "@/services/logger";
import { Project } from "@/schemas/project/Project.model";
import { ServiceResponse } from "@/types";
```

### Error Handling

- **Use typed error responses** with consistent structure
- **Implement proper error boundaries** with try-catch for async operations
- **Use IntentionalError class** for business logic errors
- **Always log errors** with appropriate context (see `logging.mdc` for details)

✅ Good:

```typescript
export const createProject = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const project = await projectService.create(req.body);
    res.status(201).json({ success: true, data: project });
  } catch (error) {
    log.error("Failed to create project", {
      error,
      userId: res.locals.user?.id,
    });
    res.status(500).json(createErrorResponse("Internal server error"));
  }
};
```

### Async/Await Patterns

- **Always use async/await** instead of Promise chains
- **Handle Promise.all** properly for concurrent operations
- **Use proper error handling** in async functions
- **Avoid nested async calls** when possible

## Exceptions

- Legacy code can maintain existing patterns during refactoring
- Third-party library integrations may require `any` types temporarily
- Performance-critical sections may use optimized patterns with documentation
