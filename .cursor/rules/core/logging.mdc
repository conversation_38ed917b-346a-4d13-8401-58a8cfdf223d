# Logging Standards

## Objective

Ensure consistent, structured, and secure logging across the AIDA backend service using the centralized HTTP logging middleware and standard logging patterns.

## Context

- Node.js 20+ backend service with Express.js
- WinstonLogger with Google Cloud Logging integration
- HTTP logging middleware for automatic request/response logging
- Structured logging with sanitization and feature extraction
- Multi-environment support (development, staging, production)

## Rules

### HTTP Logging Middleware

- **Automatic HTTP logging** is handled by `httpLoggingMiddleware`
- **Applied globally** in `src/index.ts` - captures all HTTP requests/responses
- **Structured data** with request IDs, response times, and feature extraction
- **Automatic sanitization** of sensitive data (passwords, tokens, etc.)

✅ HTTP Logging is Automatic:

```typescript
// src/index.ts - Already configured
import { httpLoggingMiddleware } from "./middlewares/httpLogging";
app.use(httpLoggingMiddleware);

// No manual HTTP logging needed in controllers/handlers
```

### Application Logging Standards

- **For services/handlers**: Use `createLogger("ServiceName")` for named loggers
- **For simple cases**: Use `import { log } from "@/services/logger"`
- **Structured logging**: Include relevant context objects
- **Error logging**: Always log errors with context
- **Avoid console.log**: Use proper logging methods instead

✅ Good Application Logging (Services):

```typescript
import { createLogger } from "@/services/logger";

const logger = createLogger("ProjectService");

export const createProject = async (
  userId: string,
  name: string,
  description: string
) => {
  try {
    const project = await models.Project.xCreate({
      name,
      description,
      createdById: userId,
    });

    logger.info("Project created successfully", {
      projectId: project.id,
      userId,
      projectName: project.name,
    });

    return {
      success: true,
      message: "Project created successfully",
      statusCode: 200,
      data: project,
    };
  } catch (error) {
    logger.error(`Failed to create project: ${error.message}`);

    return {
      success: false,
      message: "Failed to create project",
      statusCode: 500,
      data: null,
    };
  }
};
```

✅ Good Application Logging (Controllers/Simple Cases):

```typescript
import { log } from "@/services/logger";

export const createProject = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const project = await projectService.create(req.body);

    // Success logging with context
    log.info("Project created successfully", {
      projectId: project.id,
      userId: res.locals.user?.id,
      projectName: project.name,
    });

    res.status(201).json({ success: true, data: project });
  } catch (error) {
    // Error logging with full context
    log.error("Failed to create project", {
      error: error.message,
      stack: error.stack,
      userId: res.locals.user?.id,
      requestBody: req.body,
    });

    res.status(500).json(createErrorResponse("Internal server error"));
  }
};
```

### Log Levels and Usage

- **`logger.error()`**: Errors, exceptions, failed operations
- **`logger.warn()`**: Warnings, deprecated usage, non-critical issues
- **`logger.info()`**: Important business events, successful operations
- **`logger.debug()`**: Detailed debugging information (development only)

✅ Good Log Level Usage:

```typescript
// Error - system failures, exceptions
logger.error("Database connection failed", { error, retryCount });

// Warning - potential issues, deprecated usage
logger.warn("Using deprecated API endpoint", { endpoint, userId });

// Info - business events, successful operations
logger.info("User authenticated successfully", { userId, email });

// Debug - detailed troubleshooting (filtered in production)
logger.debug("Processing request parameters", { params, query });
```

### Data Sanitization

- **Sensitive data** is automatically sanitized by HTTP middleware
- **Manual logging** should exclude sensitive information
- **Use structured objects** for better searchability

✅ Good Data Sanitization:

```typescript
// Automatic sanitization in HTTP middleware
const SENSITIVE_HEADERS = ["authorization", "cookie", "x-api-key"];
const SENSITIVE_BODY_FIELDS = ["password", "token", "secret"];

// Manual logging - exclude sensitive data
logger.info("User login attempt", {
  email: user.email,
  // password: req.body.password, // ❌ Never log passwords
  ip: req.ip,
  userAgent: req.headers["user-agent"],
});
```

### Feature-Based Logging

- **Feature extraction** is automatic from URL paths (`/api/projects` → `projects`)
- **Feature labels** are added to all HTTP logs
- **Business logic** should include feature context when relevant

✅ Feature Context in Logs:

```typescript
// HTTP middleware automatically extracts feature from URL
// /api/projects/123 → feature: "projects"
// /api/resources/upload → feature: "resources"

// Manual logging can include feature context
logger.info("Resource processing completed", {
  feature: "resources",
  resourceId: resource.id,
  processingTime: Date.now() - startTime,
});
```

### Error Handling Patterns

- **Always log errors** before returning responses
- **Include full context** for debugging
- **Use appropriate log levels** based on severity
- **Avoid logging sensitive data** in error messages

✅ Good Error Handling:

```typescript
export const updateResource = async (req: Request, res: Response) => {
  try {
    const result = await resourceService.update(req.params.id, req.body);
    return res.status(200).json(result);
  } catch (error) {
    if (error instanceof ValidationError) {
      logger.warn("Resource validation failed", {
        resourceId: req.params.id,
        validationErrors: error.errors,
        userId: res.locals.user?.id,
      });
      return res.status(400).json(createErrorResponse("Validation failed"));
    }

    logger.error("Resource update failed", {
      resourceId: req.params.id,
      error: error.message,
      stack: error.stack,
      userId: res.locals.user?.id,
    });

    return res.status(500).json(createErrorResponse("Internal server error"));
  }
};
```

### Performance Logging

- **Response times** are automatically tracked by HTTP middleware
- **Long-running operations** should include timing information
- **Database queries** should log execution times when relevant

✅ Good Performance Logging:

```typescript
export const processLargeFile = async (fileId: string) => {
  const startTime = Date.now();

  try {
    const result = await fileProcessor.process(fileId);

    logger.info("File processing completed", {
      fileId,
      processingTime: Date.now() - startTime,
      fileSize: result.size,
      feature: "resources",
    });

    return result;
  } catch (error) {
    logger.error("File processing failed", {
      fileId,
      processingTime: Date.now() - startTime,
      error: error.message,
    });
    throw error;
  }
};
```

### Logger Configuration

- **WinstonLogger** is configured for Google Cloud Logging
- **Environment-specific** log levels and outputs
- **Structured JSON** format for cloud environments
- **Console output** for development

✅ Logger Usage Patterns:

```typescript
// For services and handlers - use named loggers
import { createLogger } from "@/services/logger";
const logger = createLogger("ProjectService");

// For controllers and simple cases - use global logger
import { log } from "@/services/logger";

// Both provide the same logging methods:
logger.info("Message", { context });
log.info("Message", { context });
```

## Best Practices

### DO:

- Use `createLogger("ServiceName")` for services and handlers
- Use `import { log }` for controllers and simple cases
- Use structured logging with context objects
- Include user IDs and request IDs for traceability
- Log business events for audit trails
- Use appropriate log levels
- Sanitize sensitive data

### DON'T:

- Use `console.log()` in production code
- Log passwords, tokens, or sensitive data
- Create excessive debug logs in production
- Log the same event multiple times
- Include circular references in log objects

## HTTP Logging Features

The `httpLoggingMiddleware` automatically provides:

- **Request/Response logging** with sanitization
- **Response time tracking**
- **Feature extraction** from URL paths
- **User context** from authentication
- **Request ID generation** for traceability
- **Structured labels** for filtering and search

This eliminates the need for manual HTTP logging in controllers and handlers.

## Exceptions

- Legacy code may use different logging patterns during migration
- Third-party integrations may require specific logging formats
- Performance-critical sections may use optimized logging with documentation

---
