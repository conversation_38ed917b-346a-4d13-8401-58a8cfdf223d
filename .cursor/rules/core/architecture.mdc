---
description: 
globs: 
alwaysApply: true
---
# Architecture & Project Structure

## Objective
Maintain clean, scalable architecture with clear separation of concerns following the established layered pattern.

## Context
- Express.js REST API with MVC architecture
- Layered structure: Routes → Controllers → Handlers → Services
- Domain-driven organization by business features
- PostgreSQL with Sequelize ORM
- Google Cloud Platform deployment

## Rules

### Layer Separation
- **Routes** define endpoints and apply middleware only
- **Controllers** handle HTTP concerns (request/response)  
- **Handlers** contain business logic orchestration
- **Services** implement core business logic and data access
- **Models** define data structures and relationships

✅ Good:
```typescript
// routes/project.routes.ts
router.post('/projects', checkAuthMiddleware, createProject);

// controllers/project.controller.ts  
export const createProject = async (req: Request, res: Response) => {
  const result = await createProjectHandler(req.body, res.locals.user);
  res.status(result.statusCode).json(result);
};

// handlers/projects/createProjectHandler.ts
export const createProjectHandler = async (data: ProjectData, user: User) => {
  return await projectService.create(data, user.id);
};
```

### File Naming Conventions
- **Use descriptive, consistent naming**: `[entity][Action][Layer].ts`
- **Routes**: `[entity].routes.ts`
- **Controllers**: `[entity].controller.ts`  
- **Handlers**: `[action][Entity]Handler.ts`
- **Services**: `[entity].service.ts`
- **Models**: `[Entity].model.ts`

### Directory Structure
- **Group by domain** (projects, resources, sessions, users)
- **Separate by layer** within shared concerns
- **Keep related files together** in domain folders
- **Use index files** for clean imports

```
src/
├── api/                 # Main API routes
├── controllers/         # HTTP request handlers
├── handlers/           # Business logic orchestration
│   ├── projects/       # Project domain handlers
│   ├── resources/      # Resource domain handlers
│   └── sessions/       # Session domain handlers
├── services/           # Core business logic
├── schemas/            # Data models and DB definitions
├── middlewares/        # Cross-cutting concerns
└── types/              # Type definitions
```

### Dependency Flow
- **Never import from higher layers**: Services cannot import Controllers
- **Use dependency injection** for external services
- **Import from same level or lower** only
- **Avoid circular dependencies** between domains

```
Routes → Controllers → Handlers → Services → Models
  ↓         ↓           ↓          ↓         ↓
Middlewares can be used at any level
```

### Error Handling Architecture
- **Centralized error handling** with expressErrorHandler
- **Consistent error responses** using createErrorResponse utility
- **Layer-specific error handling**: validation in handlers, data errors in services
- **Proper error logging** with context at each layer

### Response Patterns
- **Standardized response format** with ServiceResponse interface
- **HTTP status codes** follow REST conventions
- **Consistent success/error structure** across all endpoints
- **Include relevant metadata** (pagination, counts, etc.)

✅ Good Response Structure:
```typescript
interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  statusCode: number;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}
```

## Exceptions
- Legacy handlers may combine multiple responsibilities during migration
- Callback handlers can directly access services for performance
- Internal utilities may bypass layer restrictions with documentation
