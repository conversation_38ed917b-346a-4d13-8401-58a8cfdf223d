---
description: 
globs: 
alwaysApply: true
---
# Git Workflow & Permission Rules

## 🚨 CRITICAL: User Permission Required

### ⛔ NEVER Auto-Execute These Commands
```bash
# FORBIDDEN without explicit user permission:
git add .
git add <file>
git commit -m "message"
git push origin <branch>
git merge <branch>
git rebase <branch>
git reset --hard
git checkout <branch>
git pull origin <branch>
```

### ✅ Always Safe to Execute (Read-Only)
```bash
# ALWAYS OK to run:
git status
git diff
git diff --staged  
git log --oneline -10
git branch -l
git remote -v
git show HEAD
```

## 🤝 Proper Workflow

### 1. After Making Code Changes
```markdown
✅ CORRECT:
"I've made the following changes to optimize the Cloud Run configuration:
- Updated CPU allocation from 1 to 2
- Increased memory from 1Gi to 4Gi
- Added timeout configuration

Would you like me to commit and push these changes?"

❌ WRONG:
*Automatically runs git add, commit, push without asking*
```

### 2. Getting User Permission
**Required phrases to look for:**
- "commit this"
- "push this" 
- "save these changes"
- "git commit"
- "git push"
- "yes, commit and push"

**NOT sufficient:**
- "looks good" (could mean review, not commit)
- "thanks" (acknowledgment, not permission)
- Silence (never assume permission)

### 3. When User Says Commit/Push
```bash
# Then and ONLY then:
git add <specific-files>
git commit -m "descriptive message"
git push origin <branch>
```

## 🎯 Best Practices

1. **Always ask**: "Should I commit and push these changes?"
2. **Be specific**: Show exactly what files changed
3. **Wait for explicit approval**: Don't assume consent
4. **Use descriptive commit messages**: Explain what and why
5. **One logical change per commit**: Don't bundle unrelated changes

## ⚠️ Common Mistakes to Avoid

1. **Auto-committing after code changes** ❌
2. **Pushing without asking** ❌  
3. **Assuming "looks good" means "commit"** ❌
4. **Bundling unrelated changes in one commit** ❌
5. **Using vague commit messages** ❌

---
**🎯 REMEMBER: The user owns the repository. Always ask permission for any state-changing git operations.**
