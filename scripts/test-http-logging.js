#!/usr/bin/env node

/**
 * Test script for HTTP logging functionality
 * This script demonstrates the HTTP logging by making test requests to the API
 */

const axios = require('axios');

const BASE_URL = process.env.API_URL || 'http://localhost:8080';

async function testHttpLogging() {
  console.log('🧪 Testing HTTP Logging Functionality');
  console.log(`📍 API URL: ${BASE_URL}`);
  console.log('');

  try {
    // Test 1: Simple GET request
    console.log('📝 Test 1: GET request to /api/health');
    try {
      const response = await axios.get(`${BASE_URL}/api/health`);
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error) {
      console.log(`❌ Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    // Test 2: POST request with body (will likely fail auth, but should log)
    console.log('📝 Test 2: POST request with body to /api/projects');
    try {
      const response = await axios.post(`${BASE_URL}/api/projects`, {
        name: 'Test Project',
        description: 'A test project for HTTP logging',
        password: 'secret123', // This should be redacted in logs
        token: 'sensitive-token' // This should be redacted in logs
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer fake-token', // This should be redacted in logs
          'X-API-Key': 'fake-api-key', // This should be redacted in logs
          'User-Agent': 'HTTP-Logging-Test/1.0'
        }
      });
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error) {
      console.log(`❌ Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    // Test 3: Request with query parameters
    console.log('📝 Test 3: GET request with query parameters');
    try {
      const response = await axios.get(`${BASE_URL}/api/projects?page=1&limit=10&search=test`);
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error) {
      console.log(`❌ Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    // Test 4: Request to non-existent endpoint (404)
    console.log('📝 Test 4: Request to non-existent endpoint (should return 404)');
    try {
      const response = await axios.get(`${BASE_URL}/api/non-existent`);
      console.log(`✅ Status: ${response.status}`);
    } catch (error) {
      console.log(`❌ Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    console.log('🎉 HTTP Logging Test Completed!');
    console.log('');
    console.log('📋 Check the logs to see:');
    console.log('   • Request headers (with sensitive data redacted)');
    console.log('   • Request body (with sensitive fields redacted)');
    console.log('   • Response data (with sensitive fields redacted)');
    console.log('   • Response status codes and timing');
    console.log('   • Feature extraction from URLs');
    console.log('   • Request correlation with unique IDs');
    console.log('');
    console.log('🔍 In development, check the console output.');
    console.log('🔍 In production, check Google Cloud Logging with queries like:');
    console.log('   logName="projects/YOUR_PROJECT_ID/logs/aida-service-info"');
    console.log('   jsonPayload.logger="HTTP-Request"');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testHttpLogging(); 