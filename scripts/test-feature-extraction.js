#!/usr/bin/env node

/**
 * Test script for feature extraction logic
 */

function extractFeature(url) {
  // Extract feature from URL path after /api
  // Handle both /api/v1/feature and /api/feature patterns
  const match = url.match(/\/api\/(?:v1\/)?([^\/?]+)/);
  if (match) {
    return match[1];
  }
  
  // Fallback: extract first path segment after /api
  const pathSegments = url.split('/').filter(Boolean);
  const apiIndex = pathSegments.findIndex(segment => segment === 'api');
  if (apiIndex !== -1 && apiIndex + 1 < pathSegments.length) {
    return pathSegments[apiIndex + 1];
  }
  
  return 'unknown';
}

// Test cases
const testUrls = [
  '/api/project/shared-link/01JSYKC9CSZJE2CD0RS4BVCT21',
  '/api/v1/projects',
  '/api/projects',
  '/api/projects/123',
  '/api/projects/123/share-link',
  '/api/resources',
  '/api/sessions',
  '/api/health',
  '/api/non-existent',
  '/v1/projects', // Old format
  '/projects', // No /api prefix
  '/api'
];

console.log('🧪 Testing Feature Extraction Logic');
console.log('');

testUrls.forEach(url => {
  const feature = extractFeature(url);
  console.log(`URL: ${url}`);
  console.log(`Feature: ${feature}`);
  console.log('---');
});

console.log('');
console.log('✅ Feature extraction test completed!'); 