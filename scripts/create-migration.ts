#!/usr/bin/env tsx

import { writeFile, readFile } from "fs/promises";
import { join } from "path";
import { MigrationGenerator } from "@/services/migrationGenerator";

const migrationName = process.argv[2];
const autoGenerate = process.argv.includes("--auto");

if (!migrationName) {
  console.error("Usage:");
  console.error(
    "  yarn create-migration <migration-name>        # Manual migration"
  );
  console.error(
    "  yarn create-migration <name> --auto          # Auto-generated migration"
  );
  console.error("");
  console.error("Examples:");
  console.error("  yarn create-migration add-user-table");
  console.error("  yarn create-migration update-schema --auto");
  process.exit(1);
}

// Generate timestamp-based filename
const timestamp = new Date().toISOString().replace(/[-:]/g, "").split(".")[0];
const filename = `${timestamp}-${migrationName}.ts`;

const template = `import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  // Add your migration logic here
  // Example:
  // await queryInterface.createTable("users", {
  //   id: {
  //     type: DataTypes.INTEGER,
  //     primaryKey: true,
  //     autoIncrement: true,
  //   },
  //   email: {
  //     type: DataTypes.STRING,
  //     allowNull: false,
  //     unique: true,
  //   },
  //   created_at: {
  //     type: DataTypes.DATE,
  //     defaultValue: Sequelize.NOW,
  //   },
  // });
}

export async function down(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  // Add your rollback logic here
  // Example:
  // await queryInterface.dropTable("users");
}
`;

async function createMigration() {
  const migrationsDir = join(process.cwd(), "migrations");
  const filePath = join(migrationsDir, filename);

  try {
    let content: string;

    if (autoGenerate) {
      console.log("🔍 Analyzing current schema...");
      content = await generateAutoMigration();

      if (content.includes("// No changes detected")) {
        console.log("ℹ️  No schema changes detected");
        return;
      }
    } else {
      content = template;
    }

    await writeFile(filePath, content);
    console.log(`✅ Created migration: ${filename}`);
    console.log(`📁 Location: ${filePath}`);
    console.log("");

    if (autoGenerate) {
      console.log("Next steps:");
      console.log("1. Review the auto-generated migration file");
      console.log("2. Run 'yarn migrate up' to apply the migration");
    } else {
      console.log("Next steps:");
      console.log("1. Edit the migration file to add your database changes");
      console.log("2. Run 'yarn migrate up' to apply the migration");
    }
  } catch (error) {
    console.error("❌ Failed to create migration:", error);
    process.exit(1);
  }
}

async function generateAutoMigration(): Promise<string> {
  try {
    const generator = new MigrationGenerator();

    // Get current database schema
    const currentSchema = await generator.getCurrentSchema();

    // Try to load the last schema snapshot
    const schemaSnapshotPath = join(
      process.cwd(),
      "migrations",
      ".schema-snapshot.json"
    );
    let lastSchema = [];

    try {
      const snapshotContent = await readFile(schemaSnapshotPath, "utf-8");
      lastSchema = JSON.parse(snapshotContent);
    } catch (error) {
      console.log(
        "📝 No previous schema snapshot found, creating initial migration"
      );
    }

    // Generate migration based on differences
    const migrationContent = generator.compareSchemas(
      lastSchema,
      currentSchema
    );

    // Save current schema as snapshot for next time
    await writeFile(schemaSnapshotPath, JSON.stringify(currentSchema, null, 2));

    return migrationContent;
  } catch (error) {
    console.error("❌ Failed to auto-generate migration:", error);
    throw error;
  }
}

createMigration();
