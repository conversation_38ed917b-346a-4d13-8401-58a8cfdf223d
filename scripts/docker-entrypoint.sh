#!/bin/bash

# Docker Entrypoint Script for Cloud Run
# This script runs loadenv.js to fetch secrets and create .env file
# before starting the Node.js application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🚀 Starting Aida Service Container..."

# Debug environment variables
print_status $YELLOW "Debug: Environment variables:"
echo "  K_SERVICE: ${K_SERVICE:-'not set'}"
echo "  BUILD_MODE: ${BUILD_MODE:-'not set'}"
echo "  GCP_PROJECT_ID: ${GCP_PROJECT_ID:-'not set'}"
echo "  PORT: ${PORT:-'not set'}"
echo "  NODE_ENV: ${NODE_ENV:-'not set'}"

# Ensure PORT is set (Cloud Run requirement)
if [[ -z "${PORT}" ]]; then
    print_status $YELLOW "⚠️  PORT not set, defaulting to 8080"
    export PORT=8080
fi

# Check if we're running in Cloud Run or Cloud Build
if [[ -n "${K_SERVICE}" ]]; then
    print_status $BLUE "☁️  Running in Cloud Run environment"
    ENVIRONMENT="cloud-run"
elif [[ -n "${CLOUD_BUILD_PROJECT_ID}" || -n "${BUILD_ID}" ]]; then
    print_status $BLUE "🏗️  Running in Cloud Build environment"
    ENVIRONMENT="cloud-build"
else
    print_status $BLUE "🐳 Running in Docker container environment"
    ENVIRONMENT="docker"
fi

# Validate required environment variables
if [[ -z "${BUILD_MODE}" ]]; then
    print_status $RED "❌ BUILD_MODE environment variable is required"
    exit 1
fi

if [[ -z "${GCP_PROJECT_ID}" ]]; then
    print_status $RED "❌ GCP_PROJECT_ID environment variable is required"
    exit 1
fi

print_status $YELLOW "Environment: ${BUILD_MODE}"
print_status $YELLOW "Project: ${GCP_PROJECT_ID}"

# Ensure upload directory exists and has proper permissions
print_status $BLUE "📁 Setting up upload directory..."
mkdir -p /tmp/aida-uploads
chmod 755 /tmp/aida-uploads
print_status $GREEN "✅ Upload directory ready: /tmp/aida-uploads"

# Verify FFmpeg installation
print_status $BLUE "🎬 Verifying FFmpeg installation..."
if command -v ffmpeg >/dev/null 2>&1; then
    print_status $GREEN "✅ FFmpeg found: $(ffmpeg -version | head -n1)"
else
    print_status $RED "❌ FFmpeg not found!"
    exit 1
fi

if command -v ffprobe >/dev/null 2>&1; then
    print_status $GREEN "✅ FFprobe found: $(ffprobe -version | head -n1)"
else
    print_status $RED "❌ FFprobe not found!"
    exit 1
fi

# Check if .env file already exists (skip secret loading for true local development only)
if [[ -f ".env" && "${ENVIRONMENT}" == "local" ]]; then
    print_status $GREEN "✅ .env file already exists, skipping secret loading"
else
    # Load environment configuration and secrets
    print_status $BLUE "🔐 Loading environment configuration and secrets..."

    # Export environment variables for loadenv.js
    export BUILD_MODE
    export GCP_PROJECT_ID

    # Check authentication status
    if [[ "${ENVIRONMENT}" == "cloud-run" || "${ENVIRONMENT}" == "cloud-build" ]]; then
        print_status $BLUE "🔐 Using service account authentication..."

        # Verify we can access the metadata server (indicates proper SA setup)
        if curl -s -f -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/email" > /dev/null 2>&1; then
            print_status $GREEN "✅ Service account authentication available"
        else
            print_status $YELLOW "⚠️  Service account metadata not accessible, but continuing..."
        fi
    fi

    # Run loadenv.js to fetch secrets and create .env file
    print_status $BLUE "📦 Running loadenv.js..."
    if timeout 60 node scripts/loadenv.js; then
        print_status $GREEN "✅ Environment configuration loaded successfully"
    else
        print_status $RED "❌ Failed to load environment configuration"

        # In Cloud environments, this is critical - exit if we can't load secrets
        if [[ "${ENVIRONMENT}" == "cloud-run" || "${ENVIRONMENT}" == "cloud-build" ]]; then
            print_status $RED "💥 Cannot start without environment configuration in cloud environment"
            print_status $YELLOW "🔧 Troubleshooting tips:"
            echo "  1. Check if service account has 'Secret Manager Secret Accessor' role"
            echo "  2. Verify secrets exist in Secret Manager for project: ${GCP_PROJECT_ID}"
            echo "  3. Check Cloud Run service account permissions"
            echo "  4. Check loadenv.js script for errors"

            # Show recent logs for debugging
            print_status $BLUE "📋 Recent logs:"
            tail -20 /var/log/*.log 2>/dev/null || echo "No log files found"

            exit 1
        else
            print_status $YELLOW "⚠️  Continuing without secret loading (Docker local testing)"
        fi
    fi
fi

# Verify .env file exists
if [[ ! -f ".env" ]]; then
    print_status $RED "❌ .env file not found after loading"
    exit 1
fi

print_status $GREEN "✅ Environment setup completed"

# Run database migrations only if explicitly requested
if [[ "${RUN_MIGRATIONS}" == "true" ]]; then
    print_status $BLUE "🗄️ Running database migrations (RUN_MIGRATIONS=true)..."
    if [[ "${BUILD_MODE}" == "production" ]]; then
        print_status $YELLOW "⚠️  Production environment detected - running migrations with extra safety"
        # Add a small delay for production to ensure database is ready
        sleep 5
    fi

    # Run migrations using the compiled JavaScript files
    if timeout 120 node dist/scripts/migrate.js up; then
        print_status $GREEN "✅ Database migrations completed successfully"
    else
        migration_exit_code=$?
        print_status $RED "❌ Database migrations failed (exit code: ${migration_exit_code})"
        print_status $YELLOW "🔧 Migration troubleshooting tips:"
        echo "  1. Check database connection and credentials"
        echo "  2. Verify migration files are valid"
        echo "  3. Check database permissions"
        echo "  4. Review migration logs above"
        
        # In production, migrations failure should stop container startup
        if [[ "${BUILD_MODE}" == "production" ]]; then
            print_status $RED "💥 Cannot start application with failed migrations in production"
            exit 1
        else
            print_status $YELLOW "⚠️  Continuing startup despite migration failure in non-production environment"
        fi
    fi
else
    print_status $GREEN "✅ Skipping database migrations (RUN_MIGRATIONS not set)"
    print_status $BLUE "ℹ️  To run migrations, set RUN_MIGRATIONS=true during deployment"
fi

# Start the Node.js application
print_status $BLUE "🎯 Starting Node.js application..."

# Verify dist directory exists
if [[ ! -d "dist" ]]; then
    print_status $RED "❌ dist directory not found - build may have failed"
    ls -la
    exit 1
fi

# Verify main file exists
if [[ ! -f "dist/src/index.js" ]]; then
    print_status $RED "❌ dist/src/index.js not found - build may have failed"
    ls -la dist/
    exit 1
fi

print_status $GREEN "✅ Application files verified"

# Always run in production mode in Docker containers
# Development mode is only for local development outside of containers
print_status $BLUE "🚀 Starting in production mode (Docker container)..."
print_status $YELLOW "Environment: ${BUILD_MODE}"
print_status $YELLOW "Port: ${PORT}"
print_status $YELLOW "Working directory: $(pwd)"

# Final check before starting
if [[ ! -f "dist/src/index.js" ]]; then
    print_status $RED "❌ CRITICAL: dist/src/index.js not found!"
    ls -la dist/ || echo "dist directory not found"
    exit 1
fi

print_status $GREEN "🎯 Starting Node.js application..."
print_status $BLUE "Debug: Final environment check before starting..."
echo "  NODE_ENV: ${NODE_ENV:-'not set'}"
echo "  BUILD_MODE: ${BUILD_MODE:-'not set'}"
echo "  GCP_PROJECT_ID: ${GCP_PROJECT_ID:-'not set'}"
echo "  PORT: ${PORT:-'not set'}"
echo "  Working directory: $(pwd)"
echo "  .env file exists: $(test -f .env && echo 'yes' || echo 'no')"
echo "  dist/src/index.js exists: $(test -f dist/src/index.js && echo 'yes' || echo 'no')"

print_status $GREEN "🚀 Executing: node dist/src/index.js"
exec node dist/src/index.js
