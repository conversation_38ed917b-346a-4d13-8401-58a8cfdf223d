const fs = require("fs");
const path = require("path");
const { SecretManagerServiceClient } = require("@google-cloud/secret-manager");
require("dotenv").config();

// List of secret keys to fetch from GCP Secret Manager
const SECRET_KEYS = [
  "GOOGLE_GENERATIVE_AI_API_KEY",
  "DB_PASSWORD",
  "REV_AI_TOKEN",
  "SENDGRID_API_KEY",
  "SPEECHMATICS_API_KEY",
];

let env = "development";
if (process.env.BUILD_MODE) {
  env = process.env.BUILD_MODE;
}

console.log(`Selecting environment: ${env}`);
const filePath = path.join(__dirname, "../env", `/.env.${env}`);
const envFilePath = path.join(__dirname, "../", "/.env");

// Copy the base .env file
fs.copyFileSync(filePath, envFilePath);

// Check if we're running in a cloud environment
const isCloudRun = process.env.K_SERVICE !== undefined;
const isCloudBuild =
  process.env.CLOUD_BUILD_PROJECT_ID !== undefined ||
  process.env.BUILD_ID !== undefined;
const isCloudEnvironment = isCloudRun || isCloudBuild;

console.log(`Environment detection:`);
console.log(`  Cloud Run: ${isCloudRun}`);
console.log(`  Cloud Build: ${isCloudBuild}`);
console.log(`  Cloud Environment: ${isCloudEnvironment}`);

// Initialize Secret Manager client
let secretManagerClient;
try {
  secretManagerClient = new SecretManagerServiceClient();
  console.log("✅ Secret Manager client initialized successfully");
} catch (error) {
  console.error(
    "❌ Error: Could not initialize Google Cloud Secret Manager client."
  );

  if (isCloudEnvironment) {
    console.error("\n🔧 Cloud Environment Troubleshooting:");
    console.error(
      "1. Ensure the service account has 'Secret Manager Secret Accessor' role"
    );
    console.error("2. Verify the secrets exist in the correct GCP project");
    console.error("3. Check that the service account is properly configured");
  } else {
    console.error("\n🔧 Local Development Setup:");
    console.error("Option 1 - Using gcloud CLI (Recommended):");
    console.error("1. Install gcloud CLI: brew install google-cloud-sdk");
    console.error("2. Login: gcloud auth login");
    console.error(
      "3. Set up application default credentials: gcloud auth application-default login"
    );
    console.error(
      `4. Set project: gcloud config set project ${
        process.env.GCP_PROJECT_ID || "your-project-id"
      }`
    );
    console.error("\nOption 2 - Using service account key:");
    console.error("1. Create a service account key from Google Cloud Console");
    console.error(
      '2. Set environment variable: export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"'
    );
  }
  process.exit(1);
}

async function fetchSecrets() {
  try {
    const projectId = process.env.GCP_PROJECT_ID;
    console.log(`Using GCP Project ID: ${projectId}`);

    const failedSecrets = [];
    const successfulSecrets = [];

    // Fetch each secret and append to .env file
    for (const key of SECRET_KEYS) {
      try {
        const [version] = await secretManagerClient.accessSecretVersion({
          name: `projects/${projectId}/secrets/${key}/versions/latest`,
        });

        const secretValue = version.payload.data.toString();
        fs.appendFileSync(envFilePath, `\n${key}=${secretValue}`);
        console.log(`✅ Successfully fetched secret: ${key}`);
        successfulSecrets.push(key);
      } catch (error) {
        failedSecrets.push({ key, error });
        if (error.code === 5) {
          // NOT_FOUND
          console.error(`❌ Secret ${key} not found in project ${projectId}`);
        } else if (error.code === 7) {
          // PERMISSION_DENIED
          console.error(
            `❌ Permission denied to access secret ${key}. Please check your credentials and IAM permissions.`
          );
        } else {
          console.error(`❌ Error fetching secret ${key}:`, error.message);
        }
      }
    }

    // Report results
    console.log(`\n📊 Secret loading summary:`);
    console.log(
      `  ✅ Successful: ${successfulSecrets.length}/${SECRET_KEYS.length}`
    );
    console.log(`  ❌ Failed: ${failedSecrets.length}/${SECRET_KEYS.length}`);

    if (successfulSecrets.length > 0) {
      console.log(`  ✅ Loaded secrets: ${successfulSecrets.join(", ")}`);
    }

    if (failedSecrets.length > 0) {
      console.log(
        `  ❌ Failed secrets: ${failedSecrets.map((f) => f.key).join(", ")}`
      );
      console.error(
        `\n💥 CRITICAL: Failed to load ${failedSecrets.length} required secrets!`
      );
      console.error(`This will cause application startup to fail.`);
      console.error(`\n🔧 Troubleshooting steps:`);
      console.error(
        `1. Verify secrets exist in Secret Manager for project: ${projectId}`
      );
      console.error(
        `2. Check service account has 'Secret Manager Secret Accessor' role`
      );
      console.error(
        `3. Ensure Cloud Run service is configured with correct service account`
      );

      // Exit with error code to fail the container startup
      process.exit(1);
    }

    console.log(`\n🎉 All secrets loaded successfully!`);
  } catch (error) {
    console.error("❌ Error initializing Secret Manager:", error.message);
    if (error.message.includes("Could not load the default credentials")) {
      console.error(
        "\nPlease ensure you have set up Google Cloud credentials properly."
      );
      console.error("See the error message above for setup instructions.");
    }
    // Exit with error code to fail the container startup
    process.exit(1);
  } finally {
    // Close the Secret Manager client to prevent hanging
    if (secretManagerClient) {
      try {
        await secretManagerClient.close();
      } catch (closeError) {
        // Ignore close errors
      }
    }
  }
}

// Execute the secret fetching
fetchSecrets()
  .catch((error) => {
    console.error("💥 FATAL: Secret fetching failed:", error);
    process.exit(1);
  })
  .finally(() => {
    // Force exit to ensure the process doesn't hang
    process.exit(0);
  });
