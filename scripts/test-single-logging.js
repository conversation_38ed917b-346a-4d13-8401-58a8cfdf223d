#!/usr/bin/env node

/**
 * Test Single Logging Verification
 * 
 * This script tests that each HTTP request/response is logged only once,
 * not multiple times due to multiple response methods being called.
 */

const axios = require('axios');

async function testSingleLogging() {
  console.log('🧪 Testing Single Logging Verification...\n');

  const API_URL = process.env.API_URL || 'http://localhost:8080';
  
  console.log(`📍 API URL: ${API_URL}`);
  console.log('📝 This test will make requests and verify single logging...\n');

  try {
    // Test 1: Simple GET request (should use res.json)
    console.log('📝 Test 1: GET request (res.json)');
    const response1 = await axios.get(`${API_URL}/api/health`);
    console.log(`✅ Status: ${response1.status}`);
    console.log('✅ Request completed - check logs for single entry\n');

    // Test 2: POST request with body (should use res.json)
    console.log('📝 Test 2: POST request with body (res.json)');
    try {
      const response2 = await axios.post(`${API_URL}/api/projects`, {
        name: 'Test Project',
        description: 'Test Description'
      });
      console.log(`✅ Status: ${response2.status}`);
    } catch (error) {
      console.log(`❌ Expected error: ${error.response?.status} - ${error.response?.statusText}`);
    }
    console.log('✅ Request completed - check logs for single entry\n');

    // Test 3: Request that might use res.send
    console.log('📝 Test 3: Request that might use res.send');
    try {
      const response3 = await axios.get(`${API_URL}/api/nonexistent`);
      console.log(`✅ Status: ${response3.status}`);
    } catch (error) {
      console.log(`❌ Expected error: ${error.response?.status} - ${error.response?.statusText}`);
    }
    console.log('✅ Request completed - check logs for single entry\n');

    // Test 4: Request with query parameters
    console.log('📝 Test 4: Request with query parameters');
    try {
      const response4 = await axios.get(`${API_URL}/api/projects?page=1&limit=10`);
      console.log(`✅ Status: ${response4.status}`);
    } catch (error) {
      console.log(`❌ Expected error: ${error.response?.status} - ${error.response?.statusText}`);
    }
    console.log('✅ Request completed - check logs for single entry\n');

    console.log('🎉 Single Logging Test Completed!\n');
    console.log('📋 Expected Results:');
    console.log('  • Each request should have exactly 1 request log');
    console.log('  • Each response should have exactly 1 response log');
    console.log('  • No duplicate logs for the same request/response');
    console.log('  • Total logs should be 2 per request (1 request + 1 response)');
    console.log('\n🔍 Check Google Cloud Logging to verify:');
    console.log('  • No duplicate entries for the same requestId');
    console.log('  • Each requestId appears exactly twice (request + response)');
    console.log('  • Clean, single logging per HTTP interaction');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSingleLogging().catch(console.error); 