#!/usr/bin/env tsx

import { Migrator } from "../src/services/migrator";
import { log } from "../src/services/logger";

const command = process.argv[2];
const migrationName = process.argv[3];

async function main() {
  const migrator = new Migrator();

  try {
    switch (command) {
      case "up":
        await migrator.runMigrations();
        break;

      case "down":
        await migrator.rollbackMigration(migrationName);
        break;

      case "status":
        const pending = await migrator.getPendingMigrations();
        const executed = await migrator.getExecutedMigrations();

        console.log("Migration Status:");
        console.log(`Executed: ${executed.length}`);
        console.log(`Pending: ${pending.length}`);

        if (pending.length > 0) {
          console.log("\nPending migrations:");
          pending.forEach((m) => console.log(`  - ${m}`));
        }
        break;

      default:
        console.log("Usage:");
        console.log("  yarn migrate up           - Run all pending migrations");
        console.log(
          "  yarn migrate down [name]  - Rollback migration (latest if no name)"
        );
        console.log("  yarn migrate status       - Show migration status");
        process.exit(1);
    }
  } catch (error) {
    log.error("Migration command failed:", error);
    process.exit(1);
  }
}

main();
