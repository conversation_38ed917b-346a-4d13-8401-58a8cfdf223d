#!/usr/bin/env node

/**
 * Test Google Cloud Logging Setup
 *
 * This script tests if Google Cloud Logging is properly configured
 * and can write logs to Google Cloud.
 */

const { WinstonLogger } = require("../dist/src/services/logger/winston");

async function testGcpLogging() {
  console.log("🧪 Testing Google Cloud Logging Setup...\n");

  // Check environment variables
  console.log("📋 Environment Check:");
  console.log(`  NODE_ENV: ${process.env.NODE_ENV || "undefined"}`);
  console.log(
    `  GCP_LOGGING_ENABLED: ${process.env.GCP_LOGGING_ENABLED || "undefined"}`
  );
  console.log(
    `  GOOGLE_APPLICATION_CREDENTIALS: ${
      process.env.GOOGLE_APPLICATION_CREDENTIALS || "undefined"
    }`
  );
  console.log(`  GCP_PROJECT_ID: ${process.env.GCP_PROJECT_ID || "undefined"}`);
  console.log("");

  // Test logger initialization
  try {
    console.log("🔧 Initializing Winston Logger...");
    const logger = new WinstonLogger("GCP-Test");
    console.log("✅ Winston Logger initialized successfully");
    console.log("");

    // Test different log levels
    console.log("📝 Testing log levels...");

    logger.info("Test info message from GCP logging test", {
      test: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development",
      metadata: {
        userId: "test-user-123",
        projectId: "test-project-456",
        requestId: "req-789",
        details: {
          action: "test_logging",
          status: "success",
          nested: {
            level: "deep",
            value: 42,
          },
        },
      },
    });

    logger.warn("Test warning message from GCP logging test", {
      test: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development",
      warningType: "test_warning",
      severity: "medium",
    });

    // Test error logging with actual Error object
    const testError = new Error("This is a test error for GCP logging");
    testError.stack = testError.stack; // Ensure stack trace exists

    logger.error("Test error message from GCP logging test", {
      test: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development",
      error: testError,
      contextData: {
        operation: "test_error_logging",
        attemptNumber: 1,
      },
    });

    console.log("✅ Log messages sent successfully");
    console.log("");

    // Wait a moment for logs to be processed
    console.log("⏳ Waiting 3 seconds for logs to be processed...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("✅ Test completed!");
    console.log("");
    console.log(
      "📊 Check Google Cloud Console > Logging to see if logs appeared:"
    );
    console.log("   https://console.cloud.google.com/logs");
    console.log("");
    console.log(
      "🔍 Search for structured logs (should now appear as jsonPayload):"
    );
    console.log('   - logName="aida-service"');
    console.log('   - jsonPayload.logger="GCP-Test"');
    console.log("   - jsonPayload.test=true");
    console.log('   - jsonPayload.metadata.userId="test-user-123"');
    console.log('   - jsonPayload.metadata.details.action="test_logging"');
    console.log("");
    console.log(
      "✅ Expected Result: Logs should appear as jsonPayload (structured) instead of textPayload (string)"
    );
  } catch (error) {
    console.error("❌ Error during GCP logging test:", error);
    console.log("");
    console.log("🔧 Troubleshooting:");
    console.log("   1. Make sure you have Google Cloud credentials set up");
    console.log(
      "   2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable"
    );
    console.log("   3. Set GCP_PROJECT_ID environment variable");
    console.log("   4. Enable Google Cloud Logging API in your project");
    console.log("   5. Set GCP_LOGGING_ENABLED=true to force enable");
  }
}

// Run the test
testGcpLogging().catch(console.error);
