#!/usr/bin/env node

/**
 * Test Console Logging Configuration
 * 
 * This script demonstrates different console logging configurations
 * to show how to control console output when GCP logging is enabled.
 */

const { WinstonLogger } = require('../dist/services/logger/winston');

async function testConsoleLogging() {
  console.log('🧪 Testing Console Logging Configuration...\n');

  // Test different configurations
  const configs = [
    {
      name: 'Default (Development)',
      env: {},
      description: 'Console enabled, GCP disabled'
    },
    {
      name: 'GCP Only (Local)',
      env: { GCP_LOGGING_ENABLED: 'true' },
      description: 'Console disabled, GCP enabled (default behavior)'
    },
    {
      name: 'Both Enabled',
      env: { GCP_LOGGING_ENABLED: 'true', CONSOLE_LOGGING_ENABLED: 'true' },
      description: 'Console enabled, GCP enabled'
    },
    {
      name: 'Console Only',
      env: { CONSOLE_LOGGING_ENABLED: 'true' },
      description: 'Console enabled, G<PERSON> disabled'
    },
    {
      name: 'GCP Only (Explicit)',
      env: { GCP_LOGGING_ENABLED: 'true', CONSOLE_LOGGING_ENABLED: 'false' },
      description: 'Console disabled, GCP enabled (explicit)'
    }
  ];

  for (const config of configs) {
    console.log(`\n🔧 Testing: ${config.name}`);
    console.log(`📝 Description: ${config.description}`);
    console.log('📋 Environment Variables:');
    
    // Set environment variables for this test
    Object.entries(config.env).forEach(([key, value]) => {
      process.env[key] = value;
      console.log(`  ${key}=${value}`);
    });
    
    console.log('');

    try {
      // Clear the logger cache to force re-initialization
      delete require.cache[require.resolve('../dist/services/logger/winston')];
      
      // Re-import to get fresh configuration
      const { WinstonLogger: FreshWinstonLogger } = require('../dist/services/logger/winston');
      
      const logger = new FreshWinstonLogger('Console-Test');
      
      // Send test messages
      logger.info('Test info message', { config: config.name, test: true });
      logger.warn('Test warning message', { config: config.name, test: true });
      logger.error('Test error message', { config: config.name, test: true });
      
      console.log('✅ Test completed for this configuration');
      
    } catch (error) {
      console.error('❌ Error during test:', error.message);
    }
    
    // Wait a moment between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n🎉 All console logging tests completed!');
  console.log('\n📊 Summary:');
  console.log('  • Default: Console enabled, GCP disabled');
  console.log('  • GCP Only: Console disabled when GCP enabled (prevents duplicates)');
  console.log('  • Both: Console and GCP both enabled (for debugging)');
  console.log('  • Explicit: Full control with environment variables');
  console.log('\n🔧 Environment Variables:');
  console.log('  GCP_LOGGING_ENABLED=true|false');
  console.log('  CONSOLE_LOGGING_ENABLED=true|false');
  console.log('  NODE_ENV=production|development');
}

// Run the test
testConsoleLogging().catch(console.error); 