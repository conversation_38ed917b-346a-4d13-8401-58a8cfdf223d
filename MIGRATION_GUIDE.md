# Database Migration Guide

This comprehensive guide covers the complete database migration system for the AIDA service, including automated Docker-based deployment, tracking, safety features, and team workflows.

## 🚀 **Automatic Migration Deployment**

Database migrations now run **automatically during container startup** in all environments, ensuring seamless schema updates with enterprise-grade safety features and eliminating complex CI/CD database connection issues.

### **🏗️ Architecture: Deployment-Triggered Migrations**

- **✅ Deployment-Only Migrations**: Run only during actual deployments, not container restarts
- **✅ Environment Flag Control**: `RUN_MIGRATIONS=true` set automatically during deployments
- **✅ Simple CI/CD**: Just build and deploy containers - no database connection complexity
- **✅ Environment Consistency**: Same migration environment as application runtime
- **🎯 Result**: Reliable deployments, no unnecessary migration runs, faster container restarts

## 📋 **Environment-Specific Workflows**

### **Development Environment**

- **Trigger**: Automatic when PR is merged to `develop` branch
- **Migration Execution**: During container startup after deployment
- **Safety**: Container startup includes pre-check → Run migrations → Start application
- **Failure Handling**: Container fails to start if migrations fail, ensuring safety

### **Staging Environment**

- **Trigger**: Manual dispatch (requires approval)
- **Migration Execution**: During container startup after deployment
- **Safety**: Same robust checks as development
- **Purpose**: Final validation before production

### **Production Environment**

- **Trigger**: Manual dispatch with explicit confirmation
- **Migration Execution**: During container startup after deployment
- **Safety**: Enhanced checks + production-specific timeouts + startup delay logging
- **Confirmation**: Must type "YES" to proceed with deployment

## 🛡️ **Migration Control System**

### **Environment-Based Execution Control**

Migrations are controlled via the `RUN_MIGRATIONS` environment variable:

- **✅ During Deployment**: `RUN_MIGRATIONS=true` is automatically set by deployment scripts
- **❌ During Container Restart**: `RUN_MIGRATIONS` is not set, migrations are skipped
- **🎯 Result**: Migrations only run when actually deploying new code, not on autoscaling/restarts

### **Automatic Tracking (Built-In)**

The migration system includes **built-in tracking** that prevents duplicate executions across all environments. This is a **critical safety feature** that requires no manual intervention.

#### **How Tracking Works**

Each environment automatically maintains a `migrations` table:

```sql
-- Table structure (auto-created)
CREATE TABLE migrations (
  name VARCHAR(255) PRIMARY KEY,           -- Migration filename
  executed_at TIMESTAMP DEFAULT NOW()     -- When it was executed
);
```

#### **Tracking Logic**

```typescript
// System automatically:
1. Checks which migrations have been executed
2. Compares with available migration files
3. Only runs migrations not in the tracking table
4. Records successful executions in the table
```

#### **Environment Independence**

Each environment maintains **independent tracking**:

| Environment | Database       | Migrations Table | Status      |
| ----------- | -------------- | ---------------- | ----------- |
| DEV         | `aida-dev`     | `migrations`     | Independent |
| STAGING     | `aida-staging` | `migrations`     | Independent |
| PRODUCTION  | `aida-prod`    | `migrations`     | Independent |

### **Safety Guarantees**

- ✅ **Cannot run same migration twice** in same environment
- ✅ **Safe to run `yarn migrate up` multiple times**
- ✅ **Automatic detection** of pending vs executed migrations
- ✅ **Transaction-safe recording** (all-or-nothing)

## 🔄 **Automated Deployment Flow**

```mermaid
graph TD
    A[PR Created] --> B[Code Review]
    B --> C[PR Approved & Merged to develop]
    C --> D[🤖 GitHub Actions Triggered]
    D --> E[📦 Build Container Image]
    E --> F[🚀 Deploy Container to Cloud Run]
    F --> G[🐳 Container Starts]
    G --> H[🔍 Load Environment & Secrets]
    H --> I[🗄️ Run Database Migrations]
    I --> J{Migration Success?}
    J -->|✅ Success| K[🚀 Start Application]
    J -->|❌ Failure| L[❌ Container Fails to Start]
    L --> M[📧 Cloud Run Health Check Fails]
```

## 📋 **Quick Start**

### Create a new migration

```bash
# Manual migration (for complex logic)
yarn create-migration add-user-table

# Auto-generated migration (recommended - includes safety checks)
yarn create-migration add-user-table --auto
```

### Check migration status

```bash
# Check what's been executed vs pending
yarn migrate status

# Example output:
# Migration Status:
# Executed: 2
# Pending: 1
#
# Pending migrations:
#   - 20250127T154032-add-user-avatars.ts
```

### Run migrations

```bash
yarn migrate up
```

### Rollback migrations

```bash
# Rollback latest migration
yarn migrate down

# Rollback specific migration
yarn migrate down <filename>
```

## 🔄 **Team Collaboration Workflows**

### Scenario 1: New Feature with Schema Changes

**Developer A** adds a new feature requiring database changes:

1. **Update Sequelize models**:

```typescript
// src/schemas/models/User.ts
export interface User {
  id: string;
  email: string;
  preferences: object; // ← New field
  createdAt: Date;
}
```

2. **Generate migration**:

```bash
yarn create-migration add-user-preferences --auto
```

3. **Review & test migration**:

```bash
# Check generated migration (now includes safety checks)
cat migrations/20240120-add-user-preferences.ts

# Test migration locally
yarn migrate up
yarn migrate down  # Test rollback
yarn migrate up    # Re-apply
```

4. **Commit and create PR**:

```bash
git add src/schemas/models/User.ts
git add migrations/20240120-add-user-preferences.ts
git commit -m "feat: Add user preferences system"
git push origin feature/user-preferences
```

5. **Automatic deployment after PR merge**:
   - PR gets reviewed and approved
   - Merge to `develop` → **Automatic DEV deployment with migrations!**
   - Migrations run safely with tracking and verification

### Scenario 2: Multiple Developers with Schema Changes

**Developer A** creates migration for user preferences:

```bash
git checkout -b feature/user-preferences
# ... make changes, create migration ...
git push origin feature/user-preferences
```

**Developer B** creates migration for notifications (based on develop):

```bash
git checkout -b feature/notifications
# ... make changes, create migration ...
git push origin feature/notifications
```

**When both PRs are approved:**

1. **Developer A** merges first → Auto-deploys to DEV
2. **Developer B** rebases and resolves any conflicts:

```bash
git checkout develop
git pull origin develop
git checkout feature/notifications
git rebase develop
yarn migrate up  # Test with A's migration locally
```

3. **Developer B** merges → Auto-deploys to DEV with only the new migration

### Scenario 3: Production Hotfix with Schema Change

**For urgent production fixes requiring schema changes:**

1. **Create hotfix branch from develop**:

```bash
git checkout develop
git pull origin develop
git checkout -b hotfix/fix-user-constraint
```

2. **Make minimal, safe changes**:

```bash
# Only add columns, never drop/modify
yarn create-migration fix-user-email-constraint --auto
```

3. **Test thoroughly**:

```bash
# Test on production-like data
yarn migrate up
yarn migrate down
yarn migrate up
```

4. **Deploy with enhanced production safety**:
   - Manual deployment to production requires explicit "YES" confirmation
   - 10-second warning with safety checklist
   - Enhanced error notifications for immediate attention

## 🛡️ **Safety Features**

### **Pre-Migration Checks**

- ✅ Current migration status verification
- ✅ Database connection validation
- ✅ Migration file integrity check
- ✅ Table existence checks (prevents recreation)

### **During Migration**

- ✅ Transaction-wrapped execution
- ✅ Detailed logging with progress updates
- ✅ Automatic rollback on failure
- ✅ Real-time error reporting
- ✅ Idempotent operations (safe to run multiple times)

### **Post-Migration Verification**

- ✅ Status confirmation after execution
- ✅ Schema validation
- ✅ Deployment conditional on migration success

### **Production-Specific Safety**

- ⚠️ **Explicit confirmation required** (type "YES")
- ⚠️ **10-second warning period** with safety checklist
- ⚠️ **Enhanced error notifications** for immediate attention
- ⚠️ **Deployment summary** with timestamp and commit info

## 📊 **How Migration Environments Work**

### **Local Development Environment**

```typescript
// Local development uses sync for rapid iteration
if (apiNodeEnv === "local") {
  await db.sync({ alter: true }); // Automatically handles schema changes
} else {
  // All other environments rely on CI/CD migrations
  console.info("✅ Database ready (migrations handled by CI/CD)");
}
```

### **Staging/Production Environments**

- ✅ **Migrations run during deployment** (CI/CD) - not on app startup
- ✅ **Faster application startup** - no migration overhead
- ✅ **Clean separation** - deployment concerns vs runtime concerns
- ✅ **Each environment tracks migrations independently** via CI/CD

### **Cross-Environment Example**

```bash
# DEV might have 5 migrations executed
DEV: yarn migrate status
# Executed: 5, Pending: 0

# STAGING might have 3 migrations executed
STAGING: yarn migrate status
# Executed: 3, Pending: 2

# PRODUCTION might have 2 migrations executed
PROD: yarn migrate status
# Executed: 2, Pending: 3
```

## 📋 **Migration Commands**

| Command                               | Description                                | Safety Level         |
| ------------------------------------- | ------------------------------------------ | -------------------- |
| `yarn create-migration <name>`        | Create new migration file                  | ✅ Safe              |
| `yarn create-migration <name> --auto` | Auto-generate migration with safety checks | ✅ Recommended       |
| `yarn migrate up`                     | Run all pending migrations                 | ✅ Safe (idempotent) |
| `yarn migrate down`                   | Rollback latest migration                  | ⚠️ Caution           |
| `yarn migrate down <filename>`        | Rollback specific migration                | ⚠️ Caution           |
| `yarn migrate status`                 | Show migration status                      | ✅ Safe (read-only)  |

## 🧪 **Testing Migration Tracking**

You can verify the tracking system works:

```bash
# 1. Check current status
yarn migrate status

# 2. Run migrations
yarn migrate up

# 3. Try running again (should show "No pending migrations")
yarn migrate up

# 4. Check status again (should show all executed)
yarn migrate status
```

**Expected Behavior:**

1. First `migrate up` → Executes pending migrations
2. Second `migrate up` → Shows "No pending migrations"
3. Status commands show execution history with timestamps

## 📋 **Best Practices**

### ✅ **Fully Supported Operations**

- **Tables**: Create/drop tables (with existence checks)
- **Columns**: Add/remove/modify columns (type, constraints, defaults)
- **Indexes**: Add/remove/modify indexes (fields, uniqueness)
- **Foreign Keys**: Add/remove/modify foreign key constraints
- **Complex Changes**: Multi-table operations with proper dependency handling
- **Auto-generation**: Smart migration generation with comprehensive change detection
- **Safety**: All operations include existence checks and proper rollbacks

### ⚠️ **Careful Operations**

- **Column renames**: Detected as remove + add (may cause data loss - backup first)
- **Type changes**: Ensure data compatibility before modification
- **Adding NOT NULL**: Add default value first, then modify constraint
- **Complex changes**: Test thoroughly in development environment first
- **Production changes**: Always coordinate with team and have rollback plan

### ❌ **Dangerous Operations**

- **Manual migrations table**: Never modify the `migrations` tracking table manually
- **Force operations**: Avoid using `force: true` in production environments
- **Skip rollbacks**: Always provide proper `down` migration logic
- **Direct SQL**: Avoid direct database modifications outside migration system

### 🎯 **Development Best Practices**

- ✅ **Always check status** before manual migration runs
- ✅ **Use auto-generation** (`--auto` flag) for safety
- ✅ **Test migrations locally** before pushing
- ✅ **Review generated migrations** for correctness
- ✅ **Use descriptive migration names**
- ✅ **Commit model changes and migrations together**

### 🚀 **Deployment Best Practices**

- ✅ **Test in DEV first** (happens automatically on PR merge)
- ✅ **Validate in STAGING** before production
- ✅ **Coordinate production deployments** with team
- ✅ **Monitor migration logs** during deployment
- ✅ **Have rollback plan** for critical changes

## 📝 **Enhanced Migration Example**

### **Auto-Generated Migration (Recommended)**

```typescript
import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Check and create users table (safety feature)
  const usersExists = await queryInterface.tableExists("users");
  if (!usersExists) {
    await queryInterface.createTable("users", {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      preferences: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    });
  }

  // Add column only if it doesn't exist
  const tableDescription = await queryInterface.describeTable("users");
  if (!tableDescription.preferences) {
    await queryInterface.addColumn("users", "preferences", {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
    });
  }
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Only drop table if it exists
  const usersExists = await queryInterface.tableExists("users");
  if (usersExists) {
    await queryInterface.dropTable("users");
  }
}
```

## 🔍 **Monitoring & Troubleshooting**

### **Container Startup Logs**

- Navigate to **Cloud Run** service in GCP Console
- Check **Logs** tab for container startup output
- Look for migration-specific logs:
  - 🗄️ Running database migrations...
  - ✅ Database migrations completed successfully
  - ❌ Database migrations failed

### **Common Issues & Solutions**

#### **Migration Fails During Container Startup**

```bash
# Check container logs in Cloud Run Console
# Look for migration error details in startup logs

# For local testing (migrations won't work but you can debug compilation):
yarn migrate status  # Test script compilation
yarn run build      # Ensure scripts compile correctly

# Fix migration issues:
# 1. Update migration file
# 2. Test compilation locally
# 3. Deploy new container version
```

#### **Container Startup Issues**

- Check Cloud Run service logs for detailed error messages
- Verify environment variables are properly set
- Ensure database connection string is correct for the environment
- Check that migration files are properly compiled in the container

#### **Migration File Issues**

```bash
# Verify TypeScript compilation
npx tsc --noEmit migrations/filename.ts

# Check for syntax errors in migration files
# Ensure proper up/down function definitions
# Verify all required imports are present
```

#### **Migration Tracking Issues**

```sql
-- View current tracking state
SELECT * FROM migrations ORDER BY executed_at;

-- If needed, manually add missing migration record
INSERT INTO migrations (name) VALUES ('20250127T154032-add-user-avatars.ts');

-- Or remove incorrect record (use with extreme caution)
DELETE FROM migrations WHERE name = '20250127T154032-add-user-avatars.ts';
```

## 📞 **Team Communication**

### **When Migrations Auto-Deploy to DEV**

- ✅ **Success**: No action needed, team gets notified via GitHub
- ❌ **Failure**: GitHub creates error notifications, check logs and fix

### **Before Staging/Production Deployment**

- 📋 **Coordinate with team** about potential downtime
- 🗄️ **Verify database backups** are recent and accessible
- 🧪 **Confirm migrations tested** in DEV environment
- 📝 **Document any special considerations** for the deployment

## 🆘 **Emergency Procedures**

### **If Migration Fails in Production**

1. **Don't Panic** - Application remains on previous version
2. **Check Logs** - Review GitHub Actions logs for specific error
3. **Assess Impact** - Determine if rollback is needed
4. **Coordinate Response** - Notify team and plan recovery
5. **Fix Forward** - Create hotfix migration to resolve issue

### **If Deployment Must Be Rolled Back**

```bash
# If migration succeeded but application has issues:
# 1. Deploy previous application version (manual dispatch)
# 2. Rollback database migration if needed:
yarn migrate down [problematic-migration-name]
```

### **If Migrations Table is Lost**

```sql
-- Recreate table (system will auto-create on next run)
-- But you'll need to manually mark executed migrations

-- After recreating, mark all previously-run migrations
INSERT INTO migrations (name) VALUES
  ('20250720T064232-initial-schema.ts'),
  ('20250125T143021-add-user-preferences.ts');
```

## 📈 **Benefits of the Docker-Based System**

- 🚀 **Simpler Architecture**: No complex CI/CD database connections needed
- 🛡️ **Zero Data Loss Risk**: Comprehensive safety checks during container startup
- 📊 **Full Tracking**: Complete audit trail of all migrations in container logs
- 👥 **Team Efficiency**: Developers focus on features, infrastructure is automatic
- 🔄 **Automatic Retry**: Container restart = migration retry capability
- 🌍 **Environment Consistency**: Same runtime environment for migrations and application
- 📋 **Better Reliability**: No network/proxy issues between CI/CD and database
- ⚡ **Faster CI/CD**: Just build and deploy containers - no migration orchestration

## 🎯 **Migration Workflow Summary**

```bash
# Developer Workflow:
1. Update Sequelize models
2. yarn create-migration describe-change --auto
3. Review generated migration
4. Test compilation: yarn run build
5. Commit and create PR
6. Merge PR → Automatic container build & deployment!
7. Migrations run automatically during container startup

# DevOps Workflow:
1. DEV: Automatic container deployment on PR merge
2. STAGING: Manual dispatch for container deployment
3. PRODUCTION: Manual dispatch with explicit confirmation
4. All environments: Migrations run during container startup automatically
```
