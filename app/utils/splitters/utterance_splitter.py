import re
from typing import override
import uuid

from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.document_schemas import Chunk
from app.utils.token_util import TokenUtil


logger = setup_logger(__name__)


class UtteranceSplitter:
    def __init__(
        self,
        token_util: TokenUtil,
    ):
        self._max_chunk_size = config.splitter_max_chunk_size
        self._min_chunk_size = config.splitter_min_chunk_size
        self._overlap = config.splitter_chunk_overlap
        self._token_util = token_util

    @override
    def split(self, chunks: list[Chunk]) -> list[Chunk]:
        if len(chunks) == 0:
            logger.warning("Successfully split 0 utterances to 0 utterances.")
            return chunks
        # If 2 utterances have the same speaker, combine them
        old_length = len(chunks)
        chunks = self._combine_same_speacker_utterances(chunks)

        result_chunks: list[Chunk] = []

        for i, utterance in enumerate(chunks):
            tokens: int = getattr(utterance.metadata, "tokens", 0)
            # Case 1: Utterance is the right length
            if self._min_chunk_size <= tokens <= self._max_chunk_size:
                result_chunks.append(utterance)

            # Case 2: Utterance is too long, split into sentences
            elif tokens > self._max_chunk_size:
                sub_utterances = self._split_long_utterance(utterance)

                result_chunks.extend(sub_utterances)

            else:
                # Case 3: Utterance is too short, add contextual window
                contextual_chunk = self._add_contextual_window(chunks, i)

                result_chunks.append(contextual_chunk)

        logger.info(
            f"Successfully split {old_length} utterances to {len(result_chunks)} utterances in {chunks[0].metadata.file_name}."
        )

        return result_chunks

    def _combine_same_speacker_utterances(self, utterances: list[Chunk]) -> list[Chunk]:
        if not utterances:
            return []

        combined_utterances = []
        current_utterance = None

        for utterance in utterances:
            speaker = getattr(utterance.metadata, "speaker", None)
            if current_utterance is None:
                # First utterance, start a new combined utterance
                current_utterance = utterance
            elif (
                speaker is not None
                and getattr(current_utterance.metadata, "speaker", None) == speaker
            ):
                # Same speaker, combine the text
                combined_text = f"{current_utterance.text} {utterance.text}"
                # Update metadata
                new_metadata = current_utterance.metadata
                if hasattr(new_metadata, "end_sec") and hasattr(
                    utterance.metadata, "end_sec"
                ):
                    setattr(
                        new_metadata, "end_sec", getattr(utterance.metadata, "end_sec")
                    )
                current_utterance = Chunk(
                    id=current_utterance.id, text=combined_text, metadata=new_metadata
                )
            else:
                # Different speaker, save the current combined utterance and start a new one
                combined_utterances.append(current_utterance)
                current_utterance = utterance

        # Don't forget to add the last combined utterance
        if current_utterance is not None:
            combined_utterances.append(current_utterance)

        return combined_utterances

    def _split_long_utterance(self, utterance: Chunk) -> list[Chunk]:
        """
        Split a long utterance into smaller chunks using sentence boundaries,
        implemented using regex instead of nltk.
        """
        # Tách câu bằng regex: ngắt ở các dấu . ! ? theo sau là khoảng trắng
        sentences = re.split(r"(?<=[.!?])\s+", utterance.text.strip())
        sub_utterances: list[Chunk] = []

        current_text = ""
        overlap_sentences: list[str] = []  # Store sentences for overlap

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            test_text = current_text + " " + sentence if current_text else sentence
            tokens = self._token_util.count_tokens(test_text)

            if tokens > self._max_chunk_size and current_text:
                # Add current chunk
                new_text = current_text.strip()
                new_metadata = utterance.metadata.model_copy(deep=True)
                setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))
                sub_utterances.append(
                    Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
                )

                # Calculate overlap: keep some sentences from current chunk
                if self._overlap > 0:
                    # Split current_text into sentences for overlap
                    current_sentences = re.split(r"(?<=[.!?])\s+", current_text.strip())
                    overlap_tokens = 0
                    overlap_sentences = []

                    # Add sentences from the end until we reach overlap limit
                    for sent in reversed(current_sentences):
                        sent = sent.strip()
                        if not sent:
                            continue
                        sent_tokens = self._token_util.count_tokens(sent)
                        if overlap_tokens + sent_tokens <= self._overlap:
                            overlap_sentences.insert(0, sent)
                            overlap_tokens += sent_tokens
                        else:
                            break

                    # Start new chunk with overlap sentences
                    current_text = (
                        " ".join(overlap_sentences) + " " + sentence
                        if overlap_sentences
                        else sentence
                    )
                else:
                    current_text = sentence
            else:
                current_text = test_text

        # Add the last chunk if there is any
        if current_text:
            # Add current chunk
            new_text = current_text.strip()
            new_metadata = utterance.metadata.model_copy(deep=True)
            setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))
            sub_utterances.append(
                Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
            )

        return sub_utterances

    def _add_contextual_window(
        self,
        utterances: list[Chunk],
        target_index: int,
        window_size: int = config.utterance_contextual_window_size,
        min_tokens: int = config.utterance_contextual_min_tokens,
    ) -> Chunk:
        """Add contextual window around a short utterance."""
        # Get
        start_idx = max(0, target_index - window_size)
        end_idx = min(len(utterances), target_index + window_size + 1)

        context_parts = []
        total_tokens = 0
        main_chunk_start_char = 0
        main_chunk_end_char = 0

        for i in range(start_idx, end_idx):
            context_part = f"Speaker {getattr(utterances[i].metadata, 'speaker', '')}: {utterances[i].text}"

            if i == target_index:
                # Calculate start position of the main chunk
                main_chunk_start_char = len("\n".join(context_parts))
                if context_parts:  # Add newline length if there are previous parts
                    main_chunk_start_char += 1
                main_chunk_end_char = main_chunk_start_char + len(context_part)

            context_parts.append(context_part)
            total_tokens += self._token_util.count_tokens(context_part)
            if i >= target_index and total_tokens > min_tokens:
                break

        new_text = "\n".join(context_parts)
        current_utterance = utterances[target_index]
        new_metadata = current_utterance.metadata.model_copy(deep=True)
        setattr(new_metadata, "tokens", total_tokens)
        setattr(new_metadata, "main_chunk_start_char", main_chunk_start_char)
        setattr(new_metadata, "main_chunk_end_char", main_chunk_end_char)

        return Chunk(id=current_utterance.id, text=new_text, metadata=new_metadata)
