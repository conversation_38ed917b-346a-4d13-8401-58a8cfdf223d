import re
import uuid

from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.document_schemas import Chunk
from app.utils.token_util import TokenUtil


logger = setup_logger(__name__)


class ChunkSplitter:
    def __init__(
        self,
        token_util: TokenUtil,
    ):
        self._max_chunk_size = config.splitter_max_chunk_size
        self._min_chunk_size = config.splitter_min_chunk_size
        self._overlap = config.splitter_chunk_overlap
        self._token_util = token_util

    def split(self, chunks: list[Chunk]) -> list[Chunk]:
        """Split chunks based on token size constraints."""
        if len(chunks) == 0:
            logger.warning("Successfully split 0 chunks to 0 chunks.")
            return chunks

        old_length = len(chunks)

        result_chunks: list[Chunk] = []
        i = 0

        while i < len(chunks):
            paragraph = chunks[i]
            tokens: int = getattr(paragraph.metadata, "tokens", 0)

            # Case 1: Paragraph is the right length
            if self._min_chunk_size <= tokens <= self._max_chunk_size:
                result_chunks.append(paragraph)
                i += 1

            # Case 2: Paragraph is too long, split into smaller chunks
            elif tokens > self._max_chunk_size:
                sub_chunks = self._split_long_paragraph(paragraph)
                result_chunks.extend(sub_chunks)
                i += 1

            else:
                # Case 3: Paragraph is too short, merge with following paragraphs
                merged_chunk, paragraphs_consumed = self._merge_short_paragraph(
                    chunks, i
                )
                result_chunks.append(merged_chunk)
                i += paragraphs_consumed  # Skip the consumed paragraphs

        logger.info(
            f"Successfully split {old_length} chunks to {len(result_chunks)} chunks in {chunks[0].metadata.file_name}."
        )
        return result_chunks

    def _split_long_paragraph(self, paragraph: Chunk) -> list[Chunk]:
        """Split a long paragraph into smaller chunks using sentence boundaries."""
        # Split paragraph into sentences using regex
        sentences = re.split(r"(?<=[.!?])\s+", paragraph.text.strip())
        sub_chunks: list[Chunk] = []

        current_text = ""
        overlap_sentences: list[str] = []

        # Get original paragraph start position for calculating sub-chunk positions
        original_start_char = getattr(paragraph.metadata, "start_char", 0)
        paragraph_text = paragraph.text.strip()
        current_char_offset = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            test_text = current_text + " " + sentence if current_text else sentence
            tokens = self._token_util.count_tokens(test_text)

            if tokens > self._max_chunk_size and current_text:
                # Calculate character positions for current chunk
                new_text = current_text.strip()
                chunk_start_char = original_start_char + paragraph_text.find(
                    new_text, current_char_offset
                )
                chunk_end_char = chunk_start_char + len(new_text)

                new_metadata = paragraph.metadata.model_copy(deep=True)
                setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))
                setattr(new_metadata, "start_char", chunk_start_char)
                setattr(new_metadata, "end_char", chunk_end_char)
                sub_chunks.append(
                    Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
                )

                # Update char offset for next chunk
                current_char_offset = paragraph_text.find(
                    new_text, current_char_offset
                ) + len(new_text)

                # Calculate overlap: keep some sentences from current chunk
                if self._overlap > 0:
                    current_sentences = re.split(r"(?<=[.!?])\s+", current_text.strip())
                    overlap_tokens = 0
                    overlap_sentences = []

                    # Add sentences from the end until we reach overlap limit
                    for sent in reversed(current_sentences):
                        sent = sent.strip()
                        if not sent:
                            continue
                        sent_tokens = self._token_util.count_tokens(sent)
                        if overlap_tokens + sent_tokens <= self._overlap:
                            overlap_sentences.insert(0, sent)
                            overlap_tokens += sent_tokens
                        else:
                            break

                    # Start new chunk with overlap sentences
                    current_text = (
                        " ".join(overlap_sentences) + " " + sentence
                        if overlap_sentences
                        else sentence
                    )

                    # Adjust char offset for overlap
                    if overlap_sentences:
                        overlap_text = " ".join(overlap_sentences)
                        overlap_start = paragraph_text.rfind(
                            overlap_text, 0, current_char_offset
                        )
                        if overlap_start != -1:
                            current_char_offset = overlap_start
                else:
                    current_text = sentence
            else:
                current_text = test_text

        # Add the last chunk if there's remaining text
        if current_text:
            new_text = current_text.strip()
            chunk_start_char = original_start_char + paragraph_text.find(
                new_text, current_char_offset
            )
            chunk_end_char = chunk_start_char + len(new_text)

            new_metadata = paragraph.metadata.model_copy(deep=True)
            setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))
            setattr(new_metadata, "start_char", chunk_start_char)
            setattr(new_metadata, "end_char", chunk_end_char)
            sub_chunks.append(
                Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
            )

        return sub_chunks

    def _merge_short_paragraph(
        self, chunks: list[Chunk], start_index: int
    ) -> tuple[Chunk, int]:
        """Merge short paragraph with following paragraphs."""
        current_chunk = chunks[start_index]
        merged_text = current_chunk.text
        current_tokens = getattr(current_chunk.metadata, "tokens", 0)
        paragraphs_consumed = 1  # Start with 1 for the current paragraph

        # Get character positions for merged chunk
        start_char = getattr(current_chunk.metadata, "start_char", 0)
        end_char = getattr(current_chunk.metadata, "end_char", len(current_chunk.text))

        # Try to merge with following paragraphs
        for i in range(start_index + 1, len(chunks)):
            next_chunk = chunks[i]

            # Check if adding next paragraph would exceed max_chunk_size
            test_text = merged_text + "\n\n" + next_chunk.text
            test_tokens = self._token_util.count_tokens(test_text)

            if test_tokens <= self._max_chunk_size:
                merged_text = test_text
                current_tokens = test_tokens
                paragraphs_consumed += 1

                # Update end position to include the next chunk
                end_char = getattr(
                    next_chunk.metadata, "end_char", end_char + len(next_chunk.text)
                )

                # If we've reached min_chunk_size, we can stop here
                if current_tokens >= self._min_chunk_size:
                    break
            else:
                # Can't add more without exceeding max_chunk_size
                break

        # Create merged chunk
        new_metadata = current_chunk.metadata.model_copy(deep=True)
        setattr(new_metadata, "tokens", current_tokens)
        setattr(new_metadata, "start_char", start_char)
        setattr(new_metadata, "end_char", end_char)

        merged_chunk = Chunk(
            id=current_chunk.id, text=merged_text, metadata=new_metadata
        )

        return merged_chunk, paragraphs_consumed
