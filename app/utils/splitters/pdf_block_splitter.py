import re
import uuid
from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.document_schemas import Chunk, Metadata
from app.schemas.extractor_schema import PdfExtractedBlock
from app.utils.token_util import TokenUtil


logger = setup_logger(__name__)


class PdfBlockSplitter:
    def __init__(
        self,
        token_util: TokenUtil,
    ):
        self._max_chunk_size = config.splitter_max_chunk_size
        self._min_chunk_size = config.splitter_min_chunk_size
        self._overlap = config.splitter_chunk_overlap
        self._token_util = token_util

    def split(self, blocks: list[PdfExtractedBlock], metadata: Metadata) -> list[Chunk]:
        """Extract chunks from PDF blocks with intelligent splitting and merging.

        Args:
            blocks: List of PDF blocks extracted from the document
            metadata: Base metadata for all chunks

        Returns:
            List of chunks extracted from the blocks
        """
        if len(blocks) == 0:
            logger.warning("Successfully split 0 blocks to 0 chunks.")
            return []

        old_length = len(blocks)

        chunks: list[Chunk] = []
        i = 0

        while i < len(blocks):
            block = blocks[i]

            # Get the text content
            text = str(block["content"]).strip()
            if not text:
                i += 1
                continue

            # Count tokens for the block
            tokens = self._token_util.count_tokens(text)

            # Create base metadata with page number
            block_metadata = metadata.model_copy(deep=True)
            setattr(block_metadata, "page_number", block["page_number"])
            setattr(block_metadata, "tokens", tokens)

            # Case 1: Block is the right length
            if self._min_chunk_size <= tokens <= self._max_chunk_size:
                chunks.append(
                    Chunk(
                        id=str(uuid.uuid4()),
                        text=text,
                        metadata=block_metadata,
                    )
                )
                i += 1

            # Case 2: Block is too long, split only if it's text type
            elif tokens > self._max_chunk_size:
                if block["type"] == "text":
                    # Split text blocks that are too long
                    sub_chunks = self._split_long_block(block, metadata)
                    chunks.extend(sub_chunks)
                else:
                    # For table/image blocks, keep as single chunk even if oversized
                    logger.warning(
                        f"Block of type '{block['type']}' exceeds max size but cannot be split. Keeping as single chunk."
                    )
                    chunks.append(
                        Chunk(
                            id=str(uuid.uuid4()),
                            text=text,
                            metadata=block_metadata,
                        )
                    )
                i += 1

            else:
                # Case 3: Block is too short, merge with following blocks
                merged_chunk, blocks_consumed = self._merge_short_block(
                    blocks, i, metadata
                )
                chunks.append(merged_chunk)
                i += blocks_consumed  # Skip the consumed blocks

        logger.info(
            f"Successfully split {old_length} blocks to {len(chunks)} chunks in {metadata.file_name}."
        )
        return chunks

    def _split_long_block(
        self, block: PdfExtractedBlock, metadata: Metadata
    ) -> list[Chunk]:
        """Split a long text block into smaller chunks using sentence boundaries."""
        text = str(block["content"]).strip()

        # Split block into sentences using regex
        sentences = re.split(r"(?<=[.!?])\s+", text)
        sub_chunks: list[Chunk] = []

        current_text = ""
        overlap_sentences: list[str] = []

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            test_text = current_text + " " + sentence if current_text else sentence
            tokens = self._token_util.count_tokens(test_text)

            if tokens > self._max_chunk_size and current_text:
                # Create chunk from current text
                new_text = current_text.strip()
                new_metadata = metadata.model_copy(deep=True)
                setattr(new_metadata, "page_number", block["page_number"])
                setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))

                sub_chunks.append(
                    Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
                )

                # Calculate overlap: keep some sentences from current chunk
                if self._overlap > 0:
                    current_sentences = re.split(r"(?<=[.!?])\s+", current_text.strip())
                    overlap_tokens = 0
                    overlap_sentences = []

                    # Add sentences from the end until we reach overlap limit
                    for sent in reversed(current_sentences):
                        sent = sent.strip()
                        if not sent:
                            continue
                        sent_tokens = self._token_util.count_tokens(sent)
                        if overlap_tokens + sent_tokens <= self._overlap:
                            overlap_sentences.insert(0, sent)
                            overlap_tokens += sent_tokens
                        else:
                            break

                    # Start new chunk with overlap sentences
                    current_text = (
                        " ".join(overlap_sentences) + " " + sentence
                        if overlap_sentences
                        else sentence
                    )
                else:
                    current_text = sentence
            else:
                current_text = test_text

        # Add the last chunk if there's remaining text
        if current_text:
            new_text = current_text.strip()
            new_metadata = metadata.model_copy(deep=True)
            setattr(new_metadata, "page_number", block["page_number"])
            setattr(new_metadata, "tokens", self._token_util.count_tokens(new_text))

            sub_chunks.append(
                Chunk(id=str(uuid.uuid4()), text=new_text, metadata=new_metadata)
            )

        return sub_chunks

    def _merge_short_block(
        self, blocks: list[PdfExtractedBlock], start_index: int, metadata: Metadata
    ) -> tuple[Chunk, int]:
        """Merge short block with following blocks."""
        current_block = blocks[start_index]
        merged_text = str(current_block["content"]).strip()
        current_tokens = self._token_util.count_tokens(merged_text)
        blocks_consumed = 1  # Start with 1 for the current block

        # Use page number from the first block
        page_number = current_block["page_number"]

        # Try to merge with following blocks
        for i in range(start_index + 1, len(blocks)):
            next_block = blocks[i]
            next_text = str(next_block["content"]).strip()

            if not next_text:
                blocks_consumed += 1
                continue

            # Check if adding next block would exceed max_chunk_size
            test_text = merged_text + "\n\n" + next_text
            test_tokens = self._token_util.count_tokens(test_text)

            if test_tokens <= self._max_chunk_size:
                merged_text = test_text
                current_tokens = test_tokens
                blocks_consumed += 1

                # If we've reached min_chunk_size, we can stop here
                if current_tokens >= self._min_chunk_size:
                    break
            else:
                # Can't add more without exceeding max_chunk_size
                break

        # Create merged chunk
        new_metadata = metadata.model_copy(deep=True)
        setattr(new_metadata, "page_number", page_number)
        setattr(new_metadata, "tokens", current_tokens)

        merged_chunk = Chunk(
            id=str(uuid.uuid4()), text=merged_text, metadata=new_metadata
        )

        return merged_chunk, blocks_consumed
