def parse_time_to_seconds(time_str: str) -> float:
    """Convert time format (HH:MM:SS or MM:SS) to seconds."""
    time_str = time_str.strip()

    # Handle HH:MM:SS format
    if time_str.count(":") == 2:
        hours, minutes, seconds = map(float, time_str.split(":"))
        return hours * 3600 + minutes * 60 + seconds
    # Handle MM:SS format
    elif time_str.count(":") == 1:
        minutes, seconds = map(float, time_str.split(":"))
        return minutes * 60 + seconds
    else:
        # Assume it's already in seconds
        return float(time_str)