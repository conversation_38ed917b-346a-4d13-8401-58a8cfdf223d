import re
import uuid

from app.core.logger import setup_logger
from app.schemas.document_schemas import Chun<PERSON>, Metadata
from app.utils.token_util import TokenUtil


logger = setup_logger(__name__)


class ChunkExtractor:
    def __init__(
        self,
        token_util: TokenUtil,
    ):
        self._token_util = token_util

    def extract(self, text: str, metadata: Metadata) -> list[Chunk]:
        """Extract paragraphs from text based on newline characters."""
        if not text.strip():
            return []

        original_text = text.strip()

        # Split text into paragraphs based on multiple newlines
        # Split on patterns like \n\n, \n\n\n, etc. but keep single \n as part of paragraph
        paragraphs = re.split(r"\n{2,}", original_text)

        chunks: list[Chunk] = []
        current_pos = 0

        for paragraph in paragraphs:
            paragraph_stripped = paragraph.strip()
            if not paragraph_stripped:
                # Find the next non-empty paragraph position
                current_pos = original_text.find(paragraph_stripped, current_pos) + len(
                    paragraph
                )
                continue

            # Find the actual start position of this paragraph in the original text
            start_char = original_text.find(paragraph_stripped, current_pos)
            end_char = start_char + len(paragraph_stripped)

            tokens = self._token_util.count_tokens(paragraph_stripped)
            additional_metadata = {
                "tokens": tokens,
                "start_char": start_char,
                "end_char": end_char,
            }

            new_metadata = Metadata(**{**metadata.model_dump(), **additional_metadata})

            chunks.append(
                Chunk(
                    id=str(uuid.uuid4()),
                    text=paragraph_stripped,
                    metadata=new_metadata,
                )
            )

            # Update current position for next search
            current_pos = end_char

        logger.info(f"Extract {len(chunks)} paragraphs successfully.")
        return chunks
