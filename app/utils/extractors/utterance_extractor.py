import re
import uuid

from fastapi import HTTPException
from app.adapters.llm.llm_adapter import LLMAdapter
from app.core.logger import setup_logger
from app.schemas.document_schemas import Chunk, Metadata
from app.schemas.extractor_schema import ExtractedUtterances
from app.utils.time_util import parse_time_to_seconds
from app.utils.token_util import TokenUtil


logger = setup_logger(__name__)


class UtteranceExtractor:
    def __init__(self, token_util: TokenUtil, llm_adapter: LLMAdapter):
        self._token_util = token_util
        self._llm_adapter = llm_adapter

    async def extract(self, text: str, metadata: Metadata) -> list[Chunk]:
        """Extract utterances from transcript text using multiple patterns."""
        if not text.strip():
            return []

        trace_info = f"(project_id: {metadata.project_id}, user_id: {metadata.user_id}, file_id: {metadata.file_id}, file_name: {metadata.file_name})"

        patterns_and_field_names = [
            # Case 1:
            # [Speaker 0] (00:01 - 00:02):
            # Is it better now?
            (
                r"\[Speaker (\d+)\]\s*\((\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})\):\s*\n(.*?)(?=\n\[Speaker|\Z)",
                ("speaker", "start_sec", "end_sec", "text"),
            ),
            # Case 2:
            # Speaker 2 00:01:34 Yeah. That's correct. Yes.
            (
                r"Speaker (\d+)\s+(\d{2}:\d{2}:\d{2})\s+(.*?)(?=\nSpeaker|\Z)",
                ("speaker", "start_sec", "text"),
            ),
            # Case 3:
            # [00:00:35] Speaker 0: Okay, so, um.
            (
                r"\[(\d{2}:\d{2}:\d{2})\]\s*Speaker\s+(\d+):\s*(.*?)(?=\n\[\d{2}:\d{2}:\d{2}\]\s*Speaker|\Z)",
                ("start_sec", "speaker", "text"),
            ),
        ]

        chunks: list[Chunk] = []
        for pattern, field_names in patterns_and_field_names:
            extracted_results = self._extract_with_regex(text, pattern, field_names)
            if extracted_results:
                chunks.extend(self._convert_to_chunks(extracted_results, metadata))
                logger.info(
                    f"Extract {len(extracted_results)} utterances successfully from {metadata.file_name} using predefined regex... {trace_info}"
                )
                break

        if not chunks:
            logger.info(
                f"No utterances found in {metadata.file_name} using predefined regex, using LLM to extract... {trace_info}"
            )
            extracted_results = await self._extract_with_llm(text)
            chunks.extend(self._convert_to_chunks(extracted_results, metadata))
            logger.info(
                f"Extract {len(extracted_results)} utterances successfully from {metadata.file_name} using LLM... {trace_info}"
            )

        logger.info(
            f"Extract {len(chunks)} utterances successfully from {metadata.file_name}."
        )

        return chunks

    def _extract_with_regex(
        self, text: str, regex: str, field_names: tuple[str, ...]
    ) -> list[dict[str, str]]:
        results: list[dict[str, str]] = []
        matches = re.finditer(regex, text, re.DOTALL)

        for match in matches:
            groups = match.groups()

            if len(groups) != len(field_names):
                raise HTTPException(
                    status_code=400,
                    detail=f"Number of captured groups ({len(groups)}) does not match number of field names ({len(field_names)})",
                )

            result = {
                field: value.strip() if isinstance(value, str) else value
                for field, value in zip(field_names, groups)
            }
            results.append(result)

        return results

    async def _extract_with_llm(self, text: str) -> list[dict[str, str]]:
        prompt = f"""Extract utterances from the following text. Each utterance will have:
        - speaker: the speaker of the utterance, e.g. 0, 1, 2, etc.
        - start_sec: the start time of the utterance, in hh:mm:ss format, e.g. 00:00:00
        - end_sec: the end time of the utterance, if available, in hh:mm:ss format, e.g. 00:00:00
        - text: the text of the utterance

        Example 1:
        [Speaker 0] (00:01 - 00:02): # Is it better now? => [{{"speaker": "0", "start_sec": "00:00:01", "end_sec": "00:00:02", "text": "Is it better now?"}}]

        Example 2:
        [00:00:35] Speaker 0: Okay, so, um. => [{{"speaker": "0", "start_sec": "00:00:35", "text": "Okay, so, um."}}]

        If the utterance is not available, return an empty list.

        Text:
        {text}
        """

        response = await self._llm_adapter.generate_schema(
            [{"type": "text", "text": prompt}],
            schema=ExtractedUtterances,
        )
        response_dict = response.model_dump(exclude_none=True)

        return response_dict.get("data", [])

    def _convert_to_chunks(
        self, extracted_results: list[dict[str, str]], metadata: Metadata
    ) -> list[Chunk]:
        chunks: list[Chunk] = []
        for result in extracted_results:
            additional_metadata = {k: v for k, v in result.items() if k != "text"}
            additional_metadata["tokens"] = self._token_util.count_tokens(
                result.get("text", "")
            )
            if additional_metadata.get("start_sec"):
                additional_metadata["start_sec"] = parse_time_to_seconds(
                    additional_metadata["start_sec"]
                )
            if additional_metadata.get("end_sec"):
                additional_metadata["end_sec"] = parse_time_to_seconds(
                    additional_metadata["end_sec"]
                )
            new_metadata = Metadata(**{**metadata.model_dump(), **additional_metadata})
            chunks.append(
                Chunk(
                    id=str(uuid.uuid4()),
                    text=result.get("text", ""),
                    metadata=new_metadata,
                )
            )
        return chunks
