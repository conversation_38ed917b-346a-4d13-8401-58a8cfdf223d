from app.adapters.llm.llm_adapter import LLMAdapter
from app.schemas.llm_schemas import ContentDict


class BaseBlockExtractor:
    def __init__(self, llm_adapter: LLMAdapter):
        self._llm_adapter = llm_adapter

    async def _generate_image_summary(self, image_bytes: bytes) -> str:
        """
        Generate image summary using LLM adapter

        Args:
            image_bytes: Binary image data

        Returns:
            ImageSummary object with description
        """
        contents: list[ContentDict] = [
            {
                "type": "text",
                "text": """
Please carefully examine the image and provide a comprehensive description of everything visible in it.
Include the following details in your response:
- Main subjects or objects – What are the key things shown in the image? (e.g., people, animals, buildings, landscapes, objects)
- Scene context – Where does the image appear to be taken? Describe the environment (e.g., indoor, outdoor, natural, urban, professional setting).
- Actions or interactions – What are the subjects doing, if anything? Are there any notable gestures, expressions, or events happening?
- Visual appearance – Include notable details such as colors, lighting, composition, positioning, or perspective.
- Mood or atmosphere – What kind of feeling does the image evoke (e.g., peaceful, energetic, formal, casual)?
- Any visible text, symbols, or signage – Please transcribe and mention any readable text.
- Additional interpretations (optional) – If relevant, feel free to offer a possible interpretation or story behind the image.
Structure your description in full sentences and aim for clarity and depth. Do not include any other text in your response. Don't anwser more than 200 words.""",
            },
            {
                "type": "image",
                "data": image_bytes,
                "mime_type": "image/jpeg",
            },
        ]
        response = await self._llm_adapter.generate_content(contents=contents)
        return response
