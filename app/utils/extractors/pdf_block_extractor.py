import io
from typing import List, Tuple
import fitz  # PyMuPDF
import pdfplumber
from pdfplumber.page import Page as PdfPlumberPage

from app.core.logger import setup_logger
from app.schemas.extractor_schema import PdfExtractedBlock

logger = setup_logger(__name__)


class PdfBlockExtractor:
    """
    Simple PDF block extractor that processes pages sequentially and extracts
    tables and text in top-to-bottom order.
    """

    async def extract(self, file_content: bytes) -> list[PdfExtractedBlock]:
        """
        Extract content from PDF sequentially (tables and text only).

        Args:
            file_content: Binary PDF data

        Returns:
            List of extracted blocks (tables and text) in document order
        """
        doc_fitz = None
        pdf_bytes_io = None
        pdf_context = None

        try:
            # Open PDF documents with proper resource management
            doc_fitz = fitz.open(stream=file_content)
            pdf_bytes_io = io.BytesIO(file_content)
            pdf_context = pdfplumber.open(pdf_bytes_io)

            all_elements: list[PdfExtractedBlock] = []

            # Process pages sequentially
            for page_num in range(len(pdf_context.pages)):
                page = pdf_context.pages[page_num]
                fitz_page = doc_fitz[page_num]

                # Extract tables first
                tables = self._extract_tables(page)

                # Then extract text blocks, filtering those overlapping with tables
                text_blocks = self._extract_text_blocks(fitz_page, tables)

                # Combine and sort by y position within the page
                page_elements = []
                page_elements.extend(tables)
                page_elements.extend(text_blocks)
                page_elements.sort(key=lambda x: x["y_pos"])

                all_elements.extend(page_elements)

            # Sort by page number first, then by y position
            sorted_elements = sorted(
                all_elements,
                key=lambda x: (x["page_number"], x["y_pos"]),  # type: ignore[index]
            )

            return sorted_elements

        finally:
            # Ensure all resources are properly closed
            if pdf_context is not None:
                pdf_context.close()
            if pdf_bytes_io is not None:
                pdf_bytes_io.close()
            if doc_fitz is not None:
                doc_fitz.close()

    def _extract_text_blocks(
        self, fitz_page: fitz.Page, tables: List[PdfExtractedBlock]
    ) -> List[PdfExtractedBlock]:
        """
        Extract text blocks from a page, filtering out text that overlaps with tables.

        Args:
            fitz_page: PyMuPDF page object
            tables: List of table elements with bbox information

        Returns:
            List of text elements with y_pos, content, and type
        """
        elements: List[PdfExtractedBlock] = []
        text_blocks = fitz_page.get_text("dict")  # type: ignore

        for block in text_blocks["blocks"]:
            if "lines" in block:  # Text block
                block_bbox = block["bbox"]  # (x0, y0, x1, y1)
                y_pos = block_bbox[1]  # y coordinate

                # Skip text blocks that are inside tables
                if self._is_text_in_table(block_bbox, tables):
                    continue

                text_content = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        text_content += span["text"]
                    text_content += "\n"

                if text_content.strip():
                    elements.append(
                        {
                            "content": text_content.strip(),
                            "type": "text",
                            "y_pos": y_pos,
                            "page_number": fitz_page.number,
                            "bbox": block_bbox,
                        }
                    )

        return elements

    def _is_text_in_table(
        self,
        text_bbox: Tuple[float, float, float, float],
        tables: List[PdfExtractedBlock],
    ) -> bool:
        """
        Check if a text block overlaps with any table

        Args:
            text_bbox: Text block bounding box (x0, y0, x1, y1)
            tables: List of table elements with bbox information

        Returns:
            bool: True if text block overlaps with any table
        """
        text_x0, text_y0, text_x1, text_y1 = text_bbox

        for table in tables:
            if table["type"] != "table" or "bbox" not in table:
                continue

            table_bbox = table["bbox"]
            if table_bbox is None:
                continue

            table_x0, table_y0, table_x1, table_y1 = table_bbox

            # Check if text block overlaps with table
            # Consider overlap if text block is mostly inside table area
            overlap_x = max(0, min(text_x1, table_x1) - max(text_x0, table_x0))
            overlap_y = max(0, min(text_y1, table_y1) - max(text_y0, table_y0))

            if overlap_x > 0 and overlap_y > 0:
                # Calculate overlap area
                text_area = (text_x1 - text_x0) * (text_y1 - text_y0)
                overlap_area = overlap_x * overlap_y

                # If more than 50% of text block overlaps with table, consider it inside
                if overlap_area / text_area > 0.5:
                    return True

        return False

    def _extract_tables(self, page: PdfPlumberPage) -> List[PdfExtractedBlock]:
        """
        Extract tables from a page.

        Args:
            page: pdfplumber page object

        Returns:
            List of table elements with y_pos, content, and type
        """
        elements: List[PdfExtractedBlock] = []
        tables = page.find_tables()

        for table in tables:
            bbox = table.bbox
            y_pos = bbox[1]  # top y coordinate

            # Extract table data
            table_data = table.extract()

            if not table_data:
                continue

            # Remove empty rows
            table_data = [
                row for row in table_data if any(cell for cell in row if cell)
            ]

            if not table_data:
                continue

            # Filter by number of rows and columns
            num_rows = len(table_data)
            num_cols = max(len(row) for row in table_data if row)
            if num_rows < 2 or num_cols < 2:
                continue  # Skip blocks that don't look like real tables

            # Build Markdown-formatted table (handle multiline cells and pipes)
            def sanitize_cell(value: object) -> str:
                text = str(value) if value is not None else ""
                text = text.replace("\r\n", "\n").replace("\r", "\n")
                text = text.replace("\n", "<br/>")
                text = text.replace("|", "\\|")
                return text.strip()

            sanitized_rows: list[list[str]] = [
                [sanitize_cell(cell) for cell in (row or [])] for row in table_data
            ]

            # Normalize all rows to the same number of columns
            normalized_rows: list[list[str]] = []
            for row in sanitized_rows:
                normalized = row[:num_cols] + [""] * (num_cols - len(row))
                normalized_rows.append(normalized)

            md_lines: list[str] = []
            for row in normalized_rows:
                md_lines.append("| " + " | ".join(row) + " |")

            table_content = "\n".join(md_lines).strip()

            elements.append(
                {
                    "content": table_content,
                    "type": "table",
                    "y_pos": y_pos,
                    "page_number": page.page_number,
                    "bbox": bbox,  # (x0, y0, x1, y1)
                }
            )

        return elements

    def cleanup(self):
        """No-op cleanup for compatibility."""
        return None
