import io
from docx import Document
from docx.document import Document as DocxDocument
from docx.table import Table
from docx.text.paragraph import Paragraph

from app.core.logger import setup_logger
from app.schemas.extractor_schema import WordExtractedBlock

logger = setup_logger(__name__)


class DocxBlockExtractor:
    """
    Simple DOCX block extractor that processes document elements sequentially
    and extracts text and tables in order.
    """

    async def extract(self, file_content: bytes) -> list[WordExtractedBlock]:
        """
        Extract content from DOCX sequentially (tables and text only).

        Args:
            file_content: Binary DOCX data

        Returns:
            List of extracted blocks (tables and text) in document order
        """
        file_stream = io.BytesIO(file_content)
        try:
            doc = Document(file_stream)

            # Extract all elements in document order
            elements = self._extract_all_elements(doc)
            return elements

        except Exception as e:
            logger.error(f"Failed to extract content from DOCX: {str(e)}")
            raise Exception(f"Failed to extract content from DOCX: {str(e)}")
        finally:
            file_stream.close()

    def _extract_all_elements(self, doc: DocxDocument) -> list[WordExtractedBlock]:
        """
        Extract all elements from the document in order.

        Args:
            doc: python-docx Document object

        Returns:
            list of extracted elements in document order
        """
        elements: list[WordExtractedBlock] = []
        element_position = 0  # Track position in document

        # Process each element in the document
        for element in doc.element.body:
            if element.tag.endswith("p"):  # Paragraph
                paragraph = Paragraph(element, doc)
                text_content = paragraph.text.strip()
                if text_content:
                    elements.append(
                        {
                            "content": text_content,
                            "type": "text",
                            "y_pos": element_position,
                        }
                    )

            elif element.tag.endswith("tbl"):  # Table
                table = Table(element, doc)
                table_content = self._extract_table_content(table)
                if table_content:
                    elements.append(
                        {
                            "content": table_content,
                            "type": "table",
                            "y_pos": element_position,
                        }
                    )

            element_position += 1

        return elements

    def _extract_table_content(self, table: Table) -> str:
        """
        Extract content from a table and return Markdown-formatted table text.

        - Converts newlines in cells to <br/>
        - Escapes pipe characters
        - Builds a header row and separator
        """
        try:
            table_data: list[list[str]] = []

            for row in table.rows:
                row_data: list[str] = []
                for cell in row.cells:
                    cell_text = cell.text.strip() if cell.text else ""
                    row_data.append(cell_text)
                table_data.append(row_data)

            # Filter empty rows
            table_data = [row for row in table_data if any(cell for cell in row)]

            if not table_data or len(table_data) < 2:
                return ""

            num_cols = max(len(row) for row in table_data if row)

            def sanitize_cell(value: object) -> str:
                text = str(value) if value is not None else ""
                text = text.replace("\r\n", "\n").replace("\r", "\n")
                text = text.replace("\n", "<br/>")
                text = text.replace("|", "\\|")
                return text.strip()

            # Sanitize and normalize rows
            sanitized_rows: list[list[str]] = [
                [sanitize_cell(cell) for cell in (row or [])] for row in table_data
            ]
            normalized_rows: list[list[str]] = []
            for row in sanitized_rows:
                normalized = row[:num_cols] + [""] * (num_cols - len(row))
                normalized_rows.append(normalized)

            md_lines: list[str] = []
            for row in normalized_rows:
                md_lines.append("| " + " | ".join(row) + " |")

            return "\n".join(md_lines).strip()

        except Exception as e:
            logger.error(f"Error extracting table content: {e}")
            return ""

    def cleanup(self):
        """No-op cleanup for compatibility."""
        return None
