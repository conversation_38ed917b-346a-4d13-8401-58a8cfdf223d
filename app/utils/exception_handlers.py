from fastapi import HTTP<PERSON>x<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from typing import Any, Dict

from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY

from app.schemas.base_schemas import DataResponse
from app.core.logger import setup_logger

logger = setup_logger(__name__)


async def handle_exception(_: Request, exc: Exception) -> JSONResponse:
    """
    Handle global exception and return the error response.
    """
    error_response: Dict[str, Any] = {
        "status": "error",
        "message": "Internal server error",
    }

    status_code = 500
    return JSONResponse(status_code=status_code, content=error_response)


async def handle_http_exception(_: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTP exceptions and return the error response with logging.
    """
    error_response: DataResponse[None] = DataResponse[None](
        success=False, message=exc.detail, data=None
    )

    return JSONResponse(
        status_code=exc.status_code, content=error_response.model_dump()
    )


async def handle_request_validation_exception(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.error(f"Request validation error: {exc.errors()}")
    return JSONResponse(
        status_code=HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": jsonable_encoder(exc.errors())},
    )
