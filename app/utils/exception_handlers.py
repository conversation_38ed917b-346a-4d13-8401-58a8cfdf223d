from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from typing import Any, Dict
from pydantic import ValidationError
from starlette.status import HTTP_202_ACCEPTED, HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR

from app.schemas.base_schemas import DataResponse
from app.core.logger import setup_logger

logger = setup_logger(__name__)


async def handle_exception(_: Request, exc: Exception) -> JSONResponse:
    """
    Handle global exception and return the error response.
    """
    error_response: Dict[str, Any] = {
        "status": "error",
        "message": "Internal server error",
    }

    status_code = 500
    return JSONResponse(status_code=status_code, content=error_response)


async def handle_http_exception(_: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTP exceptions and return the error response with logging.
    """
    error_response: DataResponse[None] = DataResponse[None](
        success=False, message=exc.detail, data=None
    )

    if exc.status_code >= HTTP_400_BAD_REQUEST and exc.status_code < HTTP_500_INTERNAL_SERVER_ERROR:
        return JSONResponse(
            status_code=HTTP_202_ACCEPTED, content=error_response.model_dump()
        )
    else:
        return JSONResponse(
            status_code=exc.status_code, content=error_response.model_dump()
        )


async def handle_request_validation_exception(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    logger.error(f"Request validation error: {exc.errors()}")
    return JSONResponse(
        status_code=HTTP_202_ACCEPTED,
        content={"detail": jsonable_encoder(exc.errors())},
    )

async def handle_validation_exception(
    request: Request, exc: ValidationError
) -> JSONResponse:
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=HTTP_202_ACCEPTED,
        content={"detail": jsonable_encoder(exc.errors())},
    )
