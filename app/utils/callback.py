import httpx
import json

from app.core.config import config
from app.schemas.callback_schemas import CallbackStatus
from app.core.logger import setup_logger

logger = setup_logger(__name__)


async def callback(
    resource_id: str,
    status: CallbackStatus,
    project_id: str,
    url: str = config.callback_url,
    authorization: str = config.aida_api_key,
    content_type: str = config.callback_content_type,
):
    try:
        logger.info(
            f"Sending callback {status} (resource_id: {resource_id}, project_id: {project_id})"
        )
        async with httpx.AsyncClient() as client:
            result = await client.post(
                url,
                data=json.dumps(
                    {
                        "resourceId": resource_id,
                        "status": status,
                        "projectId": project_id,
                    }
                ),
                timeout=httpx.Timeout(
                    config.callback_timeout,
                    connect=config.callback_connect_timeout,
                ),
                headers={"x-api-key": authorization, "Content-Type": content_type},
            )
            result.raise_for_status()
            logger.info(
                f"Successfully sent callback {status} (resource_id: {resource_id}, project_id: {project_id}), response status code: {result.status_code}, response text: {result.text}"
            )
            return result
    except httpx.HTTPStatusError as e:
        logger.error(
            f"Error sending callback {status} (resource_id: {resource_id}, project_id: {project_id}), response status code: {e.response.status_code}, response text: {e.response.text}"
        )
    except httpx.ReadTimeout:
        logger.error(
            f"Read timeout sending callback {status} (resource_id: {resource_id}, project_id: {project_id})"
        )
    except httpx.ConnectTimeout:
        logger.error(
            f"Connect timeout sending callback {status} (resource_id: {resource_id}, project_id: {project_id})"
        )
    except Exception as e:
        logger.error(
            f"Unexpected error sending callback {status} (resource_id: {resource_id}, project_id: {project_id}): {str(e)}"
        )
