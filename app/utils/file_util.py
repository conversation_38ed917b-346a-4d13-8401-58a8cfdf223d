from fastapi import HTTPException
import httpx
from urllib.parse import urlparse, unquote

from app.schemas.document_schemas import File
from app.core.logger import setup_logger

logger = setup_logger(__name__)


async def fetch_file(url: str, file_id: str) -> File:
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()

            file_bytes = response.content

            headers = response.headers
            content_type = headers.get("content-type")
            content_length = headers.get("content-length")

            return File(
                file_id=file_id,
                size=int(content_length) if content_length else len(file_bytes),
                content_type=content_type,
                file_content=file_bytes,
            )
    except httpx.UnsupportedProtocol as e:
        logger.error(f"Unsupported protocol for url {url} of file {file_id}: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported protocol for url {url} of file {file_id}",
        )
    except httpx.HTTPStatusError as e:
        logger.error(
            f"HTTP status error ({e.response.status_code}) for url {url} of file {file_id}"
        )
        raise HTTPException(
            status_code=400,
            detail=f"HTTP status error ({e.response.status_code}) for url {url} of file {file_id}",
        )
    except httpx.ConnectError as e:
        logger.error(f"Connection error for url {url} of file {file_id}: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Connection error for url {url} of file {file_id}",
        )
    except Exception as e:
        logger.error(f"Error fetching file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching file {file_id}")


def extract_blob_name(url: str) -> str:
    parsed_url = urlparse(url)
    blob_path = parsed_url.path.lstrip("/")
    blob_path = unquote(blob_path)
    return blob_path
