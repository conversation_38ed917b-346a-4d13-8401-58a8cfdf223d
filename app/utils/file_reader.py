from typing import Literal
from markitdown import MarkItDown
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from app.adapters.llm.llm_adapter import LLMAdapter
from app.core.logger import setup_logger
from app.schemas.llm_schemas import ContentDict
from io import BytesIO

logger = setup_logger(__name__)


class FileReader:
    def __init__(self, llm_adapter: LLMAdapter, md: MarkItDown):
        self._md = md
        self._llm_adapter = llm_adapter

    async def read(self, file_content: bytes, file_name: str, mime_type: str) -> str:
        """Extract text from various file formats."""

        file_ext = file_name.lower().split(".")[-1]

        try:
            if file_ext in ["txt", "md", "csv", "xml", "html", "pdf", "docx", "json"]:
                # Use context manager to ensure BytesIO is properly closed
                with BytesIO(file_content) as bio:
                    result = self._md.convert(bio)

                if not result.text_content and file_ext == "pdf":
                    text = await self._read_with_llm(
                        file_content=file_content,
                        file_type="pdf",
                        mime_type="application/pdf",
                    )
                    return text

                return result.text_content
            elif file_ext == "doc":
                from email.parser import BytesParser
                from email import policy

                try:
                    msg = BytesParser(policy=policy.default).parsebytes(file_content)

                    # Find HTML part in email
                    html_part = None
                    for part in msg.walk():
                        if part.get_content_type() == "text/html":
                            html_part = part.get_payload(decode=True)
                            break

                    # If HTML part is found
                    if html_part:
                        from bs4 import BeautifulSoup, Comment

                        try:
                            soup = BeautifulSoup(html_part, "html.parser")
                            # 1. Remove <style> (CSS)
                            for style in soup("style"):
                                style.decompose()

                            # 2. Remove HTML comment
                            for comment in soup.find_all(
                                string=lambda text: isinstance(text, Comment)
                            ):
                                comment.extract()

                            # 3. Get clean text content
                            text_content = soup.get_text(separator="\n", strip=True)

                            # Explicitly clear soup to free memory
                            soup.clear()
                            del soup
                            del html_part

                            return text_content
                        except Exception:
                            # Fallback if BeautifulSoup fails
                            del html_part
                            with BytesIO(file_content) as bio:
                                result = self._md.convert(bio)
                            return result.text_content
                    else:
                        with BytesIO(file_content) as bio:
                            result = self._md.convert(bio)
                        return result.text_content
                finally:
                    # Cleanup email parser objects
                    del msg
            elif file_ext in ["png", "jpg", "jpeg", "gif", "bmp", "webp"]:
                text = await self._read_with_llm(
                    file_content=file_content,
                    file_type="image",
                    mime_type=mime_type,
                )
                return text
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported file type: {file_ext}",
                )

        except Exception as e:
            logger.error(f"Failed to extract text from file {file_name}")
            raise e

    async def _read_with_llm(
        self, file_content: bytes, file_type: Literal["pdf", "image"], mime_type: str
    ) -> str:
        contents: list[ContentDict] = [
            {
                "type": "text",
                "text": """
Please extract all readable text content from the following file. The file may be a PDF, image, or other document format.
- Return only the raw text exactly as it appears in the document.
- Do not include any introductions, explanations, summaries, or commentary.
- Maintain the original reading order and structure (e.g., paragraphs, headings, tables if possible).
- Do not rephrase, correct, or interpret the text in any way.
- Your output must contain only the extracted text—no additional notes or system messages.
""",
            },
            {
                "type": file_type,
                "data": file_content,
                "mime_type": mime_type,
            },
        ]

        response = await self._llm_adapter.generate_content(
            contents=contents,
        )
        return response
