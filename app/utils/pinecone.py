import re
from urllib.parse import urlparse


def extract_index_name_from_host(host: str):
    """
    Extract the index name from the Pinecone host URL.
    Args:
        host (str): The host URL of the Pinecone index.
    Returns:
        str or None: The extracted index name or None if not matched.
    """
    # Parse netloc from URL
    parsed = urlparse(host)
    netloc = parsed.netloc if parsed.netloc else host  # fallback if it's not a full URL

    # Match pattern from netloc
    match = re.match(r"^(.*)-[a-z0-9]{5,}\.svc\.", netloc)
    if match:
        return match.group(1)
    return None