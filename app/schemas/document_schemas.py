from pydantic import BaseModel


class File(BaseModel):
    """File schema"""
    file_id: str
    size: int
    content_type: str
    file_content: bytes

class Metadata(BaseModel):
    file_id: str
    file_name: str
    file_type: str
    content_type: str
    file_size: int
    created_at: float
    project_id: str
    user_id: str

    class Config:
        extra = "allow"

class Chunk(BaseModel):
    id: str
    text: str
    metadata: Metadata