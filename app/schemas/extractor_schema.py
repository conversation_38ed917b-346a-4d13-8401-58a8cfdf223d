from typing import Literal, TypedDict, Optional, Tuple

from pydantic import BaseModel


class PdfExtractedBlock(TypedDict):
    content: str | dict | list
    type: Literal["text", "table", "image"]
    y_pos: float
    page_number: int
    bbox: Optional[Tuple[float, float, float, float]]  # x0, y0, x1, y1

class WordExtractedBlock(TypedDict):
    content: str | dict | list
    type: Literal["text", "table", "image"]
    y_pos: float

class ExtractedUtterance(BaseModel):
    speaker: str
    start_sec: str
    end_sec: str | None = None
    text: str

class ExtractedUtterances(BaseModel):
    data: list[ExtractedUtterance]