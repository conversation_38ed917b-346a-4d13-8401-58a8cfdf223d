from pydantic import BaseModel, Field

from app.core.config import config
from app.schemas.document_schemas import ChunkWithScore, Metadata


class PineconeMatchItem(BaseModel):
    id: str
    metadata: Metadata
    score: float
    values: list[float]


class PineconeQueryResponse(BaseModel):
    matches: list[PineconeMatchItem]
    namespace: str
    usage: dict


class QueryRequest(BaseModel):
    query: str = Field(..., description="The query to search for")
    project_id: str = Field(..., description="The project id")
    file_ids: list[str] = Field(..., description="The file ids")
    user_id: str = Field(..., description="The user id")
    top_k: int | None = Field(
        config.query_top_k, description="The number of chunks to return"
    )
    score_threshold: float | None = Field(
        config.query_score_threshold, description="The score threshold"
    )
    start_date: float | None = Field(
        None, description="The start date filter for created_at metadata"
    )
    end_date: float | None = Field(
        None, description="The end date filter for created_at metadata"
    )
    file_types: list[str] | None = Field(
        None, description="The file type filter for file_type metadata"
    )
    metadata: dict | None = Field(
        None, description="The additional metadata filter, e.g. {'speaker': '1'}"
    )


class QueryData(BaseModel):
    chunks: list[ChunkWithScore]
