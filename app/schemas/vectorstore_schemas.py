from typing import Any, Dict, List, Literal, TypedDict


class Record(TypedDict):
    id: str
    vector: list[float]
    payload: dict


class RetrievedRecord(TypedDict):
    id: str
    payload: dict | None
    score: float
    vector: list[float] | None

Operators = Literal[
    "$eq", "$ne", "$gt", "$gte", "$lt", "$lte", "$in", "$nin", "$exists", "$and", "$or"
]

Operation = Dict[Operators, Any]

SimpleFilter = Dict[str, Operation]

Filter = Dict[Literal["$and", "$or"], List[SimpleFilter]] | SimpleFilter
