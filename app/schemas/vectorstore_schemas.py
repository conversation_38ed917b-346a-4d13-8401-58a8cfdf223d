from datetime import datetime
from typing import Any, Dict, List, Literal, TypedDict

from pydantic import BaseModel


class Record(TypedDict):
    id: str
    vector: list[float]
    payload: dict


class RetrievedRecord(TypedDict):
    id: str
    payload: dict | None
    score: float
    vector: list[float] | None


Operators = Literal[
    "$eq", "$ne", "$gt", "$gte", "$lt", "$lte", "$in", "$nin", "$exists", "$and", "$or"
]

Operation = Dict[Operators, Any]

SimpleFilter = Dict[str, Operation]

Filter = Dict[Literal["$and", "$or"], List[SimpleFilter]] | SimpleFilter


class Backup(BaseModel):
    backup_id: str
    source_index_name: str
    source_index_id: str
    name: str
    description: str | None = None
    dimension: int
    size_bytes: int | None = None
    created_at: datetime

    class Config:
        extra = "ignore"
