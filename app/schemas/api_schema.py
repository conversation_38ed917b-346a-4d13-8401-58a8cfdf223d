from enum import Enum
from typing import Any
from pydantic import BaseModel


class MessagePayloadType(str, Enum):
    UPLOAD = "upload"
    DELETE = "delete"
    UPDATE = "update"
    DELETE_PROJECT = "delete_project"


class MessagePayloadMetadata(BaseModel):
    filename: str
    custom: dict[str, Any]

    class Config:
        extra = "allow"


class BaseMessagePayload(BaseModel):
    user_id: str
    project_id: str
    type: MessagePayloadType

    class Config:
        extra = "allow"


class UploadMessagePayload(BaseMessagePayload):
    file_id: str
    presigned_url: str
    metadata: MessagePayloadMetadata


class DeleteMessagePayload(BaseMessagePayload):
    file_id: str


class UpdateMessagePayload(BaseMessagePayload):
    file_id: str
    metadata: MessagePayloadMetadata


class DeleteProjectMessagePayload(BaseMessagePayload):
    pass


class PubsubMessage(BaseModel):
    data: str
    message_id: str
    publish_time: str
    attributes: dict[str, str] | None = None
    ordering_key: str | None = None


class PubsubPushRequest(BaseModel):
    message: PubsubMessage
    subscription: str
    deliveryAttempt: Any = None
