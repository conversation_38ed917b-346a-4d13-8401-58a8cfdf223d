import asyncio
import math
from typing import override
from google import genai
from google.genai import types

from app.adapters.embedding.embedding_adapter import EmbeddingAdapter
from app.core.config import config
from app.core.credentials import google_credentials
from app.core.logger import setup_logger

logger = setup_logger(__name__)


class GeminiEmbeddingAdapter(EmbeddingAdapter):
    def __init__(self):
        self._client = genai.Client(
            vertexai=True,
            project=config.google_cloud_project,
            location=config.gemini_location,
            credentials=google_credentials,
            http_options=types.HttpOptions(
                retry_options=types.HttpRetryOptions(
                    attempts=config.retry_attempts,
                )
            ),
        )

    @override
    async def embed_content(self, content: str) -> list[float]:
        response = await self._client.aio.models.embed_content(
            model=config.gemini_embedding_model,
            contents=[content],
            config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT"),
        )

        return response.embeddings[0].values

    @override
    async def embed_batch_of_contents(
        self, contents: list[str], batch_size: int = config.embedding_batch_size
    ) -> list[list[float]]:
        """
        Embed a batch of contents using asyncio for parallel processing with proper error handling.

        Args:
            contents: List of text contents to embed
            batch_size: Number of contents to process in each batch

        Returns:
            List of embedding responses

        Raises:
            Exception: If any embedding task fails, all tasks in the batch are cancelled
        """
        all_results: list[list[float]] = []
        logger.info(f"Embedding {len(contents)} contents in batches of {batch_size}.")

        # Process contents in batches
        for i in range(0, len(contents), batch_size):
            batch_num = (i // batch_size) + 1
            total_batches = math.ceil(len(contents) / batch_size)

            logger.info(f"Starting embedding batch {batch_num} of {total_batches}.")
            batch = contents[i : i + batch_size]

            try:
                # Use TaskGroup for automatic cleanup and cancellation
                async with asyncio.TaskGroup() as tg:
                    tasks = [
                        tg.create_task(self.embed_content(content)) for content in batch
                    ]

                # Collect results from completed tasks
                batch_results = [task.result() for task in tasks]
                logger.info(f"Finished embedding batch {batch_num} of {total_batches}.")
                all_results.extend(batch_results)

            except* Exception as eg:
                # Handle exception group from TaskGroup
                logger.error(f"Error in embedding batch {batch_num}: {eg}")
                # TaskGroup automatically cancels all running tasks
                raise Exception(
                    f"Embedding failed in batch {batch_num}"
                ) from eg.exceptions[0]

        return all_results
