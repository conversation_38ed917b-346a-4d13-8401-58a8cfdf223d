from asyncio import create_task, gather
import math
from typing import override
from google import genai
from google.genai import types

from app.adapters.embedding.embedding_adapter import EmbeddingAdapter
from app.core.config import config
from app.core.credentials import google_credentials
from app.core.logger import setup_logger

logger = setup_logger(__name__)


class GeminiEmbeddingAdapter(EmbeddingAdapter):
    def __init__(self):
        self._client = genai.Client(
            vertexai=True,
            project=config.google_cloud_project,
            location=config.gemini_location,
            credentials=google_credentials,
            http_options=types.HttpOptions(
                retry_options=types.HttpRetryOptions(
                    attempts=config.retry_attempts,
                )
            ),
        )

    @override
    async def embed_content(self, content: str) -> list[float]:
        response = await self._client.aio.models.embed_content(
            model=config.gemini_embedding_model,
            contents=[content],
            config=types.EmbedContentConfig(task_type="QUESTION_ANSWERING"),
        )

        return response.embeddings[0].values

    @override
    async def embed_batch_of_contents(
        self, contents: list[str], batch_size: int = 100
    ) -> list[list[float]]:
        """
        Embed a batch of contents using asyncio for parallel processing.

        Args:
            contents: List of text contents to embed
            batch_size: Number of contents to process in each batch

        Returns:
            List of embedding responses
        """
        all_results: list[list[float]] = []
        logger.info(f"Embedding {len(contents)} contents in batches of {batch_size}.")

        # Process contents in batches
        for i in range(0, len(contents), batch_size):
            logger.info(
                f"Embedding batch {(i // batch_size) + 1} of {math.ceil(len(contents) / batch_size)}."
            )
            batch = contents[i : i + batch_size]

            # Create tasks for parallel processing within each batch
            tasks = [create_task(self.embed_content(content)) for content in batch]

            # Wait for all tasks in the current batch to complete
            batch_results = await gather(*tasks)
            all_results.extend(batch_results)

        return all_results
