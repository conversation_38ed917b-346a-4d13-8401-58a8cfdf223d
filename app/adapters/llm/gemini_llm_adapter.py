from typing import override
from google import genai
from google.genai import types
from pydantic import BaseModel

from app.adapters.llm.llm_adapter import LLMAdapter
from app.core.config import config as app_config
from app.core.credentials import google_credentials
from app.schemas.llm_schemas import ContentDict
from app.core.logger import setup_logger

logger = setup_logger(__name__)


class GeminiLLMAdapter(LLMAdapter):
    def __init__(self):
        self._client = genai.Client(
            vertexai=True,
            project=app_config.google_cloud_project,
            location=app_config.gemini_location,
            credentials=google_credentials,
            http_options=types.HttpOptions(
                retry_options=types.HttpRetryOptions(
                    attempts=app_config.retry_attempts,
                )
            ),
        )

    def _convert_contents(self, contents: list[ContentDict]) -> list[types.Part]:
        """
        Convert the general content format to the format expected by the Gemini API.
        """
        return [
            types.Part.from_bytes(data=content["data"], mime_type=content["mime_type"])
            if content["type"] == "image" or content["type"] == "pdf"
            else content["text"]
            for content in contents
        ]

    @override
    async def generate_content(
        self,
        contents: list[ContentDict],
    ) -> str:
        logger.info("Generating content")
        converted_contents = self._convert_contents(contents)

        response = await self._client.aio.models.generate_content(
            model=app_config.gemini_model,
            contents=converted_contents,
        )

        return response.text

    @override
    async def generate_schema[T: BaseModel](
        self,
        contents: list[ContentDict],
        schema: T,
    ) -> T:
        logger.info("Generating schema")
        converted_contents = self._convert_contents(contents)
        response = await self._client.aio.models.generate_content(
            model=app_config.gemini_model,
            contents=converted_contents,
            config=types.GenerateContentConfig(
                response_schema=schema,
                response_mime_type="application/json",
            ),
        )

        result = schema.model_validate_json(response.text)

        return result
