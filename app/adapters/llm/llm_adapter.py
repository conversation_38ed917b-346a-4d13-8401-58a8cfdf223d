from abc import ABC, abstractmethod
from typing import Type
from pydantic import BaseModel

from app.schemas.llm_schemas import ContentDict


class LLMAdapter(ABC):
    @abstractmethod
    async def generate_content(self, contents: list[ContentDict]) -> str:
        pass

    @abstractmethod
    async def generate_schema[T: BaseModel](self, contents: list[ContentDict], schema: Type[T]) -> T:
        pass