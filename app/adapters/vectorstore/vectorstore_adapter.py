from abc import ABC, abstractmethod

from app.core.config import config
from app.schemas.vectorstore_schemas import Backup, Filter, Record, RetrievedRecord


class VectorStoreAdapter(ABC):
    @abstractmethod
    async def query(
        self,
        vector: list[float],
        top_k: int | None = None,
        project_id: str | None = None,
        filter: Filter | None = None,
        include_payload: bool | None = None,
        include_vector: bool | None = None,
    ) -> list[RetrievedRecord]:
        pass

    @abstractmethod
    async def upsert(
        self,
        records: list[Record],
        project_id: str,
        batch_size: int = config.vectorstore_upsert_batch_size,
    ) -> None:
        pass

    @abstractmethod
    async def delete(self, project_id: str, filter: Filter | None = None) -> None:
        pass

    @abstractmethod
    async def delete_project(self, project_id: str) -> None:
        pass

    @abstractmethod
    async def close(self) -> None:
        pass

    @abstractmethod
    def create_backup(self) -> None:
        pass

    @abstractmethod
    def list_backups(self) -> list[Backup]:
        pass

    @abstractmethod
    def delete_backup(self, backup_id: str) -> None:
        pass
