from abc import ABC, abstractmethod

from app.core.config import config
from app.schemas.vectorstore_schemas import Filter, RetrievedRecord


class VectorStoreAdapter(ABC):

    @abstractmethod
    async def query(
        self,
        vector: list[float],
        top_k: int | None = config.query_top_k,
        score_threshold: float | None = config.query_score_threshold,
        project_id: str | None = None,
        filter: Filter | None = None,
        include_payload: bool | None = None,
        include_vector: bool | None = None,
    ) -> list[RetrievedRecord]:
        pass

    @abstractmethod
    async def close(self) -> None:
        pass
