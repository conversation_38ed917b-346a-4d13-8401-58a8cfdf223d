from datetime import datetime
import threading
from typing import override
from pinecone import <PERSON><PERSON><PERSON>

from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.vectorstore_schemas import Filter, RetrievedRecord
from app.utils.pinecone import extract_index_name_from_host

logger = setup_logger(__name__)


class PineconeVectorstoreAdapter(VectorStoreAdapter):
    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __init__(self):
        if self._initialized:
            return
        self._pc = Pinecone(api_key=config.pinecone_api_key)
        self._dense_index = self._pc.IndexAsyncio(
            host=config.pinecone_dense_index_host,
        )
        self._initialized = True

    @override
    async def query(
        self,
        vector: list[float],
        top_k: int | None = config.query_top_k,
        score_threshold: float | None = config.query_score_threshold,
        project_id: str | None = None,
        filter: Filter | None = None,
        include_payload: bool | None = None,
        include_vector: bool | None = None,
    ) -> list[RetrievedRecord]:
        res = await self._dense_index.query(
            vector=vector,
            top_k=top_k,
            namespace=project_id,
            filter=filter,
            include_metadata=include_payload,
            include_values=include_vector,
        )
        matches = res.get("matches", [])
        return [
            RetrievedRecord(
                id=match.get("id", ""),
                payload=match.get("metadata", None),
                score=match.get("score", 0.0),
                vector=match.get("values", None),
            )
            for match in matches
            if match.get("score", 0.0) >= score_threshold
        ]

    @override
    async def close(self):
        """Close the Pinecone connection."""
        await self._dense_index.close()

    def create_backup(self):
        """Create a backup of the Pinecone indexes."""
        index_name = extract_index_name_from_host(config.pinecone_dense_index_host)
        date = datetime.now().date()
        self._pc.create_backup(
            index_name=index_name,
            backup_name=f"{index_name}-{date}",
            description=f"Backup of {index_name} on {date}",
        )

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    logger.info("Creating PineconeAdapter instance")
        return cls._instance
