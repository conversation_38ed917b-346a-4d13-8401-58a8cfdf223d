from datetime import datetime
import threading
from typing import override
from pinecone import <PERSON><PERSON><PERSON>, Vector
from pinecone.exceptions import NotFoundException

from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.vectorstore_schemas import Backup, Filter, Record, RetrievedRecord
from app.utils.pinecone import extract_index_name_from_host

logger = setup_logger(__name__)


class PineconeVectorstoreAdapter(VectorStoreAdapter):
    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __init__(self):
        if self._initialized:
            return
        self._pc = Pinecone(api_key=config.pinecone_api_key)
        self._dense_index = self._pc.IndexAsyncio(
            host=config.pinecone_dense_index_host,
        )
        self._initialized = True

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    logger.info("Creating PineconeAdapter instance")
        return cls._instance

    @override
    async def query(
        self,
        vector: list[float],
        top_k: int | None = 10,
        project_id: str | None = None,
        filter: Filter | None = None,
        include_payload: bool | None = None,
        include_vector: bool | None = None,
    ) -> list[RetrievedRecord]:
        logger.info(
            f"Querying Pinecone with {len(vector)} dimensions vector, top_k: {top_k}, project_id: {project_id}, filter: {filter}, include_payload: {include_payload}, include_vector: {include_vector}"
        )
        res = await self._dense_index.query(
            vector=vector,
            top_k=top_k,
            namespace=project_id,
            filter=filter,
            include_metadata=include_payload,
            include_values=include_vector,
        )
        matches = res.get("matches", [])
        return [
            RetrievedRecord(
                id=match.get("id", ""),
                payload=match.get("metadata", None),
                score=match.get("score", 0.0),
                vector=match.get("values", None),
            )
            for match in matches
        ]

    @override
    async def upsert(
        self,
        records: list[Record],
        project_id: str,
        batch_size: int = config.vectorstore_upsert_batch_size,
    ) -> None:
        logger.info(
            f"Upserting {len(records)} records to project {project_id} with batch size {batch_size}."
        )
        vectors = [
            Vector(
                id=record.get("id", ""),
                values=record.get("vector", []),
                metadata=record.get("payload", {}),
            )
            for record in records
        ]
        _ = await self._dense_index.upsert(
            vectors=vectors, namespace=project_id, batch_size=batch_size
        )

    @override
    async def delete(self, project_id: str, filter: Filter | None = None) -> None:
        logger.info(f"Deleting records from project {project_id} with filter {filter}.")
        try:
            _ = await self._dense_index.delete(
                filter=filter,
                namespace=project_id,
            )
        except NotFoundException as e:
            logger.warning(
                f"Delete operation failed, project {project_id} not found. {e}"
            )
        except Exception as e:
            logger.error(f"Error deleting records from project {project_id}: {e}")
            raise e

    @override
    async def delete_project(self, project_id: str) -> None:
        logger.info(f"Deleting project {project_id}.")
        try:
            _ = await self._dense_index.delete_namespace(
                namespace=project_id,
            )
        except NotFoundException as e:
            logger.warning(
                f"Delete operation failed, project {project_id} not found. {e}"
            )
        except Exception as e:
            logger.error(f"Error deleting project {project_id}: {e}")
            raise e

    @override
    async def close(self):
        """Close the Pinecone connection."""
        logger.info("Closing Pinecone connection.")
        await self._dense_index.close()

    @override
    def create_backup(self) -> None:
        """Create a backup of the Pinecone indexes."""
        index_name = extract_index_name_from_host(config.pinecone_dense_index_host)
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        self._pc.create_backup(
            index_name=index_name,
            backup_name=f"{index_name}-{timestamp}",
            description=f"Backup of {index_name} on {timestamp}",
        )

    @override
    def list_backups(self) -> list[Backup]:
        """List all backups of the Pinecone indexes."""
        index_name = extract_index_name_from_host(config.pinecone_dense_index_host)
        backups = self._pc.list_backups(
            index_name=index_name,
            limit=config.vectorstore_list_backups_limit,
        )
        # Convert to Backup model
        backups = [Backup(**backup.to_dict()) for backup in backups]
        # Filter by index name
        backups = [
            backup for backup in backups if backup.source_index_name == index_name
        ]
        # Sort by created_at
        backups = sorted(backups, key=lambda x: x.created_at, reverse=True)
        return backups

    @override
    def delete_backup(self, backup_id: str) -> None:
        try:
            self._pc.delete_backup(backup_id=backup_id)
        except Exception as e:
            logger.error(f"Error deleting backup {backup_id}: {e}")
            raise e
