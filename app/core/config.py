from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

from app.schemas.log_schemas import LogLevel

load_dotenv()


class Config(BaseSettings):
    environment: str = Field(default="development")

    api_host: str = Field(default="0.0.0.0", description="API host")
    api_port: int = Field(default=8080, description="API port", alias="PORT")

    cors_allow_origins: list[str] = Field(default=["*"])
    cors_allow_credentials: bool = Field(default=True)

    max_worker: int = Field(1)
    max_concurrent_images: int = Field(10)

    logger_level: LogLevel = Field(default=LogLevel.INFO, description="Logger level")
    logger_file: str | None = Field(default=None, description="Logger file")
    logger_cloud: bool = Field(default=False, description="Logger cloud")
    logger_cloud_name: str | None = Field(
        default="document-processor", description="Logger cloud name"
    )

    allowed_file_extensions: list[str] = Field(
        default=[
            "pdf",
            "docx",
            "doc",
            "txt",
            "csv",
            "json",
            "md",
            "html",
            "xml",
            "png",
            "jpg",
            "jpeg",
            "gif",
            "bmp",
            "webp",
        ],
        description="Allowed file extensions",
    )
    max_file_size: int = Field(default=10 * 1024 * 1024, description="Max file size")

    utterance_contextual_window_size: int = Field(
        default=1, description="Utterance contextual window size"
    )
    utterance_contextual_min_tokens: int = Field(
        default=150, description="Utterance contextual min tokens"
    )

    splitter_max_chunk_size: int = Field(default=500, description="Max chunk size")
    splitter_min_chunk_size: int = Field(default=150, description="Min chunk size")
    splitter_chunk_overlap: int = Field(default=80, description="Chunk overlap")

    callback_url: str = Field(..., description="Callback URL")
    aida_api_key: str = Field(..., description="AIDA API key")
    callback_content_type: str = Field(
        "application/json", description="Callback content type"
    )
    callback_timeout: float = Field(10.0, description="Callback timeout")
    callback_connect_timeout: float = Field(5.0, description="Callback connect timeout")
    callback_max_retries: int = Field(3, description="Callback max retries")

    retry_attempts: int = Field(3, description="Retry attempts for LLM and embedding")

    pinecone_api_key: str = Field(..., description="Pinecone API key")
    pinecone_dense_index_host: str = Field(..., description="Pinecone dense index host")
    vector_dimension: int = Field(3072, description="Vector dimension")
    vectorstore_upsert_batch_size: int = Field(
        50, description="Vectorstore upsert batch size"
    )
    vectorstore_backup_limit: int = Field(
        5, description="Number of backups to keep in the vectorstore"
    )
    vectorstore_list_backups_limit: int = Field(
        100,
        description="Number of backups to list every time we call list_backups",
    )

    # Backup scheduler configuration
    vectorstore_backup_interval_hours: int = Field(
        default=24, description="Backup schedule interval in hours"
    )
    schedule_start_time: str = Field(
        default="00:00", description="Backup schedule start time in HH:MM format"
    )

    google_cloud_project: str
    sa_key_json: str

    gemini_location: str
    gemini_embedding_model: str = "gemini-embedding-001"
    gemini_model: str = "gemini-2.5-flash"
    embedding_batch_size: int = Field(50, description="Embedding batch size")
    chunk_processing_batch_size: int = Field(
        50, description="Chunk processing batch size for memory optimization"
    )

    tiktoken_encoding: str = Field("cl100k_base")

    class Config:
        extra = "ignore"
        env_file = ".env"


config = Config()

__all__ = ["config"]
