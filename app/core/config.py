from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

from app.schemas.log_schemas import LogLevel

load_dotenv()


class Config(BaseSettings):
    environment: str = Field(default="development")

    api_host: str = Field(default="0.0.0.0", description="API host")
    api_port: int = Field(default=8080, description="API port", alias="PORT")

    cors_allow_origins: list[str] = Field(default=["*"])
    cors_allow_credentials: bool = Field(default=True)

    authentication_token: str = Field(..., description="Authentication token")

    pinecone_api_key: str = Field(..., description="Pinecone API key")
    pinecone_dense_index_host: str = Field(..., description="Pinecone dense index host")
    vector_dimension: int = Field(
        ..., description="Vector dimension",
        alias="PINECONE_DENSE_INDEX_DIMENSION"
    )

    google_cloud_project: str
    sa_key_json: str

    gemini_location: str
    gemini_embedding_model: str = "gemini-embedding-001"

    logger_level: LogLevel = Field(default=LogLevel.INFO, description="Logger level")
    logger_file: str | None = Field(default=None, description="Logger file")
    logger_cloud: bool = Field(default=False, description="Logger cloud")
    logger_cloud_name: str | None = Field(
        default="rag-service", description="Logger cloud name"
    )

    retry_attempts: int = Field(3, description="Retry attempts for LLM and embedding")

    query_top_k: int = Field(10, description="The number of chunks to return")
    query_score_threshold: float = Field(0.6, description="The score threshold")

    class Config:
        extra = "ignore"
        env_file = ".env"


config = Config()

__all__ = ["config"]
