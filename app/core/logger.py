import logging
import sys
from google.cloud.logging import Client
from google.cloud.logging_v2.handlers import CloudLoggingHandler

from app.core.config import config
from app.core.credentials import google_credentials
from app.schemas.log_schemas import LogLevel


class AlignedColorFormatter(logging.Formatter):
    """
    Custom formatter with alignment and ANSI color codes
    """

    # ANSI color codes
    COLORS = {
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[41m",  # Red background for critical
        "RESET": "\033[0m",  # Reset to default color
    }

    # Fixed width for each part of the log
    LEVEL_WIDTH = 6  # Width for log level
    TIME_WIDTH = 22  # Width for timestamp (YYYY-MM-DD HH:MM:SS)
    NAME_WIDTH = 32  # Width for logger name

    def format(self, record: logging.LogRecord) -> str:
        # Save original levelname
        levelname = record.levelname

        # Add color to levelname and align
        colored_level = ""
        if levelname in self.COLORS:
            # Create log level string with color and padding
            colored_level = f"{self.COLORS[levelname]}{levelname:<{self.LEVEL_WIDTH}}{self.COLORS['RESET']}"
        else:
            colored_level = f"{levelname:<{self.LEVEL_WIDTH}}"

        # Format timestamp
        time_str = self.formatTime(record, self.datefmt)
        time_part = f"{time_str:<{self.TIME_WIDTH}}"

        # Format logger name, truncate if too long
        name_part = f"{record.name:<{self.NAME_WIDTH}}"
        if len(record.name) > self.NAME_WIDTH:
            name_part = f"{record.name[: self.NAME_WIDTH - 4]}...:"

        # Get message
        message = record.getMessage()

        # Create formatted message according to requirements [log level] [time] [name] [message]
        result = f"{colored_level} {time_part} {name_part}{message}"

        return result


def setup_logger(
    name: str,
    level: LogLevel = config.logger_level,
    log_file: str = config.logger_file,
    cloud: bool = config.logger_cloud,
    cloud_name: str = config.logger_cloud_name,
) -> logging.Logger:
    """Create and configure logger with aligned formatting

    Args:
        name: Name of the logger
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (if you want to save logs to a file)

    Returns:
        logging.Logger: Configured logger
    """
    # Create logger
    name = name.split(".")[-1]
    logger = logging.getLogger(name)

    # Clear old handlers if any
    if logger.handlers:
        logger.handlers.clear()

    # Set log level
    logger.setLevel(getattr(logging, level))

    # Use new formatter with alignment
    formatter = AlignedColorFormatter(datefmt="%Y-%m-%d %H:%M:%S")

    # Handler for console (only add if not using cloud logging to avoid duplicates)
    if not cloud:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # Handler for file (if provided)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        # Use formatter without colors for log file
        file_formatter = logging.Formatter(
            fmt="%(levelname)-9s %(asctime)s %(name)s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    if cloud:
        # When cloud logging is enabled, Google Cloud automatically captures stdout/stderr
        # So we only use the cloud handler to avoid duplicate logs
        client = Client(credentials=google_credentials)
        cloud_handler = CloudLoggingHandler(
            client,
            name=cloud_name,
            labels={"module": name},
        )
        # Create formatter without colors for cloud logging
        cloud_formatter = logging.Formatter(
            fmt="%(levelname)-9s %(asctime)s %(name)s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        cloud_handler.setFormatter(cloud_formatter)
        logger.addHandler(cloud_handler)

    return logger
