from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from typing import AsyncGenerator

from pydantic import ValidationError

from app.adapters.vectorstore.pinecone_vectorstore_adapter import (
    PineconeVectorstoreAdapter,
)
from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.core.config import config
from app.core.logger import setup_logger
from app.routers import backup_router, document_router
from app.utils.exception_handlers import (
    handle_exception,
    handle_http_exception,
    handle_request_validation_exception,
    handle_validation_exception,
)

logger = setup_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""

    # Initialize services
    logger.info(f"Starting Document Processor, environment: {config.environment}")
    vectorstore_adapter: VectorStoreAdapter = PineconeVectorstoreAdapter()

    yield

    # Shutdown services
    logger.info("Shutting down Document Processor")
    await vectorstore_adapter.close()


app = FastAPI(
    title="Document Processor",
    description="A fully-managed, horizontally-scalable Document Processor",
    version="1.0.0",
    docs_url="/docs" if config.environment == "development" else None,
    redoc_url="/redoc" if config.environment == "development" else None,
    openapi_url="/openapi.json" if config.environment == "development" else None,
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=config.cors_allow_origins,
    allow_credentials=config.cors_allow_credentials,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_exception_handler(Exception, handle_exception)
app.add_exception_handler(HTTPException, handle_http_exception)
app.add_exception_handler(RequestValidationError, handle_request_validation_exception)
app.add_exception_handler(ValidationError, handle_validation_exception)

app.include_router(document_router.router)
app.include_router(backup_router.router)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Document Processor",
        "version": "1.0.0",
        "status": "running",
        "environment": config.environment,
    }


def main():
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.environment == "development",
    )


if __name__ == "__main__":
    main()
