from app.schemas.query_schemas import QueryData, QueryRequest
from fastapi import APIRouter, Depends

from app.schemas.base_schemas import DataResponse
from app.services.auth.auth_service import AuthService
from app.services.query_service import QueryService

router = APIRouter(prefix="/retrieve", tags=["retrieve"])


@router.post(
    "",
    response_model=DataResponse[QueryData],
    operation_id="retrieve_relevant_chunks",
    dependencies=[Depends(AuthService.verify_token)],
)
async def retrieve_relevant_chunks(
    body: QueryRequest,
    query_service: QueryService = Depends(QueryService),
):
    result = await query_service.query(body=body)
    return result
