import time

from fastapi import Depends
from app.adapters.embedding.embedding_adapter import EmbeddingAdapter
from app.adapters.embedding.gemini_embedding_adapter import GeminiEmbeddingAdapter
from app.adapters.vectorstore.pinecone_vectorstore_adapter import (
    PineconeVectorstoreAdapter,
)
from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.schemas.base_schemas import DataResponse
from app.schemas.document_schemas import ChunkWith<PERSON>core
from app.schemas.query_schemas import QueryData, QueryRequest
from app.core.logger import setup_logger
from app.schemas.vectorstore_schemas import Filter, RetrievedRecord

logger = setup_logger(__name__)


class QueryService:
    def __init__(
        self,
        embedding_adapter: EmbeddingAdapter = Depends(GeminiEmbeddingAdapter),
        vectorstore_adapter: VectorStoreAdapter = Depends(PineconeVectorstoreAdapter),
    ):
        self._embedding_adapter = embedding_adapter
        self._vectorstore_adapter = vectorstore_adapter

    async def query(
        self,
        body: QueryRequest,
    ) -> DataResponse[QueryData]:
        start_time = time.time()

        query = body.query
        embedding = await self._embedding_adapter.embed_content(content=query)
        logger.info(f"Successfully embedded query: {query}")

        filter = self._convert_request_to_filter(body=body)
        records = await self._vectorstore_adapter.query(
            vector=embedding,
            top_k=body.top_k,
            project_id=body.project_id,
            score_threshold=body.score_threshold,
            filter=filter,
            include_payload=True,
        )

        chunks = self._convert_retrieved_records_to_chunks(records)
        query_data = QueryData(
            chunks=chunks,
        )

        logger.info(
            f"Successfully retrieved chunks in file ids {body.file_ids} from project {body.project_id} by user {body.user_id}"
        )

        return DataResponse(
            data=query_data,
            success=True,
            message="Successfully retrieved chunks from Pinecone",
            processing_time_ms=int((time.time() - start_time) * 1000),
        )

    def _convert_request_to_filter(self, body: QueryRequest) -> Filter:
        filter: Filter = {"$and": [{"file_id": {"$in": body.file_ids}}]}
        if body.start_date:
            filter.get("$and").append({"created_at": {"$gte": body.start_date}})
        if body.end_date:
            filter.get("$and").append({"created_at": {"$lte": body.end_date}})
        if body.file_types:
            filter.get("$and").append({"file_type": {"$in": body.file_types}})
        if body.metadata:
            for key, value in body.metadata.keys():
                filter.get("$and").append({key: {"$eq": value}})

        return filter

    def _convert_retrieved_records_to_chunks(
        self, records: list[RetrievedRecord]
    ) -> list[ChunkWithScore]:
        return [
            ChunkWithScore(
                id=record.get("id", ""),
                text=(payload := record.get("payload", {}).copy()).pop("text", ""),
                score=record.get("score", 0),
                metadata=payload,
            )
            for record in records
        ]
