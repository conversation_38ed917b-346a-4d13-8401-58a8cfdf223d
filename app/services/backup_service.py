from fastapi import Depends
from app.adapters.vectorstore.pinecone_vectorstore_adapter import (
    PineconeVectorstoreAdapter,
)
from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.core.config import config
from app.core.logger import setup_logger

logger = setup_logger(__name__)


class BackupService:
    def __init__(
        self,
        vectorstore_adapter: VectorStoreAdapter = Depends(PineconeVectorstoreAdapter),
    ):
        self._vectorstore_adapter = vectorstore_adapter

    async def start_backup(self) -> None:
        try:
            logger.info("Starting backup creation and cleanup process...")

            # Create new backup
            logger.info("Creating new backup...")
            self._vectorstore_adapter.create_backup()
            logger.info("Successfully created new backup")

            # Get backup list and cleanup if needed
            backups = self._vectorstore_adapter.list_backups()
            logger.info(f"Found {len(backups)} existing backups")

            # Check if it exceeds the limit
            if len(backups) > config.vectorstore_backup_limit:
                # Calculate the number of backups to delete
                excess_count = len(backups) - config.vectorstore_backup_limit
                logger.info(
                    f"Need to delete {excess_count} old backups to maintain limit of {config.vectorstore_backup_limit}"
                )

                # Backup list is sorted by creation time (newest first)
                # Get the oldest backups to delete
                backups_to_delete = backups[-excess_count:]

                # Delete each backup
                for backup in backups_to_delete:
                    logger.info(
                        f"Deleting old backup: {backup.name} (created: {backup.created_at})"
                    )
                    self._vectorstore_adapter.delete_backup(backup.backup_id)
                    logger.info(f"Successfully deleted backup: {backup.name}")

                logger.info(f"Successfully deleted {excess_count} old backups")
            else:
                logger.info(
                    f"No cleanup needed. Current backup count ({len(backups)}) is within limit ({config.vectorstore_backup_limit})"
                )

            logger.info("Backup creation and cleanup process completed successfully")

        except Exception as e:
            logger.error(f"Error during backup creation and cleanup: {e}")
            raise e
