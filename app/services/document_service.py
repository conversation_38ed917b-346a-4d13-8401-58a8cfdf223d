import json
import base64
from asyncio import create_task
import time
import gc
import math

from fastapi import Depends, HTTPException
from markitdown import MarkItDown

from app.adapters.embedding.gemini_embedding_adapter import GeminiEmbeddingAdapter
from app.adapters.embedding.embedding_adapter import EmbeddingAdapter
from app.adapters.llm.gemini_llm_adapter import GeminiLLMAdapter
from app.adapters.llm.llm_adapter import LLMAdapter
from app.adapters.vectorstore.pinecone_vectorstore_adapter import (
    PineconeVectorstoreAdapter,
)
from app.adapters.vectorstore.vectorstore_adapter import VectorStoreAdapter
from app.core.config import config
from app.core.logger import setup_logger
from app.schemas.callback_schemas import CallbackStatus
from app.schemas.document_schemas import File, Chunk, Metadata
from app.schemas.api_schema import (
    BaseMessagePayload,
    DeleteMessagePayload,
    DeleteProjectMessagePayload,
    MessagePayloadType,
    PubsubPushRequest,
    UpdateMessagePayload,
    UploadMessagePayload,
)
from app.schemas.vectorstore_schemas import Filter, Record
from app.utils.callback import callback
from app.utils.extractors.chunk_extractor import ChunkExtractor
from app.utils.extractors.docx_block_extractor import DocxBlockExtractor
from app.utils.extractors.pdf_block_extractor import PdfBlockExtractor
from app.utils.file_reader import FileReader
from app.utils.file_util import fetch_file
from app.utils.splitters.chunk_splitter import ChunkSplitter
from app.utils.splitters.docx_block_splitter import DocxBlockSplitter
from app.utils.splitters.pdf_block_splitter import PdfBlockSplitter
from app.utils.token_util import TokenUtil
from app.adapters.mark_it_down import get_mark_it_down

logger = setup_logger(__name__)


class DocumentService:
    def __init__(
        self,
        embedding_adapter: EmbeddingAdapter = Depends(GeminiEmbeddingAdapter),
        vectorstore_adapter: VectorStoreAdapter = Depends(PineconeVectorstoreAdapter),
        llm_adapter: LLMAdapter = Depends(GeminiLLMAdapter),
        token_util: TokenUtil = Depends(TokenUtil),
        mark_it_down: MarkItDown = Depends(get_mark_it_down),
    ):
        self._embedding_adapter = embedding_adapter
        self._vectorstore_adapter = vectorstore_adapter
        self._llm_adapter = llm_adapter
        self._file_reader = FileReader(llm_adapter=llm_adapter, md=mark_it_down)
        self._token_util = token_util

    async def handle_request(self, request: PubsubPushRequest) -> None:
        encoded_data = request.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        message_payload_dict = json.loads(decoded_data)
        message_payload = BaseMessagePayload.model_validate(message_payload_dict)
        handler_map = {
            MessagePayloadType.UPLOAD: self.upload_document,
            MessagePayloadType.DELETE: self.delete_document,
            MessagePayloadType.UPDATE: self.update_document,
            MessagePayloadType.DELETE_PROJECT: self.delete_project,
        }

        if message_payload.type not in handler_map:
            logger.error(f"Invalid request type: {message_payload_dict}")
            raise HTTPException(
                status_code=400, detail=f"Invalid request type: {message_payload_dict}"
            )

        logger.info(f"Handling request {message_payload.type}: {message_payload_dict}")

        await handler_map[message_payload.type](message_payload_dict)

    async def upload_document(self, message_data_dict: dict) -> None:
        message_data = UploadMessagePayload.model_validate(message_data_dict)
        trace_info = f"(project_id: {message_data.project_id}, user_id: {message_data.user_id}, file_id: {message_data.file_id}, file_name: {message_data.metadata.custom.get('original_filename', '')})"
        try:
            # Send callback to indicate that the file is not synced
            not_sync_task = create_task(
                callback(
                    resource_id=message_data.file_id,
                    project_id=message_data.project_id,
                    status=CallbackStatus.NOT_SYNCED,
                )
            )

            # Validate file_id does not exist in vectorstore (minimal payload to reduce memory)
            records = await self._get_records_by_file_id_and_project_id(
                file_id=message_data.file_id,
                project_id=message_data.project_id,
                top_k=1,
                include_payload=False,
                include_vector=False,
            )
            if records:
                logger.warning(
                    f"File {message_data.file_id} already exists in vectorstore, skipping... {trace_info}."
                )
                raise HTTPException(
                    status_code=400, detail="File already exists in vectorstore"
                )

            # Fetch file from presigned URL
            filename = message_data.metadata.filename
            logger.info(
                f"Fetching file from presigned URL {message_data.presigned_url} {trace_info}."
            )
            file = await fetch_file(
                url=message_data.presigned_url, file_id=message_data.file_id
            )

            logger.info(
                f"File {message_data.file_id} fetched from presigned URL {message_data.presigned_url} {trace_info}."
            )

            # Initialize syncing_task to avoid undefined variable in exception handler
            syncing_task = None

            created_at = time.time()
            self._validate_file(file, filename)

            await not_sync_task
            # Send callback to indicate that the file is syncing
            syncing_task = create_task(
                callback(
                    resource_id=message_data.file_id,
                    project_id=message_data.project_id,
                    status=CallbackStatus.SYNCING,
                )
            )
            logger.info(
                f"Extracting text from file {message_data.file_id} {trace_info}."
            )

            # Extract text from file
            file_content = file.file_content
            file_type = filename.split(".")[-1].lower()
            metadata = Metadata(
                file_id=file.file_id,
                file_name=filename,
                file_size=file.size,
                file_type=file_type,
                content_type=file.content_type,
                project_id=message_data.project_id,
                user_id=message_data.user_id,
                created_at=created_at,
                is_transcript=message_data.metadata.custom.get("is_transcript", False),
                original_filename=message_data.metadata.custom.get(
                    "original_filename", "unknown"
                ),
                transcript_source=message_data.metadata.custom.get(
                    "transcript_source", "unknown"
                ),
                **self._filter_metadata_fields(
                    message_data.metadata.model_dump(
                        exclude={"custom", "filename"}, exclude_none=True
                    )
                ),
                **self._filter_metadata_fields(
                    getattr(message_data.metadata, "audio_video", {})
                ),
            )

            logger.info(
                f"Extracting chunks from file {message_data.file_id} {trace_info}."
            )

            # Process file in batches to optimize memory usage
            total_records_processed = await self._process_file_in_batches(
                file_content=file_content,
                file_name=filename,
                metadata=metadata,
                message_data=message_data,
                is_transcript=message_data.metadata.custom.get("is_transcript", False),
                batch_size=config.chunk_processing_batch_size,
            )

            # Clear file content from memory after processing
            del file_content
            del file
        except HTTPException as e:
            await not_sync_task
            logger.warning(
                f"Error processing file {message_data.file_id}: {str(e)} {trace_info}."
            )
            await callback(
                resource_id=message_data.file_id,
                project_id=message_data.project_id,
                status=CallbackStatus.SKIPPED,
            )
            raise e
        except Exception as e:
            logger.error(
                f"Error processing file {message_data.file_id}: {str(e)} {trace_info}."
            )
            if syncing_task is not None:
                await syncing_task
            await callback(
                resource_id=message_data.file_id,
                project_id=message_data.project_id,
                status=CallbackStatus.FAILED,
            )
            raise e
        finally:
            # Force garbage collection
            gc.collect()

        logger.info(
            f"Successfully processed {total_records_processed} records from file {message_data.file_id} {trace_info}."
        )

        # Send callback to indicate that the file is synced
        await syncing_task
        await callback(
            resource_id=message_data.file_id,
            project_id=message_data.project_id,
            status=CallbackStatus.SYNCED,
        )

    async def delete_document(
        self,
        message_data_dict: dict,
    ) -> None:
        message_data = DeleteMessagePayload.model_validate(message_data_dict)
        trace_info = f"(project_id: {message_data.project_id}, user_id: {message_data.user_id}, file_id: {message_data.file_id})"
        logger.info(f"Deleting file {message_data.file_id} {trace_info}.")
        await self._delete_records_by_file_id_and_project_id(
            file_id=message_data.file_id, project_id=message_data.project_id
        )
        logger.info(f"Successfully deleted file {message_data.file_id} {trace_info}.")

    async def update_document(
        self,
        message_data_dict: dict,
    ) -> None:
        message_data = UpdateMessagePayload.model_validate(message_data_dict)
        trace_info = f"(project_id: {message_data.project_id}, user_id: {message_data.user_id}, file_id: {message_data.file_id})"
        update_type = message_data.metadata.custom.get("update_type", None)
        if not update_type:
            logger.error(
                f"Update type is not provided for update file {message_data.file_id} {trace_info}."
            )
            raise HTTPException(
                status_code=400, detail="Update type is required for updating"
            )
        logger.info(
            f"Updating ({update_type}) file {message_data.file_id} {trace_info}."
        )
        if update_type == "name":
            new_name = message_data.metadata.custom.get("new_name", None)
            if not new_name:
                logger.error(
                    f"New name is not provided for update file {message_data.file_id} {trace_info}."
                )
                raise HTTPException(
                    status_code=400, detail="New name is required for updating"
                )
            records = await self._get_records_by_file_id_and_project_id(
                file_id=message_data.file_id,
                project_id=message_data.project_id,
                include_payload=True,
                include_vector=True,
            )
            logger.info(
                f"Found {len(records)} records for file {message_data.file_id} {trace_info}."
            )
            if not records:
                logger.warning(
                    f"No records found for file {message_data.file_id} {trace_info}."
                )
                return

            for record in records:
                record["payload"]["file_name"] = new_name

            logger.info(
                f"Upserting {len(records)} records for file {message_data.file_id} {trace_info}."
            )
            await self._vectorstore_adapter.upsert(
                records=records, project_id=message_data.project_id
            )
        elif update_type == "project_id":
            new_project_id = message_data.project_id
            old_project_id = message_data.metadata.custom.get("old_project_id", None)
            if not old_project_id:
                logger.error(
                    f"Old project ID is not provided for update file {message_data.file_id} {trace_info}."
                )
                raise HTTPException(
                    status_code=400, detail="Old project ID is required for updating"
                )
            records = await self._get_records_by_file_id_and_project_id(
                file_id=message_data.file_id,
                project_id=old_project_id,
                include_payload=True,
                include_vector=True,
            )
            logger.info(
                f"Found {len(records)} records for file {message_data.file_id} {trace_info}."
            )
            if not records:
                logger.warning(
                    f"No records found for file {message_data.file_id} in old project {old_project_id}. {trace_info}"
                )
                return
            logger.info(
                f"Upserting {len(records)} records for file {message_data.file_id} in project {new_project_id} {trace_info}."
            )
            await self._vectorstore_adapter.upsert(
                records=records, project_id=new_project_id
            )
            logger.info(
                f"Deleting {len(records)} records for file {message_data.file_id} in project {old_project_id} {trace_info}."
            )
            await self._delete_records_by_file_id_and_project_id(
                file_id=message_data.file_id, project_id=old_project_id
            )
            logger.info(
                f"Successfully updated file {message_data.file_id} in old project {old_project_id} to new project {new_project_id} {trace_info}."
            )

    async def delete_project(
        self,
        message_data_dict: dict,
    ) -> None:
        message_data = DeleteProjectMessagePayload.model_validate(message_data_dict)
        trace_info = (
            f"(project_id: {message_data.project_id}, user_id: {message_data.user_id})"
        )
        logger.info(f"Deleting project {message_data.project_id} {trace_info}.")
        await self._delete_project(project_id=message_data.project_id)
        logger.info(
            f"Successfully deleted project {message_data.project_id} {trace_info}."
        )

    def _convert_chunks_to_vectorstore_records(
        self, chunks: list[Chunk], embeddings: list[list[float]]
    ):
        return [
            Record(
                id=chunk.id,
                vector=embedding,
                payload={
                    **chunk.metadata.model_dump(exclude="text"),
                    "text": chunk.text,
                },
            )
            for chunk, embedding in zip(chunks, embeddings)
        ]

    def _filter_metadata_fields(self, metadata_dict: dict) -> dict:
        """Filter metadata to only include string, number, boolean, and list of strings (Pinecone only supports these types)"""
        filtered_dict = {}

        if not metadata_dict:
            return filtered_dict

        for key, value in metadata_dict.items():
            if value is None:
                continue

            # Allow string, number, boolean
            if isinstance(value, (str, int, float, bool)):
                filtered_dict[key] = value
            # Allow list of strings
            elif isinstance(value, list) and all(
                isinstance(item, str) for item in value
            ):
                filtered_dict[key] = value
            # Skip other types (dict, complex objects, etc.)
            else:
                logger.debug(
                    f"Skipping metadata field '{key}' with unsupported type: {type(value)}"
                )

        return filtered_dict

    def _validate_file(self, file: File, file_name: str):
        if file.size > config.max_file_size:
            logger.warning(
                f"File {file.file_id} is too large ({file.size} bytes). Skipping..."
            )
            raise HTTPException(
                status_code=400,
                detail=f"File {file.file_id} is too large ({file.size} bytes). Skipping...",
            )
        if file_name.split(".")[-1].lower() not in config.allowed_file_extensions:
            logger.warning(
                f"File {file.file_id} has an invalid extension ({file_name.split('.')[-1]}). Skipping..."
            )
            raise HTTPException(
                status_code=400,
                detail=f"File {file.file_id} has an invalid extension ({file_name.split('.')[-1]}). Skipping...",
            )

    async def _get_records_by_file_id_and_project_id(
        self,
        file_id: str,
        project_id: str,
        top_k: int = 10000,
        include_payload: bool = False,
        include_vector: bool = False,
    ) -> list[Record]:
        filter: Filter = {"file_id": {"$eq": file_id}}
        records = await self._vectorstore_adapter.query(
            vector=[0] * config.vector_dimension,
            top_k=top_k,
            project_id=project_id,
            filter=filter,
            include_payload=include_payload,
            include_vector=include_vector,
        )
        return records

    async def _delete_records_by_file_id_and_project_id(
        self, file_id: str, project_id: str
    ) -> None:
        filter: Filter = {"file_id": {"$eq": file_id}}
        await self._vectorstore_adapter.delete(project_id=project_id, filter=filter)

    async def _delete_project(self, project_id: str) -> None:
        await self._vectorstore_adapter.delete_project(project_id=project_id)

    async def _process_file_in_batches(
        self,
        file_content: bytes,
        file_name: str,
        metadata: Metadata,
        message_data: UploadMessagePayload,
        is_transcript: bool,
        batch_size: int = config.chunk_processing_batch_size,
    ) -> int:
        """
        Process file in batches to optimize memory usage and prevent memory leaks.

        Returns:
            int: Total number of records processed
        """
        total_records = 0
        file_type = file_name.split(".")[-1].lower()

        if is_transcript:
            # Process transcript files
            from app.utils.splitters.utterance_splitter import UtteranceSplitter
            from app.utils.extractors.utterance_extractor import UtteranceExtractor

            # Extract text from file
            file_text = await self._file_reader.read(
                file_content=file_content,
                file_name=file_name,
                mime_type=metadata.content_type,
            )

            utterance_extractor = UtteranceExtractor(
                token_util=self._token_util, llm_adapter=self._llm_adapter
            )
            utterances = await utterance_extractor.extract(
                text=file_text, metadata=metadata
            )

            utterance_splitter = UtteranceSplitter(token_util=self._token_util)
            utterances = utterance_splitter.split(chunks=utterances)

            # Clear text from memory
            del file_text
            # Clear extracted utterances from memory
            del utterance_extractor
            del utterance_splitter

            # Process utterances in batches
            total_records = await self._process_chunks_in_batches(
                chunks=utterances, message_data=message_data, batch_size=batch_size
            )
            # Release utterances list early
            del utterances

        else:
            # Process other file types
            if file_type == "pdf":
                total_records = await self._process_pdf_in_batches(
                    file_content=file_content,
                    metadata=metadata,
                    message_data=message_data,
                    batch_size=batch_size,
                )
            elif file_type == "docx":
                total_records = await self._process_docx_in_batches(
                    file_content=file_content,
                    metadata=metadata,
                    message_data=message_data,
                    batch_size=batch_size,
                )
            if not total_records:
                total_records = await self._process_text_file_in_batches(
                    file_content=file_content,
                    file_name=file_name,
                    metadata=metadata,
                    message_data=message_data,
                    batch_size=batch_size,
                )

        return total_records

    async def _process_chunks_in_batches(
        self,
        chunks: list[Chunk],
        message_data: UploadMessagePayload,
        batch_size: int = config.chunk_processing_batch_size,
    ) -> int:
        """Process chunks in batches to optimize memory usage."""
        total_records = 0
        number_of_batches = math.ceil(len(chunks) / batch_size)

        for i in range(0, len(chunks), batch_size):
            batch_chunks = chunks[i : i + batch_size]

            logger.info(
                f"Processing batch {i // batch_size + 1}/{number_of_batches} with {len(batch_chunks)} chunks for file {message_data.file_id}"
            )

            # Generate embeddings for batch
            embeddings = await self._embedding_adapter.embed_batch_of_contents(
                contents=[chunk.text for chunk in batch_chunks]
            )

            # Convert to records
            records = self._convert_chunks_to_vectorstore_records(
                chunks=batch_chunks, embeddings=embeddings
            )

            # Upsert to vectorstore
            await self._vectorstore_adapter.upsert(
                records=records, project_id=message_data.project_id
            )

            total_records += len(records)

            # Clear batch data from memory
            del batch_chunks
            del embeddings
            del records

            # Force garbage collection every few batches
            if (i // batch_size + 1) % 5 == 0:
                gc.collect()

        return total_records

    async def _process_pdf_in_batches(
        self,
        file_content: bytes,
        metadata: Metadata,
        message_data: UploadMessagePayload,
        batch_size: int = config.chunk_processing_batch_size,
    ) -> int:
        """Process PDF file in batches to optimize memory usage."""
        pdf_block_extractor = None
        try:
            pdf_block_extractor = PdfBlockExtractor()
            blocks = await pdf_block_extractor.extract(file_content)

            pdf_block_splitter = PdfBlockSplitter(token_util=self._token_util)
            chunks = pdf_block_splitter.split(blocks=blocks, metadata=metadata)

            # Clear blocks from memory
            del blocks

            total = await self._process_chunks_in_batches(
                chunks=chunks, message_data=message_data, batch_size=batch_size
            )
            # Release chunks early
            del chunks
            return total
        finally:
            if pdf_block_extractor:
                pdf_block_extractor.cleanup()

    async def _process_docx_in_batches(
        self,
        file_content: bytes,
        metadata: Metadata,
        message_data: UploadMessagePayload,
        batch_size: int = config.chunk_processing_batch_size,
    ) -> int:
        """Process DOCX file in batches to optimize memory usage."""
        docx_block_extractor = None
        try:
            docx_block_extractor = DocxBlockExtractor()
            blocks = await docx_block_extractor.extract(file_content)

            docx_block_splitter = DocxBlockSplitter(token_util=self._token_util)
            chunks = docx_block_splitter.split(blocks=blocks, metadata=metadata)

            # Clear blocks from memory
            del blocks

            total = await self._process_chunks_in_batches(
                chunks=chunks, message_data=message_data, batch_size=batch_size
            )
            # Release chunks early
            del chunks
            return total
        finally:
            if docx_block_extractor:
                docx_block_extractor.cleanup()

    async def _process_text_file_in_batches(
        self,
        file_content: bytes,
        file_name: str,
        metadata: Metadata,
        message_data: UploadMessagePayload,
        batch_size: int = config.chunk_processing_batch_size,
    ) -> int:
        """Process text file in batches to optimize memory usage."""
        file_text = await self._file_reader.read(
            file_content=file_content,
            file_name=file_name,
            mime_type=metadata.content_type,
        )

        chunk_extractor = ChunkExtractor(token_util=self._token_util)
        chunks = chunk_extractor.extract(text=file_text, metadata=metadata)

        chunk_splitter = ChunkSplitter(token_util=self._token_util)
        chunks = chunk_splitter.split(chunks)

        # Clear text from memory
        del file_text

        total = await self._process_chunks_in_batches(
            chunks=chunks, message_data=message_data, batch_size=batch_size
        )
        # Release chunks early
        del chunks
        return total
