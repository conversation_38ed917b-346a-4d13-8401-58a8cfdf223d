from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer


from app.core.config import config


class AuthService:
    def verify_token(
        token: Annotated[HTTPAuthorizationCredentials, Depends(HTTPBearer())],
    ) -> None:
        jwt_token = token.credentials
        if jwt_token != config.authentication_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
            )
