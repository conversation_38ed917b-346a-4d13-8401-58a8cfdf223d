# Extracting and Chunking Strategy by File Type

## 1. Extracting and Chunking components

### 1.1. File Reader

- `FileReader`: Read file content (bytes) and convert it to text for different file types.

### 1.2. Extractors

- `ChunkExtractor`: Extract chunks from text (based on `\n`, `\n\n`, `\n\n\n`, etc.).
- `DocxBlockExtractor`: Extract content blocks (text, table, image, etc.) from a Word document.
- `PdfBlockExtractor`: Extract content blocks (text, table, image, etc.) from a PDF file.
- `UtteranceExtractor`: Extract utterances from text (based on regex).

### 1.3. Splitters

- `ChunkSplitter`: Split chunks into smaller/bigger chunks (base on `max_chunk_size`, `min_chunk_size`, `overlap`).
- `DocxBlockSplitter`: Split DOCX blocks into chunks (merge short blocks, split long blocks into smaller ones, add `start_char` and `end_char` metadata to chunks).
- `PdfBlockSplitter`: Split PDF blocks into chunks (merge short blocks, split long blocks into smaller ones, add `page_size` metadata to chunks).
- `UtteranceSplitter`: Split utterances into chunks (add contextual window to short utterances, split long utterances into smaller ones).

## 2. Extracting and Chunking flows

### 2.1. If the file is a **transcript**:

1. **Extract text** from the file using `FileReader`.
2. Use `UtteranceExtractor` to extract individual **utterances** from the text.
   - This uses predefined regular regex expressions to extract utterances.
3. Apply `UtteranceSplitter` to **chunk/split** the utterances:
   - Add surrounding context to short utterances.
   - Split long utterances into smaller ones.
4. Convert the processed utterances into **chunks**.

---

### 2.2. If the file is **not a transcript** and is a **PDF**:

1. Use `PdfBlockExtractor` to extract **content blocks** by type (e.g., text, table, image, etc.).
   - Each block includes metadata such as `page_number`.
2. Apply `PdfBlockSplitter` to **chunk/split** these blocks:
   - Add context to short blocks.
   - Split long blocks into smaller ones.
   - Each block include metadata such as `page_number`.
3. Convert the processed blocks into **chunks**.

---

### 2.3. If the file is **not a transcript** and is a **Word document**:

1. Use `WordBlockExtractor` to extract content blocks (text, table, image, etc.).
   - Each block includes metadata such as `start_char` and `end_char`.
2. Apply `DocxBlockSplitter` to **chunk/split** these blocks:
   - Add context to short chunks.
   - Split long chunks into smaller ones.
   - Each chunk includes `start_char` and `end_char` metadata.
3. Convert the processed blocks into **chunks**.

---

### 2.4. If the file is **not a transcript** and is of **another format**:

1. Use `FileReader` to extract text from the file.
2. Use `ChunkExtractor` to extract chunks from the text.
3. Use `ChunkSplitter` to **chunk/split** the chunks into smaller/bigger chunks:
   - Add context to short chunks.
   - Split long chunks into smaller ones.
   - Each chunk includes `start_char` and `end_char` metadata.
