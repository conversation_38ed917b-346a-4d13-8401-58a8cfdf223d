# LangFuse Logging Architecture

## Overview

LangFuse is integrated into the AIDA service to provide comprehensive observability for AI conversations, RAG operations, and function calls. This document explains how the logging system works, from HTTP request initiation to trace completion in LangFuse.

## Architecture Flow

```
HTTP Request → Winston Logging → Agent Service → Tracing Service → LangFuse
     ↓              ↓                ↓              ↓              ↓
requestId    correlationId    conversationId    traceId      observations
```

## 1. Request Identification & Correlation

### HTTP Request Initiation
Every HTTP request is assigned a unique `requestId` by the Winston logging middleware:

```typescript
// src/middlewares/httpLogging.ts
private getRequestData(req: Request): Partial<HttpLogData> {
  const requestId = uuidv4(); // Generate unique ID
  (req as any).requestId = requestId; // Store on request object
  return { requestId, /* other data */ };
}
```

### Request ID Propagation
The `requestId` flows through the application layers:

```typescript
// 1. HTTP Middleware (sets requestId)
// 2. <PERSON><PERSON> extracts requestId
const requestId = (req as any).requestId || 
                 req.headers['x-request-id'] || 
                 req.headers['x-correlation-id'] || 
                 uuidv4();

// 3. Agent Service receives requestId
// 4. Tracing Service uses requestId for correlation
```

## 2. Tracing Service Architecture

### Singleton Pattern
The TracingService uses a singleton pattern for efficient resource management:

```typescript
export class TracingService {
  private static instance: TracingService;
  private langfuse: Langfuse;
  
  public static getInstance(): TracingService {
    if (!TracingService.instance) {
      TracingService.instance = new TracingService();
    }
    return TracingService.instance;
  }
}
```

### Batch Processing Optimization
Operations are batched for performance:

```typescript
interface BatchConfig {
  maxBatchSize: number;      // 10 operations per batch
  flushIntervalMs: number;   // 5 seconds auto-flush
  maxRetries: number;        // 3 retry attempts
  retryDelayMs: number;      // 1 second delay
}
```

## 3. Trace Grouping Strategy

### Conversation-Based Grouping
All operations within a conversation are grouped under a single trace:

```typescript
// Use conversationId as the primary trace ID
const traceId = metrics.conversationId;

// Individual operations become generations/spans within the trace
const generationId = requestId ? 
  `llm-${requestId}` : 
  `llm-${Date.now()}`;
```

### Trace Hierarchy
```
Conversation Trace (conversationId)
├── LLM Generation (llm-{requestId})
├── RAG Operation (rag-{requestId})
└── Function Call (func-{functionName}-{requestId})
```

## 4. Three Types of Traces

### 1. Conversation Traces
Main AI conversation with comprehensive metrics:

```typescript
public async traceConversation(metrics: ConversationMetrics, requestId?: string) {
  const trace = this.langfuse.trace({
    id: metrics.conversationId,
    userId: metrics.userId,
    input: metrics.input,
    output: metrics.output,
    metadata: {
      model: metrics.model,
      duration: metrics.duration,
      cost: metrics.cost,
      requestId: requestId,
      // ... comprehensive metrics
    }
  });
}
```

**Key Metrics Captured:**
- Token usage (input, output, total)
- Cost calculation
- Response time
- Custom instructions
- RAG usage statistics
- Citation count
- Conversation history length

### 2. RAG Operation Traces
Retrieval-Augmented Generation operations:

```typescript
public async traceRAGOperation(
  userId: string,
  conversationId: string,
  query: string,
  results: any[],
  processingTime: number,
  metadata?: Record<string, any>,
  requestId?: string
) {
  const generation = trace.generation({
    id: `rag-${requestId}`,
    name: 'rag_search',
    model: 'rag-system',
    input: query,
    output: JSON.stringify({ success: true, resultsCount: results.length })
  });
}
```

**Key Metrics Captured:**
- Query processing time
- Results count
- Success/failure status
- Relevance scores
- Search metadata

### 3. Function Call Traces
External function/tool executions:

```typescript
public async traceFunctionCall(
  userId: string,
  conversationId: string,
  functionName: string,
  functionArgs: any,
  functionResult: any,
  processingTime: number,
  success: boolean,
  error?: string,
  requestId?: string
) {
  const generation = trace.generation({
    id: `func-${functionName}-${requestId}`,
    name: `function_${functionName}`,
    model: 'function-system',
    input: JSON.stringify(functionArgs),
    output: success ? JSON.stringify(functionResult) : error
  });
}
```

**Key Metrics Captured:**
- Function processing time
- Success/failure status
- Arguments size
- Result size
- Error details

## 5. Performance Optimizations

### Batch Processing
```typescript
// Add operation to batch queue
await this.addToBatch(async () => {
  // LangFuse operation
});

// Automatic flush when batch is full or timer expires
if (this.pendingOperations.length >= this.batchConfig.maxBatchSize) {
  await this.flushBatch();
}
```

### Error Recovery
```typescript
// Skip tracing if too many recent errors
private shouldSkipTracing(): boolean {
  if (this.errorCount >= this.batchConfig.maxRetries && 
      timeSinceLastError < 60000) {
    return true;
  }
  return false;
}
```

### Async Operations
All tracing operations are non-blocking:
- No impact on response times
- Automatic retry on failures
- Graceful degradation under load

## 6. Data Flow in LangFuse

### Trace Structure
```json
{
  "id": "conversation-123",
  "userId": "user-456",
  "input": "User message",
  "output": "AI response",
  "metadata": {
    "conversationId": "conversation-123",
    "requestId": "req-789",
    "model": "gemini-pro",
    "duration": 1500,
    "cost": 0.0023,
    "inputTokens": 150,
    "outputTokens": 200,
    "totalTokens": 350,
    "hasCustomInstructions": true,
    "customInstructions": "Be helpful and concise",
    "ragUsed": true,
    "citations": 3
  }
}
```

### Generation Structure
```json
{
  "id": "llm-req-789",
  "name": "ai_response",
  "model": "gemini-pro",
  "input": "User message",
  "output": "AI response",
  "metadata": {
    "conversationId": "conversation-123",
    "requestId": "req-789",
    "finishReason": "STOP",
    "hasCustomInstructions": true
  },
  "scores": [
    {
      "name": "response_time_ms",
      "value": 1500
    },
    {
      "name": "cost_usd",
      "value": 0.0023
    },
    {
      "name": "tokens_per_second",
      "value": 233.33
    }
  ]
}
```

## 7. Monitoring & Debugging

### Batch Status Monitoring
```typescript
const status = tracingService.getBatchStatus();
console.log(status);
// {
//   pendingOperations: 5,
//   isFlushing: false,
//   errorCount: 0,
//   lastErrorTime: 0
// }
```

### Force Flush
```typescript
// Manually flush pending operations
await tracingService.forceFlush();
```

### Cleanup
```typescript
// Proper resource cleanup on shutdown
await tracingService.cleanup();
```

## 8. Configuration

### Environment Variables
```bash
# LangFuse Configuration
LANGFUSE_PUBLIC_KEY=your_public_key
LANGFUSE_SECRET_KEY=your_secret_key
LANGFUSE_BASE_URL=https://cloud.langfuse.com
```

### Batch Configuration
```typescript
const batchConfig = {
  maxBatchSize: 10,        // Operations per batch
  flushIntervalMs: 5000,   // Auto-flush every 5 seconds
  maxRetries: 3,          // Retry failed operations
  retryDelayMs: 1000      // Delay between retries
};
```

## 9. Integration Points

### Agent Stream Service
```typescript
// src/services/agent/stream/AgentStreamService.ts
if (context.requestId) {
  await tracingService.traceRAGOperation(
    context.userId,
    context.conversationId,
    query,
    results,
    processingTime,
    metadata,
    context.requestId
  );
}
```

### Message Handler
```typescript
// src/handlers/agent/createMessageStreamHandler.ts
const requestId = (req as any).requestId || 
                 req.headers['x-request-id'] || 
                 req.headers['x-correlation-id'] || 
                 uuidv4();

// Pass requestId to agent service
const result = await agentService.createMessageStream(
  conversationId,
  userId,
  body,
  requestId
);
```

## 10. Benefits

### Observability
- **End-to-end traceability**: Track requests from HTTP to AI response
- **Performance monitoring**: Response times, costs, token usage
- **Quality metrics**: Citation counts, custom instructions usage
- **Error tracking**: Function call failures, RAG errors

### Performance
- **Non-blocking operations**: No impact on response times
- **Batch processing**: Reduced API calls to LangFuse
- **Error recovery**: Automatic retry and graceful degradation
- **Resource efficiency**: Optimized memory and network usage

### Debugging
- **Request correlation**: Link logs across all services
- **Conversation grouping**: All operations under single trace
- **Detailed metadata**: Comprehensive context for each operation
- **Real-time monitoring**: Batch status and error tracking

## 11. Best Practices

### Request ID Management
- Always extract `requestId` from HTTP middleware
- Pass `requestId` through all service layers
- Use `requestId` for correlation in logs and traces

### Error Handling
- Never let tracing failures impact main application flow
- Use appropriate log levels (debug, info, warn, error)
- Monitor batch status for operational health

### Performance
- Use batch processing for high-volume operations
- Configure appropriate batch sizes for your workload
- Monitor and adjust flush intervals based on latency requirements

### Data Privacy
- Sanitize sensitive data in function arguments and results
- Be mindful of PII in custom instructions
- Use appropriate data retention policies

## 12. Troubleshooting

### Common Issues

#### Traces Not Appearing in LangFuse
1. Check LangFuse configuration (keys, base URL)
2. Verify batch status and flush operations
3. Check for error logs in application logs
4. Ensure network connectivity to LangFuse

#### High Memory Usage
1. Monitor batch size and flush frequency
2. Check for memory leaks in metadata objects
3. Review error recovery mechanisms

#### Performance Impact
1. Verify all operations are async
2. Check batch processing configuration
3. Monitor error rates and recovery times

### Debug Commands
```typescript
// Check batch status
console.log(tracingService.getBatchStatus());

// Force flush pending operations
await tracingService.forceFlush();

// Check LangFuse client
console.log(tracingService.getLangfuseClient());
```

This comprehensive logging system provides deep observability into AI conversations while maintaining high performance and reliability. 