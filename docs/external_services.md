# External Services

This document outlines all external services used by the Document Processor application, including setup instructions, required permissions, and configuration details.

First, please run the following command to create variables for the service accounts (replace the values with your own):
```bash
SERVICE_NAME="document-processor-dev"
PROJECT_ID="aida-xxxxx"
PROJECT_NUMBER="xxxxxxxxxxxx"
REGION="europe-west2"
CLOUD_RUN_URL="https://${SERVICE_NAME}-xxxxxxxxx.${REGION}.run.app"
```

To get the `PROJECT_NUMBER`, please run the following command:
```bash
gcloud projects describe $PROJECT_ID --format='get(projectNumber)'
```

## 1. Service Accounts

### 1.1. Document Processor Service Account
Create a service account for the Document Processor with the following roles:

- Logs Writer (`roles/logging.logWriter`): To let the Document Processor write logs to Cloud Logging
- Vertex AI Administrator (`roles/aiplatform.user`): To let the Document Processor use Vertex AI services

```bash
DP_ACCOUNT_NAME="${SERVICE_NAME}"
DP_SA="${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# 1. Create service account
gcloud iam service-accounts create $DP_ACCOUNT_NAME \
    --display-name="Document Processor Service Account"

# 2. Grant Logs Writer role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$DP_SA" \
    --role="roles/logging.logWriter"

# 3. Grant Vertex AI Administrator (user) role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$DP_SA" \
    --role="roles/aiplatform.user"
```

### 1.2. Invoker Service Account
Invoker Service Account is used to invoke the Document Processor service.

```bash
INVOKER_ACCOUNT_NAME="${SERVICE_NAME}-invoker"
INVOKER_SA="${INVOKER_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# 1. Create service account
gcloud iam service-accounts create $INVOKER_ACCOUNT_NAME \
    --display-name="Document Processor Invoker Service Account"

# Grant Cloud Run Invoker role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$INVOKER_SA" \
    --role="roles/run.invoker" \
    --region=$REGION
```

### 1.3. Admin Service Account

If you are admin of the project, you can use your own account to deploy the Document Processor service, create the pub/sub topics, create pub/sub subscriptions, create dead letter topics, and create scheduled jobs. If you are not, please request to the admin to create a service account that has the following roles:

- Cloud Run Admin (`roles/run.admin`): To deploy the Document Processor service
- IAM Service Account User (`roles/iam.serviceAccountUser`): To assign service accounts to the Document Processor, Pub/Sub, and Cloud Scheduler
- Pub/Sub Admin (`roles/pubsub.admin`): To create pub/sub topics, pub/sub subscriptions, and dead letter topics
- Cloud Scheduler Admin (`roles/cloudscheduler.admin`): To create scheduled jobs

```bash
ADMIN_ACCOUNT_NAME="${SERVICE_NAME}-admin"
ADMIN_SA="${ADMIN_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# 1. Create service account
gcloud iam service-accounts create $ADMIN_ACCOUNT_NAME \
    --display-name="Document Processor Admin Service Account"

# 2. Grant Cloud Run Admin role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$ADMIN_SA" \
    --role="roles/run.admin"

# 3. Grant IAM Service Account User role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$ADMIN_SA" \
    --role="roles/iam.serviceAccountUser"

# 4. Grant Pub/Sub Admin role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$ADMIN_SA" \
    --role="roles/pubsub.admin"

# 5. Grant Cloud Scheduler Admin role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$ADMIN_SA" \
    --role="roles/cloudscheduler.admin"
```


## 2. Google Cloud Run

### 2.1. Purpose

Google Cloud Run is used for deploying and hosting the Document Processor API as a containerized serverless application. It provides automatic scaling, pay-per-use pricing, and seamless integration with other Google Cloud services.

### 2.2. Setup Commands

Running the following commands with `ADMIN_SA` will deploy the Document Processor service to Cloud Run.

1. Enable the Cloud Run API:

```bash
gcloud services enable run.googleapis.com
```

2. Deploy the service:

```bash
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/aida-22a9a/document-processor:dev \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 1 \
    --env-vars-file env.run.yaml
```

Example for `env.run.yaml`:

```yaml
ENVIRONMENT: ""
CALLBACK_URL: ""
AIDA_API_KEY: ""
PINECONE_API_KEY: ""
PINECONE_DENSE_INDEX_HOST: ""
PINECONE_DENSE_INDEX_DIMENSION: ""
GOOGLE_CLOUD_PROJECT: ""
GEMINI_LOCATION: ""
LOGGER_CLOUD: ""
SA_KEY_JSON: ""
```

3. [Optional] For public access, grant the `Cloud Run Invoker` role to `allUsers`:

```bash
gcloud run services add-iam-policy-binding $SERVICE_NAME \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --region=$REGION
```

> Note: To grant the `Cloud Run Invoker` role to `allUsers`, you need `run.services.setIamPolicy` role.

## 2. Google Cloud Logging

### 2.1. Purpose

Google Cloud Logging stores log data from the Document Processor application. It provides real-time log streaming, structured logging, and integration with monitoring and alerting systems.

### 2.2. Setup Commands

Running the following commands with `ADMIN_SA` will enable the Cloud Logging API.

1. Enable the Cloud Logging API:

```bash
gcloud services enable logging.googleapis.com
```

2. Prepare a service account for the Document Processor service and add the `DP_SA` JSON key to the `env.run.yaml`:

```yaml
SA_KEY_JSON: |-
  ***********************************************************************************************************************************************************************************************************************************************************************
```

3. Enable the Cloud Logging for the Document Processor service by adding the following to the env.run.yaml:

```yaml
LOGGER_CLOUD: "true"
LOGGER_CLOUD_NAME: "document-processor-dev"
```

## 3. Google Cloud Pub/Sub

### 3.1. Purpose

Google Cloud Pub/Sub provides asynchronous messaging for the Document Processor, we will use it to send messages to the Document Processor.

### 3.4. Setup Commands

1. Enable the Pub/Sub API:

```bash
gcloud services enable pubsub.googleapis.com
```

2. Create a topic:

```bash
TOPIC_NAME="${SERVICE_NAME}-topic"
gcloud pubsub topics create $TOPIC_NAME
```

3. Create a dead letter topic to handle failed messages:

```bash
DEAD_LETTER_TOPIC_NAME="${SERVICE_NAME}-dead-letter"
gcloud pubsub topics create $DEAD_LETTER_TOPIC_NAME
```

4. Create a subscription to receive messages from the topic:

```bash
PUBSUB_SA="service-${PROJECT_NUMBER}@gcp-sa-pubsub.iam.gserviceaccount.com"
PUSH_SUBSCRIPTION_NAME="${SERVICE_NAME}-push-subscription"
gcloud pubsub subscriptions create $PUSH_SUBSCRIPTION_NAME \
    --topic $TOPIC_NAME \
    --push-endpoint "${CLOUD_RUN_URL}/document" \
    --push-auth-service-account "${INVOKER_SA}" \
    --ack-deadline 60 \
    --min-retry-delay 30s --max-retry-delay 600s \
    --dead-letter-topic $DEAD_LETTER_TOPIC_NAME \
    --max-delivery-attempts 5
```

5. Add roles to the service agent:

```bash
# 1. Grant Pub/Sub Publisher role
gcloud pubsub topics add-iam-policy-binding $DEAD_LETTER_TOPIC_NAME \
    --member="serviceAccount:$PUBSUB_SA" \
    --role="roles/pubsub.publisher"

# 2. Grant Pub/Sub Subscriber role
gcloud pubsub topics add-iam-policy-binding $TOPIC_NAME \
    --member="serviceAccount:$PUBSUB_SA" \
    --role="roles/pubsub.subscriber"
```

6. Publish a test message:

```bash
gcloud pubsub topics publish $TOPIC_NAME \
    --message "Test message" \
    --attribute "type=test,priority=high"
```

## 4. Google Cloud Scheduler

### 4.1. Purpose

Google Cloud Scheduler provides fully managed cron job scheduling for the Document Processor.

### 4.2. Setup Commands

1. Enable the Cloud Scheduler API:

```bash
gcloud services enable cloudscheduler.googleapis.com appengine.googleapis.com
```

2. Create a scheduled job to trigger HTTP endpoint:

Running the following commands with `ADMIN_SA` will create a scheduled job to trigger the HTTP endpoint.

```bash
BACKUP_JOB_NAME="${SERVICE_NAME}-backup"
gcloud scheduler jobs create http $BACKUP_JOB_NAME \
    --schedule="0 0 * * *" \
    --http-method=POST \
    --location=$REGION \
    --uri="${CLOUD_RUN_URL}/backup" \
    --oidc-service-account-email="${INVOKER_SA}" \
    --oidc-token-audience="${CLOUD_RUN_URL}"
```

## 5. Google Gemini API

### 5.1. Purpose

Google Gemini API provides large language model capabilities and embedding generation for the Document Processor.

### 5.2. Setup Commands

1. Enable the Vertex AI API:

```bash
gcloud services enable aiplatform.googleapis.com
```

2. Prepare a service account for the Document Processor service and add the `DP_SA` JSON key to the `env.run.yaml`:

```yaml
SA_KEY_JSON: |-
  ***********************************************************************************************************************************************************************************************************************************************************************
```

## 6. Pinecone

### 6.1. Purpose

Pinecone serves as the vector database for the Document Processor, storing and indexing document embeddings for semantic search, similarity matching, and retrieval-augmented generation (RAG) operations.

### 6.2. Setup Instructions (Web Interface)

1. **Create a new Project**:

   - Go to [pinecone.io](https://pinecone.io) and sign up
   - Verify your email and complete account setup
   - Create a new project or use the default one

2. **Generate API Key**:

   - Navigate to the "API Keys" section in the left sidebar
   - Click "Create API Key"
   - Enter the API Key name and click "Create API Key"
   - Copy and securely store the API key

3. **Create an Index**:

   - Go to "Database" in the left sidebar
   - Click "Create Index"
   - Enter the index name 
   - Select the **Custom settings**
   - Enter the Dimension (for most `gemini-embedding-001` it's `3072`) and metric (for most cases it's `cosine`)
   - Select capacity mode: `Serverless`
   - Select cloud provider: `Google Cloud` and select the region (e.g. `europe-west4`)
   - Click "Create Index"
   - Copy the host of the index 

### 6.3. Environment Configuration

Set the following environment variables in the `env.run.yaml`:

```
PINECONE_API_KEY=""
PINECONE_DENSE_INDEX_HOST=""
```
