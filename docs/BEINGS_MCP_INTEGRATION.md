# Beings MCP Integration

This document describes the integration of the Beings RAG MCP server with the AIDA backend service.

## Overview

The Beings MCP (Model Context Protocol) server provides a custom RAG (Retrieval-Augmented Generation) service that can be used as an alternative or complement to the existing Pinecone integration.

## Configuration

### Feature Flag

The Beings RAG server is controlled by a PostHog feature flag:

- **Feature Flag Name**: `beings_rag`
- **Default Value**: `false` (disabled by default)
- **Scope**: Per-user (can be enabled for specific users)

### Environment Variables

Add these environment variables for the Beings RAG service configuration:

```bash
# Beings RAG service URL (optional, has default)
BEINGS_RAG_SERVICE_URL=https://rag-service-1077482976117.europe-west1.run.app

# Beings MCP auth token (optional, has default)
BEINGS_MCP_AUTH_TOKEN=your_auth_token_here

# Beings timeout (optional, defaults to 30000ms)
BEINGS_TIMEOUT=30000
```

### Default Configuration

The Beings MCP server is configured with:
- **Base URL**: `https://rag-service-1077482976117.europe-west1.run.app`
- **RAG Endpoint**: `${BEINGS_RAG_SERVICE_URL}/retrieve`
- **Timeout**: 30 seconds (configurable via `BEINGS_TIMEOUT`)
- **Feature Flag**: `beings_rag` (enables per-user)
- **Priority**: 1 (when enabled, replaces Pinecone)

## Architecture

### Components

1. **BeingsMCPToolProvider** (`src/services/agent/tools/providers/BeingsMCPToolProvider.ts`)
   - Concrete implementation of MCP tool provider
   - Handles connection to Beings RAG server
   - Formats responses for Google GenAI consumption

2. **Beings RAG Service** (`src/services/beings/index.ts`)
   - Core service implementation
   - Handles API calls to Beings RAG server
   - Provides standardized response format

3. **ToolFactory Integration** (`src/services/agent/tools/ToolFactory.ts`)
   - Added `beings-mcp` provider type
   - Created `createBeingsMCPToolFactory()` function
   - Configurable provider management

4. **Context Manager** (`src/services/agent/context/ContextManager.ts`)
   - Checks PostHog feature flag for each user
   - Dynamically enables Beings MCP based on feature flag
   - Falls back to default tools if feature flag is disabled
   - Maintains existing functionality

### Tool Interface

The Beings MCP server exposes the following tool:

#### `retrieve_relevant_chunks`
- **Purpose**: Semantic search and chunk retrieval across user documents
- **Input Parameters**:
  - `query` (string): Search query
  - `userId` (string): User ID for access control
  - `projectId` (string, optional): Project ID for filtering
  - `fileIds` (array, optional): Specific file IDs to search
  - `limit` (number, optional): Maximum number of results

- **Response Format**:
  ```json
  {
      "success": true,
      "message": "Successfully retrieved chunks from Pinecone",
      "processing_time_ms": 204,
      "data": {
          "chunks": [
              {
                  "id": "ff3df6cf-16f4-4b12-89fe-83669b77fc88",
                  "text": "&lt;a?irmative>. Nice to meet you [Iga]. Uh, could you please, uh, so we can start by basically, uh, knowing a little bit more about your job. Please tell me a bit more about your, uh, job and day-to-Day work, what kind of work you do, and then I can start asking, uh, more questions. [Speaker 0] (01:34 - 02:02):\nYeah, sure. So, um, I'm a researcher. Uh, my day-to-Day involves writing reports and interviewing clients, identifying the market trends, and then the problems that, um, executive leaders have when it comes to executing their goals and finding solutions that can help them execute either in a cheaper way or faster way or smarter way. [Speaker 1] (02:04 - 02:13):\n\nOkay. --- Chunk 3 ---Very good. --- Chunk 4 ---Very interesting. And, uh, how much, uh, artificial intelli- gence you, uh, you at your job for various project? [Speaker 0] (02:15 - 02:29):\nUm, not very much at the moment. Um, we, I use some internally, but when it comes to external AI tools, not at all. [Speaker 1] (02:32 - 02:45):\nUh, what, what it, was it, because, uh, the rise of AI is basically happening very recently, or you feel like, uh, there is not enough tool available in the market that could help you? [Speaker 0] (02:46 - 03:28):\nYeah, I think, Hmm. I think the type of the job I'm doing, um, probably does not fit into AI tools that exist that, well, that is one argument. And the argument is, um, obviously we are restricted, uh, what we can use as well, because a lot of the data that we possess is, um, very much protected. So there's limited amount of actions that we can do with the data that we have, which involves tools external to the company systems. [Speaker 1] (03:30 - 03:45):\nRight. --- Chunk 5 ---Thanks.",
                  "metadata": {
                      "file_id": "01K0XMH9N33RDCDVC8GTH4D2TW",
                      "file_name": "resources/chunks-gemini-69e06984-5162-4da0-95c1-db73a9dfb3d9.txt",
                      "file_type": "txt",
                      "file_size": 30283,
                      "created_at": 1753352536.37487,
                      "project_id": "01K0RAZEQG209RNQWSDCTTTGCH",
                      "user_id": "ZUdrnUdKL5VeXOWOVfn1BqLxBvI3",
                      "end_sec": 2.0,
                      "speaker": "0",
                      "start_sec": 1.0,
                      "tokens": 469.0
                  },
                  "score": 0.*********
              }
          ]
      }
  }
  ```

## Usage

### Enabling the Integration

1. **Enable the feature flag in PostHog**:
   - Navigate to your PostHog dashboard
   - Create or update the `beings_rag` feature flag
   - Enable it for specific users or groups

2. **Restart the AIDA service** (if needed for configuration changes)

3. The system will automatically:
   - Check the feature flag for each user during context building
   - Initialize the Beings MCP provider for users with the flag enabled
   - Discover available tools
   - Make tools available to the AI agent


### Monitoring

The integration includes comprehensive logging:

- **Feature Flag Checks**: Logs when the `beings_rag` feature flag is checked
- **Connection**: Logs connection attempts and results
- **Tool Discovery**: Logs discovered tools from the MCP server
- **Tool Calls**: Logs each tool call with performance metrics
- **Errors**: Detailed error logging for debugging

## Fallback Behavior

If the Beings RAG feature flag is disabled or the server is unavailable:

1. **Graceful Degradation**: The system continues to work with Pinecone
2. **Error Handling**: Tool calls return appropriate error messages
3. **Logging**: All failures are logged for monitoring
4. **User Experience**: Users get helpful error messages

## Performance Considerations

- **Feature Flag Checks**: Feature flag evaluation on each context build
- **Connection Timeout**: 30 seconds for initial connection
- **Tool Call Timeout**: 30 seconds for individual tool calls
- **Fallback Priority**: Pinecone is used as fallback when Beings is disabled
- **Caching**: No caching implemented (future enhancement)

## Troubleshooting

### Common Issues

1. **Feature Flag Not Working**
   - Check if the `beings_rag` feature flag exists in PostHog
   - Verify the flag is enabled for the specific user
   - Check PostHog configuration and API key

2. **Connection Failed**
   - Check if `BEINGS_RAG_SERVICE_URL` is correct
   - Verify the server is accessible
   - Check network connectivity

3. **Tool Discovery Failed**
   - Verify the MCP server implements the expected interface
   - Check server logs for errors
   - Ensure the `retrieve_relevant_chunks` tool is available

4. **Tool Calls Failing**
   - Check input parameter format
   - Verify user/project permissions
   - Review server logs for detailed errors



## Future Enhancements

1. **Additional Tools**: Support for more MCP tools
2. **Caching**: Implement response caching for better performance
3. **Load Balancing**: Support for multiple MCP server instances
4. **Metrics**: Detailed performance and usage metrics
5. **Configuration UI**: Web interface for MCP server configuration

## Security Considerations

- **Access Control**: User and project-based filtering
- **Input Validation**: All inputs are validated before sending to MCP server
- **Error Handling**: Sensitive information is not exposed in error messages
- **Logging**: Careful logging to avoid exposing sensitive data
- **Feature Flag Security**: PostHog feature flags provide secure, auditable access control 