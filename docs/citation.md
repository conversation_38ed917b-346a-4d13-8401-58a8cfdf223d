# Citation Strategy by File Type

## 1. Citation Components

### 1.1. Transcript Files Citation

- **Type**: Time-based citation
- **Fields**: `start_sec` (required), `end_sec` (optional)
- **Description**: Citation refers to the timestamp when a speaker uttered the content
- **Format**: Seconds from the beginning of the audio/video file

### 1.2. PDF Files Citation

- **Type**: Page-based citation
- **Fields**: `page_number`
- **Description**: Citation refers to the page number where the chunk content is located
- **Format**: Integer representing the page number (0-indexed)

### 1.3. Other File Types Citation

- **Type**: Character-based citation
- **Fields**: `start_char`, `end_char`
- **Description**: Citation refers to the character positions (indices) in the original text where the chunk begins and ends
- **Format**: Integer representing character position in the document

## 2. Citation Extraction and Processing Flow

### 2.1. For **Transcript** files:

1. **Citation Extraction** by `UtteranceExtractor`:

   - Uses regex patterns to extract utterances with timestamps
   - Pattern 1: `[Speaker N] (HH:MM - HH:MM): content` → extracts `speaker`, `start_sec`, `end_sec`, `text`
   - Pattern 2: `Speaker N HH:MM:SS content` → extracts `speaker`, `start_sec`, `text`
   - Converts time format (HH:MM:SS or HH:MM) to seconds using `parse_time_to_seconds`

2. **Citation Preservation** by `UtteranceSplitter`:
   - When combining utterances from the same speaker: updates `end_sec` to the last utterance's `end_sec`
   - When splitting long utterances: preserves original `start_sec` and `end_sec` in all sub-chunks
   - When adding contextual window: preserves the target utterance's citation metadata

**Example Citation Output:**

```json
{
  "start_sec": 125.5,
  "end_sec": 130.2,
  "speaker": "1"
}
```

---

### 2.2. For **PDF** files:

1. **Citation Extraction** by `PdfBlockExtractor`:

   - Each extracted block (text, table, image) includes `page_number` from the PDF structure
   - Page numbers are 0-indexed (first page = 0)
   - All blocks from the same page share the same `page_number`

2. **Citation Preservation** by `PdfBlockSplitter`:
   - When merging short blocks: uses `page_number` from the first block
   - When splitting long blocks: all sub-chunks inherit the original block's `page_number`
   - Tables and images that cannot be split retain their original `page_number`

**Example Citation Output:**

```json
{
  "page_number": 3
}
```

---

### 2.3. For **Word Document (.docx)** files:

1. **Citation Calculation** by `DocxBlockSplitter`:

   - Calculates `start_char` and `end_char` positions for each block relative to the document start
   - Character positions are tracked incrementally as blocks are processed
   - When splitting long text blocks: calculates precise character positions for each sub-chunk using `text.find()` method
   - When merging short blocks: spans character positions from the first block's start to the last block's end

2. **Character Position Tracking**:
   - `start_char`: Beginning character index of the chunk in the original document
   - `end_char`: Ending character index of the chunk in the original document
   - Positions account for overlap when splitting long blocks

**Example Citation Output:**

```json
{
  "start_char": 1250,
  "end_char": 1687
}
```

---

### 2.4. For **Other file formats** (plain text, etc.):

1. **Citation Extraction** by `ChunkExtractor`:

   - Initial chunks extracted from text using delimiters (`\n`, `\n\n`, `\n\n\n`)
   - Basic `start_char` and `end_char` positions are established during extraction

2. **Citation Refinement** by `ChunkSplitter`:
   - Calculates precise character positions when splitting long chunks using sentence boundaries
   - Uses original chunk's `start_char` as base and finds relative positions using `text.find()` method
   - When merging short chunks: spans character positions from the first chunk's start to the last chunk's end
   - Handles overlap by adjusting character offsets to maintain accurate citation references

**Example Citation Output:**

```json
{
  "start_char": 2840,
  "end_char": 3195
}
```

## 3. Citation Use Cases

### 3.1. Document Retrieval

- Citations enable users to locate the exact source of retrieved information
- For transcripts: jump to specific timestamps in audio/video
- For PDFs: navigate to specific pages
- For other documents: highlight exact text positions

### 3.2. Context Preservation

- Citations maintain the relationship between chunks and their original location
- Enable reconstruction of document structure and context
- Support accurate attribution and source verification

### 3.3. Search and Navigation

- Time-based citations allow temporal navigation in multimedia content
- Page-based citations enable quick PDF navigation
- Character-based citations support precise text highlighting and selection
