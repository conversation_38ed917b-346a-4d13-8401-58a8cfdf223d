# Frontend Compatibility Guide

## 🎯 **Zero Impact Guarantee**

The multi-provider implementation is **completely transparent** to the frontend. All existing code continues to work without any changes.

## 🔌 **API Interface (✅ UNCHANGED)**

### **Request Format**

```typescript
// ✅ SAME REQUEST STRUCTURE
POST /api/agent/stream
Content-Type: application/json

{
  "model": "gemini-2.5-flash" | "gpt-4o" | "claude-3-5-sonnet-20241022", // ← Only change: new model options
  "message": "User message",
  "conversationId": "conversation-id",
  "resources": ["file-id-1", "file-id-2"],  // ✅ Same optional resources
  "images": [/* image data */],              // ✅ Same optional images
  "preferences": {                           // ✅ Same optional preferences
    "instructions": "Custom instructions"
  }
}
```

### **Response Format**

```typescript
// ✅ IDENTICAL SSE STREAM STRUCTURE
data: {"type":"connection","status":"connected"}

data: {"type":"start","conversationId":"123","timestamp":**********}

data: {"type":"content","content":"Hello","usage":{"inputTokens":10,"outputTokens":5}}

data: {"type":"progress","status":"analyzing"}

data: {"type":"progress","status":"searching","details":{"fileCount":3}}

data: {"type":"content","content":" world","usage":{}}

data: {"type":"progress","status":"synthesizing"}

data: {"type":"complete","content":"","finishReason":"STOP","usage":{"totalTokens":50}}
```

## 🛠️ **Tool Calling Flow (✅ UNCHANGED)**

### **Same Tool Execution Pattern**

```typescript
// ✅ IDENTICAL TOOL CALLING EXPERIENCE
// Whether using Google, OpenAI, or Anthropic:

1. User: "Search for information about AI"
2. Stream: data: {"type":"progress","status":"analyzing"}
3. Stream: data: {"type":"progress","status":"searching","details":{"fileCount":5}}
4. Stream: data: {"type":"content","content":"Based on the search results..."}
5. Stream: data: {"type":"progress","status":"synthesizing"}
6. Stream: data: {"type":"complete","finishReason":"STOP"}
```

### **Same Citation Format**

```typescript
// ✅ SAME RAG RESULTS STRUCTURE
data: {
  "type": "content",
  "content": "Based on the documents...",
  "citations": [
    {
      "source": "document-1.pdf",
      "page": 5,
      "snippet": "Relevant text excerpt"
    }
  ],
  "usage": { "inputTokens": 100, "outputTokens": 50 }
}
```

## 🎨 **Frontend Model Selection**

### **Model Dropdown Update**

```typescript
// ✅ SIMPLE FRONTEND UPDATE - Just add new options
const modelOptions = [
  // Existing Google models
  { value: "gemini-2.5-flash", label: "Gemini 2.5 Flash", provider: "Google" },
  { value: "gemini-2.5-pro", label: "Gemini 2.5 Pro", provider: "Google" },

  // New OpenAI models
  { value: "gpt-4o", label: "GPT-4o", provider: "OpenAI" },
  { value: "gpt-4o-mini", label: "GPT-4o Mini", provider: "OpenAI" },

  // New Anthropic models
  {
    value: "claude-3-5-sonnet-20241022",
    label: "Claude 3.5 Sonnet",
    provider: "Anthropic",
  },
  {
    value: "claude-3-5-haiku-20241022",
    label: "Claude 3.5 Haiku",
    provider: "Anthropic",
  },
];
```

### **Same Event Handlers**

```typescript
// ✅ NO CHANGES TO EVENT HANDLING CODE
const handleStreamResponse = (event: MessageEvent) => {
  const data = JSON.parse(event.data);

  switch (data.type) {
    case "connection":
      setConnectionStatus("connected");
      break;
    case "content":
      appendContent(data.content);
      updateUsage(data.usage);
      break;
    case "progress":
      updateProgress(data.status); // Same progress events
      break;
    case "complete":
      setComplete(true);
      break;
  }
};
```

## 🚨 **Error Handling (✅ UNCHANGED)**

### **Same Error Response Structure**

```typescript
// ✅ IDENTICAL ERROR FORMAT
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED" | "INVALID_MODEL" | "CONTEXT_TOO_LONG",
    "message": "Human-readable error message",
    "details": {
      "provider": "openai",
      "model": "gpt-4o",
      "retryAfter": 60
    }
  }
}
```

### **Same Error Handling Code**

```typescript
// ✅ NO CHANGES TO ERROR HANDLING
const handleError = (error: ApiError) => {
  switch (error.code) {
    case "RATE_LIMIT_EXCEEDED":
      showRetryMessage(error.details.retryAfter);
      break;
    case "INVALID_MODEL":
      showModelSelectionError();
      break;
    case "CONTEXT_TOO_LONG":
      showContextLimitError();
      break;
  }
};
```

## 📊 **Usage & Billing Display (✅ UNCHANGED)**

### **Same Usage Tracking**

```typescript
// ✅ SAME USAGE STRUCTURE
{
  "type": "content",
  "content": "Response text",
  "usage": {
    "inputTokens": 100,
    "outputTokens": 50,
    "totalTokens": 150,
    "imageTokens": 20  // If images processed
  }
}
```

### **Same Billing Information**

```typescript
// ✅ SAME BILLING DISPLAY CODE
const calculateCost = (usage, model) => {
  // Same calculation logic works across all providers
  return usage.totalTokens * getModelRate(model);
};

const displayUsage = (usage) => {
  // Same display components work for all providers
  return (
    <UsageDisplay
      inputTokens={usage.inputTokens}
      outputTokens={usage.outputTokens}
      cost={calculateCost(usage, selectedModel)}
    />
  );
};
```

## ✅ **Migration Checklist for Frontend**

### **Required Changes** (Minimal)

- [ ] Add new model options to model selection dropdown
- [ ] Update model labels/descriptions if desired
- [ ] Test with new models to verify identical behavior

### **No Changes Required**

- [ ] ✅ API endpoint URLs
- [ ] ✅ Request/response handling code
- [ ] ✅ Stream parsing logic
- [ ] ✅ Error handling code
- [ ] ✅ Progress event handling
- [ ] ✅ Tool calling UI components
- [ ] ✅ Citation display components
- [ ] ✅ Usage/billing display code
- [ ] ✅ Authentication/headers
- [ ] ✅ Conversation threading logic

## 🧪 **Frontend Testing**

### **Test Same Functionality Across Models**

```typescript
// ✅ SAME TESTS, DIFFERENT MODELS
const testCases = [
  { model: "gemini-2.5-flash", message: "Hello" },
  { model: "gpt-4o", message: "Hello" },
  { model: "claude-3-5-sonnet-20241022", message: "Hello" },
];

testCases.forEach(({ model, message }) => {
  test(`${model} should stream response`, async () => {
    const response = await sendMessage(model, message);

    // ✅ Same assertions work for all models
    expect(response.type).toBe("content");
    expect(response.content).toBeTruthy();
    expect(response.usage).toBeDefined();
  });
});
```

---

**Summary**: The frontend experiences **zero breaking changes**. The multi-provider implementation is completely transparent, with only new model options added to the existing interface.

**Deployment**: Frontend can be deployed independently - no coordination required with backend changes.
