# 🌊 Streaming Architecture - Multi-Provider Agent Service

## 🎯 **Overview**

The streaming architecture enables real-time AI responses across multiple providers (Google, OpenAI, Anthropic) while maintaining a unified interface for the frontend.

---

## 🔄 **Streaming Flow Architecture**

```
Frontend SSE Connection
         ↓
┌─────────────────────────────────────────────────────────────┐
│                AgentStreamService                           │
│  • Context building    • Stream processing                 │
│  • SSE formatting      • Tool execution                    │
└─────────────────┬───────────────────────────────────────────┘
                  │ AgentStream Format
                  ▼
┌─────────────────────────────────────────────────────────────┐
│                RequestExecutor                              │
│         (Format Conversion Layer)                          │
│  AgentStream ↔ Universal ↔ Provider-Specific             │
└─────────────────┬───────────────────────────────────────────┘
                  │ Universal Format
                  ▼
┌─────────────────────────────────────────────────────────────┐
│              ProviderRegistry                               │
│               (Model Routing)                              │
└─┬─────────────┬─────────────┬─────────────────────────────────┘
  │             │             │
  ▼             ▼             ▼
┌─────────┐ ┌─────────┐ ┌─────────┐
│ Google  │ │ OpenAI  │ │Anthropic│
│Provider │ │Provider │ │Provider │
└─────────┘ └─────────┘ └─────────┘
     │           │           │
     ▼           ▼           ▼
┌─────────┐ ┌─────────┐ ┌─────────┐
│Google AI│ │OpenAI   │ │Anthropic│
│   API   │ │   API   │ │   API   │
└─────────┘ └─────────┘ └─────────┘
```

---

## 📋 **Format Specifications**

### **1. Frontend SSE Format**

**Purpose**: What the frontend receives (unchanged from original)

```
data: {"type":"chunk","content":"Hello "}

data: {"type":"chunk","content":"world!"}

data: {"type":"tool_call","name":"search","args":{"query":"AI"}}

data: {"type":"tool_result","result":"Search results..."}

data: {"type":"completion","usage":{"inputTokens":100,"outputTokens":50}}

data: [DONE]
```

### **2. AgentStream Format**

**Purpose**: Internal format expected by AgentStreamService

```typescript
interface GenerateContentResponse {
  candidates: [
    {
      content: {
        parts: [{ text?: string; functionCall?: FunctionCall }];
      };
      finishReason: "STOP" | "MAX_TOKENS" | "SAFETY" | null;
    }
  ];
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
  functionCalls?: FunctionCall[];
}
```

### **3. Universal Format**

**Purpose**: Standardized communication between providers

```typescript
interface UniversalResponse {
  provider: "google" | "openai" | "anthropic";
  model: string;
  content: string;
  usage: TokenUsage;
  finishReason: "stop" | "length" | "content_filter" | "continue";
  metadata: { timestamp: string };
  toolCalls?: UniversalToolCall[];
}
```

### **4. Provider-Specific Formats**

#### **Google GenAI Format**

```typescript
interface GoogleResponse {
  candidates: [
    {
      content: { parts: [{ text: string }] };
      finishReason: "STOP" | "MAX_TOKENS" | "SAFETY";
    }
  ];
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}
```

#### **OpenAI Format**

```typescript
interface OpenAIResponse {
  choices: [
    {
      delta: { content: string };
      finish_reason: "stop" | "length" | "content_filter";
    }
  ];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
```

#### **Anthropic Format**

```typescript
interface AnthropicResponse {
  delta: { text: string };
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
}
```

---

## 🔄 **Conversion Chain Details**

### **1. Request Conversion**

```
Frontend Request
    ↓ (HTTP POST)
AgentStreamService.generateStreamingResponse()
    ↓ (builds context, system instructions)
RequestExecutor.executeStreamingRequest()
    ↓ (converts to Universal format)
ProviderRegistry.generateStream()
    ↓ (routes to provider)
Provider.executeRequest()
    ↓ (converts to native API format)
Native API Call
```

### **2. Response Conversion**

```
Native API Stream
    ↓ (provider-specific chunks)
Provider.createUniversalStream()
    ↓ (Universal JSON chunks)
RequestExecutor.convertProviderStreamToAgentFormat()
    ↓ (AgentStream format)
AgentStreamService.processStreamChunks()
    ↓ (SSE formatting)
Frontend SSE Stream
```

---

## 🔧 **Critical Conversion Points**

### **1. Universal Stream Creation**

**Location**: `BaseProvider.createUniversalStream()`
**Purpose**: Convert native provider streams to Universal format

```typescript
protected createUniversalStream(
  nativeStream: any,
  model: string,
  extractContent: (chunk: any) => string | null,
  extractUsage: (chunk: any) => TokenUsage | null,
  extractFinishReason: (chunk: any) => FinishReason | null,
  extractToolCalls: (chunk: any) => UniversalToolCall[] | null
): ReadableStream {
  return new ReadableStream({
    async start(controller) {
      for await (const chunk of nativeStream) {
        const universalChunk: UniversalResponse = {
          provider: this.name,
          model,
          content: extractContent(chunk) || "",
          usage: extractUsage(chunk) || defaultUsage,
          finishReason: extractFinishReason(chunk) || "continue", // ⚠️ CRITICAL
          metadata: { timestamp: new Date().toISOString() },
          toolCalls: extractToolCalls(chunk) || undefined,
        };

        controller.enqueue(encoder.encode(JSON.stringify(universalChunk) + "\n"));
      }
    }
  });
}
```

### **2. AgentStream Format Conversion**

**Location**: `RequestExecutor.convertProviderStreamToAgentFormat()`
**Purpose**: Convert Universal format back to AgentStream format

```typescript
private async *convertProviderStreamToAgentFormat(
  providerStream: ReadableStream
): AsyncGenerator<GenerateContentResponse> {
  const reader = providerStream.getReader();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const universalResponse = JSON.parse(decoder.decode(value));

    // Convert to AgentStream format
    yield {
      candidates: [{
        content: {
          parts: universalResponse.content ? [{ text: universalResponse.content }] : []
        },
        finishReason: this.mapFinishReason(universalResponse.finishReason) // ⚠️ CRITICAL
      }],
      usageMetadata: universalResponse.usage ? {
        promptTokenCount: universalResponse.usage.inputTokens,
        candidatesTokenCount: universalResponse.usage.outputTokens,
        totalTokenCount: universalResponse.usage.totalTokens
      } : undefined
    };
  }
}
```

### **3. Finish Reason Mapping**

**Location**: `RequestExecutor.mapFinishReason()`
**Purpose**: Map Universal finish reasons to AgentStream format

```typescript
private mapFinishReason(reason: FinishReason): string | null {
  const reasonMap = {
    "stop": "STOP",
    "length": "MAX_TOKENS",
    "content_filter": "SAFETY",
    "error": "OTHER",
    "continue": null // ⚠️ CRITICAL: Don't terminate stream
  };
  return reasonMap[reason] || null;
}
```

---

## ⚠️ **Known Issues & Fixes**

### **Issue 1: Premature Stream Termination**

**Problem**: Streams terminate before complete response
**Root Cause**: Default finish reason mapping to "stop"
**Fix Applied**:

```typescript
// BEFORE (problematic)
finishReason: finishReason || "stop";

// AFTER (fixed)
finishReason: finishReason || "continue";
```

### **Issue 2: Multi-Part Content Loss**

**Problem**: Google responses with multiple parts only show first part
**Root Cause**: Only extracting `parts[0].text`
**Fix Applied**:

```typescript
// BEFORE (problematic)
(chunk) => chunk.candidates?.[0]?.content?.parts?.[0]?.text || null

// AFTER (fixed)
(chunk) => {
  const parts = chunk.candidates?.[0]?.content?.parts;
  if (!parts) return null;

  let allText = "";
  for (const part of parts) {
    if (part.text) allText += part.text;
  }
  return allText || null;
}
```

### **Issue 3: Token Usage Timing**

**Problem**: Usage metadata not available in all chunks
**Root Cause**: Providers send usage at different times
**Current Handling**:

```typescript
usage: totalUsage || {
  inputTokens: 0,
  outputTokens: 0,
  totalTokens: 0,
};
```

---

## 🔍 **Debugging Stream Issues**

### **1. Enable Stream Debugging**

```typescript
// In AgentStreamService
private debugStream = true; // Enable debug logging

async processStreamChunks(responseStream, controller, context) {
  if (this.debugStream) {
    console.log("Starting stream processing...");
  }

  for await (const chunk of responseStream) {
    if (this.debugStream) {
      console.log("Received chunk:", JSON.stringify(chunk, null, 2));
    }
    // ... process chunk
  }
}
```

### **2. Monitor Conversion Points**

```bash
# Watch for conversion issues
tail -f logs/app.log | grep -E "(convertProviderStreamToAgentFormat|createUniversalStream)"

# Check finish reason handling
tail -f logs/app.log | grep "finishReason"

# Monitor stream termination
tail -f logs/app.log | grep -E "(stream.*complete|DONE|STOP)"
```

### **3. Validate Stream Integrity**

```typescript
// Add stream validation
private validateStreamChunk(chunk: any): boolean {
  if (!chunk.candidates || !chunk.candidates[0]) {
    console.warn("Invalid chunk structure:", chunk);
    return false;
  }

  const candidate = chunk.candidates[0];
  if (candidate.finishReason === "STOP" && candidate.content?.parts?.length === 0) {
    console.warn("Premature termination detected");
    return false;
  }

  return true;
}
```

---

## 📊 **Performance Considerations**

### **Stream Processing Overhead**

- **Conversion Latency**: ~1-5ms per chunk conversion
- **Memory Usage**: Minimal buffering (streaming design)
- **CPU Usage**: JSON parsing and format conversion

### **Optimization Strategies**

1. **Chunk Batching**: Process multiple small chunks together
2. **Format Caching**: Cache conversion results for repeated patterns
3. **Stream Pooling**: Reuse stream processing resources
4. **Error Recovery**: Graceful handling of malformed chunks

---

## 🎯 **Testing Stream Architecture**

### **Unit Tests**

```typescript
describe("Stream Conversion", () => {
  test("Universal to AgentStream conversion", async () => {
    const universalChunk = {
      provider: "google",
      content: "Hello world",
      finishReason: "continue",
    };

    const agentChunk = await convertToAgentFormat(universalChunk);
    expect(agentChunk.candidates[0].finishReason).toBeNull();
  });
});
```

### **Integration Tests**

```typescript
describe("End-to-End Streaming", () => {
  test("Complete response from each provider", async () => {
    for (const model of ["gemini-2.5-flash", "gpt-4o", "claude-3.5-sonnet"]) {
      const response = await streamRequest({ message: "Hello", model });
      expect(response).toContainCompleteResponse();
    }
  });
});
```

---

## 📚 **Related Documentation**

- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Overall system architecture
- **[IMPLEMENTATION_STATUS.md](./IMPLEMENTATION_STATUS.md)** - Current progress and issues
- **[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)** - Debugging guide
- **[TOOL_CALLING.md](./TOOL_CALLING.md)** - Tool calling in streams
