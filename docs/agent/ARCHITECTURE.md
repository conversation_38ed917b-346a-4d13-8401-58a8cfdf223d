# 🏗️ AIDA Agent Service - Multi-Provider Architecture

## 🎯 **System Overview**

AIDA Agent Service implements a **universal multi-provider AI architecture** that supports Google Gemini, OpenAI GPT, and Anthropic Claude models through a unified streaming interface.

### **🔒 Zero Frontend Impact Guarantee**

- ✅ **API Interface**: Identical `/api/agent/stream` endpoint
- ✅ **Request Format**: Same request structure and parameters
- ✅ **Response Format**: Same SSE stream format (`data: {...}\n\n`)
- ✅ **Tool Calling**: Same tool execution flow and results
- ✅ **Error Handling**: Same error response format
- ✅ **Context Logic**: Same file processing, transcripts, images

**The frontend experiences zero changes - completely transparent!**

---

## 🏗️ **Architecture Layers**

```
┌─────────────────────────────────────────────────────────────┐
│                      Frontend (React)                      │
│                   Zero Impact Guarantee                    │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP POST /api/agent/stream
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                   AgentController                          │
│              (Express Route Handler)                       │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                 AgentStreamService                         │
│        (Context Building, SSE Streaming)                  │
│   • File processing  • System instructions               │
│   • Tool management   • Stream formatting                │
└─────────────────────┬───────────────────────────────────────┘
                      │ AgentStream Format
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  RequestExecutor                           │
│               (Format Conversion Layer)                    │
│   AgentStream ↔ Universal ↔ Provider-Specific           │
└─────────────────────┬───────────────────────────────────────┘
                      │ Universal Format
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                 ProviderRegistry                           │
│                (Provider Routing)                          │
│   Routes requests to appropriate AI provider              │
└─────┬───────────────┬───────────────┬─────────────────────────┘
      │               │               │
      ▼               ▼               ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│   Google    │ │   OpenAI    │ │  Anthropic  │
│  Provider   │ │  Provider   │ │  Provider   │
│             │ │             │ │             │
│ Native API  │ │ Native API  │ │ Native API  │
└─────────────┘ └─────────────┘ └─────────────┘
```

---

## 🔄 **Data Flow & Format Conversion**

### **1. Request Flow**

```
Frontend Request (HTTP)
    ↓
AgentStreamService (builds context, system instructions)
    ↓ (AgentStream Format)
RequestExecutor (converts AgentStream → Universal)
    ↓ (Universal Format)
ProviderRegistry (routes by model)
    ↓ (Universal Format)
Specific Provider (converts Universal → Native API)
    ↓ (Native API Format)
AI Service (Google/OpenAI/Anthropic)
```

### **2. Response Flow**

```
AI Service (Native Response Stream)
    ↓
Provider (converts Native → Universal chunks)
    ↓ (Universal JSON chunks)
RequestExecutor (converts Universal → AgentStream)
    ↓ (AgentStream Format)
AgentStreamService (converts to SSE)
    ↓ (Server-Sent Events)
Frontend (receives same format as before)
```

---

## 📋 **Format Specifications**

### **AgentStream Format**

**Purpose**: Internal format expected by AgentStreamService

```typescript
interface GenerateContentResponse {
  candidates: [
    {
      content: { parts: [{ text: string }] };
      finishReason: "STOP" | "MAX_TOKENS" | "SAFETY" | null;
    }
  ];
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
  functionCalls?: FunctionCall[];
}
```

### **Universal Format**

**Purpose**: Standardized communication between providers

```typescript
interface UniversalRequest {
  model: string;
  messages: UniversalMessage[];
  systemInstructions?: string[];
  tools?: UniversalTool[];
  maxTokens?: number;
  temperature?: number;
  stream: true;
}

interface UniversalResponse {
  provider: "google" | "openai" | "anthropic";
  model: string;
  content: string;
  usage: TokenUsage;
  finishReason: FinishReason;
  metadata: ResponseMetadata;
  toolCalls?: UniversalToolCall[];
}
```

---

## 🔧 **Core Components**

### **1. AgentStreamService**

**Location**: `src/services/agent/stream/AgentStreamService.ts`
**Purpose**: Core streaming logic, context management, SSE formatting
**Key Features**:

- File processing and context building
- System instruction generation
- Tool call execution
- Stream chunk processing
- SSE formatting for frontend

### **2. RequestExecutor**

**Location**: `src/services/agent/stream/RequestExecutor.ts`
**Purpose**: Format conversion between AgentStream and Universal formats
**Key Features**:

- AgentStream → Universal conversion
- Universal → AgentStream conversion
- Error handling and logging
- Stream processing pipeline

### **3. ProviderRegistry**

**Location**: `src/services/agent/provider-registry.ts`
**Purpose**: Routes requests to appropriate AI provider
**Key Features**:

- Model-based routing logic
- Provider instantiation
- Request delegation
- Error handling and fallbacks

### **4. AI Providers**

**Locations**:

- `src/services/agent/providers/google-provider.ts`
- `src/services/agent/providers/openai-provider.ts`
- `src/services/agent/providers/anthropic-provider.ts`

**Purpose**: Native API integration for each provider
**Key Features**:

- Universal → Native API format conversion
- Native API streaming calls
- Native → Universal response conversion
- Provider-specific error handling

---

## 🛠️ **Implementation Details**

### **Model Configuration**

**Location**: `src/config/models.ts`

```typescript
export const MODELS: Record<string, ModelConfig> = {
  "gemini-2.5-flash": {
    provider: "google",
    tier: "balanced",
    pricing: { input: 0.075, output: 0.3 },
  },
  "gpt-4o": {
    provider: "openai",
    tier: "premium",
    pricing: { input: 2.5, output: 10.0 },
  },
  "claude-3.5-sonnet": {
    provider: "anthropic",
    tier: "premium",
    pricing: { input: 3.0, output: 15.0 },
  },
};
```

### **Tool Calling**

**Universal tool format** allows consistent tool calling across all providers:

```typescript
interface UniversalTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface UniversalToolCall {
  name: string;
  arguments: Record<string, any>;
  id?: string;
}
```

### **Usage Tracking**

**Location**: `src/schemas/billing/AgentUsage.model.ts`
**Purpose**: Multi-provider usage tracking and billing
**Features**:

- Provider-specific token counting
- Cost calculation per provider
- Model tier tracking
- Billing aggregation

---

## 🎯 **Current Status**

### **✅ Working Components**

- Universal type system
- All AI providers (Google, OpenAI, Anthropic)
- Provider registry and routing
- Tool calling across providers
- Billing and usage tracking
- Model configuration system

### **⚠️ Known Issues**

- **Streaming conversion issues**: Multi-provider layer causing response cutoffs
- **Format compatibility**: Universal ↔ AgentStream conversion needs fixes
- **Stream termination**: Premature finish reason handling

### **🔧 Next Steps**

1. Fix streaming conversion chain issues
2. Resolve format compatibility problems
3. Test complete flow with all providers
4. Performance optimization
5. Production deployment

---

## 🔍 **Debugging & Monitoring**

### **Key Log Points**

- `AgentStreamService`: Context building, stream processing
- `RequestExecutor`: Format conversions, errors
- `ProviderRegistry`: Routing decisions, provider selection
- `Providers`: Native API calls, response processing

### **Metrics to Monitor**

- Response completion rates by provider
- Token usage and costs by provider
- Stream processing latency
- Error rates and types
- Tool call success rates

---

## 📚 **Related Documentation**

- **[STREAMING_ARCHITECTURE.md](./STREAMING_ARCHITECTURE.md)** - Detailed streaming implementation
- **[IMPLEMENTATION_STATUS.md](./IMPLEMENTATION_STATUS.md)** - Current progress and issues
- **[TOOL_CALLING.md](./TOOL_CALLING.md)** - Universal tool calling details
- **[BILLING.md](./BILLING.md)** - Multi-provider billing implementation
