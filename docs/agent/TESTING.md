# Multi-Provider Testing Strategy

## 🎯 **Testing Objectives**

Ensure multi-provider AI architecture works reliably across Google, OpenAI, and Anthropic with identical functionality and performance.

## 🧪 **Testing Phases**

### **Phase 1: Foundation Testing**

**Scope**: Universal interface and Google provider (baseline)

```bash
# Test Google text streaming (should work)
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "message": "Hello, how are you?",
    "conversationId": "test-google-text"
  }'

# Test Google tool calling (should work)
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "message": "Search for information about AI",
    "conversationId": "test-google-tools"
  }'
```

### **Phase 2: OpenAI Integration Testing**

**Scope**: OpenAI text streaming and tool calling

```bash
# Test OpenAI text streaming
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "message": "Hello, how are you?",
    "conversationId": "test-openai-text"
  }'

# Test OpenAI tool calling
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "message": "Search for information about AI",
    "conversationId": "test-openai-tools"
  }'
```

### **Phase 3: Anthropic Integration Testing**

**Scope**: Anthropic text streaming and tool calling

```bash
# Test Anthropic text streaming
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "message": "Hello, how are you?",
    "conversationId": "test-anthropic-text"
  }'

# Test Anthropic tool calling
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "message": "Search for information about AI",
    "conversationId": "test-anthropic-tools"
  }'
```

### **Phase 4: Cross-Provider Compatibility**

**Scope**: Identical functionality across all providers

```bash
# Test same query across all providers
for model in "gemini-2.5-flash" "gpt-4o" "claude-3-5-sonnet-20241022"; do
  echo "Testing $model..."
  curl -X POST http://localhost:8080/api/agent/stream \
    -H "Content-Type: application/json" \
    -d "{
      \"model\": \"$model\",
      \"message\": \"What is artificial intelligence?\",
      \"conversationId\": \"test-cross-provider-$model\"
    }"
  echo -e "\n---\n"
done
```

## 🔍 **Test Categories**

### **Unit Tests**

```typescript
describe("GoogleProvider", () => {
  it("should extract tool calls from stream chunks", () => {
    const chunk = {
      candidates: [
        { content: { parts: [{ functionCall: { name: "test", args: {} } }] } },
      ],
    };
    const toolCalls = provider.extractGoogleToolCalls(chunk);
    expect(toolCalls).toHaveLength(1);
    expect(toolCalls[0].name).toBe("test");
  });
});

describe("OpenAIProvider", () => {
  it("should accumulate streaming tool calls", () => {
    const accumulator = new OpenAIToolCallAccumulator();

    // Add chunks sequentially
    const chunk1 = {
      choices: [
        {
          delta: { tool_calls: [{ id: "call_1", function: { name: "test" } }] },
        },
      ],
    };
    const chunk2 = {
      choices: [
        {
          delta: {
            tool_calls: [
              { index: 0, function: { arguments: '{"param": "value"}' } },
            ],
          },
        },
      ],
    };

    accumulator.addChunk(chunk1);
    const completed = accumulator.addChunk(chunk2);

    expect(completed).toHaveLength(1);
    expect(completed[0].arguments).toEqual({ param: "value" });
  });
});

describe("RequestExecutor", () => {
  it("should convert universal tool calls to Google format", () => {
    const universalResponse = {
      toolCalls: [
        {
          id: "1",
          name: "test",
          arguments: { param: "value" },
          status: "complete",
        },
      ],
    };

    const googleChunk = executor.convertToGoogleFormat(universalResponse);
    expect(googleChunk.candidates[0].content.parts[0].functionCall.name).toBe(
      "test"
    );
  });
});
```

### **Integration Tests**

```typescript
describe("End-to-End Tool Calling", () => {
  test.each(["gemini-2.5-flash", "gpt-4o", "claude-3-5-sonnet-20241022"])(
    "should handle tool calls for %s",
    async (model) => {
      const response = await request(app)
        .post("/api/agent/stream")
        .send({
          model,
          message: "Search for AI information",
          conversationId: `test-${model}`,
        });

      expect(response.status).toBe(200);
      // Verify tool call execution
      // Verify response format consistency
    }
  );
});
```

### **Performance Tests**

```typescript
describe("Streaming Performance", () => {
  it("should maintain streaming performance across providers", async () => {
    const models = ["gemini-2.5-flash", "gpt-4o", "claude-3-5-sonnet-20241022"];

    for (const model of models) {
      const startTime = Date.now();
      const stream = await agentService.createMessageStream({
        model,
        message: "Hello",
      });

      let firstChunkTime = null;
      for await (const chunk of stream) {
        if (!firstChunkTime) firstChunkTime = Date.now();
        break;
      }

      const latency = firstChunkTime - startTime;
      expect(latency).toBeLessThan(500); // 500ms max first token
    }
  });
});
```

## 🚨 **Error Scenarios**

### **Provider Unavailable**

```bash
# Test with invalid API key
OPENAI_API_KEY=invalid npm test -- --grep "provider unavailable"

# Expected: Graceful error, other providers still work
```

### **Malformed Tool Calls**

```typescript
// Test malformed tool arguments
const malformedChunk = {
  choices: [
    { delta: { tool_calls: [{ function: { arguments: "{invalid json" } }] } },
  ],
};

// Expected: Error logged, stream continues
```

### **Tool Call Timeout**

```typescript
// Test tool call that never completes
const neverCompleteChunk = {
  choices: [
    { delta: { tool_calls: [{ id: "call_1", function: { name: "test" } }] } },
  ],
};

// Expected: Cleanup after timeout, no memory leak
```

## 📊 **Performance Benchmarks**

### **Baseline Metrics** (Google Only)

- First token latency: ~100ms
- Tool call detection: ~10ms
- Memory usage: ~50MB per stream
- Throughput: ~1000 tokens/sec

### **Target Metrics** (Multi-Provider)

- First token latency: <150ms (+50ms max overhead)
- Tool call detection: <50ms
- Memory usage: <60MB per stream (+20% max)
- Throughput: >800 tokens/sec

### **Monitoring Commands**

```bash
# Performance testing
npm run test:performance

# Memory profiling
node --inspect src/index.ts &
# Connect Chrome DevTools for memory analysis

# Load testing
artillery run tests/load/multi-provider.yml
```

## ✅ **Acceptance Criteria**

### **Functional Requirements**

- [ ] All providers support text streaming
- [ ] All providers support tool calling
- [ ] Tool calls work identically across providers
- [ ] RAG functionality preserved
- [ ] Error handling graceful

### **Performance Requirements**

- [ ] Streaming latency < 150ms first token
- [ ] Tool call detection < 50ms
- [ ] Memory usage < 60MB per stream
- [ ] No blocking operations

### **Compatibility Requirements**

- [ ] Frontend receives identical stream format
- [ ] Existing tools work unchanged
- [ ] Zero breaking changes to AgentStreamService
- [ ] Billing tracking functional

## 🔧 **Test Utilities**

### **Provider Health Check**

```bash
# Check all providers
curl http://localhost:8080/api/agent/health

# Expected response:
{
  "status": "healthy",
  "providers": {
    "google": { "status": "healthy", "models": ["gemini-2.5-flash", ...] },
    "openai": { "status": "healthy", "models": ["gpt-4o", ...] },
    "anthropic": { "status": "healthy", "models": ["claude-3-5-sonnet-20241022", ...] }
  }
}
```

### **Model Testing Script**

```bash
#!/bin/bash
# Test all supported models
models=(
  "gemini-2.5-flash"
  "gemini-2.5-pro"
  "gpt-4o"
  "gpt-4o-mini"
  "claude-3-5-sonnet-20241022"
  "claude-3-5-haiku-20241022"
)

for model in "${models[@]}"; do
  echo "Testing $model..."
  npm test -- --grep "$model"
done
```

---

**Status**: Ready for Implementation Testing
**Next**: Execute Phase 1 testing after universal interface extension
