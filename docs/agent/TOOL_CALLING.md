# Universal Tool Calling Implementation

## 🎯 **Objective**

Implement universal tool calling across Google, OpenAI, and Anthropic providers while maintaining 100% compatibility with existing AgentStreamService **and zero frontend impact**.

### **🔒 Frontend Compatibility Guarantee**

- ✅ **API Interface**: POST `/api/agent/stream` unchanged
- ✅ **Request Format**: Same JSON structure, only `model` field accepts new values
- ✅ **Stream Format**: Identical SSE structure (`data: {...}\n\n`)
- ✅ **Tool Execution**: Same progress events, same result format
- ✅ **Error Handling**: Same error response structure
- ✅ **Citations/Metadata**: Same format for RAG results

**Frontend code requires ZERO changes - just new model options!**

## 🔍 **Current Status**

### **✅ Fully Working (No Changes Needed)**

- **Context Building**: Resource processing, file transcripts, images
- **System Instructions**: User preferences, model-specific instructions
- **Conversation History**: Message threading, context continuity
- **Tool Definitions**: Available tools passed to all providers
- **Progress Reporting**: analyzing → searching → synthesizing
- **Error Handling**: Graceful degradation, stream recovery

### **🚨 Only Gap: Tool Call Detection**

- ✅ **Google Gemini**: Full tool calling support (working)
- ❌ **OpenAI GPT**: Tool calls lost in conversion
- ❌ **Anthropic Claude**: Tool calls lost in conversion

**Everything else works perfectly across all providers!**

## 🚨 **Critical Gap Analysis**

### **Problem**: Tool Calls Missing from Universal Interface

```typescript
// ❌ CURRENT - Missing tool calls
interface UniversalResponse {
  content: string;
  usage: TokenUsage;
  finishReason: FinishReason;
  // Missing: toolCalls
}

// ✅ REQUIRED - Must add tool call support
interface UniversalResponse {
  content?: string;
  toolCalls?: UniversalToolCall[]; // ← CRITICAL ADDITION
  usage: TokenUsage;
  finishReason: FinishReason;
}
```

## 📊 **Provider Tool Call Formats**

### **Google Gemini** (✅ Working)

```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "functionCall": {
              "name": "searchDocuments",
              "args": { "query": "user input" }
            }
          }
        ]
      }
    }
  ]
}
```

- ✅ Complete tool call in single chunk
- ✅ Object-based arguments
- ✅ Simple streaming model

### **OpenAI GPT** (❌ Needs Implementation)

```json
// Chunk 1:
{
  "choices": [{
    "delta": {
      "tool_calls": [{
        "index": 0,
        "id": "call_abc123",
        "type": "function",
        "function": {"name": "searchDocuments"}
      }]
    }
  }]
}

// Chunk 2:
{
  "choices": [{
    "delta": {
      "tool_calls": [{
        "index": 0,
        "function": {"arguments": "{\"query\":\"user input\"}"}
      }]
    }
  }]
}
```

- ⚠️ Tool calls spread across multiple chunks
- ⚠️ JSON string arguments (requires parsing)
- ⚠️ Requires accumulation logic

### **Anthropic Claude** (❌ Needs Implementation)

```json
// Start:
{
  "type": "content_block_start",
  "content_block": {
    "type": "tool_use",
    "id": "toolu_123",
    "name": "searchDocuments"
  }
}

// Input:
{
  "type": "content_block_delta",
  "delta": {
    "type": "input_json_delta",
    "partial_json": "{\"query\": \"user input\"}"
  }
}

// Stop:
{
  "type": "content_block_stop"
}
```

- 🔥 Multi-event streaming model
- 🔥 Partial JSON accumulation required
- 🔥 Complex state management

## 🏗️ **Universal Tool Call Design**

### **Core Interface**

```typescript
interface UniversalToolCall {
  id: string; // Unique identifier
  name: string; // Function name
  arguments: object; // Parsed arguments (never string)
  status: "building" | "complete" | "error";
  metadata?: {
    provider: ProviderType;
    originalId?: string; // Provider's original ID
    chunkCount?: number; // Debug info
  };
}
```

### **Streaming State Management**

```typescript
abstract class ToolCallAccumulator {
  protected partialCalls = new Map<string, PartialToolCall>();

  abstract addChunk(chunk: any): UniversalToolCall[];
  abstract cleanup(): void;

  protected generateId(): string {
    return `${this.provider}_${Date.now()}_${Math.random()}`;
  }
}
```

## 🔄 **Implementation Phases**

### **Phase 1: Extend Universal Interface** ⭐ **START HERE**

**Files to Modify:**

- `src/types/universal-ai.ts`
- `src/services/agent/providers/base-provider.ts`

**Changes:**

```typescript
// 1. Add UniversalToolCall interface
// 2. Add toolCalls to UniversalResponse
// 3. Add extractToolCalls parameter to createUniversalStream
```

**Effort**: 2-3 hours | **Risk**: Low

### **Phase 2: Google Tool Call Extraction**

**Files to Modify:**

- `src/services/agent/providers/google-provider.ts`

**Changes:**

```typescript
private extractGoogleToolCalls(chunk: any): UniversalToolCall[] | null {
  const parts = chunk.candidates?.[0]?.content?.parts;
  if (!parts) return null;

  const toolCalls: UniversalToolCall[] = [];
  for (const part of parts) {
    if (part.functionCall) {
      toolCalls.push({
        id: this.generateToolCallId(),
        name: part.functionCall.name,
        arguments: part.functionCall.args,
        status: 'complete'
      });
    }
  }
  return toolCalls.length > 0 ? toolCalls : null;
}
```

**Effort**: 1-2 hours | **Risk**: Low

### **Phase 3: RequestExecutor Tool Call Conversion**

**Files to Modify:**

- `src/services/agent/stream/RequestExecutor.ts`

**Changes:**

```typescript
// Convert UniversalToolCall[] back to Google format for AgentStreamService
if (universalResponse.toolCalls?.length) {
  googleChunk.candidates[0].content.parts = [
    ...universalResponse.toolCalls.map((tc) => ({
      functionCall: {
        name: tc.name,
        args: tc.arguments,
      },
    })),
  ];
}
```

**Effort**: 1 hour | **Risk**: Low

### **Phase 4: OpenAI Delta Accumulation**

**Files to Modify:**

- `src/services/agent/providers/openai-provider.ts`

**Changes:**

```typescript
class OpenAIToolCallAccumulator extends ToolCallAccumulator {
  addChunk(chunk: any): UniversalToolCall[] {
    const toolCalls = chunk.choices?.[0]?.delta?.tool_calls;
    if (!toolCalls) return [];

    const completedCalls: UniversalToolCall[] = [];

    for (const tc of toolCalls) {
      const id = tc.id || `call_${tc.index}`;

      // Initialize or update partial call
      if (!this.partialCalls.has(id)) {
        this.partialCalls.set(id, {
          id,
          name: "",
          arguments: "",
          status: "building",
        });
      }

      const partial = this.partialCalls.get(id);
      if (tc.function?.name) partial.name = tc.function.name;
      if (tc.function?.arguments) partial.arguments += tc.function.arguments;

      // Check if complete
      if (this.isComplete(partial, chunk)) {
        completedCalls.push({
          id: partial.id,
          name: partial.name,
          arguments: JSON.parse(partial.arguments),
          status: "complete",
        });
        this.partialCalls.delete(id);
      }
    }

    return completedCalls;
  }
}
```

**Effort**: 4-6 hours | **Risk**: Medium

### **Phase 5: Anthropic Multi-Event Handling**

**Files to Modify:**

- `src/services/agent/providers/anthropic-provider.ts`

**Changes:**

```typescript
class AnthropicToolCallAccumulator extends ToolCallAccumulator {
  private currentToolCall: PartialToolCall | null = null;

  addChunk(chunk: any): UniversalToolCall[] {
    // Handle content_block_start
    if (
      chunk.type === "content_block_start" &&
      chunk.content_block.type === "tool_use"
    ) {
      this.currentToolCall = {
        id: chunk.content_block.id,
        name: chunk.content_block.name,
        arguments: "",
        status: "building",
      };
    }

    // Handle content_block_delta
    if (
      chunk.type === "content_block_delta" &&
      chunk.delta.type === "input_json_delta"
    ) {
      if (this.currentToolCall) {
        this.currentToolCall.arguments += chunk.delta.partial_json;
      }
    }

    // Handle content_block_stop
    if (chunk.type === "content_block_stop" && this.currentToolCall) {
      const completed: UniversalToolCall = {
        id: this.currentToolCall.id,
        name: this.currentToolCall.name,
        arguments: JSON.parse(this.currentToolCall.arguments),
        status: "complete",
      };
      this.currentToolCall = null;
      return [completed];
    }

    return [];
  }
}
```

**Effort**: 6-8 hours | **Risk**: High

## 🧪 **Testing Strategy**

### **Phase Testing**

1. **Phase 1-3**: Test Google tool calling (baseline validation)
2. **Phase 4**: Test OpenAI tool calling independently
3. **Phase 5**: Test Anthropic tool calling independently
4. **Final**: Cross-provider compatibility testing

### **Test Cases**

- Simple tool calls (search, calculation)
- Complex tool calls (multi-parameter)
- Tool call errors and recovery
- Streaming performance impact
- Memory usage and cleanup

## ⚠️ **Risk Assessment**

### **High Risk Areas**

1. **OpenAI Delta Accumulation**: JSON parsing across chunks
2. **Anthropic Multi-Event**: Complex state management
3. **Memory Leaks**: Partial tool call cleanup
4. **Stream Performance**: Tool call buffering impact

### **Mitigation Strategies**

1. **Incremental Implementation**: One provider at a time
2. **Robust Error Handling**: Graceful degradation
3. **Memory Management**: Automatic cleanup after timeout
4. **Performance Testing**: Benchmark each phase

## ✅ **Success Criteria**

### **Functional**

- ✅ Tool calls work identically across all providers
- ✅ Existing AgentStreamService unchanged
- ✅ RAG functionality preserved
- ✅ Tool call continuation works

### **Performance**

- ✅ Streaming latency < 100ms overhead
- ✅ Memory usage < 10% increase
- ✅ No blocking operations

### **Compatibility**

- ✅ **Frontend receives identical stream format**
  ```typescript
  // ✅ SAME SSE STRUCTURE across all providers
  data: {"type":"content","content":"Hello","usage":{}}
  data: {"type":"progress","status":"searching"}
  data: {"type":"complete","finishReason":"STOP"}
  ```
- ✅ **Existing tools work unchanged**
  ```typescript
  // ✅ SAME TOOL EXECUTION across all providers
  searchDocumentsTool.execute(); // Works identically
  ragSearchTool.execute(); // Same citations format
  calculatorTool.execute(); // Same result structure
  ```
- ✅ **Zero breaking changes**
  ```typescript
  // ✅ FRONTEND CODE UNCHANGED
  // Only model names expanded:
  model: "gemini-2.5-flash"; // ✅ Still works
  model: "gpt-4o"; // ✅ New option
  model: "claude-3-5-sonnet-20241022"; // ✅ New option
  ```

---

**Status**: Ready for Implementation
**Next Step**: Start Phase 1 - Extend Universal Interface
**Timeline**: 3-4 weeks total
