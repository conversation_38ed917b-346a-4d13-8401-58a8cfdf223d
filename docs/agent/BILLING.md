# AI Usage Billing System

## 🎯 **Overview**

Comprehensive billing system for tracking AI usage across multiple providers with precise cost attribution and usage analytics.

## 📊 **Database Schema**

### **AgentUsage Model**

```typescript
interface AgentUsage {
  id: string; // Primary key
  userId: string; // User identifier
  conversationId: string; // Conversation context
  messageId: string; // Specific message

  // Provider & Model
  provider: "google" | "openai" | "anthropic";
  model: string; // Specific model used

  // Usage Metrics
  inputTokens: number; // Input token count
  outputTokens: number; // Output token count
  imageTokens?: number; // Image processing tokens
  totalTokens: number; // Total token usage

  // Cost Tracking
  cost: number; // USD cost for this usage
  costCurrency: string; // Always 'USD'

  // Context
  projectId?: string; // Project context
  sessionId?: string; // Session context

  // Metadata
  createdAt: Date;
  updatedAt: Date;
}
```

## 💰 **Cost Calculation**

### **Provider Pricing** (per 1K tokens)

```typescript
const PRICING = {
  google: {
    "gemini-2.5-pro": { input: 1.25, output: 5.0 },
    "gemini-2.5-flash": { input: 0.075, output: 0.3 },
    "gemini-1.5-pro": { input: 1.25, output: 5.0 },
    "gemini-1.5-flash": { input: 0.075, output: 0.3 },
  },
  openai: {
    "gpt-4o": { input: 2.5, output: 10.0 },
    "gpt-4o-mini": { input: 0.15, output: 0.6 },
    "gpt-4": { input: 30.0, output: 60.0 },
    "gpt-3.5-turbo": { input: 0.5, output: 1.5 },
  },
  anthropic: {
    "claude-3-5-sonnet-20241022": { input: 3.0, output: 15.0 },
    "claude-3-5-haiku-20241022": { input: 0.25, output: 1.25 },
    "claude-3-opus": { input: 15.0, output: 75.0 },
    "claude-3-sonnet": { input: 3.0, output: 15.0 },
    "claude-3-haiku": { input: 0.25, output: 1.25 },
  },
};
```

## 🔄 **Usage Tracking Flow**

### **Streaming Integration**

```typescript
// In AgentService.wrapStreamWithBillingTracking()
const self = this;
return new ReadableStream({
  start(controller) {
    // Track usage from each stream chunk
    const usage = self.parseStreamChunkForUsage(chunk);
    if (usage) {
      self.trackEnhancedUsage(conversationId, messageId, modelId, usage);
    }
  },
});
```

### **Enhanced Usage Tracking**

```typescript
async trackEnhancedUsage(
  conversationId: string,
  messageId: string,
  model: string,
  usage: TokenUsage
): Promise<void> {
  const provider = this.getProviderFromModel(model);
  const cost = this.calculateCost(model, usage);

  await AgentUsage.create({
    userId: context.userId,
    conversationId,
    messageId,
    provider,
    model,
    inputTokens: usage.inputTokens,
    outputTokens: usage.outputTokens,
    totalTokens: usage.totalTokens,
    cost,
    costCurrency: 'USD'
  });
}
```

## 📈 **Analytics & Reporting**

### **Usage Queries**

```sql
-- Daily usage by provider
SELECT provider, SUM(total_tokens) as tokens, SUM(cost) as cost
FROM ai_usage
WHERE created_at >= CURRENT_DATE
GROUP BY provider;

-- User usage summary
SELECT user_id, provider, model, COUNT(*) as requests, SUM(cost) as total_cost
FROM ai_usage
WHERE user_id = ?
GROUP BY user_id, provider, model;

-- Project cost analysis
SELECT project_id, provider, SUM(cost) as project_cost
FROM ai_usage
WHERE project_id IS NOT NULL
GROUP BY project_id, provider;
```

## 🔒 **Security & Privacy**

### **Data Retention**

- Usage data retained for 90 days
- Aggregated analytics retained for 1 year
- No message content stored in billing records

### **Access Control**

- Users can view their own usage only
- Project admins can view project usage
- System admins have full access

---

**Status**: Production Ready
**Migration**: See archive/BILLING_IMPLEMENTATION_PLAN.md for historical context
