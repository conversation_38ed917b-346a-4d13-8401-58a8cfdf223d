# Multi-Provider Implementation Guide

## 🎯 **Quick Start**

This guide provides step-by-step instructions for implementing and extending the multi-provider AI architecture.

## 📋 **Current Implementation Status**

### **✅ Completed (Working) - No Changes Required**

- **Text streaming** across Google, OpenAI, Anthropic
- **Context building** - `AgentService.processResourcesAndImages()`:
  - ✅ Resource processing (file transcripts, PDFs, images)
  - ✅ Available tools detection (`availableTools`)
  - ✅ Searchable file IDs (`searchableFileIds`)
  - ✅ Inline data parts (images, attachments)
- **System instructions** - `AgentUtilsService.buildSystemInstructions()`:
  - ✅ Model-specific instructions
  - ✅ User preference instructions
  - ✅ Default system prompts
- **Conversation flow** - `AgentStreamService.generateStreamingResponse()`:
  - ✅ Conversation history retrieval
  - ✅ Content formatting for GenAI
  - ✅ Progress reporting (analyzing, searching, synthesizing)
- **Provider routing** and health checks
- **Universal interface** foundation
- **Error handling** and logging

**🎯 Key Point**: All existing context logic is preserved and works identically across all providers!

### **🔒 API Interface Preservation** (✅ UNCHANGED)

- **Endpoint**: `POST /api/agent/stream` - same URL
- **Request Format**: Same JSON structure - only `model` field accepts new values
- **Headers**: Same `Content-Type: application/json`
- **Authentication**: Same auth middleware and token validation
- **Response Format**: Identical SSE stream structure
- **Error Codes**: Same HTTP status codes and error format
- **Progress Events**: Same `analyzing`, `searching`, `synthesizing` events

**Frontend developers see zero API changes - just new model options!**

### **🚧 In Progress (Tool Calling)**

- Universal tool call interface extension
- Provider-specific tool call extraction
- Stream format conversion

### **📅 Planned**

- OpenAI streaming tool call accumulation
- Anthropic multi-event tool call handling
- Cross-provider testing and optimization

## 🛠️ **Implementation Steps**

### **Step 1: Extend Universal Interface**

**File**: `src/types/universal-ai.ts`

```typescript
// Add to existing file:
export interface UniversalToolCall {
  id: string;
  name: string;
  arguments: object;
  status: "building" | "complete" | "error";
  metadata?: {
    provider: ProviderType;
    originalId?: string;
    chunkCount?: number;
  };
}

// Update existing interface:
export interface UniversalResponse {
  provider: ProviderType;
  model: string;
  content?: string; // Make optional
  toolCalls?: UniversalToolCall[]; // Add tool calls
  usage: TokenUsage;
  finishReason: FinishReason;
  metadata: ResponseMetadata;
}
```

### **Step 2: Update BaseProvider**

**File**: `src/services/agent/providers/base-provider.ts`

```typescript
// Update createUniversalStream method signature:
protected createUniversalStream(
  nativeStream: any,
  model: string,
  extractContent: (chunk: any) => string | null,
  extractToolCalls: (chunk: any) => UniversalToolCall[] | null, // ← ADD
  extractUsage: (chunk: any) => TokenUsage | null,
  extractFinishReason: (chunk: any) => FinishReason | null
): ReadableStream {
  // Update implementation to handle tool calls
}
```

### **Step 3: Update Google Provider**

**File**: `src/services/agent/providers/google-provider.ts`

```typescript
// Add tool call extraction method:
private extractGoogleToolCalls(chunk: any): UniversalToolCall[] | null {
  const parts = chunk.candidates?.[0]?.content?.parts;
  if (!parts) return null;

  const toolCalls: UniversalToolCall[] = [];
  for (const part of parts) {
    if (part.functionCall) {
      toolCalls.push({
        id: `google_${Date.now()}_${Math.random()}`,
        name: part.functionCall.name,
        arguments: part.functionCall.args,
        status: 'complete',
        metadata: { provider: 'google' }
      });
    }
  }
  return toolCalls.length > 0 ? toolCalls : null;
}

// Update generateStream call:
return this.createUniversalStream(
  streamGenerator,
  request.model,
  (chunk) => chunk.candidates?.[0]?.content?.parts?.[0]?.text || null,
  (chunk) => this.extractGoogleToolCalls(chunk), // ← ADD
  (chunk) => { /* usage extraction */ },
  (chunk) => this.mapGoogleFinishReason(chunk.candidates?.[0]?.finishReason)
);
```

### **Step 4: Update RequestExecutor**

**File**: `src/services/agent/stream/RequestExecutor.ts`

```typescript
// Update convertProviderStreamToGoogleFormat:
const googleChunk: GenerateContentResponse = {
  candidates: [
    {
      content: {
        parts: universalResponse.toolCalls?.length
          ? universalResponse.toolCalls.map((tc) => ({
              functionCall: { name: tc.name, args: tc.arguments },
            }))
          : [{ text: universalResponse.content || "" }],
      },
      finishReason: this.mapFinishReason(universalResponse.finishReason),
    },
  ],
  // ... rest unchanged
};
```

### **Step 5: Test Google Tool Calling**

```bash
# Test existing RAG functionality
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "message": "Search for information about project management",
    "conversationId": "test-123"
  }'
```

### **Step 6: Implement OpenAI Tool Calls**

**File**: `src/services/agent/providers/openai-provider.ts`

```typescript
class OpenAIToolCallAccumulator {
  private partialCalls = new Map<string, any>();

  addChunk(chunk: any): UniversalToolCall[] {
    // Implementation for OpenAI delta accumulation
    // See TOOL_CALLING.md for full code
  }
}

// Add to OpenAIProvider:
private toolCallAccumulator = new OpenAIToolCallAccumulator();

private extractOpenAIToolCalls(chunk: any): UniversalToolCall[] | null {
  return this.toolCallAccumulator.addChunk(chunk);
}
```

### **Step 7: Implement Anthropic Tool Calls**

**File**: `src/services/agent/providers/anthropic-provider.ts`

```typescript
class AnthropicToolCallAccumulator {
  private currentToolCall: any = null;

  addChunk(chunk: any): UniversalToolCall[] {
    // Implementation for Anthropic multi-event handling
    // See TOOL_CALLING.md for full code
  }
}
```

## 🧪 **Testing Each Phase**

### **Phase 1-3 Testing (Google Baseline)**

```bash
# Verify Google tool calling still works
npm test -- --grep "tool calling"
npm test -- --grep "RAG"
```

### **Phase 4 Testing (OpenAI)**

```bash
# Test OpenAI tool calling
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "message": "Search for project information",
    "conversationId": "test-openai-123"
  }'
```

### **Phase 5 Testing (Anthropic)**

```bash
# Test Anthropic tool calling
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "message": "Search for project information",
    "conversationId": "test-anthropic-123"
  }'
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: Tool Call Not Detected**

```typescript
// Check: Is extractToolCalls returning null?
// Debug: Add logging in provider tool call extraction
log.info("Tool call extraction", {
  chunk: JSON.stringify(chunk),
  toolCalls: extractedToolCalls,
});
```

### **Issue 2: Malformed Tool Arguments**

```typescript
// Solution: Robust JSON parsing with fallback
try {
  arguments = JSON.parse(argumentsString);
} catch (error) {
  log.warn("Failed to parse tool arguments", { argumentsString, error });
  arguments = { error: "Failed to parse arguments" };
}
```

### **Issue 3: Memory Leaks from Partial Calls**

```typescript
// Solution: Cleanup timer in accumulator
class ToolCallAccumulator {
  private cleanupTimer = setInterval(() => {
    this.cleanupStalePartialCalls();
  }, 30000); // 30 second cleanup
}
```

## 📊 **Performance Monitoring**

### **Metrics to Track**

- Tool call detection latency
- Memory usage during accumulation
- Stream throughput impact
- Error rates per provider

### **Monitoring Code**

```typescript
// Add to each provider:
private logToolCallMetrics(toolCall: UniversalToolCall) {
  log.info("Tool call completed", {
    provider: this.name,
    toolName: toolCall.name,
    argumentSize: JSON.stringify(toolCall.arguments).length,
    processingTime: Date.now() - toolCall.startTime
  });
}
```

---

**Ready to Start**: Phase 1 can begin immediately
**Total Effort**: 3-4 weeks  
**Success Rate**: High (incremental approach reduces risk)
