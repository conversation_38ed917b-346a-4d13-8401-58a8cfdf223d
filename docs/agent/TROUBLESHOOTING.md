# 🔧 Troubleshooting Guide - Multi-Provider Agent Service

## 🚨 **Known Critical Issues**

### **1. Streaming Response Cutoffs ⚠️**

**Symptom**: Responses terminate prematurely, often mid-sentence
**Impact**: Incomplete responses to users
**Status**: **ACTIVE ISSUE** - Under investigation

#### **Quick Diagnosis**

```bash
# Check if multi-provider layer is enabled
grep -r "Using multi-provider execution" src/services/agent/stream/
grep -r "Using direct Google AI fallback" src/services/agent/stream/
```

#### **Temporary Workaround**

The system automatically falls back to direct Google AI when multi-provider fails:

```typescript
// In AgentStreamService.executeStreamingRequest()
if (this.requestExecutor) {
  // Multi-provider (currently has issues)
} else {
  // Direct Google AI (working fallback)
}
```

#### **Root Cause Analysis**

1. **Format Conversion Issues**: Universal ↔ AgentStream conversion losing data
2. **Finish Reason Problems**: Premature "stop" conditions in conversion
3. **Multi-Part Content Loss**: Google responses with multiple parts not fully extracted

#### **Debug Steps**

```bash
# 1. Check AgentStreamService logs
grep "executeStreamingRequest" logs/app.log

# 2. Check RequestExecutor conversion logs
grep "convertProviderStreamToAgentFormat" logs/app.log

# 3. Check provider-specific logs
grep "createUniversalStream" logs/app.log

# 4. Check finish reason handling
grep "finishReason" logs/app.log
```

---

## 🔍 **Diagnostic Commands**

### **Check Multi-Provider Status**

```bash
# Verify all providers are registered
node -e "
const { ProviderRegistry } = require('./dist/services/agent/provider-registry.js');
const registry = new ProviderRegistry();
console.log('Registered providers:', registry.getAvailableProviders());
"

# Check model configuration
node -e "
const { MODELS } = require('./dist/config/models.js');
console.log('Available models:', Object.keys(MODELS));
"
```

### **Test Individual Providers**

```bash
# Test Google provider directly
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "model": "gemini-2.5-flash"}'

# Test OpenAI provider
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "model": "gpt-4o"}'

# Test Anthropic provider
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "model": "claude-3.5-sonnet"}'
```

### **Check Database State**

```sql
-- Check recent usage records
SELECT provider, model_id, model_tier, total_tokens, total_cost_usd, created_at
FROM ai_usage
ORDER BY created_at DESC
LIMIT 10;

-- Check provider distribution
SELECT provider, COUNT(*) as request_count, SUM(total_cost_usd) as total_cost
FROM ai_usage
GROUP BY provider;

-- Check model tier distribution
SELECT model_tier, COUNT(*) as request_count
FROM ai_usage
GROUP BY model_tier;
```

---

## 🛠️ **Common Issues & Solutions**

### **Issue: "Provider not found for model"**

**Cause**: Model not properly configured in model registry
**Solution**:

```typescript
// Check src/config/models.ts
export const MODELS: Record<string, ModelConfig> = {
  "your-model-id": {
    provider: "google", // or "openai", "anthropic"
    tier: "balanced",
    pricing: { input: 0.075, output: 0.3 },
  },
};
```

### **Issue: "Request executor not available"**

**Cause**: RequestExecutor not properly injected into AgentStreamService
**Solution**:

```typescript
// Check service instantiation in agent.service.ts
const requestExecutor = new RequestExecutor(providerRegistry);
const agentStreamService = new AgentStreamService(requestExecutor);
```

### **Issue: "Universal format conversion failed"**

**Cause**: Format conversion between Universal and AgentStream formats
**Solution**:

1. Check `RequestExecutor.convertProviderStreamToAgentFormat()`
2. Verify Universal response structure matches expected format
3. Check finish reason mapping logic

### **Issue: "Tool calls not working with provider X"**

**Cause**: Provider-specific tool format conversion issues
**Solution**:

```typescript
// Check provider's tool conversion methods
// In each provider file:
convertToProviderTools(tools: UniversalTool[]): ProviderToolFormat[]
extractToolCalls(chunk: ProviderChunk): UniversalToolCall[]
```

### **Issue: "Billing records missing or incorrect"**

**Cause**: Usage tracking not capturing multi-provider data
**Solution**:

1. Check `AgentUsage.model.ts` for proper field definitions
2. Verify `usage-tracker.ts` handles all provider formats
3. Check migration scripts for database schema

---

## 📊 **Performance Debugging**

### **Latency Analysis**

```bash
# Check response times by provider
grep -E "(Google|OpenAI|Anthropic)" logs/app.log | grep "response_time"

# Check conversion overhead
grep "convertProviderStreamToAgentFormat" logs/app.log | grep "duration"

# Check stream processing times
grep "processStreamChunks" logs/app.log | grep "completed"
```

### **Memory Usage**

```bash
# Check for memory leaks in stream processing
node --expose-gc --inspect server.js

# Monitor stream cleanup
grep "stream cleanup" logs/app.log
```

### **Error Rate Analysis**

```bash
# Check error rates by provider
grep "ERROR" logs/app.log | grep -E "(Google|OpenAI|Anthropic)" | wc -l

# Check specific error types
grep "Failed to execute multi-provider" logs/app.log
grep "Stream error in.*provider" logs/app.log
```

---

## 🔄 **Recovery Procedures**

### **Emergency Fallback to Single Provider**

If multi-provider system fails completely:

```typescript
// In AgentStreamService.executeStreamingRequest()
// Temporarily disable multi-provider by setting requestExecutor to null
private requestExecutor?: StreamingRequestExecutor = null; // Force fallback
```

### **Provider-Specific Disable**

To disable a specific problematic provider:

```typescript
// In ProviderRegistry.getProvider()
if (provider === "problematic-provider") {
  throw new Error("Provider temporarily disabled");
}
```

### **Database Recovery**

If billing data gets corrupted:

```sql
-- Backup current data
CREATE TABLE ai_usage_backup AS SELECT * FROM ai_usage;

-- Run migration to fix schema
npm run migrate

-- Verify data integrity
SELECT COUNT(*) FROM ai_usage WHERE model_tier IS NOT NULL;
```

---

## 📝 **Logging Configuration**

### **Enable Debug Logging**

```typescript
// In logger configuration
export const logger = {
  level: "debug", // Enable detailed logging
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
};
```

### **Key Log Messages to Monitor**

- `"Using multi-provider execution"` - Multi-provider enabled
- `"Using direct Google AI fallback"` - Fallback mode
- `"convertProviderStreamToAgentFormat"` - Format conversion
- `"Stream error in.*provider"` - Provider-specific errors
- `"Failed to execute multi-provider"` - System-wide failures

---

## 🎯 **Validation Tests**

### **End-to-End Test**

```bash
# Test complete flow with each provider
for model in "gemini-2.5-flash" "gpt-4o" "claude-3.5-sonnet"; do
  echo "Testing $model..."
  curl -X POST http://localhost:8080/api/agent/stream \
    -H "Content-Type: application/json" \
    -d "{\"message\": \"Write a haiku\", \"model\": \"$model\"}" \
    | grep -o "data: .*" | head -10
done
```

### **Tool Calling Test**

```bash
# Test tool calling with each provider
curl -X POST http://localhost:8080/api/agent/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Search for information about AI",
    "model": "gemini-2.5-flash",
    "conversationId": "test-conv"
  }' | grep -o "tool_call"
```

---

## 📞 **Escalation Path**

### **Level 1: Self-Service**

- Check this troubleshooting guide
- Review implementation status
- Check logs and metrics

### **Level 2: Development Team**

- Provide logs and error messages
- Include steps to reproduce
- Specify which providers are affected

### **Level 3: Architecture Review**

- System-wide issues affecting all providers
- Performance degradation across the board
- Data integrity or billing issues

---

## 📚 **Related Documentation**

- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - System design and components
- **[IMPLEMENTATION_STATUS.md](./IMPLEMENTATION_STATUS.md)** - Current progress and known issues
- **[TOOL_CALLING.md](./TOOL_CALLING.md)** - Tool calling implementation details
- **[BILLING.md](./BILLING.md)** - Billing and usage tracking
