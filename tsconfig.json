{"compilerOptions": {"module": "commonjs", "target": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "sourceMap": true, "outDir": "dist", "allowJs": true, "lib": ["dom", "es6"], "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules"], "include": ["src/**/*", "src/**/*.json", "scripts/**/*", "migrations/**/*", ".env"], "types": ["src/types/express/index.d.ts"]}