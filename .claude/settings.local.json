{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:docs.pinecone.io)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(npm install:*)", "Bash(find:*)", "Bash(ls:*)", "WebFetch(domain:github.com)", "Bash(node:*)", "Bash(rm:*)", "WebFetch(domain:www.pinecone.io)", "WebFetch(domain:ai.google.dev)", "<PERSON><PERSON>(mkdir:*)", "Bash(yarn build)", "Bash(yarn migrate:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(tsx:*)", "Bash(yarn tsx:*)", "WebFetch(domain:googleapis.github.io)", "<PERSON><PERSON>(mv:*)", "mcp__ide__getDiagnostics", "Bash(npx tsc:*)", "Bash(npm start)", "Bash(git restore:*)", "<PERSON><PERSON>(timeout 10s npm start)", "<PERSON><PERSON>(timeout 8s npm start)", "<PERSON><PERSON>(curl:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(true)", "Bash(npm test:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(timeout:*)"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/.claude"]}}