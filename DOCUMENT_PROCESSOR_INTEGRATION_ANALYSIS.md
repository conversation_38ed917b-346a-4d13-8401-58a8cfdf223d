# Document Processor Service Integration Analysis

## Overview
This document provides a comprehensive analysis of all places where the Document Processor Service is called throughout the AIDA backend codebase, and identifies potential gaps in coverage.

## Document Processor Service Methods

The `DocumentProcessorService` class provides three main methods:
1. `publishUploadMessage()` - For new document uploads
2. `publishDeleteMessage()` - For document deletions
3. `publishUpdateMessage()` - For document updates (name changes, project moves)

## Current Integration Points

### 1. Resource Creation (Upload)
**Location**: `src/services/rag/ragService.ts`
**Method**: `publishUploadMessage()`
**Trigger**: When the posthog flag  is true and a resource is uploaded to RAG
**Coverage**: ✅ **COVERED**
- **Lines 144-165**: For regular document uploads
- **Lines 328-349**: For transcript uploads (audio/video files)

**Details**:
- Publishes upload messages for both regular documents and transcripts
- Includes comprehensive metadata (filename, category, audio/video info, tags, custom data)
- Has proper error handling that doesn't fail resource creation
- Only triggers when posthog flag is enabled

### 2. Resource Deletion
**Location**: `src/handlers/resources/deleteResourceHandler.ts`
**Method**: `publishDeleteMessage()`
**Trigger**: When a document/image resource is deleted
**Coverage**: ✅ **COVERED**
- **Lines 67-85**: Publishes delete message for document/image files

**Details**:
- Only triggers for document/image files (using `isDocumentOrImage()`)
- Includes proper error handling
- Doesn't fail resource deletion if document processor message fails

### 3. Resource Updates
**Location**: `src/handlers/resources/updateResourceHandler.ts`
**Method**: `publishUpdateMessage()`
**Trigger**: When resource name or project ID is changed
**Coverage**: ✅ **COVERED**
- **Lines 70-95**: Publishes update messages for name or project ID changes

**Details**:
- Handles both `NAME` and `PROJECT_ID` update types
- Only triggers for resources with `RagSyncStatus.SYNCED`
- Includes metadata about old and new values
- Proper error handling

### 4. Project Deletion
**Location**: `src/handlers/projects/deleteProjectHandler.ts`
**Method**: `publishUpdateMessage()`
**Trigger**: When a project is deleted and resources are moved to default project
**Coverage**: ✅ **COVERED** (Recently Added)
- **Lines 48-75**: Publishes update messages for all document/image resources in the project

**Details**:
- Uses `PROJECT_ID` update type since resources are moved, not deleted
- Processes all document/image resources in the project
- Moves resources to default project
- Comprehensive error handling

## Potential Gaps in Coverage

### 1. Retention Service Resource Deletion
**Location**: `src/services/retention/index.ts`
**Issue**: ✅ **COVERED** (Recently Added)
- **Lines 218-240**: Resources are deleted when retention period expires
- **Coverage**: Document processor is now notified when resources are automatically deleted due to retention policies

**Details**:
- Publishes delete messages for document/image files before deletion
- Uses the original creator's ID as the user_id
- Includes proper error handling that doesn't fail retention deletion
- Comprehensive logging for both success and failure cases


### 2. Resource Move Operations
**Issue**: ✅ **COVERED**
- Project deletion now covers moving resources to default project
- Individual resource updates cover project ID changes


## File Type Coverage Analysis

### Document/Image Files
- ✅ **Upload**: Covered via RAG service
- ✅ **Delete**: Covered via delete resource handler
- ✅ **Update**: Covered via update resource handler
- ✅ **Project Move**: Covered via project deletion handler

### Audio/Video Files
- ✅ **Upload**: Covered via RAG service (transcript upload)
- ✅ **Delete**: Covered via delete resource handler
- ✅ **Project Move**: Covered via project deletion handler

## Error Handling Analysis

All current integrations have proper error handling:
- ✅ Document processor failures don't fail the main operation
- ✅ Comprehensive logging for both success and failure cases
- ✅ Error messages include relevant context (resource ID, user ID, project ID)


## Summary

**Current Coverage**: 100% ✅
- All major resource operations (create, update, delete, project move) are covered
- Retention-based deletions are now covered
- Error handling is comprehensive across all integrations

