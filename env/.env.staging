NODE_ENV=staging
PORT=8080
# Cloud Run URL format: https://SERVICE_NAME-PROJECT_NUMBER.REGION.run.app
# This will be updated during deployment with actual Cloud Run URL
SERVER_ORIGIN=https://aida-service-staging-334815083034.europe-west2.run.app
GCP_PROJECT_ID=aida---stg
GCP_LOCATION=europe-west2

# db
DB_HOST=/cloudsql/aida---stg:europe-west2:aida-db-stg
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-stg
GCS_TEMP_FOLDER_FOR_DOCUMENT_PROCESSOR=temp_folder_for_document_processor

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=a760403330e0b2ac369e79804b709f1d84c7dc1e
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida---stg/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida---stg/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida---stg/topics/staging-transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida---stg/subscriptions/staging-transcoder-notify-sub
GCP_PUBSUB_DOCUMENT_PROCESSOR_TOPIC_NAME=projects/aida---stg/topics/document-processor
AIDA_API_KEY=8IXtyR898g8OplVd9frruCzcf7sqXQINeH5GaUp7l28Knd03aFmpF3bXuhLTFcFX

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash

# aida client
AIDA_ENDPOINT=https://staging-aida.beings.com

JWT_SECRET=aIdAsTg-secret

# Posthog
POSTHOG_KEY=phc_enkexxSwlhgDvA0OFP5F9Eng7Q9gLhxxMHFdlq1jXCy
POSTHOG_HOST=https://eu.i.posthog.com

WEB_APP_URL=https://staging-aida.beings.com

HTTP_LOGGING_ENABLED=true
CONSOLE_LOGGING_ENABLED=false
GCP_LOGGING_ENABLED=true

# Pinecone
PINECONE_API_KEY=pcsk_3GFfR5_N5TmgYA4MyEvgLhhEZ2hWzDX7JtStcdDpuSX7oRKJVTmBh3QMaPDNnEHiPyzSsW
PINECONE_ASSISTANT_NAME=beings-stg
PINECONE_ASSISTANT_HOST=https://prod-eu-data.ke.pinecone.io

#Beings RAG Server
#TODO need to change when Document processor deployed for STG
BEINGS_RAG_SERVICE_URL=https://rag-service-dev-1077482976117.europe-west1.run.app
BEINGS_MCP_AUTH_TOKEN=63f4945d921d599f27ae4fdf5bada3f1

# Shortlink config
URL_DEFAULT_EXPIRY_DAYS=30
URL_MAX_CLICKS_PER_HOUR=100
URL_SHORT_CODE_LENGTH=7
URL_SHORTENING_ENABLED=true
URL_SHORTENER_SECRET_KEY=y0K29PMRsuxcQpsziGpXX6gAHYM12uwa
ENABLE_SPRITE_SHEET_GEN=true
STORAGE_CDN_URL=https://aida-cdn-staging.beings.com/



#Langfuse
#TODO need to change when Document processor deployed for STG
LANGFUSE_PUBLIC_KEY=pk-lf-5cc3a68d-558b-4023-a5af-520a87314147
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_BASE_URL=https://cloud.langfuse.com