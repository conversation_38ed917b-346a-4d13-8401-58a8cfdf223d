NODE_ENV=production
PORT=8080
# Cloud Run URL format: https://SERVICE_NAME-PROJECT_NUMBER.REGION.run.app
# This will be updated during deployment with actual Cloud Run URL
SERVER_ORIGIN=https://aida-service-prod-567796665340.europe-west2.run.app/
GCP_PROJECT_ID=aida-prod-447812
GCP_LOCATION=europe-west2

# db
DB_HOST=/cloudsql/aida-prod-447812:europe-west2:aida-db-prod
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-prod
GCS_TEMP_FOLDER_FOR_DOCUMENT_PROCESSOR=temp_folder_for_document_processor

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=cd52341f1ca1f0f3c2860861edffec4b144ee25d
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida-prod-447812/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida-prod-447812/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida-prod-447812/topics/transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida-prod-447812/subscriptions/transcoder-notify-sub
GCP_PUBSUB_DOCUMENT_PROCESSOR_TOPIC_NAME=projects/aida-prod-447812/topics/document-processor

AIDA_API_KEY=g7jWzK215p9RsT6b8yqqaDk4f1mXvAHEcZ3BnL8o96Iwd57rGjxF2aUeYKPzNlQ

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash

# aida client
AIDA_ENDPOINT=https://app.beings.com

# jwt
JWT_SECRET=aIdApRoD-secret

# Posthog
POSTHOG_KEY=phc_HFF4QaKZxnY8qZqSmdtu3UOFDcIUBFHF2XtY2mnCErf
POSTHOG_HOST=https://eu.i.posthog.com

WEB_APP_URL=https://app.beings.com

# logging
HTTP_LOGGING_ENABLED=true
CONSOLE_LOGGING_ENABLED=false
GCP_LOGGING_ENABLED=true

# Pinecone
PINECONE_API_KEY=pcsk_3GFfR5_N5TmgYA4MyEvgLhhEZ2hWzDX7JtStcdDpuSX7oRKJVTmBh3QMaPDNnEHiPyzSsW
PINECONE_ASSISTANT_NAME=beings-prod
PINECONE_ASSISTANT_HOST=https://prod-eu-data.ke.pinecone.io

#Beings RAG Server
#TODO need to change when Document processor deployed for PROD
BEINGS_RAG_SERVICE_URL=https://rag-service-dev-1077482976117.europe-west1.run.app
BEINGS_MCP_AUTH_TOKEN=63f4945d921d599f27ae4fdf5bada3f1


# Shortlink config
URL_DEFAULT_EXPIRY_DAYS=30
URL_MAX_CLICKS_PER_HOUR=100
URL_SHORT_CODE_LENGTH=7
URL_SHORTENING_ENABLED=true
URL_SHORTENER_SECRET_KEY=011rskMv1GGlv9ns9Efii4VCeosREJ5Z
ENABLE_SPRITE_SHEET_GEN=true


#Langfuse
#TODO need to change when Document processor deployed for PROD
LANGFUSE_PUBLIC_KEY=pk-lf-5cc3a68d-558b-4023-a5af-520a87314147
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_BASE_URL=https://cloud.langfuse.com
