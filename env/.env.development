NODE_ENV=development
PORT=8080
# Cloud Run URL format: https://SERVICE_NAME-PROJECT_NUMBER.REGION.run.app
# This will be updated during deployment with actual Cloud Run URL
SERVER_ORIGIN=https://aida-service-dev-1077482976117.europe-west2.run.app
GCP_PROJECT_ID=aida-22a9a
GCP_LOCATION=europe-west2

# db
DB_HOST=/cloudsql/aida-22a9a:europe-west2:aida-db-dev
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-dev
GCS_TEMP_FOLDER_FOR_DOCUMENT_PROCESSOR=temp_folder_for_document_processor

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=e86da6ec7768c9e00044e7095050b8fb3f4151be
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida-22a9a/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida-22a9a/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida-22a9a/topics/transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida-22a9a/subscriptions/transcoder-notify-sub
GCP_PUBSUB_DOCUMENT_PROCESSOR_TOPIC_NAME=projects/aida-22a9a/topics/document-processor
AIDA_API_KEY=8IXtyR898g8OplVd9frruCzcf7sqXQINeH5GaUp7l28Knd03aFmpF3bXuhLTFcFX

AIDA_ENDPOINT=https://dev-aida.beings.com

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash
WEB_APP_URL=https://dev-aida.beings.com

JWT_SECRET=aIdAdEv-secret

# Posthog
POSTHOG_KEY=phc_SxDbeikguqvCW2d8j1q7sOdJVeuKBOQObE4OLYdxZKV
POSTHOG_HOST=https://eu.i.posthog.com

# logging
HTTP_LOGGING_ENABLED=true # please set it false for local development
CONSOLE_LOGGING_ENABLED=false # please set it true for local development
GCP_LOGGING_ENABLED=true # please set it false to avoid spaming DEV Google Cloud Logging from local development

# Pinecone
PINECONE_API_KEY=pcsk_3GFfR5_N5TmgYA4MyEvgLhhEZ2hWzDX7JtStcdDpuSX7oRKJVTmBh3QMaPDNnEHiPyzSsW
PINECONE_ASSISTANT_NAME=beings-dev
PINECONE_ASSISTANT_HOST=https://prod-eu-data.ke.pinecone.io

#Beings RAG Server
BEINGS_RAG_SERVICE_URL=https://rag-service-dev-1077482976117.europe-west1.run.app
BEINGS_MCP_AUTH_TOKEN=63f4945d921d599f27ae4fdf5bada3f1

# Shortlink config
URL_DEFAULT_EXPIRY_DAYS=30
URL_MAX_CLICKS_PER_HOUR=100
URL_SHORT_CODE_LENGTH=7
URL_SHORTENING_ENABLED=true
URL_SHORTENER_SECRET_KEY=XOAZf5ZFRxl0UptlRqMEayyAXfjKOa2u

ENABLE_SPRITE_SHEET_GEN=true
STORAGE_CDN_URL=https://aida-cdn-dev.beings.com/


#Langfuse
LANGFUSE_PUBLIC_KEY=pk-lf-10071748-d4f3-49c5-9782-55d171eebe99
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_BASE_URL=https://dev-langfuse.beings.com

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Anthropic
ANTHROPIC_API_KEY=************************************************************************************************************
