## Server for Aida project

# Pre-requisites

- Install Node 20
- Install Yarn
- Install Postgres
- Install GCP CLI to login

# Getting started

- Install dependencies

```
yarn install
```

- Login into GCP account for auth

```
gcloud auth application-default login
```

- Run command to fetch secrets from GCP SecretManager and generate local `.env` file

```
yarn load-env
```

- Modify the `.env` accordingly to local usage. Such as: `PORT`; `DB_HOST`; `DB_PASSWORD`; etc...

- **Run database migrations** (first time setup or after pulling changes with migrations)

```bash
yarn migrate up
```

- Start server

```
yarn dev
```

Server should be ready on `http://localhost:4000`

# Database Migrations

This project uses **automated database migrations** for safe schema management across environments.

## 🚀 Quick Commands

| Command | Description |
|---------|-------------|
| `yarn create-migration <name>` | Create manual migration template |
| `yarn create-migration <name> --auto` | Auto-generate migration from schema changes |
| `yarn migrate up` | Run all pending migrations |
| `yarn migrate down` | Rollback latest migration |
| `yarn migrate status` | Show migration status |

## 📋 Development Workflow

### When Making Schema Changes

1. **Update your Sequelize models** (in `src/schemas/models/`)
2. **Generate migration**: `yarn create-migration describe-change --auto`
3. **Review generated migration** in `migrations/` folder
4. **Test locally**: `yarn migrate up`
5. **Commit both model changes AND migration file**

### When Pulling Changes with Migrations

```bash
git pull
yarn migrate up  # Run any new migrations
yarn dev         # Start development
```

## 🔄 Pull Request Guidelines

### For Contributors

**✅ REQUIRED for PRs with schema changes:**
1. Include migration file in your PR
2. Test migration locally before submitting
3. Ensure migration has proper rollback (`down` function)

**Example PR Description:**
```
## Database Changes
- ✅ Migration included: `20240120-add-user-preferences.ts`
- ✅ Tested locally with `yarn migrate up/down`
- ✅ Rollback tested and working

## Changes
- Added `preferences` JSONB column to `users` table
- Added index on `preferences->>'theme'`
```

### For Reviewers

**✅ Migration Review Checklist:**
- [ ] Migration file included for schema changes
- [ ] Migration has both `up` and `down` functions
- [ ] No destructive operations without data migration plan
- [ ] Migration follows naming convention: `YYYYMMDDHHMMSS-description.ts`

## 🏗️ Environment Behavior

- **Development**: Uses `db.sync({ alter: true })` for rapid development
- **Staging/Production**: Uses migrations for controlled schema updates

## ⚠️ Important Notes

### DO NOT:
- ❌ Modify existing migration files (create new ones instead)
- ❌ Drop columns without data migration strategy
- ❌ Skip migrations in staging/production
- ❌ Commit model changes without corresponding migration

### Best Practices:
- ✅ Always test migrations locally before committing
- ✅ Use descriptive migration names
- ✅ Add indexes for new foreign keys
- ✅ Handle data migration for complex schema changes

## 🚨 Troubleshooting

### Migration Failed
```bash
# Check migration status
yarn migrate status

# Rollback if needed
yarn migrate down

# Fix migration file, then re-run
yarn migrate up
```

### Schema Drift (development vs staging)
```bash
# Regenerate migration from current state
yarn create-migration fix-schema-drift --auto
```

### New Team Member Setup
```bash
yarn install
yarn load-env
yarn migrate up  # Creates all tables
yarn dev
```

For detailed migration documentation, see [MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md)
