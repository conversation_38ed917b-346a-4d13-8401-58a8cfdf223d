# Document Processor

Document Processor is a tool for processing documents with support for embedding, LLM, and integration with Google Pub/Sub and Pinecone.

## Installation (Local)

1. **Create a virtual environment:**
   ```bash
   uv venv
   ```
2. **Activate the virtual environment:**
   - On macOS/Linux:
     ```bash
     source .venv/bin/activate
     ```
   - On Windows:
     ```cmd
     .venv\Scripts\activate
     ```
3. **Install dependencies:**
   ```bash
   uv sync --locked
   ```
4. **Environment variables:**

   - Copy `env.example` to `.env` and fill in the required values.

5. **Run the application:**
   ```bash
   uv run python -m app.main
   ```

## Running with Docker

1. **Build the Docker image:**
   ```bash
   docker build -t document-processor .
   ```
2. **Create a `.env.docker` file:**
   - Copy `env.example` to `.env.docker` and fill in the required values.
   ```bash
   cp env.example .env.docker
   ```
3. **Run the Docker container:**
   ```bash
   docker run --env-file=.env.docker document-processor
   ```

## Environment Variables

See `env.example` for all required environment variables. Make sure to provide valid values for all fields, especially for Google Cloud and Pinecone integration.

---

For more details, refer to the code and comments in the repository.
