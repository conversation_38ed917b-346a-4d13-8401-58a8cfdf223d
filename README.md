# RAG Service

A fully-managed, horizontally-scalable RAG (Retrieval-Augmented Generation) Service with MCP support.

## Features

- FastAPI-based REST API
- MCP (Model Control Protocol) integration
- Pinecone vector database integration
- Horizontal scalability
- CORS support
- Environment-based configuration
- Swagger UI documentation (in development mode)

## Installation (Local)

1. **Create a virtual environment:**

   ```bash
   uv venv
   ```

2. **Activate the virtual environment:**

   - On macOS/Linux:
     ```bash
     source .venv/bin/activate
     ```
   - On Windows:
     ```cmd
     .venv\Scripts\activate
     ```

3. **Install dependencies:**

   ```bash
   uv sync --locked
   ```

4. **Environment variables:**

   - Copy `env.example` to `.env` and fill in the required values

5. **Run the application:**
   ```bash
   python -m app.main
   ```
   The service will be available at `http://localhost:8000`

## Running with Docker

1. **Build the Docker image:**

   ```bash
   docker build -t rag-service .
   ```

2. **Create a `.env.docker` file:**

   - Copy `env.example` to `.env.docker` and fill in the required values.

   ```bash
   cp env.example .env.docker
   ```

3. **Run the Docker container:**
   ```bash
   docker run -p 8000:8000 --env-file=.env.docker rag-service
   ```

## MCP Integration

The service supports MCP (Model Control Protocol) integration. The MCP endpoint is available at:

```
http://localhost:8000/mcp
```

To configure MCP client, use the following configuration:

```json
{
  "mcpServers": {
    "relevant-chunk-retriever": {
      "url": "http://localhost:8000/mcp",
      "headers": {
        "Authorization": "Bearer <API_KEY>"
      }
    }
  }
}
```

Make sure to set the correct API key in the `Authorization` header. The value of API_KEY is the value of `AUTHENTICATION_TOKEN` variable in the `.env` file.

## API Documentation

When running in development mode, you can access:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
- OpenAPI Spec: `http://localhost:8000/openapi.json`

## Environment Variables

See `env.example` for all required environment variables. Make sure to provide valid values for all fields, especially for Pinecone integration.

Important environment variables:

- Additional variables can be found in the `env.example` file

## Development

The service supports hot-reloading in development mode for faster development cycles.

---

For more details, refer to the code and comments in the repository.
