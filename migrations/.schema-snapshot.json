[{"name": "Resource", "columns": [{"name": "orgId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "name", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "url", "type": "TEXT", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "fileSize", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "fileLastModified", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "sessionId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "duration", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "ffprobe", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "thumbnailUrl", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "topics", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "originalResourceState", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "thumbnailResourceId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "originalResourceId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "type", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "resource", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "folderId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "transcodedUrl", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "transcodedFileSize", "type": "DOUBLE PRECISION", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isTranscoding", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "ragSyncStatus", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "not_synced", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isSpriteSheets", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "vttSrc", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "transcriptionSrc", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ProjectAccessRequest", "columns": [{"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "sharedLinkId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "requestedById", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "status", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "PENDING", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "approvedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "rejectedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "rejectedReason", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "expiredAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "UserStatistics", "columns": [{"name": "orgId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "userId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "totalResourcesDuration", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "totalResourcesCount", "type": "INTEGER", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "totalTranscriptionsWordCount", "type": "INTEGER", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "totalFilesSize", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ProjectInvitation", "columns": [{"name": "status", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "SENT", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "code", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "email", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "role", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "VIEWER", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "expiredAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "userId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ProjectSharedLink", "columns": [{"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "code", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "expiredAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "maxAccessCount", "type": "INTEGER", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "currentAccessCount", "type": "INTEGER", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "VideoProcessing", "columns": [{"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "processingType", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "status", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "pending", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "jobId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "inputUri", "type": "TEXT", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "outputUri", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "retryCount", "type": "INTEGER", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "maxRetries", "type": "INTEGER", "allowNull": false, "defaultValue": "3", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "retryReason", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "jobData", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "errorDetails", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "startedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "completedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ResourceInInsightEngine", "columns": [{"name": "insightEngineId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "note", "type": "TEXT", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "revAiJobId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "revAiExtractionJobId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isFromBot", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDownloading", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "status", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "UnTranscript", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ProjectFolder", "columns": [{"name": "name", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "description", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "parentId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDeleted", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Summary", "columns": [{"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "title", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "purpose", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "keyTopics", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "percentageTalktime", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "keyThemes", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "keyActions", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "keySentiments", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "quantitativeInsights", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "qualitativeInsights", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "questionsWithAnswers", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "updatedById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "InsightEngine", "columns": [{"name": "orgId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "name", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "Unknown name", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "platform", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "web", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "uploadAction", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "upload", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "UserAida", "columns": [{"name": "userId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isConfirmed", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ProjectMember", "columns": [{"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "userId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "role", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "VIEWER", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": "2025-07-20 05:09:15.13+00", "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "RetentionSetting", "columns": [{"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "expiryDate", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "extensionCount", "type": "INTEGER", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "lastExtensionDate", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "extensionHistory", "type": "JSON", "allowNull": false, "defaultValue": "[]", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "policyType", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "default", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "notes", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Transcription", "columns": [{"name": "content", "type": "TEXT", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "resourceInInsightEngineId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "nameFromRevAi", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "Unknown name", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "startTime", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "endTime", "type": "DOUBLE PRECISION", "allowNull": false, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "speakerId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDeleted", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Note", "columns": [{"name": "title", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "content", "type": "TEXT", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "updatedById", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDeleted", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Session", "columns": [{"name": "orgId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "meetingUrl", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "videoUrl", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "title", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "description", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "settings", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "layout", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "startTime", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "endTime", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "actualEndTime", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "insightEngineId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "recallBotId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "platForm", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "<PERSON><PERSON><PERSON>", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "eventMeetingId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "eventMeetingPlatform", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "<PERSON><PERSON><PERSON>", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isBlocked", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "status", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": "NotStarted", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "recallBotStatus", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "shouldSendSummaryToEmail", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Project", "columns": [{"name": "name", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "description", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDeleted", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "sharedWith", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "isDefault", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ResourceTranscodedJob", "columns": [{"name": "jobId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "outputFileName", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "input", "type": "TEXT", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "output", "type": "TEXT", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "callback_data", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "retryCount", "type": "INTEGER", "allowNull": true, "defaultValue": "0", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "retryReason", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "hasAudio", "type": "BOOLEAN", "allowNull": true, "defaultValue": true, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "mode", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": "keep-both", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "resourceId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "UserFeedback", "columns": [{"name": "entityId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "userId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "feedback", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "reason", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "type", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "rating", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": "now()", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "updatedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": "now()", "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "metadata", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "ChatMessage", "columns": [{"name": "conversationId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "role", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "content", "type": "TEXT", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "model", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "usage", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "cost", "type": "DOUBLE PRECISION", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "resources", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "Conversation", "columns": [{"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "deletedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdById", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "projectId", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "CalendarAuditLog", "columns": [{"name": "eventId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "calendarId", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "changeType", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "changedBy", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "timestamp", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "changes", "type": "JSON", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "aidaBotInvolved", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "processed", "type": "BOOLEAN", "allowNull": false, "defaultValue": false, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}, {"name": "SSEConnections", "columns": [{"name": "id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": true, "autoIncrement": false, "unique": false}, {"name": "user_id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "project_id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "channel", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "connection_id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "instance_id", "type": "CHARACTER VARYING(255)", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "user_agent", "type": "TEXT", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "ip_address", "type": "CHARACTER VARYING(255)", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "connected_at", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "last_heartbeat", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": false, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "is_active", "type": "BOOLEAN", "allowNull": false, "defaultValue": true, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "createdAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}, {"name": "updatedAt", "type": "TIMESTAMP WITH TIME ZONE", "allowNull": true, "defaultValue": null, "primaryKey": false, "autoIncrement": false, "unique": false}], "indexes": [], "foreignKeys": []}]