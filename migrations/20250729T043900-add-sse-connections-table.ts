import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  // Check and create SSEConnections table
  const sseConnectionsExists = await queryInterface.tableExists("SSEConnections");
  if (!sseConnectionsExists) {
    await queryInterface.createTable("SSEConnections", {
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      project_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      channel: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      connection_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      instance_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      ip_address: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      connected_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      last_heartbeat: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
      },
    });

    // Add indexes for performance
    await queryInterface.addIndex("SSEConnections", ["project_id"], {
      name: "idx_sse_connections_project_id",
    });

    await queryInterface.addIndex("SSEConnections", ["user_id"], {
      name: "idx_sse_connections_user_id",
    });

    await queryInterface.addIndex("SSEConnections", ["channel"], {
      name: "idx_sse_connections_channel",
    });

    await queryInterface.addIndex("SSEConnections", ["is_active"], {
      name: "idx_sse_connections_active",
    });

    await queryInterface.addIndex("SSEConnections", ["last_heartbeat"], {
      name: "idx_sse_connections_heartbeat",
    });

    await queryInterface.addIndex("SSEConnections", ["instance_id"], {
      name: "idx_sse_connections_instance",
    });
  }
}

export async function down(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  // Remove indexes first
  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_project_id");
  } catch (error) {
    // Index might not exist, continue
  }

  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_user_id");
  } catch (error) {
    // Index might not exist, continue
  }

  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_channel");
  } catch (error) {
    // Index might not exist, continue
  }

  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_active");
  } catch (error) {
    // Index might not exist, continue
  }

  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_heartbeat");
  } catch (error) {
    // Index might not exist, continue
  }

  try {
    await queryInterface.removeIndex("SSEConnections", "idx_sse_connections_instance");
  } catch (error) {
    // Index might not exist, continue
  }

  // Drop the table if it exists
  const sseConnectionsExists = await queryInterface.tableExists("SSEConnections");
  if (sseConnectionsExists) {
    await queryInterface.dropTable("SSEConnections");
  }
}
