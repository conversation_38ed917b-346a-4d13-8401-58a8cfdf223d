import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Add soft delete fields to ProjectFolder table while keeping existing isDeleted
  await queryInterface.addColumn("ProjectFolder", "deletedAt", {
    type: DataTypes.DATE,
    allowNull: true,
    comment: "Timestamp when the project folder was soft deleted",
  });

  await queryInterface.addColumn("ProjectFolder", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this project folder",
  });

  // Add indexes for soft delete queries
  await queryInterface.addIndex("ProjectFolder", ["deletedAt"], {
    name: "idx_project_folder_deleted_at",
  });

  await queryInterface.addIndex("ProjectFolder", ["projectId", "deletedAt"], {
    name: "idx_project_folder_project_not_deleted",
  });

  await queryInterface.addIndex("ProjectFolder", ["isDeleted", "deletedAt"], {
    name: "idx_project_folder_soft_delete_combined",
  });
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Remove indexes
  await queryInterface.removeIndex("ProjectFolder", "idx_project_folder_soft_delete_combined");
  await queryInterface.removeIndex("ProjectFolder", "idx_project_folder_project_not_deleted");
  await queryInterface.removeIndex("ProjectFolder", "idx_project_folder_deleted_at");

  // Remove soft delete fields from ProjectFolder table
  await queryInterface.removeColumn("ProjectFolder", "deletedBy");
  await queryInterface.removeColumn("ProjectFolder", "deletedAt");
} 