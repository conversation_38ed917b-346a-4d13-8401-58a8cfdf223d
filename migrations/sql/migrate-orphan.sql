-- =====================================================
-- Migration: Link Orphaned Resources to Default Projects
-- =====================================================
-- This script links all resources without a projectId to their creator's default project
-- Excludes system resources (avatars, logos) from migration
-- =====================================================

-- Start transaction
BEGIN;

-- Log the start of migration
DO $$
BEGIN
    RAISE NOTICE '🔗 Starting migration: Linking orphaned resources to default projects...';
END $$;

-- Step 1: Count orphaned resources before migration
DO $$
DECLARE
    orphaned_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO orphaned_count
    FROM "Resource" r
    WHERE r."projectId" IS NULL 
      AND r."deletedAt" IS NULL
      AND r."createdById" IS NOT NULL
      AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted');
    
    RAISE NOTICE '📊 Found % orphaned resources to process', orphaned_count;
    
    IF orphaned_count = 0 THEN
        RAISE NOTICE '✅ No orphaned resources found. Migration completed.';
        RETURN;
    END IF;
END $$;

-- Step 2: Update all orphaned resources in one operation
-- This links resources to their creator's default project
UPDATE "Resource" r
SET 
    "projectId" = (
        SELECT p.id 
        FROM "Project" p 
        WHERE p."createdById" = r."createdById"
          AND p."isDefault" = TRUE
          AND p."deletedAt" IS NULL
          AND p."isDeleted" = FALSE
        LIMIT 1
    )
WHERE r."projectId" IS NULL 
  AND r."deletedAt" IS NULL
  AND r."createdById" IS NOT NULL
  AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
  AND EXISTS (
      SELECT 1 
      FROM "Project" p 
      WHERE p."createdById" = r."createdById"
        AND p."isDefault" = TRUE
        AND p."deletedAt" IS NULL
        AND p."isDeleted" = FALSE
  );

-- Step 3: Get migration results
DO $$
DECLARE
    updated_count INTEGER;
    skipped_count INTEGER;
    remaining_count INTEGER;
BEGIN
    -- Count how many were updated
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Count how many were skipped (no default project)
    SELECT COUNT(*) INTO skipped_count
    FROM "Resource" r
    WHERE r."projectId" IS NULL 
      AND r."deletedAt" IS NULL
      AND r."createdById" IS NOT NULL
      AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
      AND NOT EXISTS (
          SELECT 1 
          FROM "Project" p 
          WHERE p."createdById" = r."createdById"
                    AND p."isDefault" = TRUE
        AND p."deletedAt" IS NULL
        AND p."isDeleted" = FALSE
      );
    
    -- Count remaining orphaned resources
    SELECT COUNT(*) INTO remaining_count
    FROM "Resource" r
    WHERE r."projectId" IS NULL 
      AND r."deletedAt" IS NULL
      AND r."createdById" IS NOT NULL
      AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted');
    
    -- Log migration summary
    RAISE NOTICE '';
    RAISE NOTICE '📋 Migration Summary:';
    RAISE NOTICE '   Successfully linked: % resources', updated_count;
    RAISE NOTICE '   Skipped (no default project): % resources', skipped_count;
    RAISE NOTICE '   Remaining orphaned: % resources', remaining_count;
    
    IF remaining_count = 0 THEN
        RAISE NOTICE '🎉 All orphaned resources have been successfully linked to default projects!';
    ELSE
        RAISE NOTICE '⚠️  % resources still remain orphaned (likely users without default projects)', remaining_count;
    END IF;
END $$;

-- Step 4: Verification - Show distribution of resources across projects
DO $$
DECLARE
    project_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 Resource Distribution Across Default Projects:';
    RAISE NOTICE '================================================';
    
    FOR project_record IN
        SELECT 
            p.id,
            p.name as project_name,
            p."createdById",
            COUNT(r.id) as resource_count
        FROM "Project" p
        LEFT JOIN "Resource" r ON p.id = r."projectId" AND r."deletedAt" IS NULL
        WHERE p."isDefault" = TRUE
          AND p."deletedAt" IS NULL
          AND p."isDeleted" = FALSE
        GROUP BY p.id, p.name, p."createdById"
        ORDER BY resource_count DESC
    LOOP
        RAISE NOTICE 'Project: % (ID: %) - User: % - Resources: %', 
            project_record.project_name, 
            project_record.id, 
            project_record."createdById", 
            project_record.resource_count;
    END LOOP;
END $$;

-- Step 5: Show details of remaining orphaned resources (if any)
DO $$
DECLARE
    orphaned_record RECORD;
    remaining_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO remaining_count
    FROM "Resource" r
    WHERE r."projectId" IS NULL 
      AND r."deletedAt" IS NULL
      AND r."createdById" IS NOT NULL
      AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted');
    
    IF remaining_count > 0 THEN
        RAISE NOTICE '';
        RAISE NOTICE '📋 Remaining Orphaned Resources:';
        RAISE NOTICE '================================';
        
        FOR orphaned_record IN
            SELECT 
                r.id,
                r.name,
                r."createdById",
                r.type,
                r."createdAt"
            FROM "Resource" r
            WHERE r."projectId" IS NULL 
              AND r."deletedAt" IS NULL
              AND r."createdById" IS NOT NULL
              AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
            ORDER BY r."createdAt" DESC
            LIMIT 20
        LOOP
            RAISE NOTICE 'Resource: % (ID: %) - Type: % - User: % - Created: %', 
                orphaned_record.name, 
                orphaned_record.id, 
                orphaned_record.type, 
                orphaned_record."createdById", 
                orphaned_record."createdAt";
        END LOOP;
        
        IF remaining_count > 20 THEN
            RAISE NOTICE '... and % more resources', remaining_count - 20;
        END IF;
    END IF;
END $$;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Migration completed successfully!';
    RAISE NOTICE 'Transaction will be committed.';
END $$;

-- Commit the transaction
COMMIT;

-- =====================================================
-- Rollback Script (if needed)
-- =====================================================
-- To rollback, uncomment and run this section:
/*
BEGIN;

-- Unlink resources from default projects
UPDATE "Resource" r
SET 
    "projectId" = NULL
WHERE r."projectId" IS NOT NULL
  AND r."deletedAt" IS NULL
  AND EXISTS (
      SELECT 1 
      FROM "Project" p 
      WHERE p.id = r."projectId"
        AND p."isDefault" = true
        AND p."createdById" = r."createdById"
        AND p."deletedAt" IS NULL
        AND p."isDeleted" = false
  )
  AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted');

-- Log rollback results
DO $$
DECLARE
    unlinked_count INTEGER;
BEGIN
    GET DIAGNOSTICS unlinked_count = ROW_COUNT;
    RAISE NOTICE '🔄 Rollback completed: % resources unlinked', unlinked_count;
END $$;

COMMIT;
*/
