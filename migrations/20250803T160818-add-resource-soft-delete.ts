import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Add soft delete fields to Resource table
  await queryInterface.addColumn("Resource", "deletedAt", {
    type: DataTypes.DATE,
    allowNull: true,
    comment: "Timestamp when the resource was soft deleted",
  });

  await queryInterface.addColumn("Resource", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this resource",
  });

  // Add index for soft delete queries (deletedAt IS NULL for active resources)
  await queryInterface.addIndex("Resource", ["deletedAt"], {
    name: "idx_resource_deleted_at",
  });

  // Add composite index for project resources excluding deleted ones
  await queryInterface.addIndex("Resource", ["projectId", "deletedAt"], {
    name: "idx_resource_project_not_deleted",
  });
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Remove indexes
  await queryInterface.removeIndex("Resource", "idx_resource_project_not_deleted");
  await queryInterface.removeIndex("Resource", "idx_resource_deleted_at");

  // Remove soft delete fields from Resource table
  await queryInterface.removeColumn("Resource", "deletedBy");
  await queryInterface.removeColumn("Resource", "deletedAt");
} 
