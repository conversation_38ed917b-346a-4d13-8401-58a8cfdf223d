import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Add soft delete fields to VideoProcessing table
  await queryInterface.addColumn("VideoProcessing", "deletedAt", {
    type: DataTypes.DATE,
    allowNull: true,
    comment: "Timestamp when the video processing record was soft deleted",
  });

  await queryInterface.addColumn("VideoProcessing", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this video processing record",
  });

  // Add soft delete fields to ResourceInInsightEngine table
  await queryInterface.addColumn("ResourceInInsightEngine", "deletedAt", {
    type: DataTypes.DATE,
    allowNull: true,
    comment: "Timestamp when the resource insight engine relation was soft deleted",
  });

  await queryInterface.addColumn("ResourceInInsightEngine", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this resource insight engine relation",
  });

  // Add deletedBy field to Transcription table (deletedAt already exists)
  await queryInterface.addColumn("Transcription", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this transcription",
  });

  // Rename deletedById to deletedBy in InsightEngine table for consistency
  await queryInterface.renameColumn("InsightEngine", "deletedById", "deletedBy");

  // Add indexes for soft delete queries
  await queryInterface.addIndex("VideoProcessing", ["deletedAt"], {
    name: "idx_video_processing_deleted_at",
  });

  await queryInterface.addIndex("VideoProcessing", ["resourceId", "deletedAt"], {
    name: "idx_video_processing_resource_not_deleted",
  });

  await queryInterface.addIndex("ResourceInInsightEngine", ["deletedAt"], {
    name: "idx_resource_insight_engine_deleted_at",
  });

  await queryInterface.addIndex("ResourceInInsightEngine", ["resourceId", "deletedAt"], {
    name: "idx_resource_insight_engine_resource_not_deleted",
  });

  await queryInterface.addIndex("Transcription", ["deletedAt"], {
    name: "idx_transcription_deleted_at",
  });

  await queryInterface.addIndex("Transcription", ["resourceInInsightEngineId", "deletedAt"], {
    name: "idx_transcription_resource_ie_not_deleted",
  });

  await queryInterface.addIndex("InsightEngine", ["deletedAt"], {
    name: "idx_insight_engine_deleted_at",
  });
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Remove indexes
  await queryInterface.removeIndex("InsightEngine", "idx_insight_engine_deleted_at");
  await queryInterface.removeIndex("Transcription", "idx_transcription_resource_ie_not_deleted");
  await queryInterface.removeIndex("Transcription", "idx_transcription_deleted_at");
  await queryInterface.removeIndex("ResourceInInsightEngine", "idx_resource_insight_engine_resource_not_deleted");
  await queryInterface.removeIndex("ResourceInInsightEngine", "idx_resource_insight_engine_deleted_at");
  await queryInterface.removeIndex("VideoProcessing", "idx_video_processing_resource_not_deleted");
  await queryInterface.removeIndex("VideoProcessing", "idx_video_processing_deleted_at");

  // Rename deletedBy back to deletedById in InsightEngine table
  await queryInterface.renameColumn("InsightEngine", "deletedBy", "deletedById");

  // Remove deletedBy field from Transcription table
  await queryInterface.removeColumn("Transcription", "deletedBy");

  // Remove soft delete fields from ResourceInInsightEngine table
  await queryInterface.removeColumn("ResourceInInsightEngine", "deletedBy");
  await queryInterface.removeColumn("ResourceInInsightEngine", "deletedAt");

  // Remove soft delete fields from VideoProcessing table
  await queryInterface.removeColumn("VideoProcessing", "deletedBy");
  await queryInterface.removeColumn("VideoProcessing", "deletedAt");
} 
