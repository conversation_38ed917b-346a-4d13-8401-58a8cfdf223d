import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Check and create Resource table
  const resourceExists = await queryInterface.tableExists("Resource");
  if (!resourceExists) {
    await queryInterface.createTable("Resource", {
      orgId: {
        type: DataTypes.STRING(255),
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      url: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      fileSize: {
        type: DataTypes.DOUBLE,
        allowNull: false,
      },
      fileLastModified: {
        type: DataTypes.DATE,
      },
      sessionId: {
        type: DataTypes.STRING(255),
      },
      duration: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: "0",
      },
      ffprobe: {
        type: DataTypes.JSON,
      },
      thumbnailUrl: {
        type: DataTypes.STRING(255),
      },
      topics: {
        type: DataTypes.JSON,
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      originalResourceState: {
        type: DataTypes.JSON,
      },
      thumbnailResourceId: {
        type: DataTypes.STRING(255),
      },
      originalResourceId: {
        type: DataTypes.STRING(255),
      },
      type: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "resource",
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      projectId: {
        type: DataTypes.STRING(255),
      },
      folderId: {
        type: DataTypes.STRING(255),
      },
      transcodedUrl: {
        type: DataTypes.TEXT,
      },
      transcodedFileSize: {
        type: DataTypes.DOUBLE,
      },
      isTranscoding: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      ragSyncStatus: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "not_synced",
      },
      isSpriteSheets: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      vttSrc: {
        type: DataTypes.TEXT,
      },
      transcriptionSrc: {
        type: DataTypes.TEXT,
      },
    });
  }

  // Check and create ProjectAccessRequest table
  const projectAccessRequestExists = await queryInterface.tableExists(
    "ProjectAccessRequest"
  );
  if (!projectAccessRequestExists) {
    await queryInterface.createTable("ProjectAccessRequest", {
      projectId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      sharedLinkId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      requestedById: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "PENDING",
      },
      approvedAt: {
        type: DataTypes.DATE,
      },
      rejectedAt: {
        type: DataTypes.DATE,
      },
      rejectedReason: {
        type: DataTypes.STRING(255),
      },
      expiredAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create UserStatistics table
  const userStatisticsExists = await queryInterface.tableExists(
    "UserStatistics"
  );
  if (!userStatisticsExists) {
    await queryInterface.createTable("UserStatistics", {
      orgId: {
        type: DataTypes.STRING(255),
      },
      userId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      totalResourcesDuration: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: "0",
      },
      totalResourcesCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "0",
      },
      totalTranscriptionsWordCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "0",
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      totalFilesSize: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: "0",
      },
    });
  }

  // Check and create ProjectInvitation table
  const projectInvitationExists = await queryInterface.tableExists(
    "ProjectInvitation"
  );
  if (!projectInvitationExists) {
    await queryInterface.createTable("ProjectInvitation", {
      status: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "SENT",
      },
      code: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      projectId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      role: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "VIEWER",
      },
      expiredAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      userId: {
        type: DataTypes.STRING(255),
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create ProjectSharedLink table
  const projectSharedLinkExists = await queryInterface.tableExists(
    "ProjectSharedLink"
  );
  if (!projectSharedLinkExists) {
    await queryInterface.createTable("ProjectSharedLink", {
      projectId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      expiredAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      maxAccessCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      currentAccessCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "0",
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create VideoProcessing table
  const videoProcessingExists = await queryInterface.tableExists(
    "VideoProcessing"
  );
  if (!videoProcessingExists) {
    await queryInterface.createTable("VideoProcessing", {
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      resourceId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      processingType: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "pending",
      },
      jobId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      inputUri: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      outputUri: {
        type: DataTypes.TEXT,
      },
      retryCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "0",
      },
      maxRetries: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "3",
      },
      retryReason: {
        type: DataTypes.TEXT,
      },
      jobData: {
        type: DataTypes.JSON,
      },
      errorDetails: {
        type: DataTypes.JSON,
      },
      startedAt: {
        type: DataTypes.DATE,
      },
      completedAt: {
        type: DataTypes.DATE,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create ResourceInInsightEngine table
  const resourceInInsightEngineExists = await queryInterface.tableExists(
    "ResourceInInsightEngine"
  );
  if (!resourceInInsightEngineExists) {
    await queryInterface.createTable("ResourceInInsightEngine", {
      insightEngineId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      resourceId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: false,
        defaultValue: "",
      },
      revAiJobId: {
        type: DataTypes.STRING(255),
      },
      revAiExtractionJobId: {
        type: DataTypes.STRING(255),
      },
      isFromBot: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isDownloading: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      status: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "UnTranscript",
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create ProjectFolder table
  const projectFolderExists = await queryInterface.tableExists("ProjectFolder");
  if (!projectFolderExists) {
    await queryInterface.createTable("ProjectFolder", {
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
      },
      projectId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      parentId: {
        type: DataTypes.STRING(255),
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create Summary table
  const summaryExists = await queryInterface.tableExists("Summary");
  if (!summaryExists) {
    await queryInterface.createTable("Summary", {
      resourceId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      purpose: {
        type: DataTypes.TEXT,
      },
      keyTopics: {
        type: DataTypes.JSON,
      },
      percentageTalktime: {
        type: DataTypes.JSON,
      },
      keyThemes: {
        type: DataTypes.JSON,
      },
      keyActions: {
        type: DataTypes.TEXT,
      },
      keySentiments: {
        type: DataTypes.JSON,
      },
      quantitativeInsights: {
        type: DataTypes.JSON,
      },
      qualitativeInsights: {
        type: DataTypes.JSON,
      },
      questionsWithAnswers: {
        type: DataTypes.JSON,
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      updatedById: {
        type: DataTypes.STRING(255),
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create InsightEngine table
  const insightEngineExists = await queryInterface.tableExists("InsightEngine");
  if (!insightEngineExists) {
    await queryInterface.createTable("InsightEngine", {
      orgId: {
        type: DataTypes.STRING(255),
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "Unknown name",
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
      deletedById: {
        type: DataTypes.STRING(255),
      },
      platform: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "web",
      },
      uploadAction: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "upload",
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create UserAida table
  const userAidaExists = await queryInterface.tableExists("UserAida");
  if (!userAidaExists) {
    await queryInterface.createTable("UserAida", {
      userId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      isConfirmed: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create ProjectMember table
  const projectMemberExists = await queryInterface.tableExists("ProjectMember");
  if (!projectMemberExists) {
    await queryInterface.createTable("ProjectMember", {
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      projectId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      userId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      role: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "VIEWER",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: "2025-07-20 05:09:15.13+00",
      },
    });
  }

  // Check and create RetentionSetting table
  const retentionSettingExists = await queryInterface.tableExists(
    "RetentionSetting"
  );
  if (!retentionSettingExists) {
    await queryInterface.createTable("RetentionSetting", {
      resourceId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      expiryDate: {
        type: DataTypes.DATE,
      },
      extensionCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: "0",
      },
      lastExtensionDate: {
        type: DataTypes.DATE,
      },
      extensionHistory: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: "[]",
      },
      policyType: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "default",
      },
      notes: {
        type: DataTypes.TEXT,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create Transcription table
  const transcriptionExists = await queryInterface.tableExists("Transcription");
  if (!transcriptionExists) {
    await queryInterface.createTable("Transcription", {
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
        defaultValue: "",
      },
      resourceInInsightEngineId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      nameFromRevAi: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "Unknown name",
      },
      startTime: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: "0",
      },
      endTime: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: "0",
      },
      speakerId: {
        type: DataTypes.STRING(255),
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
    });
  }

  // Check and create Note table
  const noteExists = await queryInterface.tableExists("Note");
  if (!noteExists) {
    await queryInterface.createTable("Note", {
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "",
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
        defaultValue: "",
      },
      resourceId: {
        type: DataTypes.STRING(255),
      },
      createdById: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      updatedById: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      projectId: {
        type: DataTypes.STRING(255),
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
    });
  }

  // Check and create Session table
  const sessionExists = await queryInterface.tableExists("Session");
  if (!sessionExists) {
    await queryInterface.createTable("Session", {
      orgId: {
        type: DataTypes.STRING(255),
      },
      createdById: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      meetingUrl: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      videoUrl: {
        type: DataTypes.TEXT,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "",
      },
      settings: {
        type: DataTypes.JSON,
      },
      layout: {
        type: DataTypes.JSON,
      },
      startTime: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      endTime: {
        type: DataTypes.DATE,
      },
      actualEndTime: {
        type: DataTypes.DATE,
      },
      insightEngineId: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "",
      },
      recallBotId: {
        type: DataTypes.STRING(255),
      },
      platForm: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "Default",
      },
      eventMeetingId: {
        type: DataTypes.STRING(255),
      },
      eventMeetingPlatform: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "Default",
      },
      isBlocked: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      status: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: "NotStarted",
      },
      recallBotStatus: {
        type: DataTypes.STRING(255),
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      shouldSendSummaryToEmail: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      projectId: {
        type: DataTypes.STRING(255),
      },
    });
  }

  // Check and create Project table
  const projectExists = await queryInterface.tableExists("Project");
  if (!projectExists) {
    await queryInterface.createTable("Project", {
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      sharedWith: {
        type: DataTypes.JSON,
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    });
  }

  // Check and create ResourceTranscodedJob table
  const resourceTranscodedJobExists = await queryInterface.tableExists(
    "ResourceTranscodedJob"
  );
  if (!resourceTranscodedJobExists) {
    await queryInterface.createTable("ResourceTranscodedJob", {
      jobId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      outputFileName: {
        type: DataTypes.STRING(255),
      },
      input: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      output: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      callback_data: {
        type: DataTypes.JSON,
      },
      retryCount: {
        type: DataTypes.INTEGER,
        defaultValue: "0",
      },
      retryReason: {
        type: DataTypes.STRING(255),
      },
      hasAudio: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      mode: {
        type: DataTypes.STRING(255),
        defaultValue: "keep-both",
      },
      resourceId: {
        type: DataTypes.STRING(255),
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }

  // Check and create UserFeedback table
  const userFeedbackExists = await queryInterface.tableExists("UserFeedback");
  if (!userFeedbackExists) {
    await queryInterface.createTable("UserFeedback", {
      entityId: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      feedback: {
        type: DataTypes.TEXT,
      },
      reason: {
        type: DataTypes.TEXT,
      },
      type: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      rating: {
        type: DataTypes.STRING(255),
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      metadata: {
        type: DataTypes.JSON,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
    });
  }

  // Check and create ChatMessage table
  const chatMessageExists = await queryInterface.tableExists("ChatMessage");
  if (!chatMessageExists) {
    await queryInterface.createTable("ChatMessage", {
      conversationId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      role: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      model: {
        type: DataTypes.STRING(255),
      },
      usage: {
        type: DataTypes.JSON,
      },
      cost: {
        type: DataTypes.DOUBLE,
      },
      resources: {
        type: DataTypes.JSON,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
    });
  }

  // Check and create Conversation table
  const conversationExists = await queryInterface.tableExists("Conversation");
  if (!conversationExists) {
    await queryInterface.createTable("Conversation", {
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
      },
      createdById: {
        type: DataTypes.STRING(255),
      },
      projectId: {
        type: DataTypes.STRING(255),
      },
    });
  }

  // Check and create CalendarAuditLog table
  const calendarAuditLogExists = await queryInterface.tableExists(
    "CalendarAuditLog"
  );
  if (!calendarAuditLogExists) {
    await queryInterface.createTable("CalendarAuditLog", {
      eventId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      calendarId: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      changeType: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      changedBy: {
        type: DataTypes.STRING(255),
      },
      timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      changes: {
        type: DataTypes.JSON,
      },
      aidaBotInvolved: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      processed: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      id: {
        type: DataTypes.STRING(255),
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  }
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Only drop tables if they exist - reverse order to handle dependencies
  const calendarAuditLogExists = await queryInterface.tableExists(
    "CalendarAuditLog"
  );
  if (calendarAuditLogExists) {
    await queryInterface.dropTable("CalendarAuditLog");
  }

  const conversationExists = await queryInterface.tableExists("Conversation");
  if (conversationExists) {
    await queryInterface.dropTable("Conversation");
  }

  const chatMessageExists = await queryInterface.tableExists("ChatMessage");
  if (chatMessageExists) {
    await queryInterface.dropTable("ChatMessage");
  }

  const userFeedbackExists = await queryInterface.tableExists("UserFeedback");
  if (userFeedbackExists) {
    await queryInterface.dropTable("UserFeedback");
  }

  const resourceTranscodedJobExists = await queryInterface.tableExists(
    "ResourceTranscodedJob"
  );
  if (resourceTranscodedJobExists) {
    await queryInterface.dropTable("ResourceTranscodedJob");
  }

  const projectExists = await queryInterface.tableExists("Project");
  if (projectExists) {
    await queryInterface.dropTable("Project");
  }

  const sessionExists = await queryInterface.tableExists("Session");
  if (sessionExists) {
    await queryInterface.dropTable("Session");
  }

  const noteExists = await queryInterface.tableExists("Note");
  if (noteExists) {
    await queryInterface.dropTable("Note");
  }

  const transcriptionExists = await queryInterface.tableExists("Transcription");
  if (transcriptionExists) {
    await queryInterface.dropTable("Transcription");
  }

  const retentionSettingExists = await queryInterface.tableExists(
    "RetentionSetting"
  );
  if (retentionSettingExists) {
    await queryInterface.dropTable("RetentionSetting");
  }

  const projectMemberExists = await queryInterface.tableExists("ProjectMember");
  if (projectMemberExists) {
    await queryInterface.dropTable("ProjectMember");
  }

  const userAidaExists = await queryInterface.tableExists("UserAida");
  if (userAidaExists) {
    await queryInterface.dropTable("UserAida");
  }

  const insightEngineExists = await queryInterface.tableExists("InsightEngine");
  if (insightEngineExists) {
    await queryInterface.dropTable("InsightEngine");
  }

  const summaryExists = await queryInterface.tableExists("Summary");
  if (summaryExists) {
    await queryInterface.dropTable("Summary");
  }

  const projectFolderExists = await queryInterface.tableExists("ProjectFolder");
  if (projectFolderExists) {
    await queryInterface.dropTable("ProjectFolder");
  }

  const resourceInInsightEngineExists = await queryInterface.tableExists(
    "ResourceInInsightEngine"
  );
  if (resourceInInsightEngineExists) {
    await queryInterface.dropTable("ResourceInInsightEngine");
  }

  const videoProcessingExists = await queryInterface.tableExists(
    "VideoProcessing"
  );
  if (videoProcessingExists) {
    await queryInterface.dropTable("VideoProcessing");
  }

  const projectSharedLinkExists = await queryInterface.tableExists(
    "ProjectSharedLink"
  );
  if (projectSharedLinkExists) {
    await queryInterface.dropTable("ProjectSharedLink");
  }

  const projectInvitationExists = await queryInterface.tableExists(
    "ProjectInvitation"
  );
  if (projectInvitationExists) {
    await queryInterface.dropTable("ProjectInvitation");
  }

  const userStatisticsExists = await queryInterface.tableExists(
    "UserStatistics"
  );
  if (userStatisticsExists) {
    await queryInterface.dropTable("UserStatistics");
  }

  const projectAccessRequestExists = await queryInterface.tableExists(
    "ProjectAccessRequest"
  );
  if (projectAccessRequestExists) {
    await queryInterface.dropTable("ProjectAccessRequest");
  }

  const resourceExists = await queryInterface.tableExists("Resource");
  if (resourceExists) {
    await queryInterface.dropTable("Resource");
  }
}
