import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Create ConversationImage table
  await queryInterface.createTable("ConversationImage", {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true,
    },
    conversationId: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "ID of the conversation this image belongs to",
    },
    messageId: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "ID of the specific message this image was sent with",
    },
    originalFileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "Original filename of the uploaded image",
    },
    mimeType: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "MIME type of the image (e.g., image/jpeg, image/png)",
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "File size in bytes",
    },
    gcsPath: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: "Full GCS path to the stored image",
    },
    thumbnailPath: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: "GCS path to the thumbnail image (WebP format)",
    },
    width: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Image width in pixels",
    },
    height: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Image height in pixels",
    },
    inlineDataHash: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "Hash of the inline data for deduplication",
    },
    createdById: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "ID of the user who uploaded this image",
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  });

  // Add indexes for ConversationImage
  await queryInterface.addIndex("ConversationImage", ["conversationId"], {
    name: "conversation_images_conversation_id_idx",
  });

  await queryInterface.addIndex("ConversationImage", ["messageId"], {
    name: "conversation_images_message_id_idx",
  });

  await queryInterface.addIndex("ConversationImage", ["createdById"], {
    name: "conversation_images_created_by_idx",
  });

  await queryInterface.addIndex(
    "ConversationImage",
    ["conversationId", "messageId"],
    {
      name: "conversation_images_conversation_message_idx",
    }
  );

  // Add image-related fields to ChatMessage table
  await queryInterface.addColumn("ChatMessage", "hasImages", {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: "Quick lookup flag to check if this message contains images",
  });

  await queryInterface.addColumn("ChatMessage", "imageCount", {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: "Number of images attached to this message",
  });
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Remove image-related fields from ChatMessage table
  await queryInterface.removeColumn("ChatMessage", "hasImages");
  await queryInterface.removeColumn("ChatMessage", "imageCount");

  // Drop ConversationImage table
  await queryInterface.dropTable("ConversationImage");
}
