import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  await queryInterface.addIndex("Project", ["isDefault", "createdById"], {
    name: "idx_project_default_creator",
  });

  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    console.log('🔗 Starting migration: Linking orphaned resources to default projects...');
    
    // Step 1: Count orphaned resources before migration
    const [orphanedCountResult] = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count
      FROM "Resource" r
      WHERE r."projectId" IS NULL 
        AND r."deletedAt" IS NULL
        AND r."createdById" IS NOT NULL
        AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
    `, { transaction });
    
    const orphanedCount = (orphanedCountResult as any)[0]?.count || 0;
    console.log(`📊 Found ${orphanedCount} orphaned resources to process`);
    
    if (orphanedCount === 0) {
      console.log('✅ No orphaned resources found. Migration completed.');
      await transaction.commit();
      return;
    }
    
    // Step 2: Update all orphaned resources in one operation
    // This links resources to their creator's default project
    const [updateResult] = await queryInterface.sequelize.query(`
      UPDATE "Resource" r
      SET 
          "projectId" = (
              SELECT p.id 
              FROM "Project" p 
              WHERE p."createdById" = r."createdById"
                AND p."isDefault" = TRUE
                AND p."deletedAt" IS NULL
                AND p."isDeleted" = FALSE
              LIMIT 1
          )
      WHERE r."projectId" IS NULL 
        AND r."deletedAt" IS NULL
        AND r."createdById" IS NOT NULL
        AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
        AND EXISTS (
            SELECT 1 
            FROM "Project" p 
            WHERE p."createdById" = r."createdById"
              AND p."isDefault" = TRUE
              AND p."deletedAt" IS NULL
              AND p."isDeleted" = FALSE
        )
    `, { transaction });
    
    const updatedCount = (updateResult as any).rowCount || 0;
    
    // Step 3: Get migration results
    const [skippedCountResult] = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count
      FROM "Resource" r
      WHERE r."projectId" IS NULL 
        AND r."deletedAt" IS NULL
        AND r."createdById" IS NOT NULL
        AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
        AND NOT EXISTS (
            SELECT 1 
            FROM "Project" p 
            WHERE p."createdById" = r."createdById"
              AND p."isDefault" = TRUE
              AND p."deletedAt" IS NULL
              AND p."isDeleted" = FALSE
        )
    `, { transaction });
    
    const skippedCount = (skippedCountResult as any)[0]?.count || 0;
    
    const [remainingCountResult] = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count
      FROM "Resource" r
      WHERE r."projectId" IS NULL 
        AND r."deletedAt" IS NULL
        AND r."createdById" IS NOT NULL
        AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
    `, { transaction });
    
    const remainingCount = (remainingCountResult as any)[0]?.count || 0;
    
    // Log migration summary
    console.log('');
    console.log('📋 Migration Summary:');
    console.log(`   Successfully linked: ${updatedCount} resources`);
    console.log(`   Skipped (no default project): ${skippedCount} resources`);
    console.log(`   Remaining orphaned: ${remainingCount} resources`);
    
    if (remainingCount === 0) {
      console.log('🎉 All orphaned resources have been successfully linked to default projects!');
    } else {
      console.log(`⚠️  ${remainingCount} resources still remain orphaned (likely users without default projects)`);
    }
    
    // Step 4: Verification - Show distribution of resources across projects
    const [distributionResult] = await queryInterface.sequelize.query(`
      SELECT 
          p.id,
          p.name as project_name,
          p."createdById",
          COUNT(r.id) as resource_count
      FROM "Project" p
      LEFT JOIN "Resource" r ON p.id = r."projectId" AND r."deletedAt" IS NULL
      WHERE p."isDefault" = TRUE
        AND p."deletedAt" IS NULL
        AND p."isDeleted" = FALSE
      GROUP BY p.id, p.name, p."createdById"
      ORDER BY resource_count DESC
    `, { transaction });
    
    console.log('');
    console.log('🔍 Resource Distribution Across Default Projects:');
    console.log('================================================');
    
    (distributionResult as any[]).forEach((project: any) => {
      console.log(`Project: ${project.project_name} (ID: ${project.id}) - User: ${project.createdById} - Resources: ${project.resource_count}`);
    });
    
    // Step 5: Show details of remaining orphaned resources (if any)
    if (remainingCount > 0) {
      const [orphanedDetailsResult] = await queryInterface.sequelize.query(`
        SELECT 
            r.id,
            r.name,
            r."createdById",
            r.type,
            r."createdAt"
        FROM "Resource" r
        WHERE r."projectId" IS NULL 
          AND r."deletedAt" IS NULL
          AND r."createdById" IS NOT NULL
          AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
        ORDER BY r."createdAt" DESC
        LIMIT 20
      `, { transaction });
      
      console.log('');
      console.log('📋 Remaining Orphaned Resources:');
      console.log('================================');
      
      (orphanedDetailsResult as any[]).forEach((resource: any) => {
        console.log(`Resource: ${resource.name} (ID: ${resource.id}) - Type: ${resource.type} - User: ${resource.createdById} - Created: ${resource.createdAt}`);
      });
      
      if (remainingCount > 20) {
        console.log(`... and ${remainingCount - 20} more resources`);
      }
    }
    
    console.log('');
    console.log('✅ Migration completed successfully!');
    
    await transaction.commit();
  } catch (error) {
    console.error('❌ Migration failed:', error);
    await transaction.rollback();
    throw error;
  }
}

export async function down(queryInterface: QueryInterface, sequelize: Sequelize): Promise<void> {
  await queryInterface.removeIndex("Project", "idx_project_default_creator");

  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    console.log('🔄 Starting rollback: Unlinking resources from default projects...');
    
    // Unlink resources from default projects
    const [rollbackResult] = await queryInterface.sequelize.query(`
      UPDATE "Resource" r
      SET 
          "projectId" = NULL
      WHERE r."projectId" IS NOT NULL
        AND r."deletedAt" IS NULL
        AND EXISTS (
            SELECT 1 
            FROM "Project" p 
            WHERE p.id = r."projectId"
              AND p."isDefault" = true
              AND p."createdById" = r."createdById"
              AND p."deletedAt" IS NULL
              AND p."isDeleted" = false
        )
        AND r.type NOT IN ('avatar', 'avatarDeleted', 'systemLogo', 'systemLogoDeleted')
    `, { transaction });
    
    const unlinkedCount = (rollbackResult as any).rowCount || 0;
    console.log(`🔄 Rollback completed: ${unlinkedCount} resources unlinked`);
    
    await transaction.commit();
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    await transaction.rollback();
    throw error;
  }
}
