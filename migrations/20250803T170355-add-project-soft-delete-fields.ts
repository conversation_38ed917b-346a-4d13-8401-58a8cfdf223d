import { QueryInterface, Sequelize, DataTypes } from "sequelize";

export async function up(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Add soft delete fields to Project table while keeping existing isDeleted
  await queryInterface.addColumn("Project", "deletedAt", {
    type: DataTypes.DATE,
    allowNull: true,
    comment: "Timestamp when the project was soft deleted",
  });

  await queryInterface.addColumn("Project", "deletedBy", {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: "ID of the user who deleted this project",
  });

  // Add indexes for soft delete queries
  await queryInterface.addIndex("Project", ["deletedAt"], {
    name: "idx_project_deleted_at",
  });

  await queryInterface.addIndex("Project", ["createdById", "deletedAt"], {
    name: "idx_project_creator_not_deleted",
  });

  await queryInterface.addIndex("Project", ["isDeleted", "deletedAt"], {
    name: "idx_project_soft_delete_combined",
  });
}

export async function down(
  queryInterface: QueryInterface,
  sequelize: Sequelize
): Promise<void> {
  // Remove indexes
  await queryInterface.removeIndex("Project", "idx_project_soft_delete_combined");
  await queryInterface.removeIndex("Project", "idx_project_creator_not_deleted");
  await queryInterface.removeIndex("Project", "idx_project_deleted_at");

  // Remove soft delete fields from Project table
  await queryInterface.removeColumn("Project", "deletedBy");
  await queryInterface.removeColumn("Project", "deletedAt");
} 
