# No BuildKit features needed
FROM ghcr.io/astral-sh/uv:latest AS uvbin
FROM python:3.12-slim-bookworm AS base

FROM base AS builder
# copy uv binaries from the uvbin stage
COPY --from=uvbin /uv /uvx /bin/
ENV UV_COMPILE_BYTECODE=1 UV_LINK_MODE=copy
WORKDIR /app

# lockfiles first to leverage layer cache
COPY uv.lock pyproject.toml /app/
RUN uv sync --frozen --no-install-project --no-dev

COPY app/ ./app/
RUN uv sync --frozen --no-dev

FROM base
WORKDIR /app
COPY --from=builder /app /app
ENV PATH="/app/.venv/bin:$PATH"
EXPOSE 8000
CMD ["python", "-m", "app.main"]