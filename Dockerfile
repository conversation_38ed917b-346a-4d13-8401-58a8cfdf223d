# Multi-stage build for production optimization
FROM node:20-alpine AS builder

# Install build dependencies for native modules
RUN apk add --no-cache python3 make g++

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile --production=false

# Copy source code
COPY . .

# Build the application (without loading secrets - secrets loaded at runtime)
RUN yarn build:cloud-run

# Verify build output
RUN ls -la dist/ && echo "Build completed successfully"

# Production stage
FROM node:20-alpine AS production

# Install dumb-init, build dependencies, and FFmpeg for media processing
RUN apk add --no-cache \
    dumb-init \
    python3 \
    make \
    g++ \
    curl \
    bash \
    ffmpeg

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy package files (as root first)
COPY package.json yarn.lock ./

# Install only production dependencies
RUN yarn install --frozen-lockfile --production=true && \
    yarn cache clean

# Copy built application from builder stage (as root first)
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/scripts ./scripts
COPY --from=builder /app/env ./env

# Make entrypoint script executable BEFORE switching to non-root user
RUN chmod +x /app/scripts/docker-entrypoint.sh && \
    chmod +x /app/scripts/deploy.sh && \
    chmod +x /app/scripts/loadenv.js

# Change ownership of all app files to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:8080/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Use dumb-init to handle signals properly and run entrypoint script
ENTRYPOINT ["dumb-init", "--"]

# Start with entrypoint script using absolute path and bash
CMD ["/bin/bash", "/app/scripts/docker-entrypoint.sh"]
