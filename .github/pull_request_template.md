## 📋 Pull Request Template

### 🎯 Description

<!-- Provide a brief description of the changes -->

### 📝 Type of Change

<!--
🤖 AUTOMATED LABELING: Checking a box below will automatically apply the corresponding GitHub label!
⚠️ Select ONLY ONE option - multiple selections will trigger a warning
✨ Labels are required for proper semantic versioning and changelog generation
-->

**Select exactly one release type:**

- [ ] 🐛 `release: patch` - Bug fix (patch version bump)
- [ ] ✨ `release: minor` - New feature (minor version bump)
- [ ] 💥 `release: major` - Breaking change (major version bump)
- [ ] 📚 `release: skip` - Documentation/CI changes (no version bump)

> **💡 How it works:** When you check a box above and save this PR, GitHub Actions will automatically apply the corresponding label. No manual labeling needed!

### 🔗 Related Issues

<!-- Link to related issues using #issue_number -->

Closes #

### 🧪 Testing

<!-- Describe the testing you have performed -->

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All tests pass
- [ ] Health endpoint tested
- [ ] MCP endpoint tested
- [ ] API documentation updated

### 🐍 Python/FastAPI Specific

<!-- Fill this section for Python/FastAPI related changes -->

**If this PR includes Python/FastAPI changes:**

- [ ] Dependencies updated in `pyproject.toml` (if applicable)
- [ ] Virtual environment tested with `uv sync`
- [ ] FastAPI endpoints tested manually
- [ ] OpenAPI documentation generated correctly
- [ ] Type hints added/updated where appropriate
- [ ] Async/await patterns used correctly

**API Changes:**
<!-- Describe any API endpoint changes -->

```
New endpoints: /api/v1/...
Modified endpoints: /api/v1/...
Deprecated endpoints: /api/v1/...
```

### 🔌 RAG & MCP Specific

<!-- Fill this section for RAG and MCP related changes -->

**If this PR includes RAG/MCP changes:**

- [ ] RAG functionality tested with sample queries
- [ ] Vector retrieval tested
- [ ] MCP integration tested
- [ ] Pinecone integration tested (if applicable)
- [ ] Google Cloud integration tested (if applicable)
- [ ] Horizontal scalability considered
- [ ] Performance impact assessed

**MCP Changes:**
<!-- Describe any MCP (Model Control Protocol) changes -->

- [ ] MCP endpoint functionality
- [ ] MCP client compatibility
- [ ] Authentication/authorization
- [ ] Other: _______________

**RAG Changes:**
<!-- Describe any RAG functionality changes -->

- [ ] Document retrieval improvements
- [ ] Vector search enhancements
- [ ] Generation quality improvements
- [ ] Other: _______________

### 📚 Documentation

<!-- Check if documentation needs to be updated -->

- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)
- [ ] MCP integration guide updated (if needed)
- [ ] Comments added to complex code
- [ ] Environment variables documented (if new ones added)

### 🚀 Deployment Notes

<!-- Any special deployment considerations -->

- [ ] No special deployment steps required
- [ ] Environment variables to be updated
- [ ] Google Cloud configuration changes
- [ ] Pinecone configuration changes
- [ ] MCP client configuration changes
- [ ] Docker image changes
- [ ] Resource requirements changed (CPU/Memory)
- [ ] Scaling configuration updated

**⚠️ Important:** Service deployments are automated via GitHub Actions

### ⚠️ Breaking Changes

<!-- If this is a breaking change, describe the impact -->

**MCP Breaking Changes:**
<!-- Describe any breaking changes to MCP integration -->

### 🏃‍♂️ Pre-merge Checklist

- [ ] Release type selected above (auto-applies label) ✨
- [ ] Code follows Python/FastAPI conventions
- [ ] Type hints added where appropriate
- [ ] Self-review completed
- [ ] Peer review requested
- [ ] CI/CD checks passing
- [ ] Branch is up to date with develop

### 📸 Screenshots (if applicable)

<!-- Add screenshots for UI changes or API documentation -->

---

**Note for Reviewers:**

- ✅ Release labels are now applied automatically based on template checkboxes
- ✅ PR validation ensures proper labeling before merge
- ✅ Verify that the changes match the selected version bump type
- ✅ Check that breaking changes are properly documented
- ✅ Ensure FastAPI best practices are followed
- ✅ Verify MCP compatibility if applicable

**🐍 Python/FastAPI Review Checklist:**
- ✅ Type hints are present and correct
- ✅ Async/await patterns used appropriately
- ✅ Error handling implemented properly
- ✅ API endpoints follow RESTful conventions
- ✅ Dependencies are properly managed in pyproject.toml

**🔌 RAG/MCP Review Checklist:**
- ✅ RAG functionality works as expected
- ✅ MCP integration maintains compatibility
- ✅ Vector operations are optimized
- ✅ Horizontal scalability is maintained
- ✅ Performance impact is acceptable

**Automated Workflows:**

- 🤖 Auto-label PRs based on template selections
- 🔍 Validate release labels before merge
- 📝 Generate semantic releases and changelogs
- 🚀 Deploy to dev/staging/production environments
