## 📋 Pull Request Template

### 🎯 Description

<!-- Provide a brief description of the changes -->

### 📝 Type of Change

<!--
🤖 AUTOMATED LABELING: Checking a box below will automatically apply the corresponding GitHub label!
⚠️ Select ONLY ONE option - multiple selections will trigger a warning
✨ Labels are required for proper semantic versioning and changelog generation
-->

**Select exactly one release type:**

- [ ] 🐛 `release: patch` - Bug fix (patch version bump)
- [ ] ✨ `release: minor` - New feature (minor version bump)
- [ ] 💥 `release: major` - Breaking change (major version bump)
- [ ] 📚 `release: skip` - Documentation/CI changes (no version bump)

> **💡 How it works:** When you check a box above and save this PR, GitHub Actions will automatically apply the corresponding label. No manual labeling needed!

### 🔗 Related Issues

<!-- Link to related issues using #issue_number -->

Closes #

### 🧪 Testing

<!-- Describe the testing you have performed -->

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All tests pass

### 🗄️ Database Changes

<!-- Only fill this section if your PR includes database schema changes -->

**If this PR includes database schema changes:**

- [ ] Migration file included (`migrations/YYYYMMDDHHMMSS-description.ts`)
- [ ] Migration tested locally with `yarn migrate up/down`
- [ ] Both model changes AND migration file committed together
- [ ] Migration has proper rollback functionality (`down` function)
- [ ] No destructive operations without data migration plan

**Migration Details:**
<!-- Describe what database changes are included -->

```
Migration file: migrations/20240120-describe-change.ts
Changes:
- [ ] Add new table
- [ ] Add new column(s)
- [ ] Add/modify index
- [ ] Add foreign key constraint
- [ ] Data migration included
- [ ] Other: _______________
```

### 📚 Documentation

<!-- Check if documentation needs to be updated -->

- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)
- [ ] Comments added to complex code

### 🚀 Deployment Notes

<!-- Any special deployment considerations -->

- [ ] No special deployment steps required
- [ ] Database migrations will run automatically in staging/production
- [ ] Environment variables to be updated
- [ ] Google Cloud configuration changes
- [ ] Firebase configuration changes
- [ ] Third-party AI services affected (Rev.ai, Recall.ai, OpenAI)
- [ ] Storage bucket changes required

**⚠️ Important:** Database migrations run automatically during deployment via GitHub Actions

### ⚠️ Breaking Changes

<!-- If this is a breaking change, describe the impact -->

### 🏃‍♂️ Pre-merge Checklist

- [ ] Release type selected above (auto-applies label) ✨
- [ ] Code follows project conventions
- [ ] Self-review completed
- [ ] Peer review requested
- [ ] CI/CD checks passing
- [ ] Branch is up to date with develop

### 📸 Screenshots (if applicable)

<!-- Add screenshots for UI changes -->

---

**Note for Reviewers:**

- ✅ Release labels are now applied automatically based on template checkboxes
- ✅ PR validation ensures proper labeling before merge
- ✅ Verify that the changes match the selected version bump type
- ✅ Check that breaking changes are properly documented

**🗄️ Migration Review Checklist:**
- ✅ If schema changes: migration file must be included
- ✅ Verify migration file naming: `YYYYMMDDHHMMSS-description.ts`
- ✅ Check both `up` and `down` functions are implemented
- ✅ Ensure no destructive operations without data migration strategy
- ✅ Model changes and migration committed together

**Automated Workflows:**

- 🤖 Auto-label PRs based on template selections
- 🔍 Validate release labels before merge
- 📝 Generate semantic releases and changelogs
