# Semantic Versioning Labels
- name: "release: major"
  color: "d73a49"
  description: "Breaking changes (major version bump)"

- name: "release: minor"
  color: "0075ca"
  description: "New features (minor version bump)"

- name: "release: patch"
  color: "28a745"
  description: "Bug fixes (patch version bump)"

- name: "release: skip"
  color: "6f42c1"
  description: "No version bump needed (docs, CI, etc.)"

# Additional categorization labels
- name: "type: feature"
  color: "0075ca"
  description: "New feature or enhancement"

- name: "type: bugfix"
  color: "28a745"
  description: "Bug fix"

- name: "type: breaking"
  color: "d73a49"
  description: "Breaking change requiring major version bump"

- name: "type: documentation"
  color: "6f42c1"
  description: "Documentation only changes"

- name: "type: dependencies"
  color: "0366d6"
  description: "Dependency updates"

- name: "type: refactor"
  color: "fbca04"
  description: "Code refactoring without functional changes"

- name: "priority: high"
  color: "d73a49"
  description: "High priority"

- name: "priority: medium"
  color: "fbca04"
  description: "Medium priority"

- name: "priority: low"
  color: "28a745"
  description: "Low priority"

- name: "status: needs-review"
  color: "fbca04"
  description: "Awaiting review"

- name: "status: work-in-progress"
  color: "d73a49"
  description: "Work in progress, not ready for review"

# Document Processor Specific Labels
- name: "scope: document-processing"
  color: "0052cc"
  description: "Changes to document processing and parsing"

- name: "scope: embedding"
  color: "0052cc"
  description: "Changes to document embedding and vectorization"

- name: "scope: pinecone"
  color: "fbca04"
  description: "Pinecone vector database integration changes"

- name: "scope: google-cloud"
  color: "fef2c0"
  description: "Google Cloud services integration changes"

- name: "scope: api"
  color: "c2e0c6"
  description: "FastAPI endpoint or route changes"

- name: "scope: llm"
  color: "bfd4f2"
  description: "LLM integration and processing changes"

- name: "scope: deployment"
  color: "d4c5f9"
  description: "Deployment configuration or CI/CD changes"

- name: "scope: performance"
  color: "ff9500"
  description: "Performance improvements and optimizations"
