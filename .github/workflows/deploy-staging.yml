name: Deploy to Staging

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Install dependencies
        run: yarn install

      - name: Build
        run: yarn run build
        env:
          BUILD_MODE: staging
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Make deploy script executable
        run: chmod +x ./scripts/deploy.sh

      - name: Deploy to Cloud Run
        run: |
          echo "🚀 Deploying to STAGING environment..."
          echo "ℹ️  Database migrations will run automatically during container startup"
          yarn deploy
          echo "✅ Deployment to STAGING completed successfully!"
        env:
          BUILD_MODE: staging
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

      - name: Deployment Success Summary
        if: success()
        run: |
          echo "🎉 STAGING Deployment Summary:"
          echo "✅ Migrations: Completed successfully"
          echo "✅ Application: Deployed successfully"
          echo "🔗 Environment: Staging"
          echo "📝 Commit: ${{ github.sha }}"