name: Deploy RAG Service to Staging

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: latest

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/uv
            .venv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/uv.lock') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install dependencies
        run: uv sync --frozen --no-dev

      - name: Build Docker image
        run: |
          echo "🔨 Building Docker image for RAG Service STAGING..."
          docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-${{ github.sha }} .
          docker tag gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-${{ github.sha }} gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-latest

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Push Docker image to Google Container Registry
        run: |
          echo "📦 Pushing Docker image to GCR..."
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-${{ github.sha }}
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-latest

      - name: Load environment variables from Secret Manager
        run: |
          echo "🔐 Loading environment variables from Google Secret Manager..."
          
          # Create .env file from secret manager (already in .env format)
          gcloud secrets versions access latest --secret="rag-service-staging-env" --format="get(payload.data)" | base64 -d > .env || echo "No secret found for rag-service-staging-env, using defaults"
          
          echo "✅ Environment variables loaded from Secret Manager"

      - name: Deploy to Cloud Run
        run: |
          echo "🚀 Deploying RAG Service to STAGING environment..."
          echo "ℹ️  Application will start with staging configuration"
          
          # Deploy with environment variables from .env file
          gcloud run deploy rag-service-staging \
            --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-${{ github.sha }} \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --allow-unauthenticated \
            --env-vars-file .env \
            --memory 2Gi \
            --cpu 1 \
            --max-instances 15 \
            --timeout 300 \
            --concurrency 100
          
          echo "✅ Deployment to STAGING completed successfully!"

      - name: Get deployment URL
        id: get-url
        run: |
          URL=$(gcloud run services describe rag-service-staging \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --format 'value(status.url)')
          echo "url=$URL" >> $GITHUB_OUTPUT

      - name: Test deployment health
        run: |
          echo "🏥 Testing deployment health..."
          sleep 30  # Give the service time to start
          
          # Test the health endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/health || echo "000")
          
          if [ "$response" = "200" ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status: $response"
            echo "Trying root endpoint..."
            response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/ || echo "000")
            if [ "$response" = "200" ]; then
              echo "✅ Root endpoint responding"
            else
              echo "❌ Service not responding properly"
              exit 1
            fi
          fi

      - name: Test MCP endpoint
        run: |
          echo "🔌 Testing MCP endpoint..."
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/mcp || echo "000")
          
          if [ "$response" = "200" ] || [ "$response" = "405" ]; then
            echo "✅ MCP endpoint responding (status: $response)"
          else
            echo "⚠️ MCP endpoint returned status: $response"
          fi

      - name: Deployment Success Summary
        if: success()
        run: |
          echo "🎉 RAG Service STAGING Deployment Summary:"
          echo "✅ Application: Deployed successfully"
          echo "🔗 Environment: Staging"
          echo "🌐 URL: ${{ steps.get-url.outputs.url }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "🐳 Image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:staging-${{ github.sha }}"
          echo "📋 API Documentation: ${{ steps.get-url.outputs.url }}/docs"
          echo "🔄 OpenAPI Spec: ${{ steps.get-url.outputs.url }}/openapi.json"
          echo "🔌 MCP Endpoint: ${{ steps.get-url.outputs.url }}/mcp"
