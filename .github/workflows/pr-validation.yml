name: PR Validation

on:
  pull_request:
    types: [opened, edited, synchronize, labeled, unlabeled, ready_for_review]

jobs:
  validate-release-label:
    name: Validate Release Label
    runs-on: ubuntu-latest
    
    steps:
      - name: Check for release label
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.payload.pull_request.number;
            const isDraft = context.payload.pull_request.draft;
            
            // Skip validation for draft PRs
            if (isDraft) {
              console.log('🚧 Draft PR - skipping validation');
              return;
            }
            
            // Get PR labels
            const { data: labels } = await github.rest.issues.listLabelsOnIssue({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber
            });
            
            const labelNames = labels.map(label => label.name);
            const releaseLabels = labelNames.filter(name => name.startsWith('release:'));
            
            console.log(`🏷️ Found labels: ${labelNames.join(', ') || 'none'}`);
            console.log(`🎯 Release labels: ${releaseLabels.join(', ') || 'none'}`);
            
            // Check for exactly one release label
            if (releaseLabels.length === 0) {
              console.log('❌ No release label found');
              
              await github.rest.repos.createCommitStatus({
                owner: context.repo.owner,
                repo: context.repo.repo,
                sha: context.payload.pull_request.head.sha,
                state: 'failure',
                target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`,
                description: 'Missing release label - check a box in PR template',
                context: 'PR Validation / Release Label'
              });
              
              // Add comment with instructions
              const existingComments = await github.rest.issues.listComments({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber
              });
              
              const botComment = existingComments.data.find(comment => 
                comment.user.type === 'Bot' && 
                comment.body.includes('🏷️ **Release Label Required**')
              );
              
              if (!botComment) {
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  body: `🚨 **Release Label Required for Merge**
                  
                  This PR needs a release label to enable automatic semantic versioning.
                  
                  **Quick fix:** Edit your PR description and check one of these boxes:
                  
                  - [ ] 🐛 \`release: patch\` - Bug fix (patch version bump)
                  - [ ] ✨ \`release: minor\` - New feature (minor version bump)  
                  - [ ] 💥 \`release: major\` - Breaking change (major version bump)
                  - [ ] 📚 \`release: skip\` - Documentation/CI changes (no version bump)
                  
                  The label will be applied automatically! ✨
                  
                  **Why this is required:**
                  - ✅ Enables automatic version bumping
                  - ✅ Generates proper changelog categories  
                  - ✅ Tracks release impact
                  - ✅ Maintains semantic versioning standards`
                });
              }
              
              core.setFailed('PR must have exactly one release label');
              return;
            }
            
            if (releaseLabels.length > 1) {
              console.log('❌ Multiple release labels found');
              
              await github.rest.repos.createCommitStatus({
                owner: context.repo.owner,
                repo: context.repo.repo,
                sha: context.payload.pull_request.head.sha,
                state: 'failure',
                target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`,
                description: 'Multiple release labels - select only one',
                context: 'PR Validation / Release Label'
              });
              
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: `⚠️ **Multiple Release Labels Detected**
                
                Found multiple release labels:
                ${releaseLabels.map(label => `- \`${label}\``).join('\n')}
                
                **Please select only ONE release type:**
                1. Edit your PR description
                2. Uncheck all boxes in the "Type of Change" section
                3. Check only the appropriate release type
                
                The correct label will be applied automatically! 🤖`
              });
              
              core.setFailed('PR must have exactly one release label');
              return;
            }
            
            // Success case
            const releaseLabel = releaseLabels[0];
            console.log(`✅ Valid release label found: ${releaseLabel}`);
            
            await github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.payload.pull_request.head.sha,
              state: 'success',
              target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`,
              description: `Release label: ${releaseLabel}`,
              context: 'PR Validation / Release Label'
            });
            
            console.log('✅ PR validation passed');

  validate-pr-template:
    name: Validate PR Template Usage
    runs-on: ubuntu-latest
    
    steps:
      - name: Check PR template usage
        uses: actions/github-script@v7
        with:
          script: |
            const prBody = context.payload.pull_request.body || '';
            const prNumber = context.payload.pull_request.number;
            const isDraft = context.payload.pull_request.draft;
            
            // Skip for draft PRs
            if (isDraft) {
              console.log('🚧 Draft PR - skipping template validation');
              return;
            }
            
            // Check if PR template is being used
            const hasTemplate = /## 📋 Pull Request Template/.test(prBody);
            const hasDescription = /### 🎯 Description/.test(prBody);
            const hasTypeSection = /### 📝 Type of Change/.test(prBody);
            
            if (!hasTemplate || !hasDescription || !hasTypeSection) {
              console.log('⚠️ PR template not fully used');
              
              await github.rest.repos.createCommitStatus({
                owner: context.repo.owner,
                repo: context.repo.repo,
                sha: context.payload.pull_request.head.sha,
                state: 'failure',
                target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`,
                description: 'PR template incomplete or not used',
                context: 'PR Validation / Template Usage'
              });
              
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: `📋 **PR Template Required**
                
                Please use the PR template to provide necessary information:
                
                1. **Description** of changes
                2. **Type of change** selection (for automatic labeling)
                3. **Testing** information
                4. **Documentation** updates
                
                The template helps ensure:
                - ✅ Proper categorization for releases
                - ✅ Complete information for reviewers
                - ✅ Automatic label application
                - ✅ Better changelog generation
                
                Edit your PR description to include the template sections! 📝`
              });
              
              core.setFailed('PR template is required');
              return;
            }
            
            console.log('✅ PR template is being used');
            
            await github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.payload.pull_request.head.sha,
              state: 'success',
              target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`,
              description: 'PR template properly used',
              context: 'PR Validation / Template Usage'
            });
