name: Sync Labels

on:
  push:
    branches:
      - develop
    paths:
      - '.github/labels.yml'
  workflow_dispatch:

jobs:
  sync-labels:
    name: Sync Repository Labels
    runs-on: ubuntu-latest
    permissions:
      issues: write
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Sync labels
        uses: EndBug/label-sync@v2
        with:
          config-file: .github/labels.yml
          token: ${{ secrets.GITHUB_TOKEN }}
          delete-other-labels: false
