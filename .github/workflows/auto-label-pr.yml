name: Auto-apply PR Labels

on:
  pull_request:
    types: [opened, edited, synchronize]

permissions:
  pull-requests: write
  contents: read

jobs:
  auto-label:
    name: Auto-apply Release Labels
    runs-on: ubuntu-latest
    
    steps:
      - name: Auto-apply labels from PR template
        uses: actions/github-script@v7
        with:
          script: |
            const prBody = context.payload.pull_request.body || '';
            const prNumber = context.payload.pull_request.number;
            
            console.log('🔍 Analyzing PR body for checked release types...');
            
            // Define label mappings
            const labelMappings = {
              'release: patch': /- \[x\] 🐛 `release: patch`/i,
              'release: minor': /- \[x\] ✨ `release: minor`/i,
              'release: major': /- \[x\] 💥 `release: major`/i,
              'release: skip': /- \[x\] 📚 `release: skip`/i
            };
            
            // Get current labels
            const { data: currentLabels } = await github.rest.issues.listLabelsOnIssue({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber
            });
            
            const currentReleaseLabels = currentLabels
              .map(label => label.name)
              .filter(name => name.startsWith('release:'));
            
            console.log(`📋 Current release labels: ${currentReleaseLabels.join(', ') || 'none'}`);
            
            // Find checked release types
            const checkedLabels = [];
            for (const [label, regex] of Object.entries(labelMappings)) {
              if (regex.test(prBody)) {
                checkedLabels.push(label);
              }
            }
            
            console.log(`✅ Checked release types: ${checkedLabels.join(', ') || 'none'}`);
            
            // Validate only one release type is selected
            if (checkedLabels.length > 1) {
              const comment = `⚠️ **Multiple Release Types Selected**
              
              Please select only **one** release type in your PR template:
              ${checkedLabels.map(label => `- \`${label}\``).join('\n')}
              
              **Current selections:**
              ${checkedLabels.map(label => `- ✅ \`${label}\``).join('\n')}
              
              Please edit your PR description and check only one release type.`;
              
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: comment
              });
              
              console.log('❌ Multiple release types selected - comment added');
              return;
            }
            
            // If no release type is checked, add a comment
            if (checkedLabels.length === 0) {
              // Check if PR template is being used
              const hasTemplate = /## 📋 Pull Request Template/.test(prBody);
              
              if (hasTemplate) {
                const comment = `🏷️ **Release Label Required**
                
                Please select a release type in your PR template by checking one of the boxes:
                
                - [ ] 🐛 \`release: patch\` - Bug fix (patch version bump)
                - [ ] ✨ \`release: minor\` - New feature (minor version bump)  
                - [ ] 💥 \`release: major\` - Breaking change (major version bump)
                - [ ] 📚 \`release: skip\` - Documentation/CI changes (no version bump)
                
                **Why this matters:**
                - Enables automatic semantic versioning
                - Generates proper changelog categories
                - Helps track release impact
                
                Edit your PR description and check the appropriate box. The label will be applied automatically! ✨`;
                
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  body: comment
                });
                
                console.log('📝 No release type selected - reminder comment added');
              }
              return;
            }
            
            const targetLabel = checkedLabels[0];
            
            // Remove other release labels and add the correct one
            const labelsToRemove = currentReleaseLabels.filter(label => label !== targetLabel);
            const labelsToAdd = currentReleaseLabels.includes(targetLabel) ? [] : [targetLabel];
            
            // Remove old release labels
            for (const label of labelsToRemove) {
              try {
                await github.rest.issues.removeLabel({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  name: label
                });
                console.log(`🗑️ Removed label: ${label}`);
              } catch (error) {
                console.log(`⚠️ Could not remove label ${label}: ${error.message}`);
              }
            }
            
            // Add new release label
            for (const label of labelsToAdd) {
              try {
                await github.rest.issues.addLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  labels: [label]
                });
                console.log(`🏷️ Added label: ${label}`);
              } catch (error) {
                console.log(`⚠️ Could not add label ${label}: ${error.message}`);
              }
            }
            
            // Add success comment if labels were changed
            if (labelsToRemove.length > 0 || labelsToAdd.length > 0) {
              const comment = `🤖 **Labels Auto-Applied**
              
              Based on your PR template selection, I've updated the labels:
              
              ${labelsToRemove.length > 0 ? `**Removed:**\n${labelsToRemove.map(l => `- ❌ \`${l}\``).join('\n')}\n` : ''}
              ${labelsToAdd.length > 0 ? `**Added:**\n${labelsToAdd.map(l => `- ✅ \`${l}\``).join('\n')}\n` : ''}
              
              This enables automatic semantic versioning and proper changelog generation! 🚀`;
              
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: comment
              });
            }
            
            console.log('✅ Label synchronization completed');
