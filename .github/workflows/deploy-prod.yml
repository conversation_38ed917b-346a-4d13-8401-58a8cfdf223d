name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
      confirm_deployment:
        description: 'Confirm you want to deploy to PRODUCTION (type: YES)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Validate Production Deployment Confirmation
        run: |
          if [ "${{ github.event.inputs.confirm_deployment }}" != "YES" ]; then
            echo "❌ Production deployment requires explicit confirmation"
            echo "::error::Please type 'YES' in the confirm_deployment field to proceed"
            exit 1
          fi
          echo "✅ Production deployment confirmed"

      - uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: latest

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/uv
            .venv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/uv.lock') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install dependencies
        run: uv sync --frozen --no-dev

      - name: Production Deployment Warning
        run: |
          echo "⚠️  PRODUCTION DEPLOYMENT WARNING ⚠️"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "🚨 You are about to deploy Document Processor to PRODUCTION environment"
          echo "🚨 This service handles document processing and embedding operations"
          echo "🚨 Ensure you have:"
          echo "   ✅ Tested the service thoroughly in DEV and STAGING"
          echo "   ✅ Verified all integrations (Pinecone, Google Cloud, etc.)"
          echo "   ✅ Coordinated with the team about potential service interruption"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo ""
          echo "⏳ Proceeding with deployment in 10 seconds..."
          sleep 10

      - name: Build Docker image
        run: |
          echo "🔨 Building Docker image for PRODUCTION..."
          docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-${{ github.sha }} .
          docker tag gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-${{ github.sha }} gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-latest

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Push Docker image to Google Container Registry
        run: |
          echo "📦 Pushing Docker image to GCR..."
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-${{ github.sha }}
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-latest

      - name: Load environment variables from Secret Manager
        run: |
          echo "🔐 Loading environment variables from Google Secret Manager..."
          
          # Create .env file from secret manager (already in .env format)
          gcloud secrets versions access latest --secret="document-processor-prod-env" --format="get(payload.data)" | base64 -d > .env || echo "No secret found for document-processor-prod-env, using defaults"
          
          echo "✅ Environment variables loaded from Secret Manager"

      - name: Deploy to Cloud Run
        run: |
          echo "🚀 Deploying to PRODUCTION environment..."
          echo "ℹ️  Application will start with production configuration"
          
          # Deploy with environment variables from .env file
          gcloud run deploy document-processor-prod \
            --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-${{ github.sha }} \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --allow-unauthenticated \
            --env-vars-file .env \
            --memory 4Gi \
            --cpu 2 \
            --max-instances 20 \
            --timeout 600 \
            --concurrency 80
          
          echo "✅ Deployment to PRODUCTION completed successfully!"

      - name: Get deployment URL
        id: get-url
        run: |
          URL=$(gcloud run services describe document-processor-prod \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --format 'value(status.url)')
          echo "url=$URL" >> $GITHUB_OUTPUT

      - name: Test deployment health
        run: |
          echo "🏥 Testing deployment health..."
          sleep 30  # Give the service time to start
          
          # Test the health endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/health || echo "000")
          
          if [ "$response" = "200" ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status: $response"
            echo "Trying root endpoint..."
            response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/ || echo "000")
            if [ "$response" = "200" ]; then
              echo "✅ Root endpoint responding"
            else
              echo "❌ Service not responding properly"
              exit 1
            fi
          fi

      - name: Deployment Success Summary
        if: success()
        run: |
          echo "🎉 PRODUCTION Deployment Summary:"
          echo "✅ Application: Deployed successfully"
          echo "🔗 Environment: Production"
          echo "🌐 URL: ${{ steps.get-url.outputs.url }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "🐳 Image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/document-processor:prod-${{ github.sha }}"
          echo "📋 API Documentation: ${{ steps.get-url.outputs.url }}/docs"
          echo "🔄 OpenAPI Spec: ${{ steps.get-url.outputs.url }}/openapi.json"
