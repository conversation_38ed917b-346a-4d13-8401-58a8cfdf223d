name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
      confirm_migration:
        description: 'Confirm you want to run migrations in PRODUCTION (type: YES)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Validate Production Deployment Confirmation
        run: |
          if [ "${{ github.event.inputs.confirm_migration }}" != "YES" ]; then
            echo "❌ Production deployment requires explicit confirmation"
            echo "::error::Please type 'YES' in the confirm_migration field to proceed"
            exit 1
          fi
          echo "✅ Production deployment confirmed"

      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Install dependencies
        run: yarn install

      - name: Build
        run: yarn run build
        env:
          BUILD_MODE: production
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

      - name: Production Deployment Warning
        run: |
          echo "⚠️  PRODUCTION DEPLOYMENT WARNING ⚠️"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "🚨 You are about to deploy to PRODUCTION environment"
          echo "🚨 Database migrations will run automatically during container startup"
          echo "🚨 Ensure you have:"
          echo "   ✅ Tested migrations thoroughly in DEV and STAGING"
          echo "   ✅ Verified backup procedures are in place"
          echo "   ✅ Coordinated with the team about potential startup delay"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo ""
          echo "⏳ Proceeding with deployment in 10 seconds..."
          sleep 10

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Make deploy script executable
        run: chmod +x ./scripts/deploy.sh

      - name: Deploy to Cloud Run
        run: |
          echo "🚀 Deploying to PRODUCTION environment..."
          echo "ℹ️  Database migrations will run automatically during container startup"
          yarn deploy
          echo "✅ Deployment to PRODUCTION completed successfully!"
        env:
          BUILD_MODE: production
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}