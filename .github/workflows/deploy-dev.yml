name: Deploy RAG Service to Development

on:
  push:
    branches:
      - develop
  pull_request_target:
    branches: [ develop ]
    types: [closed]
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'develop'
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development
    if: github.event.pull_request.merged == true || github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Verify branch exists
        if: github.event_name == 'workflow_dispatch'
        run: |
          BRANCH="${{ github.event.inputs.branch }}"
          if ! git ls-remote --heads origin "$BRANCH" | grep -q "$BRANCH"; then
            echo "::error::Branch '$BRANCH' does not exist in the repository"
            exit 1
          fi

      - name: Checkout selected branch
        if: github.event_name == 'workflow_dispatch'
        run: git checkout ${{ github.event.inputs.branch }}

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: latest

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/uv
            .venv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/uv.lock') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install dependencies
        run: uv sync --frozen --no-dev

      - name: Build Docker image
        run: |
          echo "🔨 Building Docker image for RAG Service DEVELOPMENT..."
          docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-${{ github.sha }} .
          docker tag gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-${{ github.sha }} gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-latest

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Push Docker image to Google Container Registry
        run: |
          echo "📦 Pushing Docker image to GCR..."
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-${{ github.sha }}
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-latest

      - name: Load environment variables from Secret Manager
        run: |
          echo "🔐 Loading environment variables from Google Secret Manager..."
          
          # Create .env file from secret manager (already in .env format)
          gcloud secrets versions access latest --secret="rag-service-dev-env" --format="get(payload.data)" | base64 -d > .env || echo "No secret found for rag-service-dev-env, using defaults"
          
          echo "✅ Environment variables loaded from Secret Manager"

      - name: Deploy to Cloud Run
        run: |
          echo "🚀 Deploying RAG Service to DEVELOPMENT environment..."
          echo "ℹ️  Application will start with development configuration"
          
          # Deploy with environment variables from .env file
          gcloud run deploy rag-service-dev \
            --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-${{ github.sha }} \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --allow-unauthenticated \
            --env-vars-file .env \
            --memory 2Gi \
            --cpu 1 \
            --max-instances 10 \
            --timeout 300 \
            --concurrency 80 \
          
          echo "✅ Deployment to DEVELOPMENT completed successfully!"

      - name: Get deployment URL
        id: get-url
        run: |
          URL=$(gcloud run services describe rag-service-dev \
            --platform managed \
            --region ${{ secrets.GCP_REGION }} \
            --format 'value(status.url)')
          echo "url=$URL" >> $GITHUB_OUTPUT

      - name: Test deployment health
        run: |
          echo "🏥 Testing deployment health..."
          sleep 30  # Give the service time to start
          
          # Test the health endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/health || echo "000")
          
          if [ "$response" = "200" ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status: $response"
            echo "Trying root endpoint..."
            response=$(curl -s -o /dev/null -w "%{http_code}" ${{ steps.get-url.outputs.url }}/ || echo "000")
            if [ "$response" = "200" ]; then
              echo "✅ Root endpoint responding"
            else
              echo "❌ Service not responding properly"
              exit 1
            fi
          fi

      - name: Deployment Success Summary
        if: success()
        run: |
          echo "🎉 RAG Service DEVELOPMENT Deployment Summary:"
          echo "✅ Application: Deployed successfully"
          echo "🔗 Environment: Development"
          echo "🌐 URL: ${{ steps.get-url.outputs.url }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "🌿 Branch: ${{ github.event.inputs.branch != '' && github.event.inputs.branch || github.ref_name }}"
          echo "🐳 Image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/rag-service:dev-${{ github.sha }}"
          echo "📋 API Documentation: ${{ steps.get-url.outputs.url }}/docs"
          echo "🔄 OpenAPI Spec: ${{ steps.get-url.outputs.url }}/openapi.json"