name: Semantic Release

on:
  workflow_run:
    workflows: ["Deploy RAG Service to Production"]
    types:
      - completed
    branches:
      - develop
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run in dry-run mode (no actual release)'
        required: false
        type: boolean
        default: false

env:
  PYTHON_VERSION: '3.12'

jobs:
  semantic-release:
    name: Create Semantic Release
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    permissions:
      contents: write
      pull-requests: read
      issues: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: latest

      - name: Install GitHub CLI
        run: |
          type -p curl >/dev/null || (sudo apt update && sudo apt install curl -y)
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
          && sudo chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
          && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
          && sudo apt update \
          && sudo apt install gh -y

      - name: Install dependencies
        run: uv sync --frozen

      - name: Get last release tag
        id: last_release
        run: |
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            LAST_TAG=$(git rev-list --max-parents=0 HEAD)
          fi
          echo "tag=$LAST_TAG" >> $GITHUB_OUTPUT
          echo "Last release tag: $LAST_TAG"

      - name: Get merged PRs since last release
        id: get_prs
        run: |
          # Get commit range
          if [ "${{ steps.last_release.outputs.tag }}" = "" ]; then
            echo "No previous release found, analyzing all commits"
            COMMITS=$(git rev-list HEAD)
          else
            echo "Analyzing commits since tag: ${{ steps.last_release.outputs.tag }}"
            COMMITS=$(git rev-list ${{ steps.last_release.outputs.tag }}..HEAD)
          fi
          
          echo "Found $(echo "$COMMITS" | wc -l) commits to analyze"
          
          # Extract PR numbers from commit messages
          if [ ! -z "$COMMITS" ]; then
            PR_NUMBERS=$(echo "$COMMITS" | xargs git show --format="%s" -s | grep -oE '#[0-9]+' | sed 's/#//' | sort -u || echo "")
          else
            PR_NUMBERS=""
          fi
          
          if [ -z "$PR_NUMBERS" ]; then
            echo "No PR numbers found in commit messages"
          else
            echo "Found PR numbers: $(echo "$PR_NUMBERS" | tr '\n' ' ')"
          fi
          
          # Get PR details with labels
          PR_DATA="[]"
          if [ ! -z "$PR_NUMBERS" ]; then
            for pr_num in $PR_NUMBERS; do
              echo "Processing PR #$pr_num..."
              PR_INFO=$(gh pr view $pr_num --json number,title,labels,author,mergeCommit --jq '{number: .number, title: .title, labels: [.labels[].name], author: .author.login, mergeCommit: .mergeCommit.oid}' 2>/dev/null || echo "null")
              if [ "$PR_INFO" != "null" ] && [ "$PR_INFO" != "{}" ]; then
                PR_DATA=$(echo "$PR_DATA" | jq --argjson pr "$PR_INFO" '. + [$pr]')
              fi
            done
          fi
          
          # Ensure PR_DATA is valid JSON
          if ! echo "$PR_DATA" | jq empty 2>/dev/null; then
            echo "Invalid JSON generated, falling back to empty array"
            PR_DATA="[]"
          fi
          
          # Use heredoc format for multi-line JSON output
          echo "prs<<EOF" >> $GITHUB_OUTPUT
          echo "$PR_DATA" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          echo "Found PRs: $PR_DATA"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Calculate semantic version
        id: calc_version
        run: |
          # Get current version from pyproject.toml
          CURRENT_VERSION=$(python -c "import tomllib; print(tomllib.load(open('pyproject.toml', 'rb'))['project']['version'])")
          echo "Current version: $CURRENT_VERSION"
          
          # Parse current version
          IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
          MAJOR=${VERSION_PARTS[0]}
          MINOR=${VERSION_PARTS[1]}
          PATCH=${VERSION_PARTS[2]}
          
          # Analyze PR labels to determine version bump
          PRS='${{ steps.get_prs.outputs.prs }}'
          
          # Check if ALL PRs have skip labels
          TOTAL_PRS=$(echo "$PRS" | jq length)
          if [ "$TOTAL_PRS" -gt 0 ]; then
            SKIP_PRS=$(echo "$PRS" | jq '[.[] | select(.labels[]? | test("^(release: skip|skip-changelog|no-release)$"; "i"))] | length')
            echo "Total PRs: $TOTAL_PRS, Skip PRs: $SKIP_PRS"
            
            if [ "$SKIP_PRS" -eq "$TOTAL_PRS" ]; then
              echo "🔄 All PRs have skip labels - no release needed"
              echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
              echo "bump_type=none" >> $GITHUB_OUTPUT
              echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
              echo "skip_reason=all_prs_skipped" >> $GITHUB_OUTPUT
              exit 0
            fi
          fi
          
          HAS_MAJOR=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: major$"; "i")) | .number' | head -1)
          HAS_MINOR=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: minor$"; "i")) | .number' | head -1)
          HAS_PATCH=$(echo "$PRS" | jq -r '.[] | select(.labels[]? | test("^release: patch$"; "i")) | .number' | head -1)
          
          # Determine version bump type
          if [ ! -z "$HAS_MAJOR" ]; then
            BUMP_TYPE="major"
            NEW_VERSION="$((MAJOR + 1)).0.0"
          elif [ ! -z "$HAS_MINOR" ]; then
            BUMP_TYPE="minor"
            NEW_VERSION="$MAJOR.$((MINOR + 1)).0"
          elif [ ! -z "$HAS_PATCH" ]; then
            BUMP_TYPE="patch"
            NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
          else
            BUMP_TYPE="none"
            NEW_VERSION="$CURRENT_VERSION"
          fi
          
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "bump_type=$BUMP_TYPE" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "skip_reason=" >> $GITHUB_OUTPUT
          
          echo "Bump type: $BUMP_TYPE"
          echo "New version: $NEW_VERSION"

      - name: Generate changelog
        id: generate_changelog
        if: steps.calc_version.outputs.bump_type != 'none'
        run: |
          # Create enhanced changelog generation script for RAG Service
          cat > generate_changelog.py << 'EOF'
          import json
          import os
          from datetime import datetime

          try:
              prs_data = json.loads(os.environ.get('PRS_DATA', '[]'))
              current_version = os.environ.get('CURRENT_VERSION')
              new_version = os.environ.get('NEW_VERSION')
              bump_type = os.environ.get('BUMP_TYPE')

              print(f"Generating RAG Service changelog for {len(prs_data)} PRs")

              # Helper function to check for specific release labels (strict matching)
              def has_release_label(pr, release_type):
                  if not pr.get('labels') or not isinstance(pr['labels'], list):
                      return False
                  return any(label.lower() == f'release: {release_type.lower()}' for label in pr['labels'])

              # Helper function to check for skip labels
              def has_skip_label(pr):
                  if not pr.get('labels') or not isinstance(pr['labels'], list):
                      return False
                  skip_labels = ['release: skip', 'skip-changelog', 'no-release']
                  return any(label.lower() in skip_labels for label in pr['labels'])

              # Strict label-based categorization using ONLY "release:xxx" convention
              breaking = [pr for pr in prs_data if has_release_label(pr, 'major')]
              features = [pr for pr in prs_data if has_release_label(pr, 'minor')]
              fixes = [pr for pr in prs_data if has_release_label(pr, 'patch')]

              # All other PRs (without proper release labels) go to others, except skipped ones
              others = [pr for pr in prs_data if not any([
                  has_release_label(pr, 'major'),
                  has_release_label(pr, 'minor'),
                  has_release_label(pr, 'patch'),
                  has_skip_label(pr)
              ])]

              # Generate changelog content
              changelog = f"# RAG Service Release {new_version}\n\n"
              changelog += f"**Release Date:** {datetime.now().strftime('%Y-%m-%d')}\n"
              changelog += f"**Version Bump:** {bump_type}\n"
              changelog += f"**Service:** RAG (Retrieval-Augmented Generation) with MCP Support\n\n"

              if breaking:
                  changelog += "## 🚨 BREAKING CHANGES\n\n"
                  for pr in breaking:
                      changelog += f"- **#{pr['number']}**: {pr['title']} by @{pr['author']}\n"
                  changelog += "\n"

              if features:
                  changelog += "## ✨ New Features\n\n"
                  for pr in features:
                      changelog += f"- **#{pr['number']}**: {pr['title']} by @{pr['author']}\n"
                  changelog += "\n"

              if fixes:
                  changelog += "## 🐛 Bug Fixes\n\n"
                  for pr in fixes:
                      changelog += f"- **#{pr['number']}**: {pr['title']} by @{pr['author']}\n"
                  changelog += "\n"

              if others:
                  changelog += "## 📝 Other Changes\n\n"
                  for pr in others:
                      changelog += f"- **#{pr['number']}**: {pr['title']} by @{pr['author']}\n"
                  changelog += "\n"

              # Enhanced statistics
              changelog += "## 📊 Release Statistics\n\n"
              changelog += f"- **Total PRs**: {len(prs_data)}\n"
              contributors = list(set(pr['author'] for pr in prs_data))
              changelog += f"- **Contributors**: {len(contributors)}\n"

              if breaking:
                  changelog += f"- **Breaking Changes**: {len(breaking)}\n"
              if features:
                  changelog += f"- **New Features**: {len(features)}\n"
              if fixes:
                  changelog += f"- **Bug Fixes**: {len(fixes)}\n"
              if others:
                  changelog += f"- **Other Changes**: {len(others)}\n"

              # Add RAG Service specific information
              changelog += "\n## 🔌 Service Information\n\n"
              changelog += "- **Service Type**: RAG (Retrieval-Augmented Generation)\n"
              changelog += "- **MCP Support**: Model Control Protocol integration\n"
              changelog += "- **Vector Database**: Pinecone integration\n"
              changelog += "- **API Framework**: FastAPI with automatic documentation\n"

              # Add contributors list
              if contributors:
                  changelog += "\n### 👥 Contributors\n\n"
                  for contributor in contributors:
                      pr_count = sum(1 for pr in prs_data if pr['author'] == contributor)
                      changelog += f"- @{contributor} ({pr_count} PR{'s' if pr_count != 1 else ''})\n"

              # Save changelog
              with open('generated_changelog.md', 'w') as f:
                  f.write(changelog)

              print('✅ RAG Service changelog generated successfully')
              print(f'📊 Categorization results:')
              print(f'   - Breaking: {len(breaking)}')
              print(f'   - Features: {len(features)}')
              print(f'   - Fixes: {len(fixes)}')
              print(f'   - Others: {len(others)}')

          except Exception as error:
              print(f'❌ Error generating changelog: {error}')

              # Create fallback changelog
              fallback_changelog = f"# RAG Service Release {os.environ.get('NEW_VERSION', 'Unknown')}\n\n"
              fallback_changelog += f"**Release Date:** {datetime.now().strftime('%Y-%m-%d')}\n"
              fallback_changelog += f"**Version Bump:** {os.environ.get('BUMP_TYPE', 'unknown')}\n\n"
              fallback_changelog += "## ⚠️ Changelog Generation Error\n\n"
              fallback_changelog += "Unable to automatically generate detailed changelog.\n"
              fallback_changelog += "Please review the included changes manually.\n\n"
              fallback_changelog += f"**Error:** {error}\n"

              with open('generated_changelog.md', 'w') as f:
                  f.write(fallback_changelog)
              print('📝 Fallback changelog created')
          EOF

          # Run changelog generation
          python generate_changelog.py

          # Validate changelog was created
          if [ ! -f "generated_changelog.md" ]; then
            echo "❌ Error: Changelog file was not created"
            exit 1
          fi

          # Check if changelog has content
          if [ ! -s "generated_changelog.md" ]; then
            echo "❌ Error: Changelog file is empty"
            exit 1
          fi

          echo "📋 Generated RAG Service changelog preview:"
          echo "================================"
          head -20 generated_changelog.md
          echo "================================"
          echo "... (showing first 20 lines)"

          # Read generated changelog and set output using heredoc
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          cat generated_changelog.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
        env:
          PRS_DATA: ${{ steps.get_prs.outputs.prs }}
          CURRENT_VERSION: ${{ steps.calc_version.outputs.current_version }}
          NEW_VERSION: ${{ steps.calc_version.outputs.version }}
          BUMP_TYPE: ${{ steps.calc_version.outputs.bump_type }}

      - name: Update pyproject.toml version
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Update pyproject.toml version using Python
          python -c "
          import tomllib
          import tomli_w

          with open('pyproject.toml', 'rb') as f:
              data = tomllib.load(f)

          data['project']['version'] = '${{ steps.calc_version.outputs.version }}'

          with open('pyproject.toml', 'wb') as f:
              tomli_w.dump(data, f)
          "

          # Commit version update
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add pyproject.toml
          git commit -m "chore: bump version to ${{ steps.calc_version.outputs.version }}"
          git push

      - name: Create release directory and file
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Create releases directory if it doesn't exist
          mkdir -p releases

          # Create release file
          cat > "releases/${{ steps.calc_version.outputs.version }}.md" << 'EOF'
          ${{ steps.generate_changelog.outputs.changelog }}
          EOF

          # Add to git
          git add releases/
          git commit -m "docs: add release ${{ steps.calc_version.outputs.version }}"
          git push

      - name: Create GitHub release
        if: steps.calc_version.outputs.bump_type != 'none' && !inputs.dry_run
        run: |
          # Create GitHub release with notes from file to avoid command line length issues
          echo "${{ steps.generate_changelog.outputs.changelog }}" > release_notes.md

          gh release create "v${{ steps.calc_version.outputs.version }}" \
            --title "RAG Service Release ${{ steps.calc_version.outputs.version }}" \
            --notes-file release_notes.md \
            --target develop

          echo "✅ GitHub release v${{ steps.calc_version.outputs.version }} created successfully"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Dry run output
        if: inputs.dry_run && steps.calc_version.outputs.bump_type != 'none'
        run: |
          echo "🔍 DRY RUN MODE - No actual changes will be made"
          echo ""
          echo "📋 Planned Actions:"
          echo "- Update pyproject.toml version to: ${{ steps.calc_version.outputs.version }}"
          echo "- Create release file: releases/${{ steps.calc_version.outputs.version }}.md"
          echo "- Create GitHub release: v${{ steps.calc_version.outputs.version }}"
          echo ""
          echo "📄 Generated Changelog:"
          echo "${{ steps.generate_changelog.outputs.changelog }}"

      - name: Summary
        run: |
          echo "## RAG Service Semantic Release Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: Production deployment completion" >> $GITHUB_STEP_SUMMARY
          echo "- **Current Version**: ${{ steps.calc_version.outputs.current_version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **New Version**: ${{ steps.calc_version.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Bump Type**: ${{ steps.calc_version.outputs.bump_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **PRs Analyzed**: $(echo '${{ steps.get_prs.outputs.prs }}' | jq length)" >> $GITHUB_STEP_SUMMARY
          echo "- **Dry Run**: ${{ inputs.dry_run || 'false' }}" >> $GITHUB_STEP_SUMMARY

          if [ "${{ steps.calc_version.outputs.skip_reason }}" = "all_prs_skipped" ]; then
            echo "- **Result**: ⏭️ Release skipped - all PRs have skip labels" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.calc_version.outputs.bump_type }}" = "none" ]; then
            echo "- **Result**: No version bump needed" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ inputs.dry_run }}" = "true" ]; then
            echo "- **Result**: Dry run completed - no changes made" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Result**: Release created successfully" >> $GITHUB_STEP_SUMMARY
          fi
