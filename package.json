{"name": "aida-service", "version": "1.0.0", "private": true, "engines": {"node": ">=20.x"}, "scripts": {"load-env": "node ./scripts/loadenv.js", "dev": "tsx watch --env-file=.env src/index.ts", "build": "rm -rf dist && yarn load-env && npx tsc && tsc-alias", "build:cloud-run": "rm -rf dist && npx tsc && tsc-alias", "start": "node --env-file=.env dist/index.js", "start:docker": "./scripts/docker-entrypoint.sh", "deploy": "./scripts/deploy.sh", "migrate": "tsx --env-file=.env scripts/migrate.ts", "create-migration": "tsx scripts/create-migration.ts", "test:http-logging": "node scripts/test-http-logging.js", "test:feature-extraction": "node scripts/test-feature-extraction.js", "test:gcp-logging": "node scripts/test-gcp-logging.js", "test:console-logging": "node scripts/test-console-logging.js", "test:single-logging": "node scripts/test-single-logging.js"}, "main": "./dist/index.js", "dependencies": {"@anthropic-ai/sdk": "^0.60.0", "@babel/runtime": "7.24.4", "@firebase/auth": "^1.10.1", "@google-cloud/logging-winston": "^6.0.1", "@google-cloud/pubsub": "^4.10.0", "@google-cloud/recaptcha-enterprise": "^5.11.0", "@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.14.0", "@google-cloud/video-transcoder": "^4.0.1", "@google/genai": "^1.8.0", "@kubernetes/client-node": "0.22.2", "@modelcontextprotocol/sdk": "^1.15.0", "@sendgrid/mail": "^8.1.4", "@speechmatics/auth": "^0.0.1", "@types/node-schedule": "^2.1.7", "altair-express-middleware": "5.0.9", "any-ascii": "0.3.2", "awaitqueue": "3.0.1", "aws-sdk": "2.1205.0", "axios": "0.27.2", "circular-json": "^0.5.9", "colors": "^1.4.0", "compression": "^1.7.5", "cookie": "0.5.0", "cors": "2.8.5", "date-fns": "^4.1.0", "docx": "8.5.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-basic-auth": "^1.2.1", "express-graphql": "0.12.0", "facebook-nodejs-business-sdk": "20.0.0", "fb": "2.0.0", "firebase-admin": "^13.0.2", "format-number": "3.0.0", "fs-extra": "11.2.0", "googleapis": "140.0.1", "graphql-depth-limit": "1.1.0", "graphql-scalars": "1.22.2", "http": "^0.0.1-security", "internal-ip": "6.2.0", "js-sha256": "^0.11.0", "js-yaml": "4.1.0", "langfuse-node": "^3.38.4", "linkedin-api-client": "0.3.0", "lodash": "4.17.21", "markdown-it": "^14.1.0", "mime": "3.0.0", "mkdirp": "3.0.1", "moment-timezone": "^0.5.48", "multer": "1.4.4", "node-schedule": "^2.1.1", "openai": "4.24.1", "openai-chat-tokens": "0.2.8", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "pino": "^9.5.0", "pluralize": "^8.0.0", "posthog-node": "^4.2.1", "qs": "6.12.0", "recall-ai": "1.1.9", "require-directory": "2.1.1", "revai-node-sdk": "^3.8.5", "rrule": "^2.8.1", "sequelize": "^6.37.5", "sharp": "^0.34.3", "socket.io": "4.7.2", "sql-formatter": "^15.4.8", "stack-trace": "0.0.10", "stripe": "12.9.0", "stripe-event-types": "2.3.0", "turndown": "^7.2.0", "ulidx": "2.1.0", "url-parse": "1.5.10", "urns": "0.6.0", "utility-types": "^3.11.0", "uuid": "^11.0.3", "winston": "^3.17.0", "zod": "^3.24.3"}, "devDependencies": {"@types/circular-json": "^0.4.0", "@types/compression": "^1", "@types/cookie": "0.5.1", "@types/cors": "2.8.13", "@types/express": "^5.0.0", "@types/facebook-nodejs-business-sdk": "^18.0.0", "@types/fb": "^0.0.28", "@types/fs-extra": "11.0.4", "@types/graphql-depth-limit": "1.1.3", "@types/lodash": "4.14.184", "@types/mime": "3.0.4", "@types/multer": "1.4.7", "@types/node": "^22.10.2", "@types/qs": "6.9.14", "@types/require-directory": "2.1.2", "@types/sharp": "^0.32.0", "@types/stack-trace": "^0.0.33", "@types/url-parse": "1.4.8", "concurrently": "^9.1.0", "copyfiles": "^2.4.1", "nodemon": "^3.1.7", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsx": "4.19.4", "typescript": "^5.7.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}