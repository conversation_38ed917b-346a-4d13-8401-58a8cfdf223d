# Releases

This directory contains automatically generated release notes for all semantic versions of the Aida Service.

## 📋 Semantic Versioning

We follow [Semantic Versioning](https://semver.org/) (SemVer) with the format `MAJOR.MINOR.PATCH`:

- **MAJOR** version when you make incompatible API changes
- **MINOR** version when you add functionality in a backwards compatible manner
- **PATCH** version when you make backwards compatible bug fixes

## 🏷️ PR Labeling System

Each Pull Request must be labeled with one of the following semantic versioning labels:

| Label            | Description      | Version Bump      | Example                         |
| ---------------- | ---------------- | ----------------- | ------------------------------- |
| `release: major` | Breaking changes | `1.0.0` → `2.0.0` | API changes, removed features   |
| `release: minor` | New features     | `1.0.0` → `1.1.0` | New functionality, enhancements |
| `release: patch` | Bug fixes        | `1.0.0` → `1.0.1` | Bug fixes, security patches     |
| `release: skip`  | No version bump  | No change         | Documentation, CI changes       |

## 🤖 Automated Release Process

Releases are automatically generated when:

1. **Production Deployment Completes** - The semantic release workflow triggers after successful production deployment
2. **PR Analysis** - System analyzes all merged PRs since the last release
3. **Version Calculation** - Determines version bump based on PR labels
4. **Changelog Generation** - AI analyzes PR titles and content to generate categorized changelog
5. **Release Creation** - Updates package.json, creates release file, and publishes GitHub release

## 📁 Release Files

Each release generates:

- **`{version}.md`** - Detailed release notes with categorized changes
- **GitHub Release** - Tagged release with changelog on GitHub
- **package.json** - Updated version number

## 📊 Release Content Structure

Each release file includes:

### 🚨 Breaking Changes

- Major version changes that break backwards compatibility

### ✨ New Features

- New functionality and enhancements (minor version)

### 🐛 Bug Fixes

- Bug fixes and patches (patch version)

### 🔧 Other Changes

- Refactoring, dependencies, and miscellaneous improvements

### 📊 Statistics

- Total PRs, contributors, and change breakdown

## 🔄 Integration with Trunk-Based Development

The semantic versioning process integrates seamlessly with our trunk-based development workflow:

1. **Feature Development** - Create feature branch from `develop`
2. **PR Creation** - Add appropriate semantic versioning label
3. **Review & Merge** - Standard PR review process
4. **Daily Releases** - Continue with existing tag-based releases for staging
5. **Production Deployment** - Manual production deployment triggers semantic release
6. **Automatic Versioning** - System handles version calculation and documentation

## 🛠️ Setup & Configuration

### 📋 Prerequisites

Before using the semantic versioning system, ensure the following are configured:

#### Required GitHub Secrets

| Secret           | Description                              | Required    |
| ---------------- | ---------------------------------------- | ----------- |
| `GITHUB_TOKEN`   | Automatically provided by GitHub Actions | ✅ Auto     |
| `GCP_SA_KEY`     | Google Cloud service account key         | ✅ Required |
| `GCP_PROJECT_ID` | Google Cloud project ID                  | ✅ Required |
| `OPENAI_API_KEY` | For AI-powered changelog generation      | 🔧 Optional |

#### Repository Permissions

Ensure the GitHub Actions have the following permissions:

- **Contents**: Write (for creating release files and updating package.json)
- **Pull Requests**: Read (for analyzing PR labels)
- **Issues**: Read (for PR metadata)

### 🚀 One-Time Setup Steps

#### 1. Sync Semantic Versioning Labels

Create the required labels in your repository:

1. Navigate to **Actions** tab in GitHub
2. Find **"Sync Labels"** workflow
3. Click **"Run workflow"**
4. Select `develop` branch
5. Click **"Run workflow"** button

This creates the following labels:

- `release: major` (red) - Breaking changes
- `release: minor` (blue) - New features
- `release: patch` (green) - Bug fixes
- `release: skip` (purple) - No version bump

#### 2. Verify Workflow Configuration

Check that the semantic release workflow is properly configured:

1. Go to **Actions** tab
2. Look for **"Semantic Release"** workflow
3. Verify it appears in the workflows list
4. Check recent runs for any configuration errors

#### 3. Test the Setup (Dry Run)

Test the semantic release workflow without making actual changes:

1. Go to **Actions** → **"Semantic Release"**
2. Click **"Run workflow"**
3. Select `develop` branch
4. ✅ **Enable "dry_run" mode**
5. Click **"Run workflow"**

This will show you what the workflow would do without creating actual releases.

### 🔧 Configuration Options

#### Customizing Changelog Generation

You can customize the changelog generation by modifying `.github/workflows/semantic-release.yml`:

```yaml
# Example customization options
env:
  NODE_VERSION: "20" # Node.js version (matches Aida Service requirements)
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }} # Optional AI enhancement
```

#### Label Detection Rules

The system detects semantic versioning labels with the following priority:

1. `release: major` (highest priority)
2. `release: minor`
3. `release: patch`
4. `release: skip` (no version bump)

### 📋 Regular Maintenance

#### Monitoring Releases

- **Check release files** in this directory after production deployments
- **Verify package.json** version updates
- **Review GitHub releases** for proper tagging
- **Monitor workflow logs** for any errors

#### Updating Labels

If you need to add or modify labels:

1. Edit `.github/labels.yml`
2. Commit changes to `develop` branch
3. The "Sync Labels" workflow will run automatically

## 🔍 Troubleshooting

### Common Issues & Solutions

#### ❌ Semantic Release Not Triggering

**Symptoms**: No release created after production deployment

**Solutions**:

- Verify production deployment completed successfully
- Check semantic release workflow has proper permissions
- Ensure merged PRs have semantic versioning labels
- Review workflow logs for error messages

#### ❌ No Version Bump Generated

**Symptoms**: Workflow runs but no new version created

**Solutions**:

- Verify PRs since last release have semantic labels
- Check that PR numbers are referenced in commit messages
- Ensure labels are applied before the workflow runs
- Confirm package.json has a valid starting version

#### ❌ Changelog Generation Issues

**Symptoms**: Empty or incorrect changelog content

**Solutions**:

- Verify PR titles are descriptive and clear
- Check that semantic labels are correctly applied
- Ensure PR authors are properly identified
- Review generated PR data in workflow logs

#### ❌ Package.json Not Updated

**Symptoms**: Version number not incremented

**Solutions**:

- Check repository write permissions for GitHub Actions
- Verify git configuration in workflow
- Ensure no merge conflicts with package.json
- Check if branch protection rules are blocking pushes

### 🔧 Manual Recovery

If the automatic process fails, you can manually create a release:

1. **Calculate the correct version** based on merged PRs
2. **Update package.json** manually
3. **Create release file** in this directory
4. **Create GitHub release** with appropriate tag

### 📞 Getting Help

If you encounter issues:

1. **Check workflow logs** in GitHub Actions
2. **Review troubleshooting steps** above
3. **Consult the quick reference guide** at `docs/semantic-versioning-quick-reference.md`
4. **Contact the development team** for technical support

## 🎯 Best Practices

### For Developers:

- Always add semantic versioning labels to PRs
- Keep PR scope focused for clear version categorization
- Document breaking changes thoroughly
- Use conventional commit messages when possible

### For Reviewers:

- Verify semantic versioning labels match PR content
- Ensure breaking changes are properly documented
- Check that version bump type is appropriate

### For Release Management:

- Review generated changelog before major releases
- Verify package.json version updates
- Monitor semantic version progression

## 📈 Version History

All releases are listed below in reverse chronological order:

<!-- Release files will be automatically listed here -->

---

## 📚 Additional Resources

- **📖 Quick Reference Guide**: `docs/semantic-versioning-quick-reference.md`
- **🔗 Semantic Versioning Spec**: https://semver.org/
- **🔧 Workflow Configuration**: `.github/workflows/semantic-release.yml`
- **🏷️ Label Definitions**: `.github/labels.yml`
- **📝 PR Template**: `.github/pull_request_template.md`

---

**Note**: This directory and its contents are automatically managed by the semantic release workflow. Manual edits to release files may be overwritten.
