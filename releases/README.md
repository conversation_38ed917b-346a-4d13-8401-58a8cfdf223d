# RAG Service Releases

This directory contains release notes for the RAG (Retrieval-Augmented Generation) Service.

## Release Process

Releases are automatically generated using semantic versioning based on PR labels:

- `release: patch` - Bug fixes (0.0.X)
- `release: minor` - New features (0.X.0)
- `release: major` - Breaking changes (X.0.0)
- `release: skip` - No version bump

## Automated Workflow

1. **PR Creation**: Use PR template with release type selection
2. **Auto-labeling**: Labels applied automatically based on template
3. **PR Validation**: Ensures proper labeling before merge
4. **Deployment**: Automatic deployment to dev/staging/production
5. **Release Creation**: Automatic semantic release after production deployment

## Release Files

Each release creates a markdown file with:
- Changelog categorized by change type
- Contributor information
- Release statistics
- RAG and MCP specific information

Release files are named: `{version}.md` (e.g., `1.2.3.md`)

## Service Information

The RAG Service provides:
- Retrieval-Augmented Generation functionality
- Model Control Protocol (MCP) integration
- Pinecone vector database integration
- Horizontal scalability
- FastAPI with automatic documentation
